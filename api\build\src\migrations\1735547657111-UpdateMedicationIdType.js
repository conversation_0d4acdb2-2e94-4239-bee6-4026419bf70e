"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateMedicationIdType1735547657111 = void 0;
class UpdateMedicationIdType1735547657111 {
    constructor() {
        this.name = 'UpdateMedicationIdType1735547657111';
    }
    async up(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE clinic_medications 
            ALTER COLUMN id TYPE uuid USING id::uuid
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE clinic_medications 
            ALTER COLUMN id TYPE integer USING id::integer
        `);
    }
}
exports.UpdateMedicationIdType1735547657111 = UpdateMedicationIdType1735547657111;
//# sourceMappingURL=1735547657111-UpdateMedicationIdType.js.map