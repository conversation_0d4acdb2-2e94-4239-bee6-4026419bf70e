"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddMoreColumsToClinicLabReports1725533401209 = void 0;
const typeorm_1 = require("typeorm");
class AddMoreColumsToClinicLabReports1725533401209 {
    async up(queryRunner) {
        await queryRunner.addColumns('clinic_lab_reports', [
            new typeorm_1.TableColumn({
                name: 'brand_id',
                type: 'uuid',
                isNullable: false,
                default: `'1a9c7e74-9e8f-4a3e-a045-ea3f141e0fe0'`
            }),
            new typeorm_1.TableColumn({
                name: 'chargeable_price',
                type: 'decimal',
                scale: 2, // it specifies digits after the decimal point
                precision: 10, //  it specifies the total number of digits that can be stored, both before and after the decimal point
                isNullable: true,
            }),
            new typeorm_1.TableColumn({
                name: 'tax',
                type: 'decimal',
                scale: 2, // it specifies digits after the decimal point
                precision: 10, //  it specifies the total number of digits that can be stored, both before and after the decimal point
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'associated_lab',
                type: 'varchar',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'description',
                type: 'varchar',
                isNullable: false,
                default: "''",
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('clinic_lab_reports', 'brand_id');
        await queryRunner.dropColumn('clinic_lab_reports', 'chargeable_price');
        await queryRunner.dropColumn('clinic_lab_reports', 'tax');
        await queryRunner.dropColumn('clinic_lab_reports', 'associated_lab');
        await queryRunner.dropColumn('clinic_lab_reports', 'description');
    }
}
exports.AddMoreColumsToClinicLabReports1725533401209 = AddMoreColumsToClinicLabReports1725533401209;
//# sourceMappingURL=1725533401209-AddMoreColumsToClinicLabReports.js.map