{"version": 3, "file": "1722242391046-DropAndCreatePatientsTable.js", "sourceRoot": "", "sources": ["../../../src/migrations/1722242391046-DropAndCreatePatientsTable.ts"], "names": [], "mappings": ";;;AAAA,qCAKiB;AAEjB,MAAa,mCAAmC;IACxC,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,MAAM,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAE1D,MAAM,WAAW,CAAC,WAAW,CAC5B,IAAI,eAAK,CAAC;YACT,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE;gBACR;oBACC,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,IAAI;oBACjB,kBAAkB,EAAE,MAAM;iBAC1B;gBACD;oBACC,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,KAAK;iBACjB;gBACD;oBACC,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,KAAK;oBACX,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC;oBACnC,OAAO,EAAE,WAAW;iBACpB;gBACD;oBACC,IAAI,EAAE,qBAAqB;oBAC3B,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC;oBACtC,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,gBAAgB;oBACtB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;iBAC5B;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;oBAC5B,QAAQ,EAAE,mBAAmB;iBAC7B;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;aACD;SACD,CAAC,EACF,IAAI,CACJ,CAAC;QAEF,MAAM,WAAW,CAAC,iBAAiB,CAAC,UAAU,EAAE;YAC/C,IAAI,yBAAe,CAAC;gBACnB,WAAW,EAAE,CAAC,WAAW,CAAC;gBAC1B,qBAAqB,EAAE,CAAC,IAAI,CAAC;gBAC7B,mBAAmB,EAAE,SAAS;gBAC9B,QAAQ,EAAE,UAAU;aACpB,CAAC;YACF,IAAI,yBAAe,CAAC;gBACnB,WAAW,EAAE,CAAC,YAAY,CAAC;gBAC3B,qBAAqB,EAAE,CAAC,IAAI,CAAC;gBAC7B,mBAAmB,EAAE,OAAO;gBAC5B,QAAQ,EAAE,UAAU;aACpB,CAAC;YACF,IAAI,yBAAe,CAAC;gBACnB,WAAW,EAAE,CAAC,YAAY,CAAC;gBAC3B,qBAAqB,EAAE,CAAC,IAAI,CAAC;gBAC7B,mBAAmB,EAAE,OAAO;gBAC5B,QAAQ,EAAE,UAAU;aACpB,CAAC;SACF,CAAC,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,MAAM,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAE1D,MAAM,WAAW,CAAC,WAAW,CAC5B,IAAI,eAAK,CAAC;YACT,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE;gBACR;oBACC,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,IAAI;oBACjB,kBAAkB,EAAE,MAAM;iBAC1B;gBACD;oBACC,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,KAAK;iBACjB;gBACD;oBACC,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,KAAK;oBACX,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC;oBACnC,OAAO,EAAE,WAAW;iBACpB;gBACD;oBACC,IAAI,EAAE,qBAAqB;oBAC3B,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC;oBACtC,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,gBAAgB;oBACtB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;iBAC5B;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;oBAC5B,QAAQ,EAAE,mBAAmB;iBAC7B;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;aACD;SACD,CAAC,EACF,IAAI,CACJ,CAAC;IACH,CAAC;CACD;AAzND,kFAyNC"}