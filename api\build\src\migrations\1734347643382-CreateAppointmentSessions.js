"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAppointmentSessions1734347643382 = void 0;
const typeorm_1 = require("typeorm");
class CreateAppointmentSessions1734347643382 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'appointment_sessions',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'user_id',
                    type: 'uuid'
                },
                {
                    name: 'socket_id',
                    type: 'varchar'
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'NOW()'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'NOW()'
                }
            ]
        }));
        await queryRunner.createForeignKey('appointment_sessions', new typeorm_1.TableForeignKey({
            columnNames: ['user_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinic_users',
            onDelete: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable('appointment_sessions');
        const foreignKey = table === null || table === void 0 ? void 0 : table.foreignKeys.find(fk => fk.columnNames.indexOf('user_id') !== -1);
        if (foreignKey) {
            await queryRunner.dropForeignKey('appointment_sessions', foreignKey);
        }
        await queryRunner.dropTable('appointment_sessions');
    }
}
exports.CreateAppointmentSessions1734347643382 = CreateAppointmentSessions1734347643382;
//# sourceMappingURL=1734347643382-CreateAppointmentSessions.js.map