import { Response } from 'express';
import { AnalyticsService } from './analytics.service';
import { AnalyticsType, AnalyticsReportType, RevenueChartDataPoint, CollectedPaymentsChartDataPoint, AppointmentsChartResponse, AppointmentAnalyticsType, DoctorSummaryResponseDto, SummaryResponseDto } from './dto/analytics.dto';
export declare class AnalyticsController {
    private readonly analyticsService;
    constructor(analyticsService: AnalyticsService);
    getRevenueChartData(startDate: string, endDate: string, clinicId: string): Promise<RevenueChartDataPoint[]>;
    getCollectedPaymentsChartData(startDate: string, endDate: string, clinicId: string): Promise<CollectedPaymentsChartDataPoint[]>;
    getAppointmentsChartData(startDate: string, endDate: string, clinicId: string, type: AppointmentAnalyticsType): Promise<AppointmentsChartResponse>;
    downloadReport(type: AnalyticsType, startDate: string, endDate: string, clinicId: string, res: Response, reportType?: AnalyticsReportType): Promise<void>;
    getDoctorSummary(startDate: string, endDate: string, clinicId: string): Promise<DoctorSummaryResponseDto[]>;
    getSummary(startDate: string, endDate: string, clinicId: string): Promise<SummaryResponseDto>;
}
