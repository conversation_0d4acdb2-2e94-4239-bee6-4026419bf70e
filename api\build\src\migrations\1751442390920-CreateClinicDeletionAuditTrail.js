"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateClinicDeletionAuditTrail1751442390920 = void 0;
const typeorm_1 = require("typeorm");
class CreateClinicDeletionAuditTrail1751442390920 {
    async up(queryRunner) {
        // Create enum for operation types
        await queryRunner.query(`
			CREATE TYPE "public"."clinic_deletion_audit_operation_enum" AS ENUM('backup', 'deletion', 'restore')
		`);
        // Create enum for target types
        await queryRunner.query(`
			CREATE TYPE "public"."clinic_deletion_audit_target_type_enum" AS ENUM('CLINIC', 'BRAND')
		`);
        // Create the clinic_deletion_audit_trail table
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'clinic_deletion_audit_trail',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'operation',
                    type: 'enum',
                    enum: ['backup', 'deletion', 'restore'],
                    enumName: 'clinic_deletion_audit_operation_enum',
                    isNullable: false,
                    comment: 'Type of operation performed (backup, deletion, restore)'
                },
                {
                    name: 'target_type',
                    type: 'enum',
                    enum: ['CLINIC', 'BRAND'],
                    enumName: 'clinic_deletion_audit_target_type_enum',
                    isNullable: false,
                    comment: 'Type of target entity (CLINIC or BRAND)'
                },
                {
                    name: 'target_id',
                    type: 'uuid',
                    isNullable: false,
                    comment: 'ID of the target entity (clinic or brand)'
                },
                {
                    name: 'target_name',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                    comment: 'Name of the target entity for easier identification'
                },
                {
                    name: 'user_id',
                    type: 'uuid',
                    isNullable: false,
                    comment: 'ID of the user who performed the operation'
                },
                {
                    name: 'success',
                    type: 'boolean',
                    isNullable: false,
                    default: false,
                    comment: 'Whether the operation was successful'
                },
                {
                    name: 'backup_id',
                    type: 'uuid',
                    isNullable: true,
                    comment: 'ID of the backup associated with this operation'
                },
                {
                    name: 'backup_location',
                    type: 'text',
                    isNullable: true,
                    comment: 'S3 path where backup is stored'
                },
                {
                    name: 'operation_data',
                    type: 'jsonb',
                    isNullable: true,
                    comment: 'Detailed operation data including results, statistics, etc.'
                },
                {
                    name: 'error_details',
                    type: 'jsonb',
                    isNullable: true,
                    comment: 'Error details if operation failed'
                },
                {
                    name: 'duration_ms',
                    type: 'integer',
                    isNullable: true,
                    comment: 'Operation duration in milliseconds'
                },
                {
                    name: 'ip_address',
                    type: 'inet',
                    isNullable: true,
                    comment: 'IP address of the user who performed the operation'
                },
                {
                    name: 'user_agent',
                    type: 'text',
                    isNullable: true,
                    comment: 'User agent of the client that performed the operation'
                },
                {
                    name: 'created_at',
                    type: 'timestamp with time zone',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false,
                    comment: 'Timestamp when the audit entry was created'
                }
            ]
        }));
        // Create indexes for better query performance
        await queryRunner.createIndex('clinic_deletion_audit_trail', new typeorm_1.TableIndex({
            name: 'IDX_clinic_deletion_audit_trail_target_id',
            columnNames: ['target_id']
        }));
        await queryRunner.createIndex('clinic_deletion_audit_trail', new typeorm_1.TableIndex({
            name: 'IDX_clinic_deletion_audit_trail_user_id',
            columnNames: ['user_id']
        }));
        await queryRunner.createIndex('clinic_deletion_audit_trail', new typeorm_1.TableIndex({
            name: 'IDX_clinic_deletion_audit_trail_operation',
            columnNames: ['operation']
        }));
        await queryRunner.createIndex('clinic_deletion_audit_trail', new typeorm_1.TableIndex({
            name: 'IDX_clinic_deletion_audit_trail_target_type',
            columnNames: ['target_type']
        }));
        await queryRunner.createIndex('clinic_deletion_audit_trail', new typeorm_1.TableIndex({
            name: 'IDX_clinic_deletion_audit_trail_created_at',
            columnNames: ['created_at']
        }));
        await queryRunner.createIndex('clinic_deletion_audit_trail', new typeorm_1.TableIndex({
            name: 'IDX_clinic_deletion_audit_trail_backup_id',
            columnNames: ['backup_id']
        }));
        await queryRunner.createIndex('clinic_deletion_audit_trail', new typeorm_1.TableIndex({
            name: 'IDX_clinic_deletion_audit_trail_success',
            columnNames: ['success']
        }));
        // Create composite indexes for common query patterns
        await queryRunner.createIndex('clinic_deletion_audit_trail', new typeorm_1.TableIndex({
            name: 'IDX_clinic_deletion_audit_trail_target_operation',
            columnNames: ['target_id', 'operation']
        }));
        await queryRunner.createIndex('clinic_deletion_audit_trail', new typeorm_1.TableIndex({
            name: 'IDX_clinic_deletion_audit_trail_user_created',
            columnNames: ['user_id', 'created_at']
        }));
        // Add foreign key constraints
        await queryRunner.query(`
			ALTER TABLE "clinic_deletion_audit_trail" 
			ADD CONSTRAINT "FK_clinic_deletion_audit_trail_user_id" 
			FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
		`);
        // Add check constraints for data integrity
        await queryRunner.query(`
			ALTER TABLE "clinic_deletion_audit_trail" 
			ADD CONSTRAINT "CHK_clinic_deletion_audit_trail_backup_data" 
			CHECK (
				(operation = 'backup' AND backup_id IS NOT NULL AND backup_location IS NOT NULL) OR
				(operation != 'backup')
			)
		`);
    }
    async down(queryRunner) {
        // Drop the table (this will also drop all indexes and constraints)
        await queryRunner.dropTable('clinic_deletion_audit_trail');
        // Drop the enums
        await queryRunner.query('DROP TYPE "public"."clinic_deletion_audit_operation_enum"');
        await queryRunner.query('DROP TYPE "public"."clinic_deletion_audit_target_type_enum"');
    }
}
exports.CreateClinicDeletionAuditTrail1751442390920 = CreateClinicDeletionAuditTrail1751442390920;
//# sourceMappingURL=1751442390920-CreateClinicDeletionAuditTrail.js.map