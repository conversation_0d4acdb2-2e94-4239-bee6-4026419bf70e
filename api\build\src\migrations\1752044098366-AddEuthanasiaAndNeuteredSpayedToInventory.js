"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddEuthanasiaAndNeuteredSpayedToInventory1752044098366 = void 0;
const typeorm_1 = require("typeorm");
class AddEuthanasiaAndNeuteredSpayedToInventory1752044098366 {
    async up(queryRunner) {
        await queryRunner.addColumns('clinic_medications', [
            new typeorm_1.TableColumn({
                name: 'euthanasia',
                type: 'boolean',
                default: false
            }),
            new typeorm_1.TableColumn({
                name: 'neutered_spayed',
                type: 'boolean',
                default: false
            })
        ]);
        await queryRunner.addColumns('clinic_services', [
            new typeorm_1.TableColumn({
                name: 'euthanasia',
                type: 'boolean',
                default: false
            }),
            new typeorm_1.TableColumn({
                name: 'neutered_spayed',
                type: 'boolean',
                default: false
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumns('clinic_medications', [
            'euthanasia',
            'neutered_spayed'
        ]);
        await queryRunner.dropColumns('clinic_services', [
            'euthanasia',
            'neutered_spayed'
        ]);
    }
}
exports.AddEuthanasiaAndNeuteredSpayedToInventory1752044098366 = AddEuthanasiaAndNeuteredSpayedToInventory1752044098366;
//# sourceMappingURL=1752044098366-AddEuthanasiaAndNeuteredSpayedToInventory.js.map