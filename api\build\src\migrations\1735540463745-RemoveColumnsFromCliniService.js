"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RemoveColumnsFromCliniService1735540463745 = void 0;
const typeorm_1 = require("typeorm");
class RemoveColumnsFromCliniService1735540463745 {
    constructor() {
        this.name = 'RemoveColumnsFromCliniService1735540463745';
    }
    async up(queryRunner) {
        await queryRunner.dropColumn('clinic_services', 'description');
        await queryRunner.dropColumn('clinic_services', 'associated_lab');
    }
    async down(queryRunner) {
        await queryRunner.addColumn('clinic_services', new typeorm_1.TableColumn({
            name: 'description',
            type: 'varchar'
        }));
        await queryRunner.addColumn('clinic_services', new typeorm_1.TableColumn({
            name: 'associated_lab',
            type: 'integer'
        }));
    }
}
exports.RemoveColumnsFromCliniService1735540463745 = RemoveColumnsFromCliniService1735540463745;
//# sourceMappingURL=1735540463745-RemoveColumnsFromCliniService.js.map