"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateLabReportsTable1724408710032 = void 0;
const typeorm_1 = require("typeorm");
class CreateLabReportsTable1724408710032 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: "lab_reports",
            columns: [
                {
                    name: "id",
                    type: "uuid",
                    isPrimary: true,
                    default: "uuid_generate_v4()",
                },
                {
                    name: "appointment_id",
                    type: "uuid",
                },
                {
                    name: "clinic_lab_report_id",
                    type: "uuid",
                },
                {
                    name: "files",
                    type: "jsonb",
                    isNullable: true,
                },
                {
                    name: "created_at",
                    type: "timestamp",
                    default: "now()",
                },
                {
                    name: "updated_at",
                    type: "timestamp",
                    default: "now()",
                },
                {
                    name: "created_by",
                    type: "uuid",
                    isNullable: true,
                },
                {
                    name: "updated_by",
                    type: "uuid",
                    isNullable: true,
                },
            ],
            foreignKeys: [
                {
                    columnNames: ["appointment_id"],
                    referencedTableName: "appointments",
                    referencedColumnNames: ["id"],
                },
                {
                    columnNames: ["clinic_lab_report_id"],
                    referencedTableName: "clinic_lab_reports",
                    referencedColumnNames: ["id"],
                },
            ],
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable("lab_reports");
    }
}
exports.CreateLabReportsTable1724408710032 = CreateLabReportsTable1724408710032;
//# sourceMappingURL=1724408710032-CreateLabReportsTable.js.map