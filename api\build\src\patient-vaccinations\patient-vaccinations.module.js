"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientVaccinationsModule = void 0;
const common_1 = require("@nestjs/common");
const patient_vaccinations_controller_1 = require("./patient-vaccinations.controller");
const patient_vaccinations_service_1 = require("./patient-vaccinations.service");
const typeorm_1 = require("@nestjs/typeorm");
const patient_vaccinations_entity_1 = require("./entities/patient-vaccinations.entity");
const s3_module_1 = require("../utils/aws/s3/s3.module");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
let PatientVaccinationsModule = class PatientVaccinationsModule {
};
exports.PatientVaccinationsModule = PatientVaccinationsModule;
exports.PatientVaccinationsModule = PatientVaccinationsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([patient_vaccinations_entity_1.PatientVaccination]), s3_module_1.S3Module],
        controllers: [patient_vaccinations_controller_1.PatientVaccinationsController],
        providers: [patient_vaccinations_service_1.PatientVaccinationsService, winston_logger_service_1.WinstonLogger],
        exports: [patient_vaccinations_service_1.PatientVaccinationsService]
    })
], PatientVaccinationsModule);
//# sourceMappingURL=patient-vaccinations.module.js.map