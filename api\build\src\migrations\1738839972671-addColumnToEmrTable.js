"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddColumnToEmrTable1738839972671 = void 0;
const typeorm_1 = require("typeorm");
class AddColumnToEmrTable1738839972671 {
    async up(queryRunner) {
        await queryRunner.addColumn('emr', new typeorm_1.TableColumn({
            name: 'appointment_updated_at',
            type: 'timestamp',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('emr', 'appointment_updated_at');
    }
}
exports.AddColumnToEmrTable1738839972671 = AddColumnToEmrTable1738839972671;
//# sourceMappingURL=1738839972671-addColumnToEmrTable.js.map