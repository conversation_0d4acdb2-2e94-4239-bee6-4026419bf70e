"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddPaymentNotesColumn1739792667225 = void 0;
class AddPaymentNotesColumn1739792667225 {
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "payment_details" ADD COLUMN "payment_notes" TEXT`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "payment_details" DROP COLUMN "payment_notes"`);
    }
}
exports.AddPaymentNotesColumn1739792667225 = AddPaymentNotesColumn1739792667225;
//# sourceMappingURL=1739792667225-add_payment_notes_column.js.map