"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddNewColPatientRemindersTable1735201825528 = void 0;
const typeorm_1 = require("typeorm");
class AddNewColPatientRemindersTable1735201825528 {
    async up(queryRunner) {
        await queryRunner.addColumn('patient_reminders', new typeorm_1.TableColumn({
            name: 'is_manually_completed',
            type: 'boolean',
            default: false,
            isNullable: false
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('patient_reminders', 'is_manually_completed');
    }
}
exports.AddNewColPatientRemindersTable1735201825528 = AddNewColPatientRemindersTable1735201825528;
//# sourceMappingURL=1735201825528-AddNewColPatientRemindersTable.js.map