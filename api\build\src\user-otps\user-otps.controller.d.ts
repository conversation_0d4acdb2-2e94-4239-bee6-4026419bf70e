import { UserOtpsService } from './user-otps.service';
import { UsersService } from '../users/users.service';
import { GenerateOtpDto } from './dto/generate-otp-dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ValidateOtpDto } from './dto/validate-user-otp.dto';
export declare class UserOtpController {
    private readonly userOtpsService;
    private readonly usersService;
    private readonly logger;
    constructor(userOtpsService: UserOtpsService, usersService: UsersService, logger: WinstonLogger);
    generateOtp(generateOtpDto: GenerateOtpDto): Promise<{
        statusCode: number;
        message: string;
    }>;
    validateOtp(validateOtpDto: ValidateOtpDto): Promise<{
        token: string;
        roleName: string;
    }>;
}
