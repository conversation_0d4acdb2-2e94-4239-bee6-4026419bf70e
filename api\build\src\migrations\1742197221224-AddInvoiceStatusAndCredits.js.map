{"version": 3, "file": "1742197221224-AddInvoiceStatusAndCredits.js", "sourceRoot": "", "sources": ["../../../src/migrations/1742197221224-AddInvoiceStatusAndCredits.ts"], "names": [], "mappings": ";;;AACA,4EAAsE;AAEtE,MAAa,uCAAuC;IAG3C,oBAAoB,CAAC,MAAc;QAC1C,MAAM,UAAU,GAAG,sCAAsC,CAAC;QAC1D,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,CAClC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAChE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACZ,CAAC;IAEM,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,6BAA6B;QAC7B,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;SAOjB,CAAC,CAAC;QAET,oCAAoC;QACpC,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKjB,CAAC,CAAC;QAET,iEAAiE;QACjE,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGjB,CAAC,CAAC;QAET,wEAAwE;QACxE,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGjB,CAAC,CAAC;QAET,gDAAgD;QAChD,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGjB,CAAC,CAAC;QAET,sCAAsC;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;SAMjB,CAAC,CAAC;QAET,0CAA0C;QAC1C,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGjB,CAAC,CAAC;QAET,sCAAsC;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKjB,CAAC,CAAC;QAET,mCAAmC;QACnC,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;SAkBjB,CAAC,CAAC;QAET,8BAA8B;QAC9B,MAAM,WAAW,CAAC,KAAK,CAAC;;;;SAIjB,CAAC,CAAC;QAET,MAAM,WAAW,CAAC,KAAK,CAAC;;;;SAIjB,CAAC,CAAC;QAET,MAAM,WAAW,CAAC,KAAK,CAAC;;;;SAIjB,CAAC,CAAC;QAET,iCAAiC;QACjC,MAAM,WAAW,CAAC,KAAK,CAAC;;SAEjB,CAAC,CAAC;QAET,MAAM,WAAW,CAAC,KAAK,CAAC;;SAEjB,CAAC,CAAC;QAET,MAAM,WAAW,CAAC,KAAK,CAAC;;SAEjB,CAAC,CAAC;QAET,sBAAsB;QACtB,uCAAuC;QACvC,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;;;;;SAchC,CAAC,CAAC;QAET,qBAAqB;QACrB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC5B,IAAI,KAAK,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;gBAC7B,mCAAmC;gBACnC,MAAM,WAAW,CAAC,KAAK,CACtB;;;;;;iBAMY,EACZ,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,QAAQ,CAAC,CACrC,CAAC;gBAEF,qCAAqC;gBACrC,MAAM,WAAW,CAAC,KAAK,CACtB;;;;;;;;;;;;;iBAaY,EACZ;oBACC,KAAK,CAAC,QAAQ;oBACd,KAAK,CAAC,aAAa;oBACnB,KAAK,CAAC,SAAS;oBACf,KAAK,CAAC,QAAQ;iBACd,CACD,CAAC;YACH,CAAC;iBAAM,IAAI,KAAK,CAAC,aAAa,GAAG,CAAC,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;gBACxD,yCAAyC;gBACzC,MAAM,UAAU,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;iBAU9B,CAAC,CAAC;gBACf,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAEhC,6DAA6D;gBAC7D,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;gBACtD,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;gBACtD,MAAM,uBAAuB,GAAG,GAAG,gBAAgB,EAAE,CAAC;gBACtD,MAAM,uBAAuB,GAAG,GAAG,gBAAgB,EAAE,CAAC;gBAEtD,uCAAuC;gBACvC,MAAM,YAAY,GAAG,MAAM,WAAW,CAAC,KAAK,CAC3C;;;;qBAIgB,EAChB,CAAC,uBAAuB,EAAE,uBAAuB,CAAC,CAClD,CAAC;gBAEF,sCAAsC;gBACtC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC7B,SAAS,CAAC,oCAAoC;gBAC/C,CAAC;gBAED,6CAA6C;gBAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;gBACjD,MAAM,aAAa,GAAG,MAAM,WAAW,CAAC,KAAK,CAC5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iBAqCY,EACZ;oBACC,MAAM;oBACN,KAAK,CAAC,UAAU;oBAChB,KAAK,CAAC,SAAS;oBACf,KAAK,CAAC,QAAQ;oBACd,KAAK,CAAC,QAAQ;oBACd,UAAU;oBACV,oCAAe,CAAC,OAAO;oBACvB,uBAAuB;iBACvB,CACD,CAAC;gBAEF,yDAAyD;gBACzD,MAAM,WAAW,CAAC,KAAK,CACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iBAiCY,EACZ;oBACC,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;oBACnB,KAAK,CAAC,QAAQ;oBACd,KAAK,CAAC,UAAU;oBAChB,KAAK,CAAC,SAAS;oBACf,KAAK,CAAC,QAAQ;oBACd,UAAU;oBACV,uBAAuB;iBACvB,CACD,CAAC;gBAEF,2BAA2B;gBAC3B,MAAM,WAAW,CAAC,KAAK,CACtB;;;;iBAIY,EACZ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAChB,CAAC;YACH,CAAC;QACF,CAAC;IACF,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,iCAAiC;QACjC,gDAAgD;QAChD,MAAM,kBAAkB,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;SAO5C,CAAC,CAAC;QAET,mDAAmD;QACnD,KAAK,MAAM,OAAO,IAAI,kBAAkB,EAAE,CAAC;YAC1C,MAAM,WAAW,CAAC,KAAK,CACtB;;;;aAIS,EACT,CAAC,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,QAAQ,CAAC,CAC1C,CAAC;YAEF,qBAAqB;YACrB,MAAM,WAAW,CAAC,KAAK,CACtB;;aAES,EACT,CAAC,OAAO,CAAC,EAAE,CAAC,CACZ,CAAC;QACH,CAAC;QAED,mDAAmD;QACnD,MAAM,iBAAiB,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;SAM3C,CAAC,CAAC;QAET,wDAAwD;QACxD,KAAK,MAAM,MAAM,IAAI,iBAAiB,EAAE,CAAC;YACxC,MAAM,WAAW,CAAC,KAAK,CACtB;;;;;;aAMS,EACT,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,CAChC,CAAC;YAEF,gCAAgC;YAChC,MAAM,WAAW,CAAC,KAAK,CACtB;;;;aAIS,EACT,CAAC,MAAM,CAAC,QAAQ,CAAC,CACjB,CAAC;QACH,CAAC;QAED,eAAe;QACf,MAAM,WAAW,CAAC,KAAK,CACtB,wDAAwD,CACxD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,iDAAiD,CACjD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,+CAA+C,CAC/C,CAAC;QAEF,+BAA+B;QAC/B,MAAM,WAAW,CAAC,KAAK,CACtB,8FAA8F,CAC9F,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,uFAAuF,CACvF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,qFAAqF,CACrF,CAAC;QAEF,iCAAiC;QACjC,MAAM,WAAW,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC;QAE5D,oCAAoC;QACpC,MAAM,WAAW,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAEpE,gDAAgD;QAChD,MAAM,WAAW,CAAC,KAAK,CACtB,wDAAwD,CACxD,CAAC;QAEF,sCAAsC;QACtC,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;SAMjB,CAAC,CAAC;QAET,+BAA+B;QAC/B,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKjB,CAAC,CAAC;QAET,2BAA2B;QAC3B,MAAM,WAAW,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;IAC5D,CAAC;CACD;AA9bD,0FA8bC"}