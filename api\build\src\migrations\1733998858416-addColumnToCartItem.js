"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddColumnToCartItem1733998858416 = void 0;
const typeorm_1 = require("typeorm");
class AddColumnToCartItem1733998858416 {
    constructor() {
        this.name = 'AddColumnToCartItem1733998858416';
    }
    async up(queryRunner) {
        await queryRunner.addColumn('cart_items', new typeorm_1.TableColumn({
            name: 'added_from',
            type: 'varchar',
            isNullable: true,
            default: null
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('cart_items', 'added_from');
    }
}
exports.AddColumnToCartItem1733998858416 = AddColumnToCartItem1733998858416;
//# sourceMappingURL=1733998858416-addColumnToCartItem.js.map