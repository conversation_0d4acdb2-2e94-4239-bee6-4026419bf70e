"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddSoftDeleteAndAuditLogCustom1748850317730 = void 0;
class AddSoftDeleteAndAuditLogCustom1748850317730 {
    constructor() {
        this.name = 'AddSoftDeleteAndAuditLogCustom1748850317730';
    }
    async up(queryRunner) {
        // Create enum for audit log operation type
        await queryRunner.query(`
			CREATE TYPE "public"."invoice_audit_log_operation_type_enum" AS ENUM('CREATE', 'UPDATE', 'DELETE', 'WRITE_OFF')
		`);
        // Create invoice_audit_log table
        await queryRunner.query(`
			CREATE TABLE "invoice_audit_log" (
				"id" uuid NOT NULL DEFAULT uuid_generate_v4(),
				"invoice_id" uuid NOT NULL,
				"user_id" uuid,
				"operation_type" "public"."invoice_audit_log_operation_type_enum" NOT NULL,
				"changes" jsonb,
				"changed_fields_summary" jsonb,
				"timestamp" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
				CONSTRAINT "PK_invoice_audit_log" PRIMARY KEY ("id")
			)
		`);
        // Add deleted_at column to invoices table
        await queryRunner.query(`
			ALTER TABLE "invoices" ADD "deleted_at" TIMESTAMP WITH TIME ZONE
		`);
        await queryRunner.query(`
            ALTER TYPE "invoice_status_enum" ADD VALUE 'written_off'
        `);
        await queryRunner.query(`
            ALTER TYPE "invoice_status_enum" ADD VALUE 'cancelled'
        `);
        // Add deleted_at column to payment_details table
        await queryRunner.query(`
			ALTER TABLE "payment_details" ADD "deleted_at" TIMESTAMP WITH TIME ZONE
		`);
        // Add deleted_at column to carts table
        await queryRunner.query(`
			ALTER TABLE "carts" ADD "deleted_at" TIMESTAMP WITH TIME ZONE
		`);
        // Add deleted_at column to patient_vaccinations table
        await queryRunner.query(`
			ALTER TABLE "patient_vaccinations" ADD "deleted_at" TIMESTAMP WITH TIME ZONE
		`);
        // Add invoice_id column to patient_reminders table for tracking reminders created from invoices
        await queryRunner.query(`
			ALTER TABLE "patient_reminders" ADD "invoice_id" uuid
		`);
        // Note: appointments table already has deleted_at column but not using @DeleteDateColumn
        // We'll update the entity to use @DeleteDateColumn decorator for consistency
        // Add foreign key constraints for audit log
        await queryRunner.query(`
			ALTER TABLE "invoice_audit_log" ADD CONSTRAINT "FK_invoice_audit_log_invoice_id" 
			FOREIGN KEY ("invoice_id") REFERENCES "invoices"("id") ON DELETE SET NULL ON UPDATE NO ACTION
		`);
        await queryRunner.query(`
			ALTER TABLE "invoice_audit_log" ADD CONSTRAINT "FK_invoice_audit_log_user_id" 
			FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
		`);
        // Add indexes for better performance
        await queryRunner.query(`
			CREATE INDEX "IDX_invoice_audit_log_invoice_id" ON "invoice_audit_log" ("invoice_id")
		`);
        await queryRunner.query(`
			CREATE INDEX "IDX_invoice_audit_log_user_id" ON "invoice_audit_log" ("user_id")
		`);
        await queryRunner.query(`
			CREATE INDEX "IDX_invoice_audit_log_timestamp" ON "invoice_audit_log" ("timestamp")
		`);
        await queryRunner.query(`
			CREATE INDEX "IDX_invoices_deleted_at" ON "invoices" ("deleted_at")
		`);
        await queryRunner.query(`
			CREATE INDEX "IDX_payment_details_deleted_at" ON "payment_details" ("deleted_at")
		`);
        await queryRunner.query(`
			CREATE INDEX "IDX_carts_deleted_at" ON "carts" ("deleted_at")
		`);
        await queryRunner.query(`
			CREATE INDEX "IDX_patient_vaccinations_deleted_at" ON "patient_vaccinations" ("deleted_at")
		`);
        await queryRunner.query(`
			CREATE INDEX "IDX_patient_reminders_invoice_id" ON "patient_reminders" ("invoice_id")
		`);
        // Backfill CREATE audit logs for existing invoices
        // Since created_by is already UUID type, we can use it directly
        await queryRunner.query(`
			INSERT INTO invoice_audit_log (invoice_id, user_id, operation_type, changes, changed_fields_summary, timestamp)
			SELECT 
				i.id as invoice_id,
				i.created_by as user_id,
				'CREATE' as operation_type,
				NULL as changes,
				jsonb_build_array(
					jsonb_build_object(
						'type', 'ADD_ITEM',
						'description', 'Invoice created'
					)
				) as changed_fields_summary,
				i.created_at as timestamp
			FROM invoices i
			WHERE NOT EXISTS (
				SELECT 1 FROM invoice_audit_log ial 
				WHERE ial.invoice_id = i.id AND ial.operation_type = 'CREATE'
			)
		`);
    }
    async down(queryRunner) {
        // Drop indexes
        await queryRunner.query(`DROP INDEX "public"."IDX_patient_reminders_invoice_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_patient_vaccinations_deleted_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_carts_deleted_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_payment_details_deleted_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_invoices_deleted_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_invoice_audit_log_timestamp"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_invoice_audit_log_user_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_invoice_audit_log_invoice_id"`);
        // Drop foreign key constraints
        await queryRunner.query(`ALTER TABLE "invoice_audit_log" DROP CONSTRAINT "FK_invoice_audit_log_user_id"`);
        await queryRunner.query(`ALTER TABLE "invoice_audit_log" DROP CONSTRAINT "FK_invoice_audit_log_invoice_id"`);
        // Drop deleted_at columns
        await queryRunner.query(`ALTER TABLE "patient_vaccinations" DROP COLUMN "deleted_at"`);
        await queryRunner.query(`ALTER TABLE "carts" DROP COLUMN "deleted_at"`);
        await queryRunner.query(`ALTER TABLE "payment_details" DROP COLUMN "deleted_at"`);
        await queryRunner.query(`ALTER TABLE "invoices" DROP COLUMN "deleted_at"`);
        // Drop invoice_id column from patient_reminders
        await queryRunner.query(`ALTER TABLE "patient_reminders" DROP COLUMN "invoice_id"`);
        // Drop audit log table
        await queryRunner.query(`DROP TABLE "invoice_audit_log"`);
        // Drop enum
        await queryRunner.query(`DROP TYPE "public"."invoice_audit_log_operation_type_enum"`);
    }
}
exports.AddSoftDeleteAndAuditLogCustom1748850317730 = AddSoftDeleteAndAuditLogCustom1748850317730;
//# sourceMappingURL=1748850317730-AddSoftDeleteAndAuditLogCustom.js.map