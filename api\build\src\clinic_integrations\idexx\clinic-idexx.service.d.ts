import { CreateClinicIdexxDto } from './dto/create-clinic-idexx.dto';
import { CreateClinicIdexxEntity } from './entities/create-clinic-idexx.entity';
import { Repository } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { ClinicLabReport } from '../../clinic-lab-report/entities/clinic-lab-report.entity';
import { CreateClinicIdexxTestItemDto } from './dto/create_clinic-idexx-test-item.dto';
import { CreateIdexxOrderDto } from './dto/create-idexx-order.dto';
import { PatientsService } from '../../patients/patients.service';
import { ClinicLabReportService } from '../../clinic-lab-report/clinic-lab-report.service';
import { S3Service } from '../../utils/aws/s3/s3.service';
import { ClinicIdexxUtilsService } from '../../utils/idexx/clinic-idexx-utils.service';
import { AppointmentsService } from '../../appointments/appointments.service';
import { LabReport } from '../../clinic-lab-report/entities/lab-report.entity';
import { AppointmentDetailsEntity } from '../../appointments/entities/appointment-details.entity';
import { AppointmentEntity } from '../../appointments/entities/appointment.entity';
import { WinstonLogger } from '../../utils/logger/winston-logger.service';
export declare class ClinicIdexxService {
    private readonly httpService;
    private readonly patientService;
    private readonly s3Service;
    private readonly logger;
    private readonly appointmentsService;
    private readonly clinisIdexxUtilsService;
    private readonly clinicLabReportService;
    private readonly clinicLabReportRepository;
    private readonly clinicIdexxRepository;
    private readonly labReportRepository;
    private appointmentRepository;
    private appointmentDetailsRepository;
    private deviceUnitsCache;
    private readonly DEVICE_CACHE_TTL_MS;
    constructor(httpService: HttpService, patientService: PatientsService, s3Service: S3Service, logger: WinstonLogger, appointmentsService: AppointmentsService, clinisIdexxUtilsService: ClinicIdexxUtilsService, clinicLabReportService: ClinicLabReportService, clinicLabReportRepository: Repository<ClinicLabReport>, clinicIdexxRepository: Repository<CreateClinicIdexxEntity>, labReportRepository: Repository<LabReport>, appointmentRepository: Repository<AppointmentEntity>, appointmentDetailsRepository: Repository<AppointmentDetailsEntity>);
    createIdexxEntry(createClinicIdexxDto: CreateClinicIdexxDto, brandId: string): Promise<{
        brandId: string;
        authKey: string;
        clinicId: string;
        userName: string;
        password: string;
        type: string;
        id: string;
        clinic: import("../../clinics/entities/clinic.entity").ClinicEntity;
    } & CreateClinicIdexxEntity>;
    getIdexxEntries(clinicId: string): Promise<{
        items: CreateClinicIdexxEntity[];
        total: number;
    }>;
    deletIdexxEntry(clinicIdexxId: string): Promise<{
        status: boolean;
        message: string;
    } | {
        status: boolean;
        message?: undefined;
    }>;
    getAllIDexxTestsList(clinicId: string): Promise<any>;
    createIdexxTestItem(createClinicIdexxTestItemDto: CreateClinicIdexxTestItemDto, brandId: string): Promise<{
        brandId: string;
        clinicId: string;
        name: string;
        description?: string;
        chargeablePrice: number;
        tax: number;
        integrationType: string;
    } & ClinicLabReport>;
    deleteIdexxTestItem(clinicLabReportEntryId: string): Promise<{
        status: boolean;
        message: string;
    } | {
        status: boolean;
        message?: undefined;
    }>;
    createIdexxOrder(createIdexxOrderDto: CreateIdexxOrderDto, brandId: string): Promise<{
        orderId: any;
        uiURL: any;
        labReportId: string;
        automationFailed: boolean;
        message: string;
        selectedVet?: undefined;
    } | {
        orderId: any;
        labReportId: string;
        message: string;
        selectedVet: string;
        uiURL?: undefined;
        automationFailed?: undefined;
    } | {
        orderId?: undefined;
        uiURL?: undefined;
        labReportId?: undefined;
        automationFailed?: undefined;
        message?: undefined;
        selectedVet?: undefined;
    }>;
    cancelIdexxOrder(clinicId: string, idexxOrderId: string): Promise<any>;
    runGetResults(): Promise<void>;
    syncIdexxLabReports(): Promise<void>;
    private downloadPDFanduploadToS3;
    private getLatestIDEXXResults;
    private acknowledgeResult;
    private getAllIdexxEntries;
    getAllIDEXXDeviceUnits(clinicId: string): Promise<any>;
    downloadWithRetry(url: string, headers: any, retries?: number, delayMs?: number): Promise<import("axios").AxiosResponse<any, any> | null>;
    /**
     * Check if IDEXX orders can be deleted by verifying their status
     * @param clinicId - The clinic ID
     * @param labReportIds - Array of lab report IDs to check
     * @returns Promise<{ canBeDeleted: boolean, details: Array<{ labReportId: string, idexxOrderId: string, status: string, canDelete: boolean }> }>
     */
    checkIdexxOrdersCanBeDeleted(clinicId: string, labReportIds: string[]): Promise<{
        canBeDeleted: boolean;
        details: Array<{
            labReportId: string;
            idexxOrderId: string | null;
            status: string | null;
            canDelete: boolean;
            error?: string;
        }>;
    }>;
}
