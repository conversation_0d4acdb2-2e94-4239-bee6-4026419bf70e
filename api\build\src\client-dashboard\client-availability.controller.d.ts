import { ClientAvailabilityService } from './client-availability.service';
import { AvailableDatesRequestDto, AvailableDatesResponseDto, AvailableTimeSlotsResponseDto, TimeSlotRequestDto } from './dto/client-availability.dto';
export declare class ClientAvailabilityController {
    private readonly clientAvailabilityService;
    constructor(clientAvailabilityService: ClientAvailabilityService);
    getAvailableDates(doctorIds: string, query: AvailableDatesRequestDto): Promise<AvailableDatesResponseDto>;
    getAvailableTimeSlots(doctorIds: string, date: string, query: TimeSlotRequestDto): Promise<AvailableTimeSlotsResponseDto>;
}
