"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateAppointmentDoctorTable1725533697992 = void 0;
const typeorm_1 = require("typeorm");
class UpdateAppointmentDoctorTable1725533697992 {
    async up(queryRunner) {
        await queryRunner.addColumn('appointment_doctors', new typeorm_1.TableColumn({
            name: 'primary',
            type: 'boolean',
            default: false
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('appointment_doctors', 'primary');
    }
}
exports.UpdateAppointmentDoctorTable1725533697992 = UpdateAppointmentDoctorTable1725533697992;
//# sourceMappingURL=1725533697992-UpdateAppointmentDoctorTable.js.map