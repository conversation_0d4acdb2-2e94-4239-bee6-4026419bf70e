"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddDownloadShareToInvoiceAuditLogEnum1752667379439 = void 0;
class AddDownloadShareToInvoiceAuditLogEnum1752667379439 {
    constructor() {
        this.name = 'AddDownloadShareToInvoiceAuditLogEnum1752667379439';
    }
    async up(queryRunner) {
        // Add DOWNLOAD and SHARE values to the existing invoice_audit_log_operation_type_enum
        await queryRunner.query(`
			ALTER TYPE "public"."invoice_audit_log_operation_type_enum" 
			ADD VALUE 'DOWNLOAD'
		`);
        await queryRunner.query(`
			ALTER TYPE "public"."invoice_audit_log_operation_type_enum" 
			ADD VALUE 'SHARE'
		`);
    }
    async down(queryRunner) {
        // Note: PostgreSQL doesn't support removing enum values directly
        // We need to recreate the enum without the new values
        // Create a new enum without DOWNLOAD and SHARE
        await queryRunner.query(`
			CREATE TYPE "public"."invoice_audit_log_operation_type_enum_new" AS ENUM('CREATE', 'UPDATE', 'DELETE', 'WRITE_OFF')
		`);
        // Update the table to use the new enum
        await queryRunner.query(`
			ALTER TABLE "invoice_audit_log" 
			ALTER COLUMN "operation_type" TYPE "public"."invoice_audit_log_operation_type_enum_new" 
			USING "operation_type"::text::"public"."invoice_audit_log_operation_type_enum_new"
		`);
        // Drop the old enum and rename the new one
        await queryRunner.query(`
			DROP TYPE "public"."invoice_audit_log_operation_type_enum"
		`);
        await queryRunner.query(`
			ALTER TYPE "public"."invoice_audit_log_operation_type_enum_new" 
			RENAME TO "invoice_audit_log_operation_type_enum"
		`);
    }
}
exports.AddDownloadShareToInvoiceAuditLogEnum1752667379439 = AddDownloadShareToInvoiceAuditLogEnum1752667379439;
//# sourceMappingURL=1752667379439-AddDownloadShareToInvoiceAuditLogEnum.js.map