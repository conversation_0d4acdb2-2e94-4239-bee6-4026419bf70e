import { Test, TestingModule } from '@nestjs/testing';
import { BrandController } from './brands.controller';
import { BrandService } from './brands.service';
import { CreateBrandDto } from './dto/create-brand.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { HttpException, HttpStatus } from '@nestjs/common';
import { Brand } from './entities/brand.entity';
import { User } from '../users/entities/user.entity';
import { RoleService } from '../roles/role.service';

describe('BrandController', () => {
	let controller: BrandController;
	let brandService: jest.Mocked<BrandService>;

	const mockBrandService = {
		createBrand: jest.fn(),
		getAllBrands: jest.fn(),
		getBrandById: jest.fn()
	};

	const mockBrand: Brand = {
		id: '1',
		name: 'Test Brand',
		createdAt: new Date(),
		updatedAt: new Date(),
		createdBy: '',
		createdByUser: new User(),
		updatedBy: '',
		updatedByUser: new User(),
		clinics: [],
		slug: '',
		generateSlug: function (): void {
			throw new Error('Function not implemented.');
		}
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [BrandController],
			providers: [
				{
					provide: BrandService,
					useValue: mockBrandService
				},
				{
					provide: RoleService,
					useValue: {
						findByName: jest.fn(),
						findById: jest.fn()
					}
				},
				{
					provide: WinstonLogger,
					useValue: { log: jest.fn(), error: jest.fn() }
				}
			]
		}).compile();

		controller = module.get<BrandController>(BrandController);
		brandService = module.get<BrandService>(
			BrandService
		) as jest.Mocked<BrandService>;
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('create', () => {
		const mockCreateBrandDto: CreateBrandDto = { name: 'New Brand' };

		it('should create a new brand successfully', async () => {
			mockBrandService.createBrand.mockResolvedValue(mockBrand);

			const result = await controller.create(mockCreateBrandDto);

			expect(result).toEqual(mockBrand);
			expect(brandService.createBrand).toHaveBeenCalledWith(
				mockCreateBrandDto
			);
		});

		it('should throw an error if creating a brand fails', async () => {
			const error = new HttpException(
				'Brand creation failed',
				HttpStatus.BAD_REQUEST
			);
			mockBrandService.createBrand.mockRejectedValue(error);

			await expect(controller.create(mockCreateBrandDto)).rejects.toThrow(
				'Brand creation failed'
			);
			expect(brandService.createBrand).toHaveBeenCalledWith(
				mockCreateBrandDto
			);
		});
	});

	describe('findById', () => {
		it('should return a brand by id', async () => {
			const brandId = '1';
			mockBrandService.getBrandById.mockResolvedValue(mockBrand);

			const result = await controller.findbyId(brandId);

			expect(result).toEqual(mockBrand);
			expect(brandService.getBrandById).toHaveBeenCalledWith(brandId);
		});

		it('should throw an error if the brand is not found', async () => {
			const brandId = '2';
			const error = new HttpException(
				'Brand not found',
				HttpStatus.NOT_FOUND
			);
			mockBrandService.getBrandById.mockRejectedValue(error);

			await expect(controller.findbyId(brandId)).rejects.toThrow(
				'Brand not found'
			);
			expect(brandService.getBrandById).toHaveBeenCalledWith(brandId);
		});
	});

	describe('findAll', () => {
		it('should return all brands with default pagination', async () => {
			const mockResponse = { brands: [mockBrand], total: 1 };
			mockBrandService.getAllBrands.mockResolvedValue(mockResponse);

			const result = await controller.findAll();

			expect(result).toEqual(mockResponse);
			expect(brandService.getAllBrands).toHaveBeenCalledWith(1, 10, '', 'DESC');
		});

		it('should return all brands with custom pagination parameters', async () => {
			const mockResponse = { brands: [mockBrand], total: 1 };
			mockBrandService.getAllBrands.mockResolvedValue(mockResponse);

			const result = await controller.findAll(2, 5, 'test', 'ASC');

			expect(result).toEqual(mockResponse);
			expect(brandService.getAllBrands).toHaveBeenCalledWith(2, 5, 'test', 'ASC');
		});

		it('should throw an error if fetching brands fails', async () => {
			const error = new Error('Database error');
			mockBrandService.getAllBrands.mockRejectedValue(error);

			await expect(controller.findAll()).rejects.toThrow(
				'Error fetching all the brands'
			);
			expect(brandService.getAllBrands).toHaveBeenCalledWith(1, 10, '', 'DESC');
		});
	});
});
