"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateMergedPaymentReceiptDocumentTable1747212096753 = void 0;
const typeorm_1 = require("typeorm");
class CreateMergedPaymentReceiptDocumentTable1747212096753 {
    constructor() {
        this.name = 'CreateMergedPaymentReceiptDocumentTable1747212096753';
    }
    async up(queryRunner) {
        // Create the enum type explicitly for status.
        // This provides more control, especially for the 'down' migration,
        // as TypeORM's cross-DB enum handling can vary.
        await queryRunner.query(`CREATE TYPE "public"."merged_payment_receipt_documents_status_enum" AS ENUM('PROCESSING', 'COMPLETED', 'ERROR')`);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'merged_payment_receipt_documents',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'uuid'
                },
                {
                    name: 'requestId',
                    type: 'varchar',
                    isUnique: true // Creates a unique constraint
                },
                {
                    name: 'payment_reference_ids_hash',
                    type: 'varchar',
                    length: '32'
                },
                {
                    name: 'payment_reference_ids',
                    type: 'jsonb' // Specific to PostgreSQL
                },
                {
                    name: 'fileKey',
                    type: 'varchar',
                    isNullable: true
                },
                {
                    name: 'isGenerating',
                    type: 'boolean',
                    default: false
                },
                {
                    name: 'status',
                    type: 'enum',
                    enumName: 'merged_payment_receipt_documents_status_enum', // Must match the manually created type
                    default: "'PROCESSING'" // Default enum value needs to be a string literal for SQL
                },
                {
                    name: 'errorMessage',
                    type: 'text',
                    isNullable: true
                },
                {
                    name: 'retryCount',
                    type: 'int',
                    default: 0
                },
                {
                    name: 'createdAt',
                    type: 'timestamp', // Corresponds to @CreateDateColumn
                    default: 'now()'
                },
                {
                    name: 'updatedAt',
                    type: 'timestamp', // Corresponds to @UpdateDateColumn
                    default: 'now()'
                },
                {
                    name: 'expires_at',
                    type: 'timestamp',
                    isNullable: true
                }
            ]
        }), true // Create dependencies like unique constraints if not already handled by column options
        );
        // Create the separate index as per the @Index() decorator on the requestId column in the entity
        await queryRunner.createIndex('merged_payment_receipt_documents', new typeorm_1.TableIndex({
            name: 'IDX_mpr_doc_requestId', // Explicit and consistent index name
            columnNames: ['requestId']
        }));
    }
    async down(queryRunner) {
        // Drop the explicit index first
        // Ensures no dependencies are left before dropping the table or type
        await queryRunner.dropIndex('merged_payment_receipt_documents', 'IDX_mpr_doc_requestId');
        // Drop the table
        // TypeORM will also handle dropping the unique constraint on `requestId` when the table is dropped.
        await queryRunner.dropTable('merged_payment_receipt_documents');
        // Drop the enum type
        // Important to clean up custom types created in the 'up' migration
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."merged_payment_receipt_documents_status_enum"`);
    }
}
exports.CreateMergedPaymentReceiptDocumentTable1747212096753 = CreateMergedPaymentReceiptDocumentTable1747212096753;
//# sourceMappingURL=1747212096753-CreateMergedPaymentReceiptDocumentTable.js.map