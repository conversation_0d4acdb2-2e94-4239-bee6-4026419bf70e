"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MakeRoomIdNullable1743757388052 = void 0;
class MakeRoomIdNullable1743757388052 {
    constructor() {
        this.name = 'MakeRoomIdNullable1743757388052';
    }
    async up(queryRunner) {
        // Make room_id nullable in appointments table
        await queryRunner.query(`
            ALTER TABLE appointments 
            ALTER COLUMN room_id DROP NOT NULL
        `);
    }
    async down(queryRunner) {
        // Revert changes, make room_id NOT NULL again
        await queryRunner.query(`
            ALTER TABLE appointments 
            ALTER COLUMN room_id SET NOT NULL
        `);
    }
}
exports.MakeRoomIdNullable1743757388052 = MakeRoomIdNullable1743757388052;
//# sourceMappingURL=1743757388052-MakeRoomIdNullable.js.map