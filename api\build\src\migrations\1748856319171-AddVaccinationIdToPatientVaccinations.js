"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddVaccinationIdToPatientVaccinations1748856319171 = void 0;
class AddVaccinationIdToPatientVaccinations1748856319171 {
    constructor() {
        this.name = 'AddVaccinationIdToPatientVaccinations1748856319171';
    }
    async up(queryRunner) {
        // 1. Add vaccination_id column to patient_vaccinations table
        await queryRunner.query(`
			ALTER TABLE "patient_vaccinations" 
			ADD COLUMN "vaccination_id" uuid
		`);
        // 2. Create index on vaccination_id for better performance
        await queryRunner.query(`
			CREATE INDEX "IDX_patient_vaccinations_vaccination_id" 
			ON "patient_vaccinations" ("vaccination_id")
		`);
        // 3. Populate vaccination_id for existing records
        await this.populateVaccinationIds(queryRunner);
        // 4. Make vaccination_id NOT NULL after populating data
        await queryRunner.query(`
			ALTER TABLE "patient_vaccinations" 
			ALTER COLUMN "vaccination_id" SET NOT NULL
		`);
        // 5. Add foreign key constraint to clinic_vaccinations
        await queryRunner.query(`
			ALTER TABLE "patient_vaccinations" 
			ADD CONSTRAINT "FK_patient_vaccinations_vaccination_id" 
			FOREIGN KEY ("vaccination_id") 
			REFERENCES "clinic_vaccinations"("id") 
			ON DELETE RESTRICT ON UPDATE CASCADE
		`);
    }
    async down(queryRunner) {
        // Remove foreign key constraint
        await queryRunner.query(`
			ALTER TABLE "patient_vaccinations" 
			DROP CONSTRAINT "FK_patient_vaccinations_vaccination_id"
		`);
        // Remove index
        await queryRunner.query(`
			DROP INDEX "IDX_patient_vaccinations_vaccination_id"
		`);
        // Remove column
        await queryRunner.query(`
			ALTER TABLE "patient_vaccinations" 
			DROP COLUMN "vaccination_id"
		`);
    }
    async populateVaccinationIds(queryRunner) {
        console.log('Starting to populate vaccination_id for existing patient_vaccinations...');
        // Get all patient vaccinations that need vaccination_id populated
        const patientVaccinations = await queryRunner.query(`
			SELECT 
				pv.id,
				pv.patient_id,
				pv.vaccine_name,
				p.clinic_id
			FROM patient_vaccinations pv
			JOIN patients p ON p.id = pv.patient_id
			WHERE pv.vaccination_id IS NULL
			ORDER BY pv.created_at
		`);
        console.log(`Found ${patientVaccinations.length} patient vaccinations to update`);
        let matchedCount = 0;
        let fallbackCount = 0;
        for (const patientVaccination of patientVaccinations) {
            const { id, patient_id, vaccine_name, clinic_id } = patientVaccination;
            // Try to find matching clinic vaccination by name
            const matchingVaccination = await queryRunner.query(`
				SELECT id 
				FROM clinic_vaccinations 
				WHERE clinic_id = $1 
				AND LOWER(product_name) = LOWER($2)
				LIMIT 1
			`, [clinic_id, vaccine_name]);
            let vaccinationId;
            if (matchingVaccination.length > 0) {
                // Found exact match
                vaccinationId = matchingVaccination[0].id;
                matchedCount++;
                console.log(`✓ Matched "${vaccine_name}" for patient ${patient_id}`);
            }
            else {
                // Try partial match (contains)
                const partialMatch = await queryRunner.query(`
					SELECT id 
					FROM clinic_vaccinations 
					WHERE clinic_id = $1 
					AND (
						LOWER(product_name) LIKE LOWER($2) 
						OR LOWER($2) LIKE LOWER(product_name)
					)
					LIMIT 1
				`, [clinic_id, `%${vaccine_name}%`]);
                if (partialMatch.length > 0) {
                    vaccinationId = partialMatch[0].id;
                    matchedCount++;
                    console.log(`~ Partial match "${vaccine_name}" for patient ${patient_id}`);
                }
                else {
                    // No match found, use any vaccination from the same clinic
                    const fallbackVaccination = await queryRunner.query(`
						SELECT id 
						FROM clinic_vaccinations 
						WHERE clinic_id = $1 
						LIMIT 1
					`, [clinic_id]);
                    if (fallbackVaccination.length > 0) {
                        vaccinationId = fallbackVaccination[0].id;
                        fallbackCount++;
                        console.log(`⚠ Using fallback for "${vaccine_name}" (patient ${patient_id})`);
                    }
                    else {
                        // No vaccinations in clinic at all, use any vaccination from any clinic
                        const anyVaccination = await queryRunner.query(`
							SELECT id 
							FROM clinic_vaccinations 
							LIMIT 1
						`);
                        if (anyVaccination.length > 0) {
                            vaccinationId = anyVaccination[0].id;
                            fallbackCount++;
                            console.log(`⚠⚠ Using global fallback for "${vaccine_name}" (patient ${patient_id})`);
                        }
                        else {
                            throw new Error(`No clinic vaccinations found in database. Cannot populate vaccination_id for patient vaccination ${id}`);
                        }
                    }
                }
            }
            // Update the patient vaccination with the vaccination_id
            await queryRunner.query(`
				UPDATE patient_vaccinations 
				SET vaccination_id = $1 
				WHERE id = $2
			`, [vaccinationId, id]);
        }
        console.log(`✅ Vaccination ID population complete:`);
        console.log(`   - Exact/Partial matches: ${matchedCount}`);
        console.log(`   - Fallback assignments: ${fallbackCount}`);
        console.log(`   - Total updated: ${matchedCount + fallbackCount}`);
    }
}
exports.AddVaccinationIdToPatientVaccinations1748856319171 = AddVaccinationIdToPatientVaccinations1748856319171;
//# sourceMappingURL=1748856319171-AddVaccinationIdToPatientVaccinations.js.map