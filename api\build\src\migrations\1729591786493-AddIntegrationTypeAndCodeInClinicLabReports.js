"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddIntegrationTypeAndCodeInClinicLabReports1729591786493 = void 0;
const typeorm_1 = require("typeorm");
class AddIntegrationTypeAndCodeInClinicLabReports1729591786493 {
    async up(queryRunner) {
        await queryRunner.addColumns('clinic_lab_reports', [
            new typeorm_1.TableColumn({
                name: 'integration_type',
                type: 'varchar',
                isNullable: true,
            })
        ]);
        await queryRunner.addColumns('clinic_lab_reports', [
            new typeorm_1.TableColumn({
                name: 'integration_code',
                type: 'varchar',
                isNullable: true,
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('clinic_lab_reports', 'integration_type');
        await queryRunner.dropColumn('clinic_lab_reports', 'integration_code');
    }
}
exports.AddIntegrationTypeAndCodeInClinicLabReports1729591786493 = AddIntegrationTypeAndCodeInClinicLabReports1729591786493;
//# sourceMappingURL=1729591786493-AddIntegrationTypeAndCodeInClinicLabReports.js.map