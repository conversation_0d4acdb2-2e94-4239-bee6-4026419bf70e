"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RemoveUniqueContraintsFromOwnerEmail1728980509901 = void 0;
const typeorm_1 = require("typeorm");
class RemoveUniqueContraintsFromOwnerEmail1728980509901 {
    async up(queryRunner) {
        var _a, _b;
        const table = await queryRunner.getTable('owners');
        for (const uniqueConstraint of (_a = table === null || table === void 0 ? void 0 : table.uniques) !== null && _a !== void 0 ? _a : []) {
            console.log(uniqueConstraint);
            for (const columnName of ['email']) {
                if (uniqueConstraint.columnNames.includes(columnName)) {
                    console.log('here');
                    await queryRunner.dropUniqueConstraint('owners', (_b = uniqueConstraint === null || uniqueConstraint === void 0 ? void 0 : uniqueConstraint.name) !== null && _b !== void 0 ? _b : '');
                }
            }
        }
    }
    async down(queryRunner) {
        await queryRunner.changeColumn('owners', 'email', new typeorm_1.TableColumn({
            name: 'email',
            type: 'varchar',
            length: '100',
            isUnique: true,
            isNullable: true
        }));
    }
}
exports.RemoveUniqueContraintsFromOwnerEmail1728980509901 = RemoveUniqueContraintsFromOwnerEmail1728980509901;
//# sourceMappingURL=1728980509901-remove-unique-contraints-from-owner-email.js.map