"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUsersTable1740464090777 = void 0;
class UpdateUsersTable1740464090777 {
    async up(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE "users" 
            ADD COLUMN "alternate_mobile_number" VARCHAR(20),
            ADD COLUMN "alternate_country_code" VARCHAR(5)
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE "users" 
            DROP COLUMN "alternate_mobile_number",
            DROP COLUMN "alternate_country_code"
        `);
    }
}
exports.UpdateUsersTable1740464090777 = UpdateUsersTable1740464090777;
//# sourceMappingURL=1740464090777-UpdateUsersTable.js.map