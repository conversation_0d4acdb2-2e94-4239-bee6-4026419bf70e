import { Repository } from 'typeorm';
import 'moment-timezone';
import { AvailableDatesResponseDto, AvailableTimeSlotsResponseDto } from './dto/client-availability.dto';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';
import { AvailabilityService } from '../availability/availability.service';
import { ClinicService } from '../clinics/clinic.service';
export declare class ClientAvailabilityService {
    private clinicUserRepository;
    private readonly availabilityService;
    private readonly clinicService;
    private readonly logger;
    constructor(clinicUserRepository: Repository<ClinicUser>, availabilityService: AvailabilityService, clinicService: ClinicService);
    private getEffectiveBookingSettings;
    getAvailableDatesForDoctor(doctorIdsString: string, startDate?: string, endDate?: string, clinicIdFromQuery?: string): Promise<AvailableDatesResponseDto>;
    /**
     * Get available time slots for a doctor or all doctors on a specific date, considering clinic settings.
     * Also finds the next available slot for doctors unavailable on the requested date.
     */
    getAvailableTimeSlotsForDoctor(doctorIdsString: string, date: string, clinicIdFromQuery?: string): Promise<AvailableTimeSlotsResponseDto>;
    private generateSlotsFromWorkingHours;
    private sortTimeSlots;
}
