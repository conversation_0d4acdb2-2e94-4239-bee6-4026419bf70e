import { TabActivitiesService } from './tab-activity.service';
import { CreateTabActivityDto } from './dto/create-tab-actvity.dto';
import { TabName } from './enums/tab-activity.enums';
export declare class TabActivitiesController {
    private readonly tabActivitiesService;
    constructor(tabActivitiesService: TabActivitiesService);
    create(createTabActivityDto: CreateTabActivityDto, req: {
        user: {
            userId: string;
        };
    }): Promise<import("./entities/tab-activity.entity").TabActivityEntity>;
    getLastActivity(tabName: TabName, referenceId: string): Promise<{
        status: boolean;
        data: {
            share: import("./entities/tab-activity.entity").TabActivityEntity | undefined;
            download: import("./entities/tab-activity.entity").TabActivityEntity | undefined;
        };
        message: string;
        error?: undefined;
    } | {
        status: boolean;
        message: string;
        error: any;
        data?: undefined;
    }>;
    getLastActivitiesByReferenceIds(body: {
        referenceIds: string[];
    }): Promise<Record<string, import("./entities/tab-activity.entity").TabActivityEntity>>;
    getActivitiesByReferenceId(referenceId: string): Promise<import("./entities/tab-activity.entity").TabActivityEntity[]>;
    deleteByReferenceId(referenceId: string): Promise<import("typeorm").DeleteResult>;
    updateByReferenceId(referenceId: string, updateData: Partial<CreateTabActivityDto>, req: {
        user: {
            userId: string;
        };
    }): Promise<import("typeorm").UpdateResult>;
}
