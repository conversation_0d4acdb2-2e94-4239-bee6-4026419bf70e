"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddMetadataToInvoice1741677383126 = void 0;
class AddMetadataToInvoice1741677383126 {
    async up(queryRunner) {
        // Add metadata column as JSONB
        await queryRunner.query(`
            ALTER TABLE "invoices"
            ADD COLUMN IF NOT EXISTS "metadata" JSONB DEFAULT '{}'::jsonb;
        `);
        // Add comment to explain the column's purpose
        await queryRunner.query(`
            COMMENT ON COLUMN "invoices"."metadata" IS 'Additional metadata for the invoice in JSONB format';
        `);
    }
    async down(queryRunner) {
        // Remove the metadata column
        await queryRunner.query(`
            ALTER TABLE "invoices"
            DROP COLUMN IF EXISTS "metadata";
        `);
    }
}
exports.AddMetadataToInvoice1741677383126 = AddMetadataToInvoice1741677383126;
//# sourceMappingURL=1741677383126-AddMetadataToInvoice.js.map