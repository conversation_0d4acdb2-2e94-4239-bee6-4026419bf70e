"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiagnosticTemplatesController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const swagger_1 = require("@nestjs/swagger");
const role_enum_1 = require("../roles/role.enum");
const roles_decorator_1 = require("../roles/roles.decorator");
const create_template_dto_1 = require("./dto/create-template.dto");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
const diagnostic_note_service_1 = require("./diagnostic-note.service");
const diagnostic_note_dto_1 = require("./dto/diagnostic-note.dto");
let DiagnosticTemplatesController = class DiagnosticTemplatesController {
    constructor(diagnosticNoteService) {
        this.diagnosticNoteService = diagnosticNoteService;
    }
    async create(createDto, req) {
        return this.diagnosticNoteService.create(createDto, req.user.id);
    }
    async findAll(clinicId) {
        return this.diagnosticNoteService.findAll(clinicId);
    }
    async findOne(id, clinicId) {
        return this.diagnosticNoteService.findOne(id, clinicId);
    }
    async update(id, updateDto, clinicId, req) {
        return this.diagnosticNoteService.update(id, updateDto, req.user.id, clinicId);
    }
    async remove(id, clinicId) {
        return this.diagnosticNoteService.remove(id, clinicId);
    }
    async createNote(createNoteDto, req) {
        const note = await this.diagnosticNoteService.createNote(createNoteDto, req.user.userId);
        return note;
    }
    async getTemplatesForLabReport(labReportId, clinicId) {
        return await this.diagnosticNoteService.getTemplatesForLabReport(labReportId, clinicId);
    }
    async getNotesByLabReport(clinicLabReportId, clinicId) {
        return await this.diagnosticNoteService.findTemplatesByDiagnostic(clinicLabReportId, clinicId);
    }
    async getPatientNotes(patientId) {
        return await this.diagnosticNoteService.getPatientNotes(patientId);
    }
    async getNote(noteId) {
        return await this.diagnosticNoteService.getNote(noteId);
    }
    async updateNote(id, updateNoteDto, req) {
        return await this.diagnosticNoteService.updateNote(id, updateNoteDto, req.user.id);
    }
    async deleteNote(noteId) {
        return await this.diagnosticNoteService.deleteNote(noteId);
    }
};
exports.DiagnosticTemplatesController = DiagnosticTemplatesController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new diagnostic template' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Template created successfully' }),
    (0, track_method_decorator_1.TrackMethod)('create-diagnostic-template'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_template_dto_1.CreateDiagnosticTemplateDto, Object]),
    __metadata("design:returntype", Promise)
], DiagnosticTemplatesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Get all templates for a clinic' }),
    (0, track_method_decorator_1.TrackMethod)('find-diagnostic-templates'),
    __param(0, (0, common_1.Query)('clinicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DiagnosticTemplatesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Get a template by id' }),
    (0, track_method_decorator_1.TrackMethod)('get-diagnostic-template'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('clinicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DiagnosticTemplatesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Update a template' }),
    (0, track_method_decorator_1.TrackMethod)('update-diagnostic-template'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Query)('clinicId')),
    __param(3, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String, Object]),
    __metadata("design:returntype", Promise)
], DiagnosticTemplatesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a template' }),
    (0, track_method_decorator_1.TrackMethod)('delete-diagnostic-template'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('clinicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DiagnosticTemplatesController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)('/diagnostic-note'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new diagnostic note' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [diagnostic_note_dto_1.CreateDiagnosticNoteDto, Object]),
    __metadata("design:returntype", Promise)
], DiagnosticTemplatesController.prototype, "createNote", null);
__decorate([
    (0, common_1.Get)('lab-report/:labReportId/templates'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Get templates for a lab report' }),
    __param(0, (0, common_1.Param)('labReportId')),
    __param(1, (0, common_1.Query)('clinicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DiagnosticTemplatesController.prototype, "getTemplatesForLabReport", null);
__decorate([
    (0, common_1.Get)('clinic-lab-report/:clinicLabReportId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Get notes for a lab report' }),
    __param(0, (0, common_1.Param)('clinicLabReportId')),
    __param(1, (0, common_1.Query)('clinicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DiagnosticTemplatesController.prototype, "getNotesByLabReport", null);
__decorate([
    (0, common_1.Get)('/diagnostic-note/:patientId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Get a diagnostic notes for patient' }),
    __param(0, (0, common_1.Param)('patientId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DiagnosticTemplatesController.prototype, "getPatientNotes", null);
__decorate([
    (0, common_1.Get)('/diagnostic-note/note/:noteId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Get a diagnostic notes for patient' }),
    __param(0, (0, common_1.Param)('noteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DiagnosticTemplatesController.prototype, "getNote", null);
__decorate([
    (0, common_1.Put)('/diagnostic-note/:id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Update a diagnostic note' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, diagnostic_note_dto_1.UpdateDiagnosticNoteDto, Object]),
    __metadata("design:returntype", Promise)
], DiagnosticTemplatesController.prototype, "updateNote", null);
__decorate([
    (0, common_1.Delete)('/diagnostic-note/:noteId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a diagnostic note' }),
    __param(0, (0, common_1.Param)('noteId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DiagnosticTemplatesController.prototype, "deleteNote", null);
exports.DiagnosticTemplatesController = DiagnosticTemplatesController = __decorate([
    (0, swagger_1.ApiTags)('Diagnostic Templates'),
    (0, common_1.Controller)('diagnostic-templates'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [diagnostic_note_service_1.DiagnosticTemplatesService])
], DiagnosticTemplatesController);
//# sourceMappingURL=diagnostic-note.controlller.js.map