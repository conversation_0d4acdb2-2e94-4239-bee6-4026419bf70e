import { DataSource } from 'typeorm';
import { <PERSON><PERSON>og<PERSON> } from '../../utils/logger/winston-logger.service';
import { S3Service } from '../../utils/aws/s3/s3.service';
import { DeletionType, DatabaseManifest } from '../dto/clinic-deletion.dto';
import { QueryManagerService } from './query-manager.service';
export interface DatabaseBackupResult {
    manifest: DatabaseManifest;
    backupPaths: string[];
    totalRecords: number;
    totalSizeBytes: number;
    duration: number;
}
export declare class DatabaseBackupService {
    private readonly dataSource;
    private readonly logger;
    private readonly s3Service;
    private readonly queryManagerService;
    constructor(dataSource: DataSource, logger: WinstonLogger, s3Service: S3Service, queryManagerService: QueryManagerService);
    /**
     * Backup all database records for a clinic or brand
     */
    backupDatabaseRecords(targetType: DeletionType, targetId: string, backupId: string, backupBasePath: string): Promise<DatabaseBackupResult>;
    /**
     * Rollback database backup by cleaning up uploaded files
     */
    private rollbackDatabaseBackup;
    /**
     * Backup a single table
     */
    private backupTable;
    /**
     * Calculate the correct restore order based on foreign key dependencies
     */
    private calculateRestoreOrder;
    /**
     * @deprecated Use QueryManagerService.getDatabaseBackupQueries() instead
     * This method has been moved to centralized QueryManagerService for consistency
     */
    private getDatabaseBackupQueries;
}
