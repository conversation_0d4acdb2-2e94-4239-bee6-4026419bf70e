"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddWorkingHoursToClinic1725464735677 = void 0;
class AddWorkingHoursToClinic1725464735677 {
    async up(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE clinics
            ADD COLUMN working_hours JSONB DEFAULT '{
                "workingHours": {
                    "monday": {"startTime": "09:00", "endTime": "17:00", "isWorkingDay": true},
                    "tuesday": {"startTime": "09:00", "endTime": "17:00", "isWorkingDay": true},
                    "wednesday": {"startTime": "09:00", "endTime": "17:00", "isWorkingDay": true},
                    "thursday": {"startTime": "09:00", "endTime": "17:00", "isWorkingDay": true},
                    "friday": {"startTime": "09:00", "endTime": "17:00", "isWorkingDay": true},
                    "saturday": {"startTime": null, "endTime": null, "isWorkingDay": false},
                    "sunday": {"startTime": null, "endTime": null, "isWorkingDay": false}
                }
            }'::jsonb
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE clinics
            DROP COLUMN working_hours
        `);
    }
}
exports.AddWorkingHoursToClinic1725464735677 = AddWorkingHoursToClinic1725464735677;
//# sourceMappingURL=1725464735677-UpdateClinicColTable.js.map