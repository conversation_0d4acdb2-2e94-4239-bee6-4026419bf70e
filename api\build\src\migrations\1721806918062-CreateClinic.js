"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateClinic1721806918062 = void 0;
class CreateClinic1721806918062 {
    constructor() {
        this.name = 'CreateClinic1721806918062';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "clinic" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "mobile" character varying NOT NULL, "name" character varying NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_29faabea729003cd88e07d6c0fa" UNIQUE ("mobile"), CONSTRAINT "PK_8e97c18debc9c7f7606e311d763" PRIMARY KEY ("id"))`);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "clinic"`);
    }
}
exports.CreateClinic1721806918062 = CreateClinic1721806918062;
//# sourceMappingURL=1721806918062-CreateClinic.js.map