"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentLibraryService = void 0;
const common_1 = require("@nestjs/common");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const typeorm_1 = require("@nestjs/typeorm");
const document_library_entity_1 = require("./entities/document-library.entity");
const typeorm_2 = require("typeorm");
const generatePdf_1 = require("../utils/generatePdf");
const uuidv7_1 = require("uuidv7");
const documentLibrary_1 = require("../utils/pdfs/documentLibrary");
const moment = require("moment");
const s3_service_1 = require("../utils/aws/s3/s3.service");
let DocumentLibraryService = class DocumentLibraryService {
    constructor(logger, documentLibraryRepository, s3Service) {
        this.logger = logger;
        this.documentLibraryRepository = documentLibraryRepository;
        this.s3Service = s3Service;
    }
    async create(createDocumentLibraryDto, brandId) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o;
        this.logger.log('Creating a new document library entry', {
            dto: createDocumentLibraryDto
        });
        const documentLibrary = this.documentLibraryRepository.create({
            ...createDocumentLibraryDto,
            brandId: brandId
        });
        if (documentLibrary.documentType === 'create') {
            const documentResponse = await this.documentLibraryRepository.findOne({
                where: { id: documentLibrary === null || documentLibrary === void 0 ? void 0 : documentLibrary.id },
                relations: ['clinic', 'clinic.brand']
            });
            const fileKey = `document-library/${(0, uuidv7_1.uuidv4)()}`;
            const documentHtml = (0, documentLibrary_1.generateDocumentLibrary)({
                bodyText: ((_a = createDocumentLibraryDto === null || createDocumentLibraryDto === void 0 ? void 0 : createDocumentLibraryDto.documentBody) === null || _a === void 0 ? void 0 : _a.bodyText) || '',
                clinicAddress: `${(_b = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.clinic) === null || _b === void 0 ? void 0 : _b.addressLine1} ${(_c = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.clinic) === null || _c === void 0 ? void 0 : _c.addressLine2}, ${(_d = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.clinic) === null || _d === void 0 ? void 0 : _d.city} ${(_e = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.clinic) === null || _e === void 0 ? void 0 : _e.addressPincode} ${(_f = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.clinic) === null || _f === void 0 ? void 0 : _f.state} ${(_g = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.clinic) === null || _g === void 0 ? void 0 : _g.country}`,
                clinicEmail: ((_h = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.clinic) === null || _h === void 0 ? void 0 : _h.email) || '',
                clinicName: ((_j = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.clinic) === null || _j === void 0 ? void 0 : _j.name) || '',
                clinicPhone: ((_l = (_k = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.clinic) === null || _k === void 0 ? void 0 : _k.phoneNumbers[0]) === null || _l === void 0 ? void 0 : _l.number) || '',
                clinicWebsite: ((_m = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.clinic) === null || _m === void 0 ? void 0 : _m.website) || '',
                digitalSignature: '',
                docDate: '',
                title: ((_o = createDocumentLibraryDto === null || createDocumentLibraryDto === void 0 ? void 0 : createDocumentLibraryDto.documentBody) === null || _o === void 0 ? void 0 : _o.title) || '',
                vetName: ' '
            });
            const pdfbuffer = await (0, generatePdf_1.generatePDF)(documentHtml);
            await this.s3Service.uploadPdfToS3(pdfbuffer, fileKey);
            documentLibrary.fileKey = fileKey;
        }
        return await this.documentLibraryRepository.save(documentLibrary);
    }
    async findAll(clinicId, page = 1, limit = 10, search, orderBy = 'DESC') {
        try {
            this.logger.log('Fetching documents', {
                page,
                limit,
                search,
                orderBy
            });
            const queryBuilder = this.documentLibraryRepository
                .createQueryBuilder('document')
                .where('document.deletedAt IS NULL') // Always true to allow appending conditions
                .andWhere('document.clinicId = :clinicId', { clinicId });
            const updatedSearch = search === null || search === void 0 ? void 0 : search.trim();
            if (updatedSearch) {
                this.logger.log('Adding search filter', { updatedSearch });
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('document.documentName ILIKE :search', {
                        search: `%${updatedSearch}%`
                    }).orWhere('document.category ILIKE :search', {
                        search: `%${updatedSearch}%`
                    });
                }));
            }
            queryBuilder
                .skip((page - 1) * limit)
                .take(limit)
                .orderBy('document.createdAt', 'DESC');
            const [documents, total] = await queryBuilder.getManyAndCount();
            this.logger.log('Documents fetched successfully', {
                count: documents.length,
                page,
                limit
            });
            return { documents, total };
        }
        catch (error) {
            this.logger.error('Error fetching documents', { error });
            throw new common_1.InternalServerErrorException('Failed to fetch documents');
        }
    }
    async findOne(id) {
        this.logger.log(`Retrieving document library entry with ID: ${id}`);
        const documentLibrary = await this.documentLibraryRepository.findOne({
            where: { id }
        });
        if (!documentLibrary) {
            this.logger.error(`Document library entry with ID: ${id} not found`);
            throw new common_1.NotFoundException(`Document library entry with ID: ${id} not found`);
        }
        return documentLibrary;
    }
    async update(id, updateDocumentLibraryDto) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
        this.logger.log(`Updating document library entry with ID: ${id}`, {
            dto: updateDocumentLibraryDto
        });
        const documentLibrary = await this.documentLibraryRepository.findOne({
            where: { id },
            relations: ['clinic', 'clinic.brand']
        });
        if (documentLibrary && updateDocumentLibraryDto.fileKey) {
            await this.s3Service.deleteFile(documentLibrary.fileKey);
        }
        if (documentLibrary &&
            (((_a = updateDocumentLibraryDto === null || updateDocumentLibraryDto === void 0 ? void 0 : updateDocumentLibraryDto.documentBody) === null || _a === void 0 ? void 0 : _a.title) ||
                ((_b = updateDocumentLibraryDto === null || updateDocumentLibraryDto === void 0 ? void 0 : updateDocumentLibraryDto.documentBody) === null || _b === void 0 ? void 0 : _b.bodyText))) {
            const newFileKey = `document-library/${(0, uuidv7_1.uuidv4)()}`;
            const documentHtml = (0, documentLibrary_1.generateDocumentLibrary)({
                bodyText: ((_c = updateDocumentLibraryDto === null || updateDocumentLibraryDto === void 0 ? void 0 : updateDocumentLibraryDto.documentBody) === null || _c === void 0 ? void 0 : _c.bodyText) ||
                    ((_d = documentLibrary === null || documentLibrary === void 0 ? void 0 : documentLibrary.documentBody) === null || _d === void 0 ? void 0 : _d.bodyText) ||
                    '',
                clinicAddress: `${(_e = documentLibrary === null || documentLibrary === void 0 ? void 0 : documentLibrary.clinic) === null || _e === void 0 ? void 0 : _e.addressLine1} ${(_f = documentLibrary === null || documentLibrary === void 0 ? void 0 : documentLibrary.clinic) === null || _f === void 0 ? void 0 : _f.addressLine2}, ${(_g = documentLibrary === null || documentLibrary === void 0 ? void 0 : documentLibrary.clinic) === null || _g === void 0 ? void 0 : _g.city} ${(_h = documentLibrary === null || documentLibrary === void 0 ? void 0 : documentLibrary.clinic) === null || _h === void 0 ? void 0 : _h.addressPincode} ${(_j = documentLibrary === null || documentLibrary === void 0 ? void 0 : documentLibrary.clinic) === null || _j === void 0 ? void 0 : _j.state} ${(_k = documentLibrary === null || documentLibrary === void 0 ? void 0 : documentLibrary.clinic) === null || _k === void 0 ? void 0 : _k.country}`,
                clinicEmail: ((_l = documentLibrary === null || documentLibrary === void 0 ? void 0 : documentLibrary.clinic) === null || _l === void 0 ? void 0 : _l.email) || '',
                clinicName: ((_m = documentLibrary === null || documentLibrary === void 0 ? void 0 : documentLibrary.clinic) === null || _m === void 0 ? void 0 : _m.name) || '',
                clinicPhone: ((_p = (_o = documentLibrary === null || documentLibrary === void 0 ? void 0 : documentLibrary.clinic) === null || _o === void 0 ? void 0 : _o.phoneNumbers[0]) === null || _p === void 0 ? void 0 : _p.number) || '',
                clinicWebsite: ((_q = documentLibrary === null || documentLibrary === void 0 ? void 0 : documentLibrary.clinic) === null || _q === void 0 ? void 0 : _q.website) || '',
                digitalSignature: '',
                docDate: moment(documentLibrary === null || documentLibrary === void 0 ? void 0 : documentLibrary.createdAt).format('DD MM YYYY'),
                title: ((_r = updateDocumentLibraryDto === null || updateDocumentLibraryDto === void 0 ? void 0 : updateDocumentLibraryDto.documentBody) === null || _r === void 0 ? void 0 : _r.title) ||
                    ((_s = documentLibrary === null || documentLibrary === void 0 ? void 0 : documentLibrary.documentBody) === null || _s === void 0 ? void 0 : _s.title) ||
                    '',
                vetName: ' '
            });
            const pdfbuffer = await (0, generatePdf_1.generatePDF)(documentHtml);
            await this.s3Service.uploadPdfToS3(pdfbuffer, newFileKey);
            updateDocumentLibraryDto.fileKey = newFileKey;
            if (documentLibrary.fileKey) {
                await this.s3Service.deleteFile(documentLibrary.fileKey);
            }
        }
        await this.documentLibraryRepository.update(id, updateDocumentLibraryDto);
        const updatedDocument = await this.documentLibraryRepository.findOne({
            where: { id }
        });
        if (!updatedDocument) {
            throw new common_1.InternalServerErrorException(`Failed to retrieve the updated document library entry with ID: ${id}`);
        }
        return updatedDocument;
    }
    async remove(id) {
        this.logger.log(`Soft deleting document library entry with ID: ${id}`);
        const documentLibrary = await this.documentLibraryRepository.findOne({
            where: { id }
        });
        if (!documentLibrary) {
            this.logger.error(`Document library entry with ID: ${id} not found`);
            throw new common_1.NotFoundException(`Document library entry with ID: ${id} not found`);
        }
        documentLibrary.deletedAt = new Date();
        await this.documentLibraryRepository.save(documentLibrary);
        this.logger.log(`Document library entry with ID: ${id} successfully soft deleted`);
    }
};
exports.DocumentLibraryService = DocumentLibraryService;
exports.DocumentLibraryService = DocumentLibraryService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(document_library_entity_1.DocumentLibrary)),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        typeorm_2.Repository,
        s3_service_1.S3Service])
], DocumentLibraryService);
//# sourceMappingURL=document-library.service.js.map