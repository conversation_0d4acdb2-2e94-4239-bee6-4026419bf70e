import { DocumentLibraryService } from './document-library.service';
import { CreateDocumentLibraryDto } from './dto/create-document-library.dto';
import { UpdateDocumentLibraryDto } from './dto/update-document-library.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ApiDocumentationBase } from '../base/api-documentation-base';
import { DocumentLibrary } from './entities/document-library.entity';
export declare class DocumentLibraryController extends ApiDocumentationBase {
    private readonly documentLibraryService;
    private readonly logger;
    constructor(documentLibraryService: DocumentLibraryService, logger: WinstonLogger);
    create(createDocumentLibraryDto: CreateDocumentLibraryDto, req: {
        user: {
            clinicId: string;
            brandId: string;
        };
    }): Promise<DocumentLibrary>;
    findAll(page: number | undefined, limit: number | undefined, clinicId: string, search?: string, orderBy?: string): Promise<{
        documents: any;
        total: number;
    }>;
    findOne(id: string): Promise<DocumentLibrary>;
    update(id: string, updateDocumentLibraryDto: UpdateDocumentLibraryDto): Promise<DocumentLibrary>;
    remove(id: string): Promise<void>;
}
