import { EnumAppointmentStatus } from '../../appointments/enums/enum-appointment-status';
/**
 * DTO for creating a new client booking
 */
export declare class CreateClientBookingDto {
    petId: string;
    doctorId: string;
    clinicId: string;
    date: string;
    startTime: string;
    endTime: string;
    reason?: string;
}
/**
 * DTO for updating a client booking (reschedule or cancel)
 */
export declare class UpdateClientBookingDto {
    date: string;
    startTime?: string;
    endTime?: string;
    status?: EnumAppointmentStatus;
    doctorId?: string;
}
/**
 * DTO for client booking response
 */
export declare class ClientBookingResponseDto {
    id: string;
    petId: string;
    petName: string;
    doctorId: string;
    doctorName: string;
    clinicId: string;
    clinicName: string;
    date: string;
    startTime: string;
    endTime: string;
    status: string;
    notes?: string;
    createdAt: Date;
    updatedAt: Date;
}
