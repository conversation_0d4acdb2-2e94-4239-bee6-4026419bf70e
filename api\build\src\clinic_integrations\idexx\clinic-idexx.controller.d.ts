import { ClinicIdexxService } from './clinic-idexx.service';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../utils/logger/winston-logger.service';
import { CreateClinicIdexxDto } from './dto/create-clinic-idexx.dto';
import { CreateClinicIdexxEntity } from './entities/create-clinic-idexx.entity';
import { CreateClinicIdexxTestItemDto } from './dto/create_clinic-idexx-test-item.dto';
import { CreateIdexxOrderDto } from './dto/create-idexx-order.dto';
import { CheckIdexxOrdersDeletionDto } from './dto/create-idexx-order.dto';
export declare class ClinicIdexxController {
    private readonly logger;
    private readonly clinicIdexxService;
    constructor(logger: WinstonLogger, clinicIdexxService: ClinicIdexxService);
    createIdexxEntry(createClinicIdexxDto: CreateClinicIdexxDto, req: {
        user: {
            clinicId: string;
            brandId: string;
        };
    }): Promise<{
        brandId: string;
        authKey: string;
        clinicId: string;
        userName: string;
        password: string;
        type: string;
        id: string;
        clinic: import("../../clinics/entities/clinic.entity").ClinicEntity;
    } & CreateClinicIdexxEntity>;
    getIdexxEntries(clinicId: string): Promise<{
        items: CreateClinicIdexxEntity[];
        total: number;
    }>;
    deletIdexxEntry(clinicIdexxId: string): Promise<{
        status: boolean;
        message: string;
    } | {
        status: boolean;
        message?: undefined;
    }>;
    getAllIDexxTestsList(clinicId: string): Promise<any>;
    createIdexxTestItem(createClinicIdexxTestItemDto: CreateClinicIdexxTestItemDto, req: {
        user: {
            clinicId: string;
            brandId: string;
        };
    }): Promise<{
        brandId: string;
        clinicId: string;
        name: string;
        description?: string;
        chargeablePrice: number;
        tax: number;
        integrationType: string;
    } & import("../../clinic-lab-report/entities/clinic-lab-report.entity").ClinicLabReport>;
    deleteIdexxTestItem(clinicLabReportEntryId: string): Promise<{
        status: boolean;
        message: string;
    } | {
        status: boolean;
        message?: undefined;
    }>;
    createIdexxOrder(createIdexxOrderDto: CreateIdexxOrderDto, req: {
        user: {
            clinicId: string;
            brandId: string;
        };
    }): Promise<{
        orderId: any;
        uiURL: any;
        labReportId: string;
        automationFailed: boolean;
        message: string;
        selectedVet?: undefined;
    } | {
        orderId: any;
        labReportId: string;
        message: string;
        selectedVet: string;
        uiURL?: undefined;
        automationFailed?: undefined;
    } | {
        orderId?: undefined;
        uiURL?: undefined;
        labReportId?: undefined;
        automationFailed?: undefined;
        message?: undefined;
        selectedVet?: undefined;
    }>;
    checkIdexxOrdersDeletion(clinicId: string, checkIdexxOrdersDeletionDto: CheckIdexxOrdersDeletionDto): Promise<{
        canBeDeleted: boolean;
        details: Array<{
            labReportId: string;
            idexxOrderId: string | null;
            status: string | null;
            canDelete: boolean;
            error?: string;
        }>;
    }>;
    cancelIdexxOrder(clinicId: string, idexxOrderId: string): Promise<any>;
}
