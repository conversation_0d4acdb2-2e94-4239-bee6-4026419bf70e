{"version": 3, "file": "1738212033390-createTablePatientEstimate.js", "sourceRoot": "", "sources": ["../../../src/migrations/1738212033390-createTablePatientEstimate.ts"], "names": [], "mappings": ";;;AAAA,qCAAiE;AAEjE,MAAa,uCAAuC;IAG5C,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,MAAM,WAAW,CAAC,WAAW,CAC5B,IAAI,eAAK,CAAC;YACT,IAAI,EAAE,kBAAkB;YACxB,OAAO,EAAE;gBACR;oBACC,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,IAAI;oBACf,OAAO,EAAE,oBAAoB;iBAC7B;gBACD;oBACC,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,MAAM;iBACZ;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,MAAM;iBACZ;gBACD;oBACC,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,gBAAgB;oBACtB,IAAI,EAAE,SAAS;iBACf;gBACD;oBACC,IAAI,EAAE,oBAAoB;oBAC1B,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,KAAK;iBACd;gBACD;oBACC,IAAI,EAAE,kBAAkB;oBACxB,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;oBAC9B,OAAO,EAAE,WAAW;iBACpB;gBACD;oBACC,IAAI,EAAE,gBAAgB;oBACtB,IAAI,EAAE,OAAO;oBACb,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,eAAe;oBACrB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,aAAa;oBACnB,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;oBAC5B,UAAU,EAAE,KAAK;iBACjB;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;oBAC5B,QAAQ,EAAE,mBAAmB;oBAC7B,UAAU,EAAE,KAAK;iBACjB;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;aACD;YACD,WAAW,EAAE;gBACZ;oBACC,WAAW,EAAE,CAAC,WAAW,CAAC;oBAC1B,mBAAmB,EAAE,SAAS;oBAC9B,qBAAqB,EAAE,CAAC,IAAI,CAAC;oBAC7B,QAAQ,EAAE,SAAS;iBACnB;gBACD;oBACC,WAAW,EAAE,CAAC,YAAY,CAAC;oBAC3B,mBAAmB,EAAE,UAAU;oBAC/B,qBAAqB,EAAE,CAAC,IAAI,CAAC;oBAC7B,QAAQ,EAAE,SAAS;iBACnB;gBACD;oBACC,WAAW,EAAE,CAAC,WAAW,CAAC;oBAC1B,mBAAmB,EAAE,OAAO;oBAC5B,qBAAqB,EAAE,CAAC,IAAI,CAAC;oBAC7B,QAAQ,EAAE,SAAS;iBACnB;aACD;SACD,CAAC,CACF,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,MAAM,WAAW,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;IACjD,CAAC;CACD;AAjHD,0FAiHC"}