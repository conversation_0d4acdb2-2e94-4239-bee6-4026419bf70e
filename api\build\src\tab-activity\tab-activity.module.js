"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TabActivityModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const tab_activity_controller_1 = require("./tab-activity.controller");
const tab_activity_service_1 = require("./tab-activity.service");
const tab_activity_entity_1 = require("./entities/tab-activity.entity");
const role_module_1 = require("../roles/role.module");
const invoice_audit_log_entity_1 = require("../invoice/entities/invoice-audit-log.entity");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
const user_entity_1 = require("../users/entities/user.entity");
let TabActivityModule = class TabActivityModule {
};
exports.TabActivityModule = TabActivityModule;
exports.TabActivityModule = TabActivityModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                tab_activity_entity_1.TabActivityEntity,
                invoice_audit_log_entity_1.InvoiceAuditLogEntity,
                invoice_entity_1.InvoiceEntity,
                user_entity_1.User
            ]),
            role_module_1.RoleModule
        ],
        controllers: [tab_activity_controller_1.TabActivitiesController],
        providers: [tab_activity_service_1.TabActivitiesService],
        exports: [tab_activity_service_1.TabActivitiesService]
    })
], TabActivityModule);
//# sourceMappingURL=tab-activity.module.js.map