"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AwsSecretsService = void 0;
const client_secrets_manager_1 = require("@aws-sdk/client-secrets-manager");
const credential_providers_1 = require("@aws-sdk/credential-providers");
const dotenv_1 = require("dotenv");
const fs = require("fs/promises");
const path = require("path");
// Load environment variables from .env file
(0, dotenv_1.config)();
class AwsSecretsService {
    constructor(region = process.env.AWS_SM_REGION || 'us-east-1') {
        this.region = region;
        const awsConfig = {
            region: this.region,
            credentials: (0, credential_providers_1.fromNodeProviderChain)()
        };
        if (process.env.AWS_SM_ACCESS_KEY_ID &&
            process.env.AWS_SM_SECRET_ACCESS_KEY_ID) {
            awsConfig.credentials = {
                accessKeyId: process.env.AWS_SM_ACCESS_KEY_ID,
                secretAccessKey: process.env.AWS_SM_SECRET_ACCESS_KEY_ID
            };
        }
        if (process.env.AWS_ENDPOINT) {
            awsConfig.endpoint = process.env.AWS_ENDPOINT;
        }
        this.client = new client_secrets_manager_1.SecretsManagerClient(awsConfig);
    }
    async fetchSecrets(secretId) {
        try {
            const command = new client_secrets_manager_1.GetSecretValueCommand({ SecretId: secretId });
            const response = await this.client.send(command);
            if (!response.SecretString) {
                throw new Error('Secret is binary or missing');
            }
            return JSON.parse(response.SecretString);
        }
        catch (error) {
            if (error instanceof Error) {
                console.error('Error fetching secrets:', error.message);
            }
            else {
                console.error('Unknown error fetching secrets:', error);
            }
            throw error;
        }
    }
    async loadSecrets(secretId) {
        const secrets = await this.fetchSecrets(secretId);
        // Load secrets into process.env
        Object.entries(secrets).forEach(([key, value]) => {
            process.env[key] = value;
        });
        console.log('Secrets file loaded');
        await this.saveSecretsToFile(secrets);
    }
    async saveSecretsToFile(secrets) {
        const envFilePath = path.resolve('.env');
        const envContent = Object.entries(secrets)
            .map(([key, value]) => `${key}=${value}`)
            .join('\n');
        let existingContent = '';
        try {
            existingContent = await fs.readFile(envFilePath, 'utf-8');
        }
        catch (err) {
            if (err.code !== 'ENOENT') {
                console.error('Error reading .env file:', err);
                throw err;
            }
        }
        const newContent = existingContent
            ? `${existingContent}\n${envContent}`
            : envContent;
        console.log('Writing to .env file:', newContent);
        await fs.writeFile(envFilePath, newContent);
        console.log('Secrets appended to .env file');
    }
}
exports.AwsSecretsService = AwsSecretsService;
//# sourceMappingURL=aws-secrets.service.js.map