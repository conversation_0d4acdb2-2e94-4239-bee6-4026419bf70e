"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddMoreColumnsToPrescription1724650812726 = void 0;
const typeorm_1 = require("typeorm");
class AddMoreColumnsToPrescription1724650812726 {
    async up(queryRunner) {
        await queryRunner.addColumns('clinic_medications', [
            new typeorm_1.TableColumn({
                name: 'is_restricted',
                type: 'boolean',
                isNullable: false,
                default: false
            }),
            new typeorm_1.TableColumn({
                name: 'drug',
                type: 'varchar',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'form',
                type: 'varchar',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'strength',
                type: 'integer',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'unit',
                type: 'varchar',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'is_added_by_user',
                type: 'boolean',
                isNullable: false,
                default: false
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('clinic_medications', 'is_restricted');
        await queryRunner.dropColumn('clinic_medications', 'drug');
        await queryRunner.dropColumn('clinic_medications', 'form');
        await queryRunner.dropColumn('clinic_medications', 'strength');
        await queryRunner.dropColumn('clinic_medications', 'unit');
        await queryRunner.dropColumn('clinic_medications', 'is_added_by_user');
    }
}
exports.AddMoreColumnsToPrescription1724650812726 = AddMoreColumnsToPrescription1724650812726;
//# sourceMappingURL=1724650812726-AddMoreColumnsToMedications.js.map