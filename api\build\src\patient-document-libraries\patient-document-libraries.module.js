"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientDocumentLibrariesModule = void 0;
const common_1 = require("@nestjs/common");
const patient_document_libraries_service_1 = require("./patient-document-libraries.service");
const patient_document_libraries_controller_1 = require("./patient-document-libraries.controller");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const typeorm_1 = require("@nestjs/typeorm");
const patient_document_library_entity_1 = require("./entities/patient-document-library.entity");
const user_entity_1 = require("../users/entities/user.entity");
const s3_service_1 = require("../utils/aws/s3/s3.service");
const ses_module_1 = require("../utils/aws/ses/ses.module");
const whatsapp_module_1 = require("../utils/whatsapp-integration/whatsapp.module");
let PatientDocumentLibrariesModule = class PatientDocumentLibrariesModule {
};
exports.PatientDocumentLibrariesModule = PatientDocumentLibrariesModule;
exports.PatientDocumentLibrariesModule = PatientDocumentLibrariesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([patient_document_library_entity_1.PatientDocumentLibrary, user_entity_1.User]),
            ses_module_1.SESModule,
            whatsapp_module_1.WhatsappModule
        ],
        controllers: [patient_document_libraries_controller_1.PatientDocumentLibrariesController],
        providers: [
            patient_document_libraries_service_1.PatientDocumentLibrariesService,
            winston_logger_service_1.WinstonLogger,
            s3_service_1.S3Service
        ]
    })
], PatientDocumentLibrariesModule);
//# sourceMappingURL=patient-document-libraries.module.js.map