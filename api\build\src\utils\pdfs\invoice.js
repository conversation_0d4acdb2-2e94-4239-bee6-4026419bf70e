"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateInvoice = void 0;
const generateInvoice = ({ invoiceNumber, invoiceDate, clinicName, clinicAddress, clinicPhone, clinicEmail, clinicWebsite, customerName, petName, petDetails, lineItems, subtotal, invoiceAmount, taxes, previousBalance, discount, totalDue, amountPaid, balanceDue, customerEmail, customerPhone }) => {
    const itemLists = lineItems.map((item, index) => {
        return `
          <tr>
            <td><p class='min-h-30'>${index + 1}.</p></td>
             <td ><p class='min-h-30'>${item.description}</p></td>
             <td ><p class='min-h-30'>${item.quantity}</p></td>
             <td ><p class='min-h-30'>${item.price}</p></td>
             <td ><p class='min-h-30'>${item.price * item.quantity}</p></td>
          </tr>
      `;
    });
    let itemLists1, itemList2;
    const size = itemLists.length;
    if (size > 14) {
        itemLists1 = itemLists.slice(0, 14).join('');
        itemList2 = itemLists.slice(14).join('');
    }
    else if (size > 10 && size <= 14) {
        itemLists1 = itemLists.slice(0, size - 1).join('');
        itemList2 = itemLists.slice(size - 1).join('');
    }
    else {
        itemLists1 = itemLists.slice(0, size).join('');
        itemList2 = itemLists.slice(size).join('');
    }
    return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Invoice</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">
  <style>
      body {
          font-family: 'Inter', sans-serif;
          margin: 0;
          padding: 0;
          color: #000000;
          background: #F9F7F2;
          font-weight: 300;
          font-size: 12px;
          line-height: 14.52px;
      }
    
      p {
          margin: 0;
      }
    .min-h-30{
        min-height: 30px
    }
      .fw-400 {
          font-weight: 400;
      }

      .fw-500 {
          font-weight: 500;
          color: #59645D;
      }

      .text-grey {
          color: #000000;
      }

      .text-center {
          text-align: center;
      }

      .text-right {
          text-align: right;
      }

      .pb-16 {
          padding-bottom: 16px;
      }

      .pt-20 {
          padding-top: 20px !important;
      }

      .pb-20 {
          padding-bottom: 20px;
      }

      .pb-25 {
          padding-bottom: 25px;
      }

      .pb-40 {
          padding-bottom: 40px;
      }

      .mr-36 {
          margin-right: 36px;
      }

      .mr-80 {
          margin-right: 80px;
      }

      .block {
          display: block;
      }

      .vertical_divider {
          border-left: 0.5px solid #D6D6D6;
          margin: 0 10px;
      }

      .invoice-container {
          max-width: 612px;
          margin: 0 auto;
          padding: 56px 34px;
      }

      .invoice-header h1 {
          font-size:54px;
          font-weight: 100;
          margin: 0 0 20px;
      }

      .invoice-header p {
          font-size: 18px;
          font-weight: 300;
          line-height: 24px;
      }

      .invoice-details {
          border-top: 0.5px solid #D6D6D6;
          padding: 25px 0;
      }

      .invoice-details h5 {
          font-size: 10px;
          font-weight: 400;
          line-height: 24px;
          margin: 0;
          color: #000000;
      }

      .invoice-details h6 {
          font-size: 12px;
          font-weight: 400;
          line-height: 14.52px;
          margin: 0;
          color: #000000;
      }

      .invoice-details p {
          font-size: 14px;
          font-weight: 400;
          line-height: 18px;
          color: #000000;
      }

      .invoice-info {
          display: flex;
          justify-content: space-between;
          gap: 20px;
          margin-top: 6px;
      }

      .invoice-info p {
        font-size: 10px;
    }

      .invoice-info div:first-child {
          max-width: 170px;
      }

      .invoice-info div:last-child {
          max-width: 145px;
      }

      .invoice-table {
          border: none;
          border-collapse: collapse;
          width: 100%;
          margin-bottom: 10px;
          border-top: 0.5px solid #D6D6D6;
      }

      .invoice-table th, .invoice-table td {
          padding: 8px;
          text-align: left;
      }

      .invoice-table thead {
          border-bottom: 0.5px solid #D6D6D6;
      }

      .invoice-table th {
          color: #000000;
          font-size: 12px;
          font-weight: 400;
          line-height: 14.52px;
      }

      .invoice-table tbody tr:first-child td {
          padding-top: 12px;
      }

      .invoice-table td {
          color: #000000;
          font-size: 12px;
          font-weight: 300;
          line-height: 14.52px;
      }

      .invoice-summary .sub-total {
          display: flex;
          flex-direction: column;
          gap: 10px;
          font-size:14px;
          color: #59645D;
          font-weight: 500;
      }

      .invoice-summary .amount-list {
          display: flex;
          flex-direction: column;
          gap: 10px;
          border-top: 0.5px solid #D6D6D6;
          padding: 12px 0;
      }

      .invoice-summary .amount-list span:last-child {
          min-width: 80px;
          display: inline-block;
          text-align: left;
      }

      .invoice-summary .amount-list p {
          display: flex;
          gap: 75px;
          justify-content: flex-end;
          font-weight: 400;
          font-size:14px;
          color: #59645D;
          font-weight: 500;
      }

      .invoice-summary .amount-list.sub-total p {
          gap: 80px;
      }

      .invoice-summary .note {
          border-top: 0.5px solid #D6D6D6;
          padding: 12px 0;
      }

      @media print {
          body {
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
              margin: 0;
          }
      }
  </style>
</head>
<body>
  <div class="invoice-container">
      <div class="invoice-header pb-16">
          <h1>Invoice</h1>
          <p>
              Invoice ID #${invoiceNumber}
              <span class="vertical_divider"></span>
              <span class="text-grey">${invoiceDate}</span>
          </p>
      </div>
      <div class="invoice-details pt-20">
          <h6>${clinicName}</h6>
          <div class="invoice-info">
              <div>
                  <p>${clinicAddress}</p>
              </div>
              <div>
                  <p>${clinicPhone}</p>
                  <p>
                      <span class="block">${clinicEmail}</span>
                      <span class="block">${clinicWebsite}</span>
                  </p>
              </div>
          </div>
      </div>
      <div class="invoice-details">
      <p>
      <span class="fw-500">${petName}</span> ${petDetails.trim() ? `| ${petDetails}` : ''}
      </p>
      <h5>${customerName}</h5>
      <h5>${customerEmail}</h5>
        <h5>${customerPhone}</h5>
      </div>
      <table class="invoice-table">
          <thead>
              <tr>
                  <th>No.</th>
                  <th>Description</th>
                  <th>QTY</th>
                  <th>Price</th>
                  <th>Total</th>
              </tr>
          </thead>
          <tbody>
              ${itemLists1}
          </tbody>
      </table>
      ${itemList2.length > 0
        ? `
        <div style="page-break-before: always;"></div>
        <table class="invoice-table" style="margin-top: 40px;">
            <thead>
                <tr>
                    <th>No.</th>
                    <th>Description</th>
                    <th>QTY</th>
                    <th>Price</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                ${itemList2}
            </tbody>
        </table>`
        : ''}
      <div class="invoice-summary text-right">
          <div class="amount-list sub-total">
              <p>
                  <span>Total Price</span> <span>${subtotal.toFixed(2)}</span>
              </p>
              <p>
                  <span>Taxes</span> <span>${taxes.toFixed(2)}</span>
              </p>
              <p>
                  <span>Discount</span> <span>${discount > 0 ? `- ${discount}` : `${discount}`}</span>
              </p>
          </div>
          <div class="amount-list sub-total">
              <p>
                  <span>Invoice Total</span>
                  <span>INR ${totalDue.toFixed(2)}</span>
              </p>
          </div>
      </div>
  </div>
</body>
</html>
`;
};
exports.generateInvoice = generateInvoice;
//# sourceMappingURL=invoice.js.map