"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePatientVaccinationsTable1726044010981 = void 0;
const typeorm_1 = require("typeorm");
class CreatePatientVaccinationsTable1726044010981 {
    constructor() {
        this.name = 'CreatePatientVaccinationsTable1726044010981';
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'patient_vaccinations',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'patient_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'appointment_id',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'system_generated',
                    type: 'bool',
                    default: false
                },
                {
                    name: 'doctor_name',
                    type: 'varchar',
                    isNullable: true
                },
                {
                    name: 'vaccine_name',
                    type: 'varchar'
                },
                {
                    name: 'vaccination_date',
                    type: 'date'
                },
                {
                    name: 'report_url',
                    type: 'varchar',
                    isNullable: true
                },
                {
                    name: 'url_meta',
                    type: 'jsonb',
                    isNullable: true
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                }
            ]
        }));
        await queryRunner.createForeignKey('patient_vaccinations', new typeorm_1.TableForeignKey({
            columnNames: ['patient_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'patients',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
        await queryRunner.createForeignKey('patient_vaccinations', new typeorm_1.TableForeignKey({
            columnNames: ['appointment_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'appointments',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('patient_vaccinations');
    }
}
exports.CreatePatientVaccinationsTable1726044010981 = CreatePatientVaccinationsTable1726044010981;
//# sourceMappingURL=1726044010981-create-patient-vaccinations-table.js.map