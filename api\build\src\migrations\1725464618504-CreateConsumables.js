"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateConsumables1725464618504 = void 0;
const typeorm_1 = require("typeorm");
class CreateConsumables1725464618504 {
    constructor() {
        this.name = 'CreateConsumables1725464618504';
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'clinic_consumables',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'clinic_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'brand_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'unique_id',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'product_name',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'current_stock',
                    type: 'integer',
                    isNullable: true
                },
                {
                    name: 'minimum_quantity',
                    type: 'integer',
                    isNullable: true
                },
                {
                    name: 'reorder_value',
                    type: 'integer',
                    isNullable: true
                },
                {
                    name: 'description',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'purchase_price',
                    type: 'decimal',
                    scale: 2, // it specifies digits after the decimal point
                    precision: 10, //  it specifies the total number of digits that can be stored, both before and after the decimal point
                    isNullable: true,
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                },
            ]
        }), true);
        await queryRunner.createForeignKey('clinic_consumables', new typeorm_1.TableForeignKey({
            columnNames: ['brand_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'brands',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
        await queryRunner.createForeignKey('clinic_consumables', new typeorm_1.TableForeignKey({
            columnNames: ['clinic_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinics',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('clinic_consumables');
    }
}
exports.CreateConsumables1725464618504 = CreateConsumables1725464618504;
//# sourceMappingURL=1725464618504-CreateConsumables.js.map