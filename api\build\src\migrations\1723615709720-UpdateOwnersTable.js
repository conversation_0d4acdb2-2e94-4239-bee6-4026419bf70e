"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddCountryCodeToOwners1723615709720 = void 0;
const typeorm_1 = require("typeorm");
class AddCountryCodeToOwners1723615709720 {
    async up(queryRunner) {
        await queryRunner.addColumn('owners', new typeorm_1.TableColumn({
            name: 'country_code',
            type: 'varchar',
            length: '5',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('owners', 'country_code');
    }
}
exports.AddCountryCodeToOwners1723615709720 = AddCountryCodeToOwners1723615709720;
//# sourceMappingURL=1723615709720-UpdateOwnersTable.js.map