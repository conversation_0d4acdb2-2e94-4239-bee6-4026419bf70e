"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimitingMiddleware = void 0;
const common_1 = require("@nestjs/common");
const rate_limiter_flexible_1 = require("rate-limiter-flexible");
const geoip = require("geoip-lite");
const config_1 = require("@nestjs/config");
let RateLimitingMiddleware = class RateLimitingMiddleware {
    constructor(configService) {
        var _a, _b, _c, _d;
        this.configService = configService;
        this.healthCheckPaths = [
            '/api/healtz/db',
            '/api/healtz/db/migration',
            '/api/healtz/redis',
            '/api/healtz/all',
            '/api/healtz',
            '/api/health',
            '/api/error'
        ];
        const globalLimit = ((_a = this.configService) === null || _a === void 0 ? void 0 : _a.get('app.global.limit')) || 80;
        const globalDuration = ((_b = this.configService) === null || _b === void 0 ? void 0 : _b.get('app.global.duration')) || 1;
        this.globalRateLimiter = new rate_limiter_flexible_1.RateLimiterMemory({
            points: globalLimit,
            duration: globalDuration
        });
        this.blacklistedIPs = new Set(((_c = this.configService) === null || _c === void 0 ? void 0 : _c.get('app.blacklistedIPs')) || []);
        this.allowedRegions = new Set(((_d = this.configService) === null || _d === void 0 ? void 0 : _d.get('app.allowedRegions')) || []);
    }
    async use(req, res, next) {
        try {
            // Skip health check endpoints
            if (this.healthCheckPaths.includes(req.path)) {
                return next();
            }
            const ip = req.ip || '';
            // Check blacklisted IPs
            if (ip && this.blacklistedIPs.has(ip)) {
                return res.status(common_1.HttpStatus.FORBIDDEN).json({ message: 'Access denied' });
            }
            // Check geo-restrictions
            const geo = ip ? geoip.lookup(ip) : null;
            if (geo && this.allowedRegions.size > 0 && !this.allowedRegions.has(geo.country)) {
                return res.status(common_1.HttpStatus.FORBIDDEN).json({ message: 'Access denied from your region' });
            }
            // Apply rate limiting
            if (ip) {
                try {
                    await this.globalRateLimiter.consume(ip);
                }
                catch (rateLimitError) {
                    if (rateLimitError instanceof rate_limiter_flexible_1.RateLimiterRes) {
                        const retryAfter = Math.ceil(rateLimitError.msBeforeNext / 1000) || 1;
                        res.set('Retry-After', String(retryAfter));
                        return res.status(common_1.HttpStatus.TOO_MANY_REQUESTS).json({ message: 'Too Many Requests' });
                    }
                }
            }
            return next();
        }
        catch (error) {
            console.error('Rate limiting middleware error:', error);
            return res.status(common_1.HttpStatus.INTERNAL_SERVER_ERROR).json({ message: 'Internal Server Error' });
        }
    }
};
exports.RateLimitingMiddleware = RateLimitingMiddleware;
exports.RateLimitingMiddleware = RateLimitingMiddleware = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], RateLimitingMiddleware);
//# sourceMappingURL=rate-limiting.middleware.js.map