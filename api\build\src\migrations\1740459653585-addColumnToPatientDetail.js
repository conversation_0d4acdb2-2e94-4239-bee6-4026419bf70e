"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddColumnToPatientDetail1740459653585 = void 0;
const typeorm_1 = require("typeorm");
class AddColumnToPatientDetail1740459653585 {
    async up(queryRunner) {
        await queryRunner.addColumn("payment_details", new typeorm_1.TableColumn({
            name: "ledger_document_filekey",
            type: "varchar",
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn("payment_details", "ledger_document_filekey");
    }
}
exports.AddColumnToPatientDetail1740459653585 = AddColumnToPatientDetail1740459653585;
//# sourceMappingURL=1740459653585-addColumnToPatientDetail.js.map