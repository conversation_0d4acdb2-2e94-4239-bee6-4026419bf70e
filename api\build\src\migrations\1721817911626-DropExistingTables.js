"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DropExistingTables1721817911626 = void 0;
class DropExistingTables1721817911626 {
    async up(queryRunner) {
        await queryRunner.query(`DROP TABLE IF EXISTS "patients"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "owners"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "users"`);
    }
    async down() {
        console.log('This migration cannot be reverted');
    }
}
exports.DropExistingTables1721817911626 = DropExistingTables1721817911626;
//# sourceMappingURL=1721817911626-DropExistingTables.js.map