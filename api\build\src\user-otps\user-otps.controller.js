"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserOtpController = void 0;
const common_1 = require("@nestjs/common");
const user_otps_service_1 = require("./user-otps.service");
const users_service_1 = require("../users/users.service");
const generate_otp_dto_1 = require("./dto/generate-otp-dto");
const swagger_1 = require("@nestjs/swagger");
const create_user_otp_dto_1 = require("./dto/create-user-otp.dto");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const user_entity_1 = require("../users/entities/user.entity");
const validate_user_otp_dto_1 = require("./dto/validate-user-otp.dto");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
let UserOtpController = class UserOtpController {
    constructor(userOtpsService, usersService, logger) {
        this.userOtpsService = userOtpsService;
        this.usersService = usersService;
        this.logger = logger;
    }
    async generateOtp(generateOtpDto) {
        try {
            this.logger.log('Generating OTP', { dto: generateOtpDto });
            const response = await this.userOtpsService.createOtp(generateOtpDto);
            this.logger.log('OTP generated successfully', {
                email: generateOtpDto.email
            });
            return response;
        }
        catch (error) {
            this.logger.error('Error generating OTP', { error });
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to generate OTP', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async validateOtp(validateOtpDto) {
        try {
            this.logger.log('Validating OTP', { dto: validateOtpDto });
            const user = await this.userOtpsService.validateOtp(validateOtpDto);
            this.logger.log('OTP validated successfully', { user });
            return user;
        }
        catch (error) {
            this.logger.error('Error validating OTP', { error });
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to validate OTP', common_1.HttpStatus.UNAUTHORIZED);
        }
    }
};
exports.UserOtpController = UserOtpController;
__decorate([
    (0, common_1.Post)('generate'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate OTP for a Super Admin' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'The OTP for Super Admin is created Successfully.',
        type: create_user_otp_dto_1.CreateUserOtpDto
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'User not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Only SuperAdmins can generate an OTP.'
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User not found.' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Failed to generate OTP.' }),
    (0, common_1.UsePipes)(common_1.ValidationPipe),
    (0, track_method_decorator_1.TrackMethod)('generateOtp-users-otps'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [generate_otp_dto_1.GenerateOtpDto]),
    __metadata("design:returntype", Promise)
], UserOtpController.prototype, "generateOtp", null);
__decorate([
    (0, common_1.Post)('validate'),
    (0, swagger_1.ApiOperation)({ summary: 'Validate OTP' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'OTP validated successfully.',
        type: user_entity_1.User
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Invalid OTP or User not found.' }),
    (0, common_1.UsePipes)(common_1.ValidationPipe),
    (0, track_method_decorator_1.TrackMethod)('validateOtp-users-otps'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [validate_user_otp_dto_1.ValidateOtpDto]),
    __metadata("design:returntype", Promise)
], UserOtpController.prototype, "validateOtp", null);
exports.UserOtpController = UserOtpController = __decorate([
    (0, swagger_1.ApiTags)('Otps'),
    (0, common_1.Controller)('otp'),
    __metadata("design:paramtypes", [user_otps_service_1.UserOtpsService,
        users_service_1.UsersService,
        winston_logger_service_1.WinstonLogger])
], UserOtpController);
//# sourceMappingURL=user-otps.controller.js.map