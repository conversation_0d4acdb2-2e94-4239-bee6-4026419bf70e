"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateChatRoomTable1725114029397 = void 0;
const typeorm_1 = require("typeorm");
class CreateChatRoomTable1725114029397 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'chat_rooms',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'last_message',
                    type: 'varchar',
                    isNullable: true
                },
                {
                    name: 'last_message_sender',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'unread_messages',
                    type: 'integer',
                    default: 0
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                }
            ]
        }), true);
        await queryRunner.createForeignKey('chat_rooms', new typeorm_1.TableForeignKey({
            columnNames: ['last_message_sender'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinic_users',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('chat_rooms');
    }
}
exports.CreateChatRoomTable1725114029397 = CreateChatRoomTable1725114029397;
//# sourceMappingURL=1725114029397-CreateChatRoomTable.js.map