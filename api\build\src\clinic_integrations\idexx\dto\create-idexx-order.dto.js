"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CheckIdexxOrdersDeletionDto = exports.CreateIdexxOrderDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateIdexxOrderDto {
}
exports.CreateIdexxOrderDto = CreateIdexxOrderDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The patient id',
        example: 'uuid'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateIdexxOrderDto.prototype, "patientId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The code of the test list item from IDEXX',
        example: 'IHC_test'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateIdexxOrderDto.prototype, "integrationCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The appointmen id',
        example: 'uuid'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateIdexxOrderDto.prototype, "appointmentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The clinic lab report id',
        example: 'uuid'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateIdexxOrderDto.prototype, "clinicLabReportId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The clinic id',
        example: 'uuid'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateIdexxOrderDto.prototype, "clinicId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The line item id',
        example: 'uuid'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateIdexxOrderDto.prototype, "lineItemId", void 0);
class CheckIdexxOrdersDeletionDto {
}
exports.CheckIdexxOrdersDeletionDto = CheckIdexxOrdersDeletionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of lab report IDs to check for deletion eligibility',
        example: ['uuid1', 'uuid2', 'uuid3'],
        type: [String]
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayNotEmpty)(),
    (0, class_validator_1.IsUUID)('4', { each: true }),
    __metadata("design:type", Array)
], CheckIdexxOrdersDeletionDto.prototype, "labReportIds", void 0);
//# sourceMappingURL=create-idexx-order.dto.js.map