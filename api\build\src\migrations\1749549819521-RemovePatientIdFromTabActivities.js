"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RemovePatientIdFromTabActivities1686107000000 = void 0;
class RemovePatientIdFromTabActivities1686107000000 {
    constructor() {
        this.name = 'RemovePatientIdFromTabActivities1686107000000';
    }
    async up(queryRunner) {
        // Find and drop only the foreign key constraint for patient_id column
        const tableName = 'tab_activities';
        const columnName = 'patient_id';
        // Get specifically the foreign key for patient_id column using key_column_usage
        const foreignKeys = await queryRunner.query(`SELECT tc.constraint_name FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
            ON tc.constraint_name = kcu.constraint_name
            WHERE tc.table_name = '${tableName}' 
            AND tc.constraint_type = 'FOREIGN KEY' 
            AND kcu.column_name = '${columnName}'`);
        // Drop each foreign key constraint related to patient_id
        for (const fk of foreignKeys) {
            await queryRunner.query(`ALTER TABLE "${tableName}" DROP CONSTRAINT "${fk.constraint_name}"`);
        }
        // Drop the patient_id column
        await queryRunner.query(`ALTER TABLE "${tableName}" DROP COLUMN IF EXISTS "${columnName}"`);
    }
    async down(queryRunner) {
        // Add back the patient_id column
        await queryRunner.query(`ALTER TABLE "tab_activities" ADD "patient_id" uuid NOT NULL`);
        // Add back the foreign key constraint
        await queryRunner.query(`ALTER TABLE "tab_activities" ADD CONSTRAINT "FK_tab_activities_patient_id" FOREIGN KEY ("patient_id") REFERENCES "patients"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }
}
exports.RemovePatientIdFromTabActivities1686107000000 = RemovePatientIdFromTabActivities1686107000000;
//# sourceMappingURL=1749549819521-RemovePatientIdFromTabActivities.js.map