"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FixPaymentDetailsOwnership1739959606740 = void 0;
class FixPaymentDetailsOwnership1739959606740 {
    async up(queryRunner) {
        // Create temporary table for payment details that need to be updated
        await queryRunner.query(`
            CREATE TEMPORARY TABLE payment_details_to_update (
                payment_id UUID,
                patient_id UUID,
                current_owner_id UUID,
                correct_owner_id UUID,
                amount DECIMAL(10,2),
                type VARCHAR(50),
                transaction_amount DECIMAL(10,2)
            );
        `);
        // First, create a temporary table to store primary owners for each patient
        await queryRunner.query(`
            CREATE TEMPORARY TABLE primary_owners AS
            SELECT DISTINCT ON (patient_id) 
                patient_id,
                owner_id as primary_owner_id,
                created_at
            FROM patient_owners
            WHERE is_primary = true
            ORDER BY patient_id, created_at DESC;
        `);
        // Insert mismatched payment details into temporary table - Case 1: Non-primary owners
        await queryRunner.query(`
            INSERT INTO payment_details_to_update (
                payment_id, 
                patient_id, 
                current_owner_id, 
                correct_owner_id, 
                amount, 
                type, 
                transaction_amount
            )
            SELECT DISTINCT
                pd.id as payment_id,
                pd.patient_id,
                pd.owner_id as current_owner_id,
                po.primary_owner_id as correct_owner_id,
                pd.amount,
                pd.type,
                pd.transaction_amount
            FROM payment_details pd
            LEFT JOIN patient_owners po_current ON pd.patient_id = po_current.patient_id 
                AND pd.owner_id = po_current.owner_id
            JOIN primary_owners po ON pd.patient_id = po.patient_id
            WHERE (
                -- Case 1: Payment is associated with a non-primary owner
                (po_current.is_primary = false OR po_current.is_primary IS NULL)
                -- Case 2: Payment owner_id doesn't match the primary owner
                AND pd.owner_id != po.primary_owner_id
                -- Case 3: Ensure we're not duplicating any records
                AND NOT EXISTS (
                    SELECT 1 
                    FROM payment_details_to_update ptu 
                    WHERE ptu.payment_id = pd.id
                )
            );
        `);
        // Insert mismatched payment details - Case 2: Orphaned payments (no owner association)
        await queryRunner.query(`
            INSERT INTO payment_details_to_update (
                payment_id, 
                patient_id, 
                current_owner_id, 
                correct_owner_id, 
                amount, 
                type, 
                transaction_amount
            )
            SELECT DISTINCT
                pd.id as payment_id,
                pd.patient_id,
                pd.owner_id as current_owner_id,
                po.primary_owner_id as correct_owner_id,
                pd.amount,
                pd.type,
                pd.transaction_amount
            FROM payment_details pd
            LEFT JOIN patient_owners po_current ON pd.patient_id = po_current.patient_id 
                AND pd.owner_id = po_current.owner_id
            JOIN primary_owners po ON pd.patient_id = po.patient_id
            WHERE po_current.id IS NULL
            AND NOT EXISTS (
                SELECT 1 
                FROM payment_details_to_update ptu 
                WHERE ptu.payment_id = pd.id
            );
        `);
        // Create migration_logs table if it doesn't exist
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS migration_logs (
                id SERIAL PRIMARY KEY,
                migration_name VARCHAR(255),
                entity_id UUID,
                field_name VARCHAR(255),
                old_value TEXT,
                new_value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        `);
        // Log changes before updating
        await queryRunner.query(`
            INSERT INTO migration_logs (migration_name, entity_id, field_name, old_value, new_value)
            SELECT 
                'FixPaymentDetailsOwnership1739955032762',
                pd.payment_id,
                'owner_id',
                pd.current_owner_id::text,
                pd.correct_owner_id::text
            FROM payment_details_to_update pd;
        `);
        // Update balances
        await queryRunner.query(`
            -- Subtract amounts from old owners
            UPDATE owner_brands ob
            SET owner_balance = COALESCE(owner_balance, 0) - COALESCE(t.total_amount, 0)
            FROM (
                SELECT 
                    current_owner_id,
                    SUM(CASE 
                        WHEN type = 'Invoice' THEN COALESCE(amount, 0)
                        WHEN type = 'Collect' THEN COALESCE(transaction_amount, 0)
                    END) as total_amount
                FROM payment_details_to_update
                GROUP BY current_owner_id
            ) t
            WHERE ob.id = t.current_owner_id;

            -- Add amounts to correct owners
            UPDATE owner_brands ob
            SET owner_balance = COALESCE(owner_balance, 0) + COALESCE(t.total_amount, 0)
            FROM (
                SELECT 
                    correct_owner_id,
                    SUM(CASE 
                        WHEN type = 'Invoice' THEN COALESCE(amount, 0)
                        WHEN type = 'Collect' THEN COALESCE(transaction_amount, 0)
                    END) as total_amount
                FROM payment_details_to_update
                GROUP BY correct_owner_id
            ) t
            WHERE ob.id = t.correct_owner_id;
        `);
        // Update payment details with correct owner IDs
        await queryRunner.query(`
            UPDATE payment_details pd
            SET owner_id = ptu.correct_owner_id
            FROM payment_details_to_update ptu
            WHERE pd.id = ptu.payment_id;
        `);
        // Clean up temporary tables
        await queryRunner.query(`
            DROP TABLE payment_details_to_update;
            DROP TABLE primary_owners;
        `);
    }
    async down(queryRunner) {
        // throw new Error(
        // 	'This migration cannot be reverted as it fixes data inconsistencies'
        // );
    }
}
exports.FixPaymentDetailsOwnership1739959606740 = FixPaymentDetailsOwnership1739959606740;
//# sourceMappingURL=1739959606740-FixPaymentDetailsOwnership.js.map