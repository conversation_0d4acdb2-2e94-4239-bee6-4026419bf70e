import { Repository } from 'typeorm';
import { InventoryItemType } from '../entities/inventory-mapping.entity';
import { ClinicServiceEntity } from '../../clinic-services/entities/clinic-service.entity';
import { ClinicProductEntity } from '../../clinic-products/entities/clinic-product.entity';
import { ClinicMedicationEntity } from '../../clinic-medications/entities/clinic-medication.entity';
import { ClinicConsumableEntity } from '../../clinic-consumables/entities/clinic-consumable.entity';
import { ClinicVaccinationEntity } from '../../clinic-vaccinations/entities/clinic-vaccination.entity';
import { ClinicLabReport } from '../../clinic-lab-report/entities/clinic-lab-report.entity';
import { AppointmentAssessmentEntity } from '../../appointment-assessment/entities/appointment-assessment.entity';
export declare class InventoryMappingService {
    private readonly clinicServiceRepository;
    private readonly clinicProductRepository;
    private readonly clinicMedicationRepository;
    private readonly clinicConsumableRepository;
    private readonly clinicVaccinationRepository;
    private readonly clinicLabReportRepository;
    private readonly appointmentAssessmentRepository;
    constructor(clinicServiceRepository: Repository<ClinicServiceEntity>, clinicProductRepository: Repository<ClinicProductEntity>, clinicMedicationRepository: Repository<ClinicMedicationEntity>, clinicConsumableRepository: Repository<ClinicConsumableEntity>, clinicVaccinationRepository: Repository<ClinicVaccinationEntity>, clinicLabReportRepository: Repository<ClinicLabReport>, appointmentAssessmentRepository: Repository<AppointmentAssessmentEntity>);
    findAndMapInventoryItem(sourceItemId: string, itemType: InventoryItemType, destinationClinicId: string): Promise<string | null>;
    private findSourceItem;
    private getItemName;
    private findDestinationItemByName;
}
