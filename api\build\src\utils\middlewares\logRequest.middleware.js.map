{"version": 3, "file": "logRequest.middleware.js", "sourceRoot": "", "sources": ["../../../../src/utils/middlewares/logRequest.middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4D;AAE5D,+BAAoC;AACpC,6EAAiE;AACjE,mDAA+C;AAC/C,wCAAwC;AAGjC,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAC7B,YAA6B,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAEtD,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QAClD,4BAAY,CAAC,GAAG,CAAC,GAAG,EAAE;YACrB,MAAM,OAAO,GAAI,GAAG,CAAC,OAAO,CAAC,UAAU,CAAY,IAAI,IAAA,SAAM,GAAE,CAAC;YAChE,MAAM,SAAS,GACb,GAAG,CAAC,OAAO,CAAC,YAAY,CAAY;gBACrC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC;gBACxB,IAAA,SAAM,GAAE,CAAC;YAEV,4BAAY,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACjC,4BAAY,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAErC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC;YAClC,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC;YAEtC,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACnC,GAAG,CAAC,SAAS,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;YAEvC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAEzB,kCAAkC;YAClC,mDAAmD;YACnD,uDAAuD;YAEvD,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACrB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;gBACpC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC;gBACxC,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC;gBAE3B,MAAM,UAAU,GAAG,aAAa,OAAO,iBAAiB,SAAS,cAAc,MAAM,WAAW,WAAW,cAAc,UAAU,gBAAgB,QAAQ,YAAY,EAAE,GAAG,CAAC;gBAE7K,MAAM,SAAS,GAAG;oBACjB,OAAO;oBACP,SAAS;oBACT,MAAM;oBACN,GAAG,EAAE,WAAW;oBAChB,UAAU;oBACV,QAAQ;oBACR,EAAE;oBACF,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC;iBACpC,CAAC;gBAEF,8BAA8B;gBAC9B,+DAA+D;gBAC/D,yDAAyD;gBAEzD,IAAI,UAAU,IAAI,GAAG,EAAE,CAAC;oBACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;oBACzC,wBAAwB;oBACxB,4CAA4C;oBAC5C,KAAK;gBACN,CAAC;qBAAM,IAAI,UAAU,IAAI,GAAG,EAAE,CAAC;oBAC9B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;oBACzC,wBAAwB;oBACxB,4CAA4C;oBAC5C,KAAK;gBACN,CAAC;qBAAM,CAAC;oBACP,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;gBACxC,CAAC;YACF,CAAC,CAAC,CAAC;YAEH,IAAI,EAAE,CAAC;QACR,CAAC,CAAC,CAAC;IACJ,CAAC;CACD,CAAA;AAlEY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;qCAEyB,sCAAa;GADtC,iBAAiB,CAkE7B"}