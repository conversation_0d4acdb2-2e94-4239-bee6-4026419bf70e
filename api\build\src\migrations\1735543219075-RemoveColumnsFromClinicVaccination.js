"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RemoveColumnsFromClinicVaccination1735543219075 = void 0;
const typeorm_1 = require("typeorm");
class RemoveColumnsFromClinicVaccination1735543219075 {
    constructor() {
        this.name = 'RemoveColumnsFromClinicVaccination1735543219075';
    }
    async up(queryRunner) {
        await queryRunner.dropColumn('clinic_vaccinations', 'purchase_price');
        await queryRunner.dropColumn('clinic_vaccinations', 'reorder_value');
        await queryRunner.dropColumn('clinic_vaccinations', 'batch');
        await queryRunner.dropColumn('clinic_vaccinations', 'expiration');
        await queryRunner.dropColumn('clinic_vaccinations', 'description');
    }
    async down(queryRunner) {
        await queryRunner.addColumn('clinic_vaccinations', new typeorm_1.TableColumn({
            name: 'description',
            type: 'varchar',
            isNullable: true
        }));
        await queryRunner.addColumn('clinic_vaccinations', new typeorm_1.TableColumn({
            name: 'expiration',
            type: 'timestamp with time zone',
            isNullable: true
        }));
        await queryRunner.addColumn('clinic_vaccinations', new typeorm_1.TableColumn({
            name: 'batch',
            type: 'varchar',
            isNullable: true
        }));
        await queryRunner.addColumn('clinic_vaccinations', new typeorm_1.TableColumn({
            name: 'reorder_value',
            type: 'integer',
            isNullable: true
        }));
        await queryRunner.addColumn('clinic_vaccinations', new typeorm_1.TableColumn({
            name: 'purchase_price',
            type: 'decimal',
            isNullable: true
        }));
    }
}
exports.RemoveColumnsFromClinicVaccination1735543219075 = RemoveColumnsFromClinicVaccination1735543219075;
//# sourceMappingURL=1735543219075-RemoveColumnsFromClinicVaccination.js.map