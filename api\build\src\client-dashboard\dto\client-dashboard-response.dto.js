"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientDashboardResponseDto = exports.OwnerInfoDto = exports.PetDto = void 0;
const swagger_1 = require("@nestjs/swagger");
// Define a DTO for Pet information
class PetDto {
}
exports.PetDto = PetDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The ID of the pet',
        example: '123e4567-e89b-12d3-a456-426614174000'
    }),
    __metadata("design:type", String)
], PetDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The name of the pet', example: 'Leo' }),
    __metadata("design:type", String)
], PetDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The breed of the pet', example: 'Labrador' }),
    __metadata("design:type", String)
], PetDto.prototype, "breed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The species of the pet', example: 'Dog' }),
    __metadata("design:type", String)
], PetDto.prototype, "species", void 0);
// Define a DTO for Owner information to be nested within the main response
// This resolves the issue where @ApiProperty decorators were incorrectly placed inside an inline object type.
class OwnerInfoDto {
}
exports.OwnerInfoDto = OwnerInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The ID of the client',
        example: '123e4567-e89b-12d3-a456-426614174001'
    }),
    __metadata("design:type", String)
], OwnerInfoDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The first name of the client',
        example: 'Shivam'
    }),
    __metadata("design:type", String)
], OwnerInfoDto.prototype, "firstName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The last name of the client',
        example: 'Agarwal'
    }),
    __metadata("design:type", String)
], OwnerInfoDto.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The full name of the client',
        example: 'Shivam Agarwal'
    }),
    __metadata("design:type", String)
], OwnerInfoDto.prototype, "fullName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The phone number of the client',
        example: '9876543210'
    }),
    __metadata("design:type", String)
], OwnerInfoDto.prototype, "phoneNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The email of the client',
        example: '<EMAIL>'
    }),
    __metadata("design:type", String)
], OwnerInfoDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The address of the client',
        example: '123 Main St, City'
    }),
    __metadata("design:type", String)
], OwnerInfoDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The balance amount of the client',
        example: 1000.0, // Example should match the type (number)
        type: Number // Explicitly set type for Swagger
    }),
    __metadata("design:type", Number)
], OwnerInfoDto.prototype, "ownerBalance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The credit amount available to the client',
        example: 50.0,
        type: Number
    }),
    __metadata("design:type", Number)
], OwnerInfoDto.prototype, "ownerCredits", void 0);
// Define the main response DTO for the Client Dashboard
class ClientDashboardResponseDto {
}
exports.ClientDashboardResponseDto = ClientDashboardResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Client information',
        type: OwnerInfoDto // Reference the OwnerInfoDto class for Swagger documentation
    }),
    __metadata("design:type", OwnerInfoDto)
], ClientDashboardResponseDto.prototype, "owner", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of pets owned by the client',
        type: [PetDto] // Reference the PetDto class for Swagger documentation (array)
    }),
    __metadata("design:type", Array)
], ClientDashboardResponseDto.prototype, "pets", void 0);
//# sourceMappingURL=client-dashboard-response.dto.js.map