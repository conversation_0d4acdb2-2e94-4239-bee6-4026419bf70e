"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientDocumentLibrariesController = void 0;
const common_1 = require("@nestjs/common");
const patient_document_libraries_service_1 = require("./patient-document-libraries.service");
const create_patient_document_library_dto_1 = require("./dto/create-patient-document-library.dto");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const swagger_1 = require("@nestjs/swagger");
const patient_document_library_entity_1 = require("./entities/patient-document-library.entity");
const update_patient_document_library_dto_1 = require("./dto/update-patient-document-library.dto");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
let PatientDocumentLibrariesController = class PatientDocumentLibrariesController {
    constructor(patientDocumentLibrariesService, logger) {
        this.patientDocumentLibrariesService = patientDocumentLibrariesService;
        this.logger = logger;
    }
    create(createPatientDocumentLibraryDto) {
        this.logger.log('Creating a new PatientDocumentLibrary', {
            action: 'create',
            data: createPatientDocumentLibraryDto
        });
        return this.patientDocumentLibrariesService.create(createPatientDocumentLibraryDto);
    }
    findAll(patientId, page = 1, limit = 10, search) {
        this.logger.log('Fetching PatientDocumentLibraries', {
            action: 'findAll',
            patientId,
            page,
            limit,
            search
        });
        return this.patientDocumentLibrariesService.findAll(patientId, +page, +limit, search);
    }
    async findOne(id) {
        this.logger.log(`Received request to retrieve document library entry with ID: ${id}`);
        const result = await this.patientDocumentLibrariesService.findOne(id);
        this.logger.log(`Successfully retrieved document library entry with ID: ${id}`);
        return result;
    }
    async sendSignedDocument(id, body) {
        return await this.patientDocumentLibrariesService.sendSignedDocument(id, body);
    }
};
exports.PatientDocumentLibrariesController = PatientDocumentLibrariesController;
__decorate([
    (0, common_1.Post)(),
    (0, track_method_decorator_1.TrackMethod)('create-patient-document-libraries'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_patient_document_library_dto_1.CreatePatientDocumentLibraryDto]),
    __metadata("design:returntype", void 0)
], PatientDocumentLibrariesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(':patientId'),
    (0, track_method_decorator_1.TrackMethod)('findAll-patient-document-libraries'),
    __param(0, (0, common_1.Param)('patientId')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __param(3, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object, String]),
    __metadata("design:returntype", void 0)
], PatientDocumentLibrariesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('document/:id'),
    (0, swagger_1.ApiOperation)({ summary: 'Retrieve a document library entry by ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'The requested document library entry.',
        type: patient_document_library_entity_1.PatientDocumentLibrary
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Document library entry not found.'
    }),
    (0, track_method_decorator_1.TrackMethod)('findOne-patient-document-libraries'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PatientDocumentLibrariesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, track_method_decorator_1.TrackMethod)('sendSignedDocument-patient-document-libraries'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_patient_document_library_dto_1.UpdateSignedDocumentDto]),
    __metadata("design:returntype", Promise)
], PatientDocumentLibrariesController.prototype, "sendSignedDocument", null);
exports.PatientDocumentLibrariesController = PatientDocumentLibrariesController = __decorate([
    (0, common_1.Controller)('patient-document-libraries'),
    __metadata("design:paramtypes", [patient_document_libraries_service_1.PatientDocumentLibrariesService,
        winston_logger_service_1.WinstonLogger])
], PatientDocumentLibrariesController);
//# sourceMappingURL=patient-document-libraries.controller.js.map