{"version": 3, "file": "1727062374221-add-clinic-user-reference-to-tasks.js", "sourceRoot": "", "sources": ["../../../src/migrations/1727062374221-add-clinic-user-reference-to-tasks.ts"], "names": [], "mappings": ";;;AAAA,qCAA2E;AAE3E,MAAa,0CAA0C;IAG/C,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,MAAM,KAAK,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,KAAK,EAAE,CAAC;YACX,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;YACtC,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;gBACtC,MAAM,WAAW,CAAC,cAAc,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;YACvD,CAAC;QACF,CAAC;QACD,MAAM,WAAW,CAAC,gBAAgB,CACjC,OAAO,EACP,IAAI,yBAAe,CAAC;YACnB,WAAW,EAAE,CAAC,SAAS,CAAC;YACxB,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,cAAc;YACnC,QAAQ,EAAE,UAAU;YACpB,QAAQ,EAAE,SAAS;SACnB,CAAC,CACF,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,MAAM,KAAK,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,KAAK,EAAE,CAAC;YACX,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;YACtC,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;gBACtC,MAAM,WAAW,CAAC,cAAc,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;YACvD,CAAC;QACF,CAAC;QACD,MAAM,WAAW,CAAC,gBAAgB,CACjC,OAAO,EACP,IAAI,yBAAe,CAAC;YACnB,WAAW,EAAE,CAAC,SAAS,CAAC;YACxB,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,OAAO;YAC5B,QAAQ,EAAE,UAAU;YACpB,QAAQ,EAAE,SAAS;SACnB,CAAC,CACF,CAAC;IACH,CAAC;CACD;AA1CD,gGA0CC"}