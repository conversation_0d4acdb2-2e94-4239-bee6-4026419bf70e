import { Patient } from '../../patients/entities/patient.entity';
import { DocumentLibrary } from '../../document-library/entities/document-library.entity';
import { User } from '../../users/entities/user.entity';
export declare class PatientDocumentLibrary {
    id: string;
    patientId: string;
    patient?: Patient;
    documentId: string;
    document?: DocumentLibrary;
    doctorId?: string;
    doctor?: User;
    doctorName?: string;
    documentSendStatus: 'pending' | 'completed';
    signedRecieved: 'pending' | 'completed';
    signedBy?: {
        firstName: string;
        lastName: string;
    };
    documentType: 'signable' | 'notSignable';
    fileKey: string;
    createdAt?: Date;
    updatedAt?: Date;
    createdBy?: string;
    updatedBy?: string;
    documentBody: {
        title: string;
        bodyText: string;
    };
}
