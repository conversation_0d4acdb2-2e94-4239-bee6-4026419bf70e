"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddColumnToPatientVaccinationTable1738132634086 = void 0;
const typeorm_1 = require("typeorm");
class AddColumnToPatientVaccinationTable1738132634086 {
    async up(queryRunner) {
        await queryRunner.addColumn("patient_vaccinations", new typeorm_1.TableColumn({
            name: "vaccine_id",
            type: "varchar",
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn("patient_vaccinations", "vaccine_id");
    }
}
exports.AddColumnToPatientVaccinationTable1738132634086 = AddColumnToPatientVaccinationTable1738132634086;
//# sourceMappingURL=1738132634086-addColumnToPatientVaccinationTable.js.map