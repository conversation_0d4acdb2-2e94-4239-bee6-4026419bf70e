"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUsersTable1726720861516 = void 0;
const typeorm_1 = require("typeorm");
class UpdateUsersTable1726720861516 {
    constructor() {
        this.name = 'UpdateUsersTable1726720861516';
    }
    async up(queryRunner) {
        await queryRunner.addColumns('users', [
            new typeorm_1.TableColumn({
                name: 'working_hours',
                type: 'jsonb',
                isNullable: true
            })
        ]);
        await queryRunner.dropColumns('users', [
            'working_hours_start',
            'working_hours_end',
            'working_days'
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumns('users', ['working_hours']);
        await queryRunner.addColumns('users', [
            new typeorm_1.TableColumn({
                name: 'working_hours_start',
                type: 'varchar',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'working_hours_end',
                type: 'varchar',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'working_days',
                type: 'text',
                isNullable: true
            })
        ]);
    }
}
exports.UpdateUsersTable1726720861516 = UpdateUsersTable1726720861516;
//# sourceMappingURL=1726720861516-UpdateUsersTable.js.map