"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateClinicLabReport1723641525368 = void 0;
const typeorm_1 = require("typeorm");
class UpdateClinicLabReport1723641525368 {
    constructor() {
        this.name = 'UpdateClinicLabReport1723641525368';
    }
    async up(queryRunner) {
        await queryRunner.addColumns('clinic_lab_reports', [
            new typeorm_1.TableColumn({
                name: 'created_by',
                type: 'uuid',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'updated_by',
                type: 'uuid',
                isNullable: true
            })
        ]);
        await queryRunner.createForeignKeys('clinic_lab_reports', [
            new typeorm_1.TableForeignKey({
                columnNames: ['created_by'],
                referencedColumnNames: ['id'],
                referencedTableName: 'users',
                onDelete: 'SET NULL'
            }),
            new typeorm_1.TableForeignKey({
                columnNames: ['updated_by'],
                referencedColumnNames: ['id'],
                referencedTableName: 'users',
                onDelete: 'SET NULL'
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumns('clinic_lab_reports', [
            new typeorm_1.TableColumn({
                name: 'created_by',
                type: 'uuid',
                length: '50',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'updated_by',
                type: 'uuid',
                length: '50',
                isNullable: true
            })
        ]);
        await queryRunner.dropForeignKeys('clinic_lab_reports', [
            new typeorm_1.TableForeignKey({
                columnNames: ['created_by'],
                referencedColumnNames: ['id'],
                referencedTableName: 'users',
                onDelete: 'SET NULL'
            }),
            new typeorm_1.TableForeignKey({
                columnNames: ['updated_by'],
                referencedColumnNames: ['id'],
                referencedTableName: 'users',
                onDelete: 'SET NULL'
            })
        ]);
    }
}
exports.UpdateClinicLabReport1723641525368 = UpdateClinicLabReport1723641525368;
//# sourceMappingURL=1723641525368-UpdateClinicLabReport.js.map