"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChangeStrengthColumnDataType1729492594406 = void 0;
const typeorm_1 = require("typeorm");
class ChangeStrengthColumnDataType1729492594406 {
    constructor() {
        this.name = 'ChangeStrengthColumnDataType1729492594406';
    }
    async up(queryRunner) {
        // Step 1: Add a temporary column with the desired type
        await queryRunner.addColumn('clinic_medications', new typeorm_1.TableColumn({
            name: 'strength_temp',
            type: 'decimal',
            precision: 10, // Adjust based on your required precision
            scale: 2, // Adjust based on your required scale
            isNullable: true
        }));
        // Step 2: Copy data from the old column to the new column, converting as needed
        await queryRunner.query(`
			UPDATE clinic_medications 
			SET strength_temp = strength::decimal
		`);
        // Step 3: Drop the old column
        await queryRunner.dropColumn('clinic_medications', 'strength');
        // Step 4: Rename the new column to the old column name
        await queryRunner.renameColumn('clinic_medications', 'strength_temp', 'strength');
    }
    async down(queryRunner) {
        // Reverse the process if you need to rollback
        await queryRunner.addColumn('clinic_medications', new typeorm_1.TableColumn({
            name: 'strength_temp',
            type: 'integer',
            isNullable: true
        }));
        await queryRunner.query(`
			UPDATE clinic_medications 
			SET strength_temp = strength::integer
		`);
        await queryRunner.dropColumn('clinic_medications', 'strength');
        await queryRunner.renameColumn('clinic_medications', 'strength_temp', 'strength');
    }
}
exports.ChangeStrengthColumnDataType1729492594406 = ChangeStrengthColumnDataType1729492594406;
//# sourceMappingURL=1729492594406-change-strength-column-data-type.js.map