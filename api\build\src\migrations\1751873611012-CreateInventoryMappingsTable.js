"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateInventoryMappingsTable1751873611012 = void 0;
const typeorm_1 = require("typeorm");
class CreateInventoryMappingsTable1751873611012 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'inventory_mappings',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'uuid'
                },
                {
                    name: 'source_item_id',
                    type: 'uuid'
                },
                {
                    name: 'source_item_type',
                    type: 'varchar'
                },
                {
                    name: 'destination_item_id',
                    type: 'uuid'
                },
                {
                    name: 'destination_item_type',
                    type: 'varchar'
                },
                {
                    name: 'source_clinic_id',
                    type: 'uuid'
                },
                {
                    name: 'destination_clinic_id',
                    type: 'uuid'
                },
                {
                    name: 'created_at',
                    type: 'timestamp with time zone',
                    default: 'now()'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp with time zone',
                    default: 'now()'
                }
            ]
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('inventory_mappings');
    }
}
exports.CreateInventoryMappingsTable1751873611012 = CreateInventoryMappingsTable1751873611012;
//# sourceMappingURL=1751873611012-CreateInventoryMappingsTable.js.map