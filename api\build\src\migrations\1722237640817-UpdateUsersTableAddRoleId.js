"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUsersTableAddRoleId1722237640817 = void 0;
const typeorm_1 = require("typeorm");
class UpdateUsersTableAddRoleId1722237640817 {
    async up(queryRunner) {
        await queryRunner.addColumn('users', new typeorm_1.TableColumn({
            name: 'roleId',
            type: 'uuid',
            isNullable: true
        }));
        await queryRunner.createForeignKey('users', new typeorm_1.TableForeignKey({
            columnNames: ['roleId'],
            referencedColumnNames: ['id'],
            referencedTableName: 'roles',
            onDelete: 'SET NULL'
        }));
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable('users');
        if (table) {
            const foreignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('roleId') !== -1);
            if (foreignKey) {
                await queryRunner.dropForeignKey('users', foreignKey);
            }
            await queryRunner.dropColumn('users', 'roleId');
        }
    }
}
exports.UpdateUsersTableAddRoleId1722237640817 = UpdateUsersTableAddRoleId1722237640817;
//# sourceMappingURL=1722237640817-UpdateUsersTableAddRoleId.js.map