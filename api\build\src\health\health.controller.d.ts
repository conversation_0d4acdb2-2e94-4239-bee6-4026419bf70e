import { HealthService } from './health.service';
export declare class HealthController {
    private readonly healthService;
    constructor(healthService: HealthService);
    checkDatabase(): Promise<import("@nestjs/terminus").HealthCheckResult>;
    checkMigrations(): Promise<import("@nestjs/terminus").HealthCheckResult>;
    checkRedis(): Promise<import("@nestjs/terminus").HealthCheckResult>;
    checkRedisLocks(): Promise<{
        status: string;
        info: import("@nestjs/terminus").HealthIndicatorResult;
        error?: undefined;
    } | {
        status: string;
        error: {
            message: string;
            details: any;
        };
        info?: undefined;
    }>;
    checkAll(): Promise<import("@nestjs/terminus").HealthCheckResult>;
    checkAllWithCluster(): Promise<import("@nestjs/terminus").HealthCheckResult>;
    checkGeneralHealth(): Promise<{
        isHealthy: boolean;
        message: string;
    }>;
    checkRedisCluster(): Promise<import("@nestjs/terminus").HealthCheckResult>;
    getRedisMetrics(): Promise<{
        status: string;
        metrics: {
            ping_time_ms: number;
            active_locks: number;
            total_monitored_locks: number;
            connection_type: string;
            timestamp: string;
        };
        locks: Record<string, {
            exists: boolean;
            ttl: number;
            value: string | null;
        }>;
        error?: undefined;
    } | {
        status: string;
        error: {
            message: string;
            details: any;
        };
        metrics?: undefined;
        locks?: undefined;
    }>;
}
