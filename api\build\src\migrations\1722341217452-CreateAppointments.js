"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAppointments1722341217452 = void 0;
class CreateAppointments1722341217452 {
    constructor() {
        this.name = 'CreateAppointments1722341217452';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "appointments" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "clinic_id" uuid NOT NULL, "patient_id" uuid NOT NULL, "room_id" uuid NOT NULL, "reason" character varying NOT NULL, "type" character varying NOT NULL, "triage" character varying NOT NULL, "date" TIMESTAMP NOT NULL, "start_time" TIMESTAMP WITH TIME ZONE NOT NULL, "end_time" TIMESTAMP WITH TIME ZONE, "deleted_at" TIMESTAMP WITH TIME ZONE, "weight" integer, "notes" jsonb, "pre_visit_questions" jsonb, "is_blocked" boolean, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "created_by" uuid, "updated_by" uuid, CONSTRAINT "PK_4a437a9a27e948726b8bb3e36ad" PRIMARY KEY ("id"))`);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "appointments"`);
    }
}
exports.CreateAppointments1722341217452 = CreateAppointments1722341217452;
//# sourceMappingURL=1722341217452-CreateAppointments.js.map