import { Repository, DataSource } from 'typeorm';
import { GlobalOwner } from '../owners/entities/global-owner.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { Patient } from '../patients/entities/patient.entity';
import { CreateAppointmentDetailsDto, CreateAppointmentDto, CreateOwnerDto, CreatePatientDto, CreatePatientOwnerDto, InvoiceDataDto, UpdateOwnerOpeningBalanceDto } from './dto/data.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { PatientOwner } from '../patients/entities/patient-owner.entity';
import { AppointmentDetailsEntity } from '../appointments/entities/appointment-details.entity';
import { AppointmentDoctorsEntity } from '../appointments/entities/appointment-doctor.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { CartEntity } from '../carts/entites/cart.entity';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { PaymentDetailsEntity } from '../payment-details/entities/payment-details.entity';
import { LabReport } from '../clinic-lab-report/entities/lab-report.entity';
import { ClinicLabReport } from '../clinic-lab-report/entities/clinic-lab-report.entity';
export declare class DataService {
    private globalOwnerRepository;
    private ownerBrandRepository;
    private patientsRepository;
    private patientOwnersRepository;
    private appointmentRepository;
    private appointmentDetailsRepository;
    private appointmentDoctorsRepository;
    private cartRepository;
    private invoiceRepository;
    private paymentDetailsRepository;
    private labReportRepository;
    private clinicLabReportRepository;
    private dataSource;
    private readonly logger;
    constructor(globalOwnerRepository: Repository<GlobalOwner>, ownerBrandRepository: Repository<OwnerBrand>, patientsRepository: Repository<Patient>, patientOwnersRepository: Repository<PatientOwner>, appointmentRepository: Repository<AppointmentEntity>, appointmentDetailsRepository: Repository<AppointmentDetailsEntity>, appointmentDoctorsRepository: Repository<AppointmentDoctorsEntity>, cartRepository: Repository<CartEntity>, invoiceRepository: Repository<InvoiceEntity>, paymentDetailsRepository: Repository<PaymentDetailsEntity>, labReportRepository: Repository<LabReport>, clinicLabReportRepository: Repository<ClinicLabReport>, dataSource: DataSource, logger: WinstonLogger);
    getHello(): string;
    private findOwnerByClientId;
    private findExistingPatientOwner;
    createOwner(createOwnerDto: CreateOwnerDto): Promise<OwnerBrand>;
    createPatient(createPatientDto: CreatePatientDto): Promise<Patient>;
    createPatientOwner(createPatientOwnerDto: CreatePatientOwnerDto): Promise<PatientOwner>;
    private findExistingAppointment;
    private findExistingAppointmentDetails;
    createAppointment(createAppointmentDto: CreateAppointmentDto): Promise<AppointmentEntity>;
    createAppointmentDetails(createAppointmentDetailsDto: CreateAppointmentDetailsDto): Promise<AppointmentDetailsEntity>;
    findByOldId(oldPatientId: string, clinicId: string): Promise<Patient>;
    findByPatientAndDate(patientId: string, date: string): Promise<AppointmentEntity>;
    createCart(appointmentId: string): Promise<CartEntity>;
    createInvoiceAndPaymentDetail(invoiceData: {
        cartId: string;
        patientId: string;
        ownerId: string;
        clinicId: string;
        brandId: string;
        s3Key: string;
        date: string;
        metadata?: Record<string, any>;
    }): Promise<{
        invoice: InvoiceEntity;
        paymentDetail: PaymentDetailsEntity;
    }>;
    processInvoices(invoiceData: InvoiceDataDto[]): Promise<any[]>;
    private createLabReport;
    private updateAppointmentDetailsWithLabReport;
    bulkUpdateOpeningBalance(updates: UpdateOwnerOpeningBalanceDto[]): Promise<Array<{
        clientId: string;
        status: 'success' | 'error';
        message?: string;
        ownerId?: string;
    }>>;
    findByOldPatientId(oldPatientId: string, clinicId: string): Promise<Patient>;
    updateMicrochipId(id: string, microchipId: string): Promise<Patient>;
}
