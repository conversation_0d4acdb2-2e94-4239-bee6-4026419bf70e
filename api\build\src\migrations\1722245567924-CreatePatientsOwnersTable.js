"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePatientOwnersTable1722243196316 = void 0;
const typeorm_1 = require("typeorm");
class CreatePatientOwnersTable1722243196316 {
    async up(queryRunner) {
        // Check if the table already exists
        const tableExists = await queryRunner.hasTable('patient_owners');
        if (!tableExists) {
            await queryRunner.createTable(new typeorm_1.Table({
                name: 'patient_owners',
                columns: [
                    {
                        name: 'id',
                        type: 'uuid',
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: 'uuid'
                    },
                    {
                        name: 'patient_id',
                        type: 'uuid'
                    },
                    {
                        name: 'owner_id',
                        type: 'uuid'
                    },
                    {
                        name: 'is_primary',
                        type: 'boolean',
                        default: false,
                        isNullable: true
                    },
                    {
                        name: 'created_at',
                        type: 'timestamp',
                        default: 'CURRENT_TIMESTAMP'
                    },
                    {
                        name: 'updated_at',
                        type: 'timestamp',
                        default: 'CURRENT_TIMESTAMP',
                        onUpdate: 'CURRENT_TIMESTAMP'
                    }
                ]
            }), true);
        }
        // Create foreign keys if they don't exist
        const table = await queryRunner.getTable('patient_owners');
        if (table) {
            const patientForeignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('patient_id') !== -1);
            if (!patientForeignKey) {
                await queryRunner.createForeignKey('patient_owners', new typeorm_1.TableForeignKey({
                    columnNames: ['patient_id'],
                    referencedColumnNames: ['id'],
                    referencedTableName: 'patients',
                    onDelete: 'CASCADE'
                }));
            }
        }
        if (table) {
            const ownerForeignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('owner_id') !== -1);
            if (!ownerForeignKey) {
                await queryRunner.createForeignKey('patient_owners', new typeorm_1.TableForeignKey({
                    columnNames: ['owner_id'],
                    referencedColumnNames: ['id'],
                    referencedTableName: 'owners',
                    onDelete: 'CASCADE'
                }));
            }
        }
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable('patient_owners');
        if (table) {
            const foreignKeys = table.foreignKeys;
            for (const foreignKey of foreignKeys) {
                await queryRunner.dropForeignKey('patient_owners', foreignKey);
            }
            await queryRunner.dropTable('patient_owners');
        }
    }
}
exports.CreatePatientOwnersTable1722243196316 = CreatePatientOwnersTable1722243196316;
//# sourceMappingURL=1722245567924-CreatePatientsOwnersTable.js.map