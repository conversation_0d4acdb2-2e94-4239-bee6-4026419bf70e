"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTabActivitiesTable1709827200000 = void 0;
const typeorm_1 = require("typeorm");
class CreateTabActivitiesTable1709827200000 {
    async up(queryRunner) {
        // Create ENUM types first
        await queryRunner.query(`
            CREATE TYPE tab_name_enum AS ENUM (
                'appointments',
                'diagnostics',
                'vaccinations',
                'communications',
                'invoices'
            )
        `);
        await queryRunner.query(`
            CREATE TYPE action_type_enum AS ENUM (
                'download',
                'share'
            )
        `);
        // Create the table using TypeORM's Table class
        await queryRunner.createTable(new typeorm_1.Table({
            name: "tab_activities",
            columns: [
                {
                    name: "id",
                    type: "uuid",
                    isPrimary: true,
                    generationStrategy: "uuid",
                    default: "uuid_generate_v4()"
                },
                {
                    name: "clinic_id",
                    type: "uuid",
                    isNullable: false
                },
                {
                    name: "brand_id",
                    type: "uuid",
                    isNullable: false
                },
                {
                    name: "patient_id",
                    type: "uuid",
                    isNullable: false
                },
                {
                    name: "tab_name",
                    type: "tab_name_enum",
                    isNullable: false
                },
                {
                    name: "action_type",
                    type: "action_type_enum",
                    isNullable: false
                },
                {
                    name: "reference_id",
                    type: "varchar",
                    isNullable: true
                },
                {
                    name: "created_at",
                    type: "timestamp",
                    default: "CURRENT_TIMESTAMP"
                },
                {
                    name: "updated_at",
                    type: "timestamp",
                    default: "CURRENT_TIMESTAMP",
                    onUpdate: "CURRENT_TIMESTAMP"
                },
                {
                    name: "created_by",
                    type: "uuid",
                    isNullable: true
                },
                {
                    name: "updated_by",
                    type: "uuid",
                    isNullable: true
                }
            ]
        }), true);
        const foreignKeys = [
            {
                columnNames: ["patient_id"],
                referencedTableName: "patients",
                referencedColumnNames: ["id"]
            },
            {
                columnNames: ["clinic_id"],
                referencedTableName: "clinics",
                referencedColumnNames: ["id"]
            },
            {
                columnNames: ["brand_id"],
                referencedTableName: "brands",
                referencedColumnNames: ["id"]
            },
            {
                columnNames: ["created_by"],
                referencedTableName: "users",
                referencedColumnNames: ["id"],
                onDelete: "SET NULL"
            },
            {
                columnNames: ["updated_by"],
                referencedTableName: "users",
                referencedColumnNames: ["id"],
                onDelete: "SET NULL"
            }
        ];
        for (const fk of foreignKeys) {
            await queryRunner.createForeignKey("tab_activities", new typeorm_1.TableForeignKey({
                columnNames: fk.columnNames,
                referencedColumnNames: fk.referencedColumnNames,
                referencedTableName: fk.referencedTableName,
                onDelete: fk.onDelete || "CASCADE"
            }));
        }
        // Add indexes
        await queryRunner.createIndex("tab_activities", new typeorm_1.TableIndex({
            name: "patient_tab_reference_id_clinic_id_brand_id",
            columnNames: ["patient_id", "reference_id", "clinic_id", "brand_id"]
        }));
        await queryRunner.createIndex("tab_activities", new typeorm_1.TableIndex({
            name: "activities_tab_name_action_type",
            columnNames: ["tab_name", "action_type"]
        }));
        await queryRunner.createIndex("tab_activities", new typeorm_1.TableIndex({
            name: "tab_activities_created_at",
            columnNames: ["created_at"],
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable("tab_activities");
        await queryRunner.query(`DROP TYPE "action_type_enum"`);
        await queryRunner.query(`DROP TYPE "tab_name_enum"`);
    }
}
exports.CreateTabActivitiesTable1709827200000 = CreateTabActivitiesTable1709827200000;
//# sourceMappingURL=1741333672007-CreateTabActivitiesTable.js.map