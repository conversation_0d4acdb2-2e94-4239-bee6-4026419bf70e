"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddColumnsToClinicUsersAndUsers1727187243802 = void 0;
class AddColumnsToClinicUsersAndUsers1727187243802 {
    async up(queryRunner) {
        // Add columns to clinic_users table
        await queryRunner.query(`
            ALTER TABLE clinic_users
            ADD COLUMN working_hours JSONB DEFAULT '{
                "workingHours": {
                    "monday": [{"endTime": "17:00", "startTime": "09:00", "isWorkingDay": true}],
                    "tuesday": [{"endTime": "17:00", "startTime": "09:00", "isWorkingDay": true}],
                    "wednesday": [{"endTime": "17:00", "startTime": "09:00", "isWorkingDay": true}],
                    "thursday": [{"endTime": "17:00", "startTime": "09:00", "isWorkingDay": true}],
                    "friday": [{"endTime": "17:00", "startTime": "09:00", "isWorkingDay": true}],
                    "saturday": [{"endTime": null, "startTime": null, "isWorkingDay": false}],
                    "sunday": [{"endTime": null, "startTime": null, "isWorkingDay": false}]
                }
            }'::jsonb,
            ADD COLUMN is_onboarded BOOLEAN DEFAULT false
        `);
        // Add country_code column to users table
        await queryRunner.query(`
            ALTER TABLE users
            ADD COLUMN country_code VARCHAR(5)
        `);
    }
    async down(queryRunner) {
        // Remove columns from clinic_users table
        await queryRunner.query(`
            ALTER TABLE clinic_users
            DROP COLUMN working_hours,
            DROP COLUMN is_onboarded
        `);
        // Remove country_code column from users table
        await queryRunner.query(`
            ALTER TABLE users
            DROP COLUMN country_code
        `);
    }
}
exports.AddColumnsToClinicUsersAndUsers1727187243802 = AddColumnsToClinicUsersAndUsers1727187243802;
//# sourceMappingURL=1727187243802-UpdateClinicUsersAndUsersTable.js.map