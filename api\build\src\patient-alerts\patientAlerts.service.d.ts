import { PatientAlertsEntity } from './entities/patientAlerts.entity';
import { CreatePatientAlertDto } from './dto/create-patientAlert.dto';
import { UpdatePatientAlertDto } from './dto/update-patientAlert.dto';
import { Repository } from 'typeorm';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
export declare class PatientAlertsService {
    private patientAlertsRepository;
    private readonly logger;
    constructor(patientAlertsRepository: Repository<PatientAlertsEntity>, logger: WinstonLogger);
    createPatientAlert(createPatientAlertDto: CreatePatientAlertDto): Promise<PatientAlertsEntity>;
    getPatientAlert(patientId: string): Promise<PatientAlertsEntity[]>;
    deletePatientAlertById(id: string, all?: string): Promise<void>;
    updatePatientAlertById(id: string, updatePatientAlertDto: UpdatePatientAlertDto): Promise<PatientAlertsEntity>;
}
