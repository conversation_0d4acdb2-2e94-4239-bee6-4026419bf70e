"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppUpdateDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class AppUpdateDto {
}
exports.AppUpdateDto = AppUpdateDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The build type',
        example: 'iOS or Android'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], AppUpdateDto.prototype, "buildType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The build  major version. Eg: X.Y.Z',
        example: 'X'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AppUpdateDto.prototype, "majorVersion", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The minor version. Eg: X.Y.Z',
        example: 'Y'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], AppUpdateDto.prototype, "minorVersion", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The build patchversion. Eg: X.Y.Z',
        example: 'Z'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], AppUpdateDto.prototype, "patchVersion", void 0);
//# sourceMappingURL=app-update.dto.js.map