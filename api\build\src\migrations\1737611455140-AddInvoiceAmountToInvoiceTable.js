"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddInvoiceAmountToInvoiceTable1737611455140 = void 0;
class AddInvoiceAmountToInvoiceTable1737611455140 {
    constructor() {
        this.name = 'AddInvoiceAmountToInvoiceTable1737611455140';
    }
    async up(queryRunner) {
        // Add transaction_amount and previous_balance columns to payment_details
        await queryRunner.query(`ALTER TABLE "payment_details" 
			 ADD COLUMN "transaction_amount" decimal(10,2) NOT NULL DEFAULT 0,
			 ADD COLUMN "previous_balance" decimal(10,2) NOT NULL DEFAULT 0`);
        // Make patient_id optional in payment_details table
        await queryRunner.query(`ALTER TABLE "payment_details" ALTER COLUMN "patient_id" DROP NOT NULL`);
        // Add invoice_amount column with decimal type
        await queryRunner.query(`ALTER TABLE "invoices" ADD COLUMN "invoice_amount" decimal(10,2) NOT NULL DEFAULT 0`);
        // Add showininvoice column with default true
        await queryRunner.query(`
			ALTER TABLE "payment_details" 
			ADD COLUMN "showininvoice" boolean NOT NULL DEFAULT true;
		`);
        // Add showinledger column with default true
        await queryRunner.query(`
			ALTER TABLE "payment_details" 
			ADD COLUMN "showinledger" boolean NOT NULL DEFAULT true;
		`);
        // Add comments for documentation
        await queryRunner.query(`
			COMMENT ON COLUMN payment_details.showininvoice IS 'Controls visibility of payment in invoices, defaults to true';
			COMMENT ON COLUMN payment_details.showinledger IS 'Controls visibility of payment in ledger, defaults to true';
		`);
        // Update invoice_amount based on invoice_type
        await queryRunner.query(`
			UPDATE "invoices" 
			SET "invoice_amount" = CASE 
				WHEN invoice_type = 'Refund' THEN COALESCE(amount_payable, 0)
				ELSE COALESCE(price_after_discount, 0) + COALESCE(total_tax, 0)
			END
			WHERE "invoice_amount" = 0
		`);
        // Update transaction_amount in payment_details based on type
        await queryRunner.query(`
			UPDATE payment_details pd
			SET transaction_amount = CASE 
				WHEN pd.type = 'Invoice' THEN 
					CASE 
						WHEN i.invoice_type = 'Refund' THEN COALESCE(i.amount_payable, 0)
						ELSE COALESCE(i.price_after_discount, 0) + COALESCE(i.total_tax, 0)
					END
				WHEN pd.type IN ('Return', 'Collect') THEN pd.amount
				ELSE pd.amount_payable
			END
			FROM invoices i
			WHERE pd.invoice_id = i.id OR pd.type IN ('Return', 'Collect')
		`);
    }
    async down(queryRunner) {
        // Remove transaction_amount and previous_balance from payment_details
        await queryRunner.query(`ALTER TABLE "payment_details" 
			 DROP COLUMN "transaction_amount",
			 DROP COLUMN "previous_balance"`);
        await queryRunner.query(`ALTER TABLE "payment_details" ALTER COLUMN "patient_id" SET NOT NULL`);
        await queryRunner.query(`
			ALTER TABLE "payment_details" DROP COLUMN "showinledger";
			ALTER TABLE "payment_details" DROP COLUMN "showininvoice";
		`);
        await queryRunner.query(`ALTER TABLE "invoices" DROP COLUMN "invoice_amount"`);
    }
}
exports.AddInvoiceAmountToInvoiceTable1737611455140 = AddInvoiceAmountToInvoiceTable1737611455140;
//# sourceMappingURL=1737611455140-AddInvoiceAmountToInvoiceTable.js.map