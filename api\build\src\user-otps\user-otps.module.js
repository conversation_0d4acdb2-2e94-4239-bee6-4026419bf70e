"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserOtpModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const user_otp_entity_1 = require("./entities/user-otp.entity");
const user_otps_service_1 = require("./user-otps.service");
const user_otps_controller_1 = require("./user-otps.controller");
const users_module_1 = require("../users/users.module");
const role_module_1 = require("../roles/role.module");
const ses_module_1 = require("../utils/aws/ses/ses.module");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
let UserOtpModule = class UserOtpModule {
};
exports.UserOtpModule = UserOtpModule;
exports.UserOtpModule = UserOtpModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([user_otp_entity_1.UserOtp]),
            users_module_1.UsersModule,
            role_module_1.RoleModule,
            ses_module_1.SESModule,
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: configService.get('JWT_SECRET'),
                    signOptions: { expiresIn: '1d' }
                }),
                inject: [config_1.ConfigService]
            }),
            config_1.ConfigModule
        ],
        controllers: [user_otps_controller_1.UserOtpController],
        providers: [user_otps_service_1.UserOtpsService],
        exports: [user_otps_service_1.UserOtpsService]
    })
], UserOtpModule);
//# sourceMappingURL=user-otps.module.js.map