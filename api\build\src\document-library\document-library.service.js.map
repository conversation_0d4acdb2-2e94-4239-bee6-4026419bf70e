{"version": 3, "file": "document-library.service.js", "sourceRoot": "", "sources": ["../../../src/document-library/document-library.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAIwB;AAGxB,mFAAuE;AACvE,6CAAmD;AACnD,gFAAqE;AACrE,qCAA+C;AAC/C,sDAAmD;AACnD,mCAAgC;AAChC,mEAAwE;AACxE,iCAAkC;AAClC,2DAAuD;AAGhD,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IAClC,YACkB,MAAqB,EAErB,yBAAsD,EAC/D,SAAoB;QAHX,WAAM,GAAN,MAAM,CAAe;QAErB,8BAAyB,GAAzB,yBAAyB,CAA6B;QAC/D,cAAS,GAAT,SAAS,CAAW;IAC1B,CAAC;IAEJ,KAAK,CAAC,MAAM,CACX,wBAAkD,EAClD,OAAe;;QAEf,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,EAAE;YACxD,GAAG,EAAE,wBAAwB;SAC7B,CAAC,CAAC;QACH,MAAM,eAAe,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC;YAC7D,GAAG,wBAAwB;YAC3B,OAAO,EAAE,OAAO;SAChB,CAAC,CAAC;QACH,IAAI,eAAe,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;YAC/C,MAAM,gBAAgB,GACrB,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC;gBAC5C,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,EAAE,EAAE;gBAClC,SAAS,EAAE,CAAC,QAAQ,EAAE,cAAc,CAAC;aACrC,CAAC,CAAC;YACJ,MAAM,OAAO,GAAG,oBAAoB,IAAA,eAAM,GAAE,EAAE,CAAC;YAC/C,MAAM,YAAY,GAAG,IAAA,yCAAuB,EAAC;gBAC5C,QAAQ,EACP,CAAA,MAAA,wBAAwB,aAAxB,wBAAwB,uBAAxB,wBAAwB,CAAE,YAAY,0CAAE,QAAQ,KAAI,EAAE;gBACvD,aAAa,EAAE,GAAG,MAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,MAAM,0CAAE,YAAY,IAAI,MAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,MAAM,0CAAE,YAAY,KAAK,MAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,MAAM,0CAAE,IAAI,IAAI,MAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,MAAM,0CAAE,cAAc,IAAI,MAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,MAAM,0CAAE,KAAK,IAAI,MAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,MAAM,0CAAE,OAAO,EAAE;gBAC3P,WAAW,EAAE,CAAA,MAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,MAAM,0CAAE,KAAK,KAAI,EAAE;gBAClD,UAAU,EAAE,CAAA,MAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,MAAM,0CAAE,IAAI,KAAI,EAAE;gBAChD,WAAW,EACV,CAAA,MAAA,MAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,MAAM,0CAAE,YAAY,CAAC,CAAC,CAAC,0CAAE,MAAM,KAAI,EAAE;gBACxD,aAAa,EAAE,CAAA,MAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,MAAM,0CAAE,OAAO,KAAI,EAAE;gBACtD,gBAAgB,EAAE,EAAE;gBACpB,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,CAAA,MAAA,wBAAwB,aAAxB,wBAAwB,uBAAxB,wBAAwB,CAAE,YAAY,0CAAE,KAAK,KAAI,EAAE;gBAC1D,OAAO,EAAE,GAAG;aACZ,CAAC,CAAC;YACH,MAAM,SAAS,GAAW,MAAM,IAAA,yBAAW,EAAC,YAAY,CAAC,CAAC;YAC1D,MAAM,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACvD,eAAe,CAAC,OAAO,GAAG,OAAO,CAAC;QACnC,CAAC;QACD,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IACnE,CAAC;IAED,KAAK,CAAC,OAAO,CACZ,QAAgB,EAChB,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,MAAe,EACf,UAAkB,MAAM;QAExB,IAAI,CAAC;YACJ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,EAAE;gBACrC,IAAI;gBACJ,KAAK;gBACL,MAAM;gBACN,OAAO;aACP,CAAC,CAAC;YAEH,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB;iBACjD,kBAAkB,CAAC,UAAU,CAAC;iBAC9B,KAAK,CAAC,4BAA4B,CAAC,CAAC,4CAA4C;iBAChF,QAAQ,CAAC,+BAA+B,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;YAE1D,MAAM,aAAa,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,EAAE,CAAC;YACrC,IAAI,aAAa,EAAE,CAAC;gBACnB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,sBAAsB,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;gBAC3D,YAAY,CAAC,QAAQ,CACpB,IAAI,kBAAQ,CAAC,EAAE,CAAC,EAAE;oBACjB,EAAE,CAAC,KAAK,CAAC,qCAAqC,EAAE;wBAC/C,MAAM,EAAE,IAAI,aAAa,GAAG;qBAC5B,CAAC,CAAC,OAAO,CAAC,iCAAiC,EAAE;wBAC7C,MAAM,EAAE,IAAI,aAAa,GAAG;qBAC5B,CAAC,CAAC;gBACJ,CAAC,CAAC,CACF,CAAC;YACH,CAAC;YAED,YAAY;iBACV,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;iBACxB,IAAI,CAAC,KAAK,CAAC;iBACX,OAAO,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;YAExC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,MAAM,YAAY,CAAC,eAAe,EAAE,CAAC;YAEhE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gCAAgC,EAAE;gBACjD,KAAK,EAAE,SAAS,CAAC,MAAM;gBACvB,IAAI;gBACJ,KAAK;aACL,CAAC,CAAC;YAEH,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACzD,MAAM,IAAI,qCAA4B,CAAC,2BAA2B,CAAC,CAAC;QACrE,CAAC;IACF,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAU;QACvB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8CAA8C,EAAE,EAAE,CAAC,CAAC;QACpE,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC;YACpE,KAAK,EAAE,EAAE,EAAE,EAAE;SACb,CAAC,CAAC;QACH,IAAI,CAAC,eAAe,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAChB,mCAAmC,EAAE,YAAY,CACjD,CAAC;YACF,MAAM,IAAI,0BAAiB,CAC1B,mCAAmC,EAAE,YAAY,CACjD,CAAC;QACH,CAAC;QACD,OAAO,eAAe,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,MAAM,CACX,EAAU,EACV,wBAAkD;;QAElD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4CAA4C,EAAE,EAAE,EAAE;YACjE,GAAG,EAAE,wBAAwB;SAC7B,CAAC,CAAC;QACH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC;YACpE,KAAK,EAAE,EAAE,EAAE,EAAE;YACb,SAAS,EAAE,CAAC,QAAQ,EAAE,cAAc,CAAC;SACrC,CAAC,CAAC;QACH,IAAI,eAAe,IAAI,wBAAwB,CAAC,OAAO,EAAE,CAAC;YACzD,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QAC1D,CAAC;QACD,IACC,eAAe;YACf,CAAC,CAAA,MAAA,wBAAwB,aAAxB,wBAAwB,uBAAxB,wBAAwB,CAAE,YAAY,0CAAE,KAAK;iBAC7C,MAAA,wBAAwB,aAAxB,wBAAwB,uBAAxB,wBAAwB,CAAE,YAAY,0CAAE,QAAQ,CAAA,CAAC,EACjD,CAAC;YACF,MAAM,UAAU,GAAG,oBAAoB,IAAA,eAAM,GAAE,EAAE,CAAC;YAClD,MAAM,YAAY,GAAG,IAAA,yCAAuB,EAAC;gBAC5C,QAAQ,EACP,CAAA,MAAA,wBAAwB,aAAxB,wBAAwB,uBAAxB,wBAAwB,CAAE,YAAY,0CAAE,QAAQ;qBAChD,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,YAAY,0CAAE,QAAQ,CAAA;oBACvC,EAAE;gBACH,aAAa,EAAE,GAAG,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,0CAAE,YAAY,IAAI,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,0CAAE,YAAY,KAAK,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,0CAAE,IAAI,IAAI,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,0CAAE,cAAc,IAAI,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,0CAAE,KAAK,IAAI,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,0CAAE,OAAO,EAAE;gBACrP,WAAW,EAAE,CAAA,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,0CAAE,KAAK,KAAI,EAAE;gBACjD,UAAU,EAAE,CAAA,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,0CAAE,IAAI,KAAI,EAAE;gBAC/C,WAAW,EACV,CAAA,MAAA,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,0CAAE,YAAY,CAAC,CAAC,CAAC,0CAAE,MAAM,KAAI,EAAE;gBACvD,aAAa,EAAE,CAAA,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,0CAAE,OAAO,KAAI,EAAE;gBACrD,gBAAgB,EAAE,EAAE;gBACpB,OAAO,EAAE,MAAM,CAAC,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,SAAS,CAAC,CAAC,MAAM,CACjD,YAAY,CACZ;gBACD,KAAK,EACJ,CAAA,MAAA,wBAAwB,aAAxB,wBAAwB,uBAAxB,wBAAwB,CAAE,YAAY,0CAAE,KAAK;qBAC7C,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,YAAY,0CAAE,KAAK,CAAA;oBACpC,EAAE;gBACH,OAAO,EAAE,GAAG;aACZ,CAAC,CAAC;YACH,MAAM,SAAS,GAAW,MAAM,IAAA,yBAAW,EAAC,YAAY,CAAC,CAAC;YAC1D,MAAM,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAC1D,wBAAwB,CAAC,OAAO,GAAG,UAAU,CAAC;YAC9C,IAAI,eAAe,CAAC,OAAO,EAAE,CAAC;gBAC7B,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;YAC1D,CAAC;QACF,CAAC;QAED,MAAM,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAC1C,EAAE,EACF,wBAAwB,CACxB,CAAC;QACF,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC;YACpE,KAAK,EAAE,EAAE,EAAE,EAAE;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,EAAE,CAAC;YACtB,MAAM,IAAI,qCAA4B,CACrC,kEAAkE,EAAE,EAAE,CACtE,CAAC;QACH,CAAC;QAED,OAAO,eAAe,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,MAAM,CAAC,EAAU;QACtB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iDAAiD,EAAE,EAAE,CAAC,CAAC;QAEvE,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC;YACpE,KAAK,EAAE,EAAE,EAAE,EAAE;SACb,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,EAAE,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAChB,mCAAmC,EAAE,YAAY,CACjD,CAAC;YACF,MAAM,IAAI,0BAAiB,CAC1B,mCAAmC,EAAE,YAAY,CACjD,CAAC;QACH,CAAC;QAED,eAAe,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAEvC,MAAM,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAE3D,IAAI,CAAC,MAAM,CAAC,GAAG,CACd,mCAAmC,EAAE,4BAA4B,CACjE,CAAC;IACH,CAAC;CACD,CAAA;AA/MY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;IAIV,WAAA,IAAA,0BAAgB,EAAC,yCAAe,CAAC,CAAA;qCADT,sCAAa;QAEM,oBAAU;QACnC,sBAAS;GALjB,sBAAsB,CA+MlC"}