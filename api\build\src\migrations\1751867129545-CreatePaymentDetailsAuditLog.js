"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePaymentDetailsAuditLog1751867129545 = void 0;
class CreatePaymentDetailsAuditLog1751867129545 {
    constructor() {
        this.name = 'CreatePaymentDetailsAuditLog1751867129545';
    }
    async up(queryRunner) {
        // Create enum for payment details audit log operation type
        await queryRunner.query(`
			CREATE TYPE "public"."payment_details_audit_log_operation_type_enum" AS ENUM('UPDATE', 'DELETE')
		`);
        // Create payment_details_audit_log table
        await queryRunner.query(`
			CREATE TABLE "payment_details_audit_log" (
				"id" uuid NOT NULL DEFAULT uuid_generate_v4(),
				"payment_detail_id" uuid NOT NULL,
				"user_id" uuid,
				"operation_type" "public"."payment_details_audit_log_operation_type_enum" NOT NULL,
				"changes" jsonb,
				"changed_fields_summary" jsonb,
				"timestamp" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
				CONSTRAINT "PK_payment_details_audit_log" PRIMARY KEY ("id")
			)
		`);
        // Add foreign key constraints for audit log
        await queryRunner.query(`
			ALTER TABLE "payment_details_audit_log" ADD CONSTRAINT "FK_payment_details_audit_log_payment_detail_id" 
			FOREIGN KEY ("payment_detail_id") REFERENCES "payment_details"("id") ON DELETE SET NULL ON UPDATE NO ACTION
		`);
        await queryRunner.query(`
			ALTER TABLE "payment_details_audit_log" ADD CONSTRAINT "FK_payment_details_audit_log_user_id" 
			FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE NO ACTION
		`);
        // Add indexes for better performance
        await queryRunner.query(`
			CREATE INDEX "IDX_payment_details_audit_log_payment_detail_id" ON "payment_details_audit_log" ("payment_detail_id")
		`);
        await queryRunner.query(`
			CREATE INDEX "IDX_payment_details_audit_log_user_id" ON "payment_details_audit_log" ("user_id")
		`);
        await queryRunner.query(`
			CREATE INDEX "IDX_payment_details_audit_log_timestamp" ON "payment_details_audit_log" ("timestamp")
		`);
    }
    async down(queryRunner) {
        // Drop indexes
        await queryRunner.query(`DROP INDEX "public"."IDX_payment_details_audit_log_timestamp"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_payment_details_audit_log_user_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_payment_details_audit_log_payment_detail_id"`);
        // Drop foreign key constraints
        await queryRunner.query(`ALTER TABLE "payment_details_audit_log" DROP CONSTRAINT "FK_payment_details_audit_log_user_id"`);
        await queryRunner.query(`ALTER TABLE "payment_details_audit_log" DROP CONSTRAINT "FK_payment_details_audit_log_payment_detail_id"`);
        // Drop audit log table
        await queryRunner.query(`DROP TABLE "payment_details_audit_log"`);
        // Drop enum
        await queryRunner.query(`DROP TYPE "public"."payment_details_audit_log_operation_type_enum"`);
    }
}
exports.CreatePaymentDetailsAuditLog1751867129545 = CreatePaymentDetailsAuditLog1751867129545;
//# sourceMappingURL=1751867129545-CreatePaymentDetailsAuditLog.js.map