import { EntityManager } from 'typeorm';
import { PetTransferDto } from './dto/pet-transfer.dto';
import { PetTransferQueryManagerService } from './services/query-manager.service';
import { InventoryMappingService } from './services/inventory-mapping.service';
export declare class PetTransferService {
    private readonly entityManager;
    private readonly petTransferQueryManager;
    private readonly inventoryMappingService;
    constructor(entityManager: EntityManager, petTransferQueryManager: PetTransferQueryManagerService, inventoryMappingService: InventoryMappingService);
    transferPet(petTransferDto: PetTransferDto): Promise<void>;
    private handleInvoiceMapping;
    private handleAppointmentAssessmentMapping;
    private handleCartItemMapping;
    private handleOwnerMigration;
    private handleDiagnosticTemplateMapping;
}
