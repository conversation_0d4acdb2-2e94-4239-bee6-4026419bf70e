"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddReminderTrackingColumns1734694484160 = void 0;
const typeorm_1 = require("typeorm");
class AddReminderTrackingColumns1734694484160 {
    async up(queryRunner) {
        await queryRunner.addColumns('patient_reminders', [
            new typeorm_1.TableColumn({
                name: 'total_occurrences',
                type: 'integer',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'completed_occurrences',
                type: 'integer',
                isNullable: true,
                default: 0
            }),
            new typeorm_1.TableColumn({
                name: 'quantity_per_occurrence',
                type: 'integer',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'total_quantity',
                type: 'integer',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'remaining_quantity',
                type: 'integer',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'completion_series_id',
                type: 'uuid',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'completed_appointment_id',
                type: 'uuid',
                isNullable: true
            })
        ]);
        // Add foreign key constraint for completed_appointment_id
        await queryRunner.query(`
            ALTER TABLE "patient_reminders"
            ADD CONSTRAINT "FK_patient_reminders_completed_appointment"
            FOREIGN KEY ("completed_appointment_id")
            REFERENCES "appointments"("id")
            ON DELETE SET NULL
        `);
        // Add index for completion_series_id for faster lookups
        await queryRunner.query(`
            CREATE INDEX "IDX_reminder_completion_series"
            ON "patient_reminders" ("completion_series_id")
        `);
    }
    async down(queryRunner) {
        // Drop foreign key constraint first
        await queryRunner.query(`
            ALTER TABLE "patient_reminders"
            DROP CONSTRAINT "FK_patient_reminders_completed_appointment"
        `);
        // Drop index
        await queryRunner.query(`
            DROP INDEX "IDX_reminder_completion_series"
        `);
        // Drop columns
        await queryRunner.dropColumns('patient_reminders', [
            'total_occurrences',
            'completed_occurrences',
            'quantity_per_occurrence',
            'total_quantity',
            'remaining_quantity',
            'completion_series_id',
            'completed_appointment_id'
        ]);
    }
}
exports.AddReminderTrackingColumns1734694484160 = AddReminderTrackingColumns1734694484160;
//# sourceMappingURL=1734694484160-UpdatePatientReminders.js.map