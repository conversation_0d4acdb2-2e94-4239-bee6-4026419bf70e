"use strict";
// src/ai/ai.controller.ts
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const role_enum_1 = require("../../../roles/role.enum");
const roles_decorator_1 = require("../../../roles/roles.decorator");
const winston_logger_service_1 = require("../../logger/winston-logger.service");
const ai_service_1 = require("./ai.service");
let AIController = class AIController {
    constructor(aiService, logger) {
        this.aiService = aiService;
        this.logger = logger;
    }
    async generateSOAPNotes(data) {
        try {
            this.logger.log('Generating SOAP notes', { notes: data.clinicalNotes });
            const result = await this.aiService.generateSOAPNotes(data.clinicalNotes);
            this.logger.log('SOAP notes generated successfully');
            return result;
        }
        catch (error) {
            this.logger.error('Error generating SOAP notes', { error });
            throw new common_1.HttpException('Failed to generate SOAP notes', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async regenerateSOAPNotes(data) {
        try {
            this.logger.log('Regenerating SOAP notes', {
                notes: data.clinicalNotes,
                hasPrevious: !!data.previousNotes
            });
            const result = await this.aiService.regenerateSOAPNotes(data.clinicalNotes, data.previousNotes);
            this.logger.log('SOAP notes regenerated successfully');
            return result;
        }
        catch (error) {
            this.logger.error('Error regenerating SOAP notes', { error });
            throw new common_1.HttpException('Failed to regenerate SOAP notes', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.AIController = AIController;
__decorate([
    (0, common_1.Post)('soap')
    // @Roles(Role.DOCTOR, Role.VET_TECHNICIAN)
    ,
    (0, swagger_1.ApiOperation)({ summary: 'Generate SOAP notes using AI' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'SOAP notes generated successfully' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AIController.prototype, "generateSOAPNotes", null);
__decorate([
    (0, common_1.Post)('soap/regenerate'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.DOCTOR, role_enum_1.Role.VET_TECHNICIAN),
    (0, swagger_1.ApiOperation)({ summary: 'Regenerate SOAP notes with modifications' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'SOAP notes regenerated successfully' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AIController.prototype, "regenerateSOAPNotes", null);
exports.AIController = AIController = __decorate([
    (0, swagger_1.ApiTags)('AI'),
    (0, common_1.Controller)('ai')
    // @UseGuards(JwtAuthGuard, RolesGuard)
    ,
    __metadata("design:paramtypes", [ai_service_1.AIService,
        winston_logger_service_1.WinstonLogger])
], AIController);
//# sourceMappingURL=ai.controller.js.map