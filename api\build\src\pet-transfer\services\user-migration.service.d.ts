import { Repository } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { ClinicUser } from '../../clinics/entities/clinic-user.entity';
import { PatientOwner } from '../../patients/entities/patient-owner.entity';
import { OwnerBrand } from '../../owners/entities/owner-brand.entity';
export declare class UserMigrationService {
    private readonly userRepository;
    private readonly clinicUserRepository;
    private readonly patientOwnerRepository;
    private readonly ownerBrandRepository;
    constructor(userRepository: Repository<User>, clinicUserRepository: Repository<ClinicUser>, patientOwnerRepository: Repository<PatientOwner>, ownerBrandRepository: Repository<OwnerBrand>);
    migrateUsers(patientId: string, sourceClinicId: string, destinationClinicId: string, destinationBrandId: string): Promise<Map<string, string>>;
}
