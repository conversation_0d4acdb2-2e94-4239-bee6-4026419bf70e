"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddStartAfterDaysToGlobalReminderRules1736839258184 = void 0;
const typeorm_1 = require("typeorm");
class AddStartAfterDaysToGlobalReminderRules1736839258184 {
    async up(queryRunner) {
        // Adding the start_after_days column to the global_reminder_rules table
        await queryRunner.addColumn('global_reminder_rules', new typeorm_1.TableColumn({
            name: 'start_after_days',
            type: 'int',
            isNullable: true // This allows the column to have null values
        }));
    }
    async down(queryRunner) {
        // Removing the start_after_days column from the global_reminder_rules table
        await queryRunner.dropColumn('global_reminder_rules', 'start_after_days');
    }
}
exports.AddStartAfterDaysToGlobalReminderRules1736839258184 = AddStartAfterDaysToGlobalReminderRules1736839258184;
//# sourceMappingURL=1736839258184-AddStartAfterDaysToGlobalReminderRules.js.map