"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddColOwnersTable1730180263938 = void 0;
const typeorm_1 = require("typeorm");
class AddColOwnersTable1730180263938 {
    async up(queryRunner) {
        await queryRunner.addColumn('owners', new typeorm_1.TableColumn({
            name: 'dummy_data',
            type: 'jsonb',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('owners', 'dummy_data');
    }
}
exports.AddColOwnersTable1730180263938 = AddColOwnersTable1730180263938;
//# sourceMappingURL=1730180263938-AddColOwnersTable.js.map