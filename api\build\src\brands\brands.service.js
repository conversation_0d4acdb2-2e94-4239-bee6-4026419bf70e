"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BrandService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const brand_entity_1 = require("./entities/brand.entity");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const brand_with_settings_dto_1 = require("./dto/brand-with-settings.dto");
let BrandService = class BrandService {
    constructor(brandRepository, logger) {
        this.brandRepository = brandRepository;
        this.logger = logger;
    }
    async createBrand(createBrandDto) {
        try {
            this.logger.log('Creating Brand', { dto: createBrandDto });
            const existingBrand = await this.brandRepository.findOne({
                where: { name: createBrandDto.name }
            });
            if (existingBrand) {
                this.logger.error('Brand already exists', {
                    email: createBrandDto.name
                });
                throw new common_1.ConflictException('Brand with this name already exists');
            }
            const brand = this.brandRepository.create(createBrandDto);
            return await this.brandRepository.save(brand);
        }
        catch (error) {
            if (error instanceof common_1.ConflictException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('An unexpected error occurred while creating the user');
        }
    }
    async getAllBrands(page = 1, limit = 10, search = '', orderBy = 'DESC') {
        try {
            this.logger.log('Fetching all Brands', {
                page,
                limit,
                search,
                orderBy
            });
            const whereCondition = search
                ? { name: (0, typeorm_2.Like)(`%${search}%`) }
                : {};
            const [brands, total] = await this.brandRepository.findAndCount({
                where: whereCondition,
                skip: (page - 1) * limit,
                take: limit,
                order: orderBy === 'ASC' ? { createdAt: 'ASC' } : { createdAt: 'DESC' }
            });
            this.logger.log('Fetched all Brands:', {
                brandsCount: brands.length,
                total,
                page,
                limit
            });
            return { brands, total };
        }
        catch (error) {
            if (error instanceof common_1.ConflictException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('An unexpected error occurred while Fetching the brands');
        }
    }
    // Backward compatibility method for non-paginated calls
    async getAllBrandsSimple() {
        try {
            this.logger.log('Fetching all Brands (simple)');
            const brands = await this.brandRepository.find({
                order: { createdAt: 'DESC' }
            });
            this.logger.log('Fetched all Brands (simple):', { brands });
            return brands;
        }
        catch (error) {
            if (error instanceof common_1.ConflictException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('An unexpected error occurred while Fetching the brands');
        }
    }
    async getBrandById(id) {
        try {
            this.logger.log('Fetching a Brand');
            const brand = await this.brandRepository.findOne({ where: { id } });
            this.logger.log('Fetched a Brand:', { brand });
            return brand;
        }
        catch (error) {
            if (error instanceof common_1.ConflictException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('An unexpected error occurred while Fetching the brand');
        }
    }
    async getBrandBySlug(slug) {
        var _a, _b;
        try {
            this.logger.log('Fetching a Brand');
            const brand = await this.brandRepository.findOne({
                where: { slug },
                relations: ['clinics']
            });
            if (!brand) {
                return null;
            }
            const hasClientBookingEnabled = ((_a = brand.clinics) === null || _a === void 0 ? void 0 : _a.some(clinic => {
                var _a, _b;
                return ((_b = (_a = clinic.customRule) === null || _a === void 0 ? void 0 : _a.clientBookingSettings) === null || _b === void 0 ? void 0 : _b.isEnabled) ===
                    true;
            })) || false;
            const brandDto = new brand_with_settings_dto_1.BrandWithSettingsDto();
            // Copy only the basic brand properties (excluding clinics)
            brandDto.id = brand.id;
            brandDto.name = brand.name;
            brandDto.slug = brand.slug;
            brandDto.createdAt = brand.createdAt;
            brandDto.updatedAt = brand.updatedAt;
            brandDto.createdBy = brand.createdBy;
            brandDto.updatedBy = brand.updatedBy;
            // Add the calculated client booking flag
            brandDto.hasClientBookingEnabled = hasClientBookingEnabled;
            // Include clinics with their contact numbers
            if (brand.clinics && brand.clinics.length > 0) {
                // Include basic clinic info including phone numbers
                brandDto.clinics = brand.clinics.map(clinic => ({
                    id: clinic.id,
                    name: clinic.name,
                    phoneNumbers: clinic.phoneNumbers || []
                }));
            }
            this.logger.log('Fetched a Brand with settings:', {
                brandId: brand.id,
                hasClientBookingEnabled,
                clinicsCount: ((_b = brand.clinics) === null || _b === void 0 ? void 0 : _b.length) || 0
            });
            return brandDto;
        }
        catch (error) {
            if (error instanceof common_1.ConflictException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('An unexpected error occurred while Fetching the brand');
        }
    }
};
exports.BrandService = BrandService;
exports.BrandService = BrandService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(brand_entity_1.Brand)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        winston_logger_service_1.WinstonLogger])
], BrandService);
//# sourceMappingURL=brands.service.js.map