interface DummyData {
    patientId: string;
    isDataImported: boolean;
    isBalanceUpdated: boolean;
}
interface OwnerDummyData {
    clientId: string;
    isDataImported: boolean;
}
export declare class CreateOwnerDto {
    firstName: string;
    lastName: string;
    phoneNumber: string;
    address: string;
    email: string;
    dummyData?: OwnerDummyData;
    countryCode: string;
    brandId: string;
    ownerBalance: number;
    openingBalance: number;
}
export declare class CreatePatientDto {
    name: string;
    species: string;
    breed: string;
    gender: 'Male' | 'Female' | 'Unknown';
    weight: string;
    clinicId: string;
    balance: number;
    dummyData?: DummyData;
}
export declare class PatientLookupDto {
    clinicId: string;
    dummyData: {
        patientId: string;
        isDataImported: boolean;
    };
}
export declare class CreatePatientOwnerDto {
    patientId: string;
    ownerId: string;
    isPrimary: boolean;
}
export declare class CreateAppointmentDto {
    clinicId: string;
    doctorIds: string[];
    providerIds?: string[];
    patientId: string;
    roomId?: string | null;
    date: string;
    startTime: string;
    endTime: string;
    reason: string;
    type: string;
    isBlocked: boolean;
    weight: number | null;
    status?: string;
}
export declare class CreateAppointmentDetailsDto {
    appointmentId: string;
    details: any;
}
export declare class InvoiceDataDto {
    date: string;
    patientId: string;
    pdfUrl?: string;
    s3Key: string;
    clinicId: string;
    brandId: string;
    metadata?: Record<string, any>;
}
export declare class DiagnosticsDataDto {
    patientId: string;
    date: string;
    pdfUrl: string;
    fileName: string;
    labReportTypeId: string;
}
export declare class MigrateAppointmentDto {
    clinicId?: string;
    doctorIds: string[];
    providerIds?: string[];
    patientId: string;
    roomId?: string | null;
    date: string;
    startTime: string;
    endTime: string;
    reason?: string;
    type: string;
    status: string;
    isBlocked: boolean;
    weight?: number | null;
}
export declare class UpdateOwnerOpeningBalanceDto {
    clientId: string;
    openingBalance: number;
}
export declare class BulkUpdateOpeningBalanceDto {
    owners: UpdateOwnerOpeningBalanceDto[];
}
export {};
