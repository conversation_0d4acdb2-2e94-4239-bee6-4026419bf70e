interface DiagnosticReportTableData {
    clinicName: string;
    clinicAddress: string;
    clinicPhone: string;
    clinicEmail: string;
    clinicWebsite: string;
    clinicStreet: string;
    clinicCity: string;
    clinicLogoUrl: string;
    title: string;
    assessmentText: string;
    petName?: string;
    petBreed?: string;
    species?: string;
    diagnosticName?: string;
    diagnosticNumber?: string;
    diagnosticDate?: string;
    reproductiveStatus?: string;
    ownerName?: string;
    ownerEmail?: string;
    ownerPhone?: string;
    templateName?: string;
    tableData?: {
        data: Array<{
            [key: string]: string;
        }>;
        columns: Array<{
            name: string;
            type: string;
        }>;
        lockedColumns: {
            [key: string]: boolean;
        };
    };
}
export declare const generateDiagnosticReportTable: ({ diagnosticName, diagnosticNumber, diagnosticDate, clinicName, clinicAddress, clinicPhone, clinicEmail, clinicWebsite, petName, petBreed, ownerName, ownerEmail, ownerPhone, templateName, tableData, clinicCity, }: DiagnosticReportTableData) => string;
export {};
