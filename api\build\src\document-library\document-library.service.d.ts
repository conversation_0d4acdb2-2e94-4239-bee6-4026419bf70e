import { CreateDocumentLibraryDto } from './dto/create-document-library.dto';
import { UpdateDocumentLibraryDto } from './dto/update-document-library.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { DocumentLibrary } from './entities/document-library.entity';
import { Repository } from 'typeorm';
import { S3Service } from '../utils/aws/s3/s3.service';
export declare class DocumentLibraryService {
    private readonly logger;
    private readonly documentLibraryRepository;
    private s3Service;
    constructor(logger: WinstonLogger, documentLibraryRepository: Repository<DocumentLibrary>, s3Service: S3Service);
    create(createDocumentLibraryDto: CreateDocumentLibraryDto, brandId: string): Promise<DocumentLibrary>;
    findAll(clinicId: string, page?: number, limit?: number, search?: string, orderBy?: string): Promise<{
        documents: any;
        total: number;
    }>;
    findOne(id: string): Promise<DocumentLibrary>;
    update(id: string, updateDocumentLibraryDto: UpdateDocumentLibraryDto): Promise<DocumentLibrary>;
    remove(id: string): Promise<void>;
}
