"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientAvailabilityController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const client_availability_service_1 = require("./client-availability.service");
const client_availability_dto_1 = require("./dto/client-availability.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../roles/roles.decorator");
const role_enum_1 = require("../roles/role.enum");
let ClientAvailabilityController = class ClientAvailabilityController {
    constructor(clientAvailabilityService) {
        this.clientAvailabilityService = clientAvailabilityService;
    }
    async getAvailableDates(doctorIds, query) {
        return this.clientAvailabilityService.getAvailableDatesForDoctor(doctorIds, query.startDate, query.endDate, query.clinicId);
    }
    async getAvailableTimeSlots(doctorIds, date, query) {
        return this.clientAvailabilityService.getAvailableTimeSlotsForDoctor(doctorIds, date, query.clinicId);
    }
};
exports.ClientAvailabilityController = ClientAvailabilityController;
__decorate([
    (0, common_1.Get)('client-availability/dates'),
    (0, swagger_1.ApiOperation)({ summary: 'Get available dates for selected doctor(s)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns available dates for selected doctor(s)',
        type: client_availability_dto_1.AvailableDatesResponseDto
    }),
    (0, swagger_1.ApiQuery)({ name: 'doctorIds', required: true, type: String, description: 'Can be a single doctor ID, "all", or comma-separated IDs' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, type: String }),
    (0, swagger_1.ApiQuery)({ name: 'clinicId', required: false, type: String }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.OWNER, role_enum_1.Role.ADMIN),
    __param(0, (0, common_1.Query)('doctorIds')),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, client_availability_dto_1.AvailableDatesRequestDto]),
    __metadata("design:returntype", Promise)
], ClientAvailabilityController.prototype, "getAvailableDates", null);
__decorate([
    (0, common_1.Get)('client-availability/timeslots/:date'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get available time slots for selected doctor(s) on a specific date'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns available time slots for selected doctor(s) on a specific date',
        type: client_availability_dto_1.AvailableTimeSlotsResponseDto
    }),
    (0, swagger_1.ApiQuery)({ name: 'doctorIds', required: true, type: String, description: 'Can be a single doctor ID, "all", or comma-separated IDs' }),
    (0, swagger_1.ApiQuery)({ name: 'clinicId', required: false, type: String }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.OWNER, role_enum_1.Role.ADMIN),
    __param(0, (0, common_1.Query)('doctorIds')),
    __param(1, (0, common_1.Param)('date')),
    __param(2, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, client_availability_dto_1.TimeSlotRequestDto]),
    __metadata("design:returntype", Promise)
], ClientAvailabilityController.prototype, "getAvailableTimeSlots", null);
exports.ClientAvailabilityController = ClientAvailabilityController = __decorate([
    (0, swagger_1.ApiTags)('Client Availability'),
    (0, common_1.Controller)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [client_availability_service_1.ClientAvailabilityService])
], ClientAvailabilityController);
//# sourceMappingURL=client-availability.controller.js.map