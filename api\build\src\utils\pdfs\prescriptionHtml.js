"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generatePrescription = void 0;
const generatePrescription = ({ prescriptionId, prescriptionDate, clinicName, clinicAddress, clinicPhone, clinicEmail, clinicWebsite, customerName, petName, petDetails, lineItems, dischargeInstructions, vetName, vetLicenseNo, digitalSignature, customerEmail, customerPhone }) => {
    const formattedDischargeInstructions = dischargeInstructions.split('\n').map(line => `${line}<br/>`).join('');
    return `
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prescription</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Qwitcher+Grypen:wght@400;700&display=swap" rel="stylesheet">
   <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            color: #000000;
            background: #F9F7F2;
            font-weight: 300;
            font-size: 12px;
            line-height: 14.52px;
        }
        p {
            margin: 0;
        }
        .fw-400 { font-weight: 400; }
        .fw-500 { font-weight: 500; }
        .text-grey { color: #000000; }
        .text-center { text-align: center; }
        .pb-16 { padding-bottom: 16px; }
        .pt-20 { padding-top: 20px !important; }
        .block { display: block; }
        .vertical_divider { border-left: 0.5px solid #BEBDBD; margin: 0 10px; }
        .invoice-container { max-width: 612px; margin: 0 auto; padding: 56px 34px; }
        .invoice-header h1 { font-size: 42px; font-weight: 100; margin: 0 0 20px; }
        .invoice-header p { font-size: 18px; font-weight: 300; line-height: 24px; }
        .invoice-details { border-top: 0.5px solid #BEBDBD; padding: 25px 0; }
        .invoice-details h5 { font-size: 18px; font-weight: 400; line-height: 24px; margin: 0; color: #000000; }
        .invoice-details h6 { font-size: 12px; font-weight: 400; line-height: 14.52px; margin: 0; color: #000000; }
        .invoice-details p {
            font-size: 10px;
            font-weight: 400;
            line-height: 18px;
            color: #000000;
        }
        .invoice-info { display: flex; justify-content: space-between; gap: 20px; margin-top: 6px; }
        .invoice-info div:first-child { max-width: 170px; }
        .invoice-info div:last-child { max-width: 145px; }
        .invoice-table { border: none; border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        .invoice-table thead { border-bottom: 0.5px solid #BEBDBD; }
        .invoice-table th { color: #000000; padding: 4px 0 6px; font-size: 12px; font-weight: 400; line-height: 14.52px; text-align: left; }
        .invoice-table tbody tr:first-child td { padding-top: 12px; }
        .invoice-table td { color: #000000; padding-bottom: 20px; font-size: 12px; font-weight: 300; line-height: 14.52px; }
        .invoice-table tr:last-child td { padding-bottom: 0; }
        .discharge-instructions .heading {font-size:14px; color:#59645D; font-weight: 500; border-bottom: 0.5px solid #D6D6D6;  margin-top:50px;padding-bottom: 6px;}
        .discharge-instructions p { font-size: 10px; font-weight: 400; line-height: 16px; color:#59645D; }
        .signature { margin: 40px 0 0 auto; max-width: max-content;}
        .signature h6 { font-size: 14px; font-weight: 400; color:#333333; line-height: 16.94px; margin: 10px 0 3px;  margin-top: 20px}
        .signature p { font-size: 10px; font-weight: 400; line-height: 16.94px; color:#333333;}
        .font-Ridenation {
            font-size: 55px !important;
            font-family: "Qwitcher Grypen", cursive;
            font-weight: 700;
            font-style: normal;
            color: #504947
        }
        @media print {
            body { -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important; margin: 0; }
        }
    </style>
</head>

<body>
    <div class="invoice-container">
        <div class="invoice-header pb-16">
            <h1>Prescription</h1>
            <p>
                Prescription ID #${prescriptionId}
                <span class="vertical_divider"></span>
                <span class="text-grey">${prescriptionDate}</span>
            </p>
        </div>

        <div class="invoice-details pt-20">
            <h6>${clinicName}</h6>
            <div class="invoice-info text-grey">
                <div>
                    <p>${clinicAddress}</p>
                </div>
                <div>
                    <p>${clinicPhone}</p>
                    <p>
                        <span class="block">${clinicEmail}</span>
                        <span class="block">${clinicWebsite}</span>
                    </p>
                </div>
            </div>
        </div>

        <div class="patient-details">
            <p class="fw-300"><span class="fw-500">${petName}</span> ${petDetails.trim() ? `| ${petDetails}` : ''}</p>
            <h5>${customerName}</h5>
            <h5>${customerEmail}</h5>
            <h5>${customerPhone}</h5>
        </div>

        ${lineItems.length > 0
        ? `
        <table class="invoice-table">
            <thead>
                <tr>
                    <th>No.</th>
                    <th>Medication</th>
                    <th>Comments</th>
                </tr>
            </thead>
            <tbody>
                ${lineItems
            .map((item, index) => `
                    <tr>
                        <td>${index + 1}.</td>
                        <td>${item.medication}</td>
                        <td>${item.comments}</td>
                    </tr>
                `)
            .join('')}
            </tbody>
        </table>
        `
        : ''}

        <div class="discharge-instructions">
            <p class="heading">Discharge Instructions</p>
            <p>${formattedDischargeInstructions}</p>
        </div>

        <div class="signature">
           <p class='font-Ridenation' style='margin-bottom: 5px;'>${digitalSignature}</p>
            <h6>${vetName}</h6>
            ${vetLicenseNo ? `<p class='text-grey'>License no. ${vetLicenseNo}</p>` : ''}
        </div>
    </div>
</body>

</html>
    `;
};
exports.generatePrescription = generatePrescription;
//# sourceMappingURL=prescriptionHtml.js.map