import { ClientDashboardService } from './client-dashboard.service';
import { ClientDashboardResponseDto } from './dto/client-dashboard-response.dto';
import { ClientAppointmentsResponseDto } from './dto/client-appointments-response.dto';
import { DirectLoginDto } from './dto/direct-login.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
interface RequestWithUser {
    user: {
        brandId?: string;
        sub?: string;
        phoneNumber?: string;
        role?: string;
        globalOwnerId?: string;
    };
}
export declare class ClientDashboardController {
    private readonly clientDashboardService;
    private readonly logger;
    constructor(clientDashboardService: ClientDashboardService, logger: WinstonLogger);
    directLogin(directLoginDto: DirectLoginDto): Promise<{
        token: string;
        owner: any;
    }>;
    getClientDashboard(ownerId: string, req: RequestWithUser): Promise<ClientDashboardResponseDto>;
    getClientAppointments(ownerId: string, req: RequestWithUser, date?: string, status?: string): Promise<ClientAppointmentsResponseDto>;
    getClientClinics(req: RequestWithUser): Promise<any[]>;
    getClientDoctors(req: RequestWithUser, clinicId?: string): Promise<any[]>;
}
export {};
