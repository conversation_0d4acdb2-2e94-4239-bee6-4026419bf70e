"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientEstimate = exports.SignatureStatus = void 0;
const typeorm_1 = require("typeorm");
const patient_entity_1 = require("../../patients/entities/patient.entity");
const user_entity_1 = require("../../users/entities/user.entity");
const clinic_entity_1 = require("../../clinics/entities/clinic.entity");
var SignatureStatus;
(function (SignatureStatus) {
    SignatureStatus["PENDING"] = "pending";
    SignatureStatus["COMPLETED"] = "completed";
})(SignatureStatus || (exports.SignatureStatus = SignatureStatus = {}));
let PatientEstimate = class PatientEstimate {
};
exports.PatientEstimate = PatientEstimate;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PatientEstimate.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', name: 'clinic_id' }),
    __metadata("design:type", String)
], PatientEstimate.prototype, "clinicId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', name: 'patient_id' }),
    __metadata("design:type", String)
], PatientEstimate.prototype, "patientId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', name: 'doctor_id', nullable: true }),
    __metadata("design:type", String)
], PatientEstimate.prototype, "doctorId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'integer', name: 'estimate_total' }),
    __metadata("design:type", Number)
], PatientEstimate.prototype, "estimateTotal", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', name: 'signature_required', default: false }),
    __metadata("design:type", Boolean)
], PatientEstimate.prototype, "signatureRequired", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: SignatureStatus,
        default: SignatureStatus.PENDING,
        name: 'signature_status'
    }),
    __metadata("design:type", String)
], PatientEstimate.prototype, "signatureStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'jsonb', name: 'treatment_plan', nullable: true }),
    __metadata("design:type", Array)
], PatientEstimate.prototype, "treatmentPlan", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'signatured_by', nullable: true }),
    __metadata("design:type", String)
], PatientEstimate.prototype, "signaturedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'file_key', nullable: true }),
    __metadata("design:type", String)
], PatientEstimate.prototype, "fileKey", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', name: 'document_id', nullable: true }),
    __metadata("design:type", String)
], PatientEstimate.prototype, "documentId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => clinic_entity_1.ClinicEntity, clinic => clinic),
    (0, typeorm_1.JoinColumn)({ name: 'clinic_id' }),
    __metadata("design:type", clinic_entity_1.ClinicEntity)
], PatientEstimate.prototype, "clinic", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => patient_entity_1.Patient, patient => patient),
    (0, typeorm_1.JoinColumn)({ name: 'patient_id' }),
    __metadata("design:type", patient_entity_1.Patient)
], PatientEstimate.prototype, "patient", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'doctor_id' }),
    __metadata("design:type", user_entity_1.User)
], PatientEstimate.prototype, "doctor", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], PatientEstimate.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], PatientEstimate.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], PatientEstimate.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], PatientEstimate.prototype, "updatedBy", void 0);
exports.PatientEstimate = PatientEstimate = __decorate([
    (0, typeorm_1.Entity)('patient_estimate')
], PatientEstimate);
//# sourceMappingURL=patient-estimate.entity.js.map