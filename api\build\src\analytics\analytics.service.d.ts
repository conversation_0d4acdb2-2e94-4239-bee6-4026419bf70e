import { Repository } from 'typeorm';
import { InvoiceEntity } from '../invoice/entities/invoice.entity';
import { PaymentDetailsEntity } from '../payment-details/entities/payment-details.entity';
import { Patient } from '../patients/entities/patient.entity';
import { OwnerBrand } from '../owners/entities/owner-brand.entity';
import { DownloadAnalyticsReportDto, GetRevenueChartDataDto, RevenueChartDataPoint, CollectedPaymentsChartDataPoint, GetAppointmentsChartDataDto, AppointmentsChartResponse, GetDoctorSummaryDto, DoctorSummaryResponseDto, GetSummaryDto, SummaryResponseDto } from './dto/analytics.dto';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
export declare class AnalyticsService {
    private invoiceRepository;
    private paymentDetailsRepository;
    private patientRepository;
    private ownerBrandRepository;
    private appointmentRepository;
    private readonly logger;
    constructor(invoiceRepository: Repository<InvoiceEntity>, paymentDetailsRepository: Repository<PaymentDetailsEntity>, patientRepository: Repository<Patient>, ownerBrandRepository: Repository<OwnerBrand>, appointmentRepository: Repository<AppointmentEntity>);
    private getClinicInfo;
    private createMetadataSheet;
    private createDataSheet;
    generateReport(dto: DownloadAnalyticsReportDto): Promise<Buffer>;
    private getProductsBillingData;
    private getServicesBillingData;
    private getVaccinationsBillingData;
    private getMedicationsBillingData;
    private getLabReportsBillingData;
    private getPatientsBillingData;
    private getBadDebtData;
    private getCollectedPaymentsLedgerData;
    private getReturnedPaymentsLedgerData;
    private createPaymentsSummarySheet;
    private getPaymentSummaryStats;
    private getOwnerSummaryData;
    getRevenueChartData(dto: GetRevenueChartDataDto): Promise<RevenueChartDataPoint[]>;
    getCollectedPaymentsChartData(dto: GetRevenueChartDataDto): Promise<CollectedPaymentsChartDataPoint[]>;
    getAppointmentsChartData(dto: GetAppointmentsChartDataDto): Promise<AppointmentsChartResponse>;
    getDoctorSummary(dto: GetDoctorSummaryDto): Promise<DoctorSummaryResponseDto[]>;
    getSummary(dto: GetSummaryDto): Promise<SummaryResponseDto>;
    private getPaymentsByMode;
    /**
     * Optimized method that fetches payment data for all modes in a single query
     * Replaces the need to call getPaymentsByMode multiple times
     */
    private getAllPaymentsByMode;
    private createPaymentModeSheet;
}
