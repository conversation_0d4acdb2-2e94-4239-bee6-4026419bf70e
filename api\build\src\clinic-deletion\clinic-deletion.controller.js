"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicDeletionController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../roles/roles.decorator");
const role_enum_1 = require("../roles/role.enum");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
const clinic_deletion_service_1 = require("./clinic-deletion.service");
const clinic_deletion_dto_1 = require("./dto/clinic-deletion.dto");
let ClinicDeletionController = class ClinicDeletionController {
    constructor(clinicDeletionService, logger) {
        this.clinicDeletionService = clinicDeletionService;
        this.logger = logger;
    }
    async analyzeImpact(dto, req) {
        try {
            // Validate request
            this.validateDeletionRequest(dto);
            // Log the impact analysis request
            this.logger.log('Deletion impact analysis requested', {
                requestedBy: req.user.id,
                userEmail: req.user.email,
                userRole: req.user.role,
                targetType: dto.type,
                clinicId: dto.clinicId,
                brandId: dto.brandId,
                timestamp: new Date().toISOString()
            });
            // Additional security check - users can only analyze deletion for their own brand
            // Skip brand access validation for super admins
            if (req.user.role !== 'super_admin') {
                await this.validateBrandAccess(dto, req.user.brandId);
            }
            const result = await this.clinicDeletionService.analyzeImpact(dto);
            this.logger.log('Deletion impact analysis completed', {
                requestedBy: req.user.id,
                targetType: dto.type,
                targetId: dto.clinicId || dto.brandId,
                totalRecords: result.databaseImpact.totalRecords,
                totalFiles: result.s3Impact.totalFiles
            });
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Error in deletion impact analysis', {
                error: errorMessage,
                requestedBy: req.user.id,
                dto
            });
            throw error;
        }
    }
    async executeDeletion(dto, req) {
        var _a;
        try {
            // Validate request
            this.validateDeletionRequest(dto);
            // Additional validation for execute mode
            if (dto.mode === clinic_deletion_dto_1.DeletionMode.EXECUTE) {
                if (!dto.confirmationPhrase ||
                    dto.confirmationPhrase !== 'BACKUP_CONFIRMED') {
                    throw new common_1.ForbiddenException('Confirmation phrase "BACKUP_CONFIRMED" is required for execute mode');
                }
            }
            // Critical security logging for actual deletions
            this.logger.log('Deletion execution requested', {
                requestedBy: req.user.id,
                userEmail: req.user.email,
                userRole: req.user.role,
                targetType: dto.type,
                clinicId: dto.clinicId,
                brandId: dto.brandId,
                mode: dto.mode,
                skipS3Backup: dto.skipS3Backup,
                timestamp: new Date().toISOString(),
                severity: dto.mode === clinic_deletion_dto_1.DeletionMode.EXECUTE ? 'CRITICAL' : 'INFO'
            });
            // Additional security check
            // Skip brand access validation for super admins
            if (req.user.role !== 'super_admin') {
                await this.validateBrandAccess(dto, req.user.brandId);
            }
            const result = await this.clinicDeletionService.executeDeletion(dto, req.user.id);
            // Log completion
            const logLevel = dto.mode === clinic_deletion_dto_1.DeletionMode.EXECUTE ? 'warn' : 'log';
            this.logger[logLevel]('Deletion execution completed', {
                requestedBy: req.user.id,
                targetType: dto.type,
                targetId: dto.clinicId || dto.brandId,
                mode: dto.mode,
                success: result.success,
                recordsDeleted: result.results.databaseDeletion.recordsDeleted,
                filesDeleted: ((_a = result.results.s3OriginalFileDeletion) === null || _a === void 0 ? void 0 : _a.filesDeleted) || 0
            });
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Error in deletion execution', {
                error: errorMessage,
                requestedBy: req.user.id,
                dto,
                severity: 'CRITICAL'
            });
            throw error;
        }
    }
    /**
     * List backups
     */
    async listBackups(query, req) {
        try {
            this.logger.log('Listing backups', {
                requestedBy: req.user.id,
                query
            });
            const result = await this.clinicDeletionService.listBackups(query);
            this.logger.log('Backup list retrieved', {
                requestedBy: req.user.id,
                totalBackups: result.total,
                returnedBackups: result.backups.length
            });
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Error listing backups', {
                error: errorMessage,
                requestedBy: req.user.id,
                query
            });
            throw error;
        }
    }
    /**
     * Get backup details
     */
    async getBackupDetails(backupId, req) {
        try {
            this.logger.log('Getting backup details', {
                requestedBy: req.user.id,
                backupId
            });
            const result = await this.clinicDeletionService.getBackupDetails(backupId);
            if (!result) {
                throw new common_1.NotFoundException(`Backup with ID ${backupId} not found`);
            }
            this.logger.log('Backup details retrieved', {
                requestedBy: req.user.id,
                backupId,
                targetType: result.targetType,
                targetId: result.targetId
            });
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Error getting backup details', {
                error: errorMessage,
                requestedBy: req.user.id,
                backupId
            });
            throw error;
        }
    }
    /**
     * Delete backup
     */
    async deleteBackup(backupId, req) {
        try {
            this.logger.warn('Deleting backup', {
                requestedBy: req.user.id,
                backupId
            });
            const result = await this.clinicDeletionService.deleteBackup(backupId);
            this.logger.warn('Backup deleted', {
                requestedBy: req.user.id,
                backupId,
                success: result.success
            });
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Error deleting backup', {
                error: errorMessage,
                requestedBy: req.user.id,
                backupId
            });
            throw error;
        }
    }
    /**
     * Analyze restore impact
     */
    async analyzeRestoreImpact(dto, req) {
        try {
            this.logger.log('Analyzing restore impact', {
                requestedBy: req.user.id,
                backupId: dto.backupId,
                mode: dto.mode
            });
            // Validate that this is a dry run request (impact analysis)
            if (dto.mode !== clinic_deletion_dto_1.RestoreMode.DRY_RUN) {
                throw new common_1.BadRequestException('This endpoint is for impact analysis only. Use mode: DRY_RUN');
            }
            // Delegate to service method
            const result = await this.clinicDeletionService.analyzeRestoreImpact(dto);
            this.logger.log('Restore impact analysis completed', {
                requestedBy: req.user.id,
                backupId: dto.backupId,
                databaseConflicts: result.conflicts.databaseConflicts.length,
                fileConflicts: result.conflicts.fileConflicts.length,
                hasConflicts: result.conflicts.databaseConflicts.length > 0 ||
                    result.conflicts.fileConflicts.length > 0
            });
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Error analyzing restore impact', {
                error: errorMessage,
                requestedBy: req.user.id,
                backupId: dto.backupId
            });
            throw error;
        }
    }
    /**
     * Execute restore
     */
    async executeRestore(dto, req) {
        var _a;
        try {
            this.logger.log('Executing restore', {
                requestedBy: req.user.id,
                backupId: dto.backupId,
                mode: dto.mode,
                conflictResolution: dto.conflictResolution
            });
            // Validate that this is an execute request
            if (dto.mode !== clinic_deletion_dto_1.RestoreMode.EXECUTE) {
                throw new common_1.BadRequestException('This endpoint is for restore execution only. Use mode: EXECUTE');
            }
            // Validate confirmation phrase
            if (dto.confirmationPhrase !== 'RESTORE_CONFIRMED') {
                throw new common_1.BadRequestException('Invalid confirmation phrase. Use: RESTORE_CONFIRMED');
            }
            // Execute the restore
            const result = await this.clinicDeletionService.executeRestore(dto, req.user.id);
            this.logger.log('Restore executed successfully', {
                requestedBy: req.user.id,
                backupId: dto.backupId,
                databaseRecordsRestored: result.results.databaseRestore.recordsRestored,
                filesRestored: ((_a = result.results.fileRestore) === null || _a === void 0 ? void 0 : _a.filesRestored) || 0,
                conflictsResolved: result.conflicts.resolved
            });
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Error executing restore', {
                error: errorMessage,
                requestedBy: req.user.id,
                backupId: dto.backupId
            });
            throw error;
        }
    }
    /**
     * Validates the deletion request parameters
     */
    validateDeletionRequest(dto) {
        if (dto.type === clinic_deletion_dto_1.DeletionType.CLINIC && !dto.clinicId) {
            throw new common_1.BadRequestException('clinicId is required when type is clinic');
        }
        if (dto.type === clinic_deletion_dto_1.DeletionType.BRAND && !dto.brandId) {
            throw new common_1.BadRequestException('brandId is required when type is brand');
        }
        if (dto.clinicId && dto.brandId) {
            throw new common_1.BadRequestException('Cannot specify both clinicId and brandId');
        }
    }
    /**
     * Validates that the user has access to the brand they're trying to delete
     */
    async validateBrandAccess(dto, userBrandId) {
        // For clinic deletion, verify the clinic belongs to user's brand
        if (dto.type === clinic_deletion_dto_1.DeletionType.CLINIC && dto.clinicId) {
            const hasAccess = await this.clinicDeletionService.validateClinicAccess(dto.clinicId, userBrandId);
            if (!hasAccess) {
                throw new common_1.ForbiddenException('You do not have permission to delete this clinic');
            }
        }
        // For brand deletion, verify it's the user's brand
        if (dto.type === clinic_deletion_dto_1.DeletionType.BRAND && dto.brandId) {
            if (dto.brandId !== userBrandId) {
                throw new common_1.ForbiddenException('You can only delete your own brand');
            }
        }
    }
};
exports.ClinicDeletionController = ClinicDeletionController;
__decorate([
    (0, common_1.Post)('impact-analysis'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN) // Only super admin can view deletion impact
    ,
    (0, common_1.UsePipes)(common_1.ValidationPipe),
    (0, swagger_1.ApiOperation)({
        summary: 'Analyze deletion impact',
        description: 'Get detailed analysis of what would be deleted (records, files, etc.)'
    }),
    (0, swagger_1.ApiBody)({ type: clinic_deletion_dto_1.DeletionImpactRequestDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Deletion impact analysis completed successfully',
        schema: {
            example: {
                targetInfo: {
                    type: 'clinic',
                    id: 'clinic-456',
                    name: 'Downtown Veterinary Clinic',
                    brandId: 'brand-123',
                    brandName: 'VetCare Network'
                },
                databaseImpact: {
                    tables: [
                        {
                            tableName: 'patients',
                            recordCount: 1250,
                            description: 'Patient records'
                        },
                        {
                            tableName: 'appointments',
                            recordCount: 3420,
                            description: 'Appointment records'
                        },
                        {
                            tableName: 'invoices',
                            recordCount: 2180,
                            description: 'Invoice records'
                        }
                    ],
                    totalRecords: 15420
                },
                s3Impact: {
                    files: [
                        {
                            sourceTable: 'document_library',
                            fileCount: 850,
                            description: 'Document library files'
                        },
                        {
                            sourceTable: 'emr_documents',
                            fileCount: 400,
                            description: 'EMR document attachments'
                        }
                    ],
                    totalFiles: 1250,
                    fileReferences: []
                },
                estimatedTime: '15 minutes',
                warnings: [
                    'Large number of patient records detected - deletion may take significant time',
                    'This action is irreversible - ensure you have proper backups'
                ]
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid request parameters' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Insufficient permissions (SUPER_ADMIN required)'
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Target clinic/brand not found' }),
    (0, track_method_decorator_1.TrackMethod)('clinic-deletion-impact-analysis'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [clinic_deletion_dto_1.DeletionImpactRequestDto, Object]),
    __metadata("design:returntype", Promise)
], ClinicDeletionController.prototype, "analyzeImpact", null);
__decorate([
    (0, common_1.Post)('execute'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN) // Only super admin can execute deletion
    ,
    (0, common_1.UsePipes)(common_1.ValidationPipe),
    (0, swagger_1.ApiOperation)({
        summary: 'Execute clinic/brand deletion',
        description: 'Execute the deletion process (dry-run or actual deletion)'
    }),
    (0, swagger_1.ApiBody)({
        type: clinic_deletion_dto_1.DeletionRequestDto,
        examples: {
            'dry-run-clinic': {
                summary: 'Dry run clinic deletion',
                value: {
                    type: 'clinic',
                    clinicId: 'clinic-456',
                    mode: 'dry_run'
                }
            },
            'execute-clinic-backup': {
                summary: 'Execute clinic backup and deletion',
                value: {
                    type: 'clinic',
                    clinicId: 'clinic-456',
                    mode: 'execute',
                    confirmationPhrase: 'BACKUP_CONFIRMED',
                    skipS3Backup: false
                }
            },
            'execute-brand-backup': {
                summary: 'Execute brand backup and deletion',
                value: {
                    type: 'brand',
                    brandId: 'brand-123',
                    mode: 'execute',
                    confirmationPhrase: 'BACKUP_CONFIRMED',
                    skipS3Backup: false
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Backup and deletion executed successfully',
        schema: {
            example: {
                success: true,
                mode: 'execute',
                backupId: 'backup-123e4567-e89b-12d3-a456-************',
                targetInfo: {
                    type: 'clinic',
                    id: 'clinic-456',
                    name: 'Downtown Veterinary Clinic'
                },
                results: {
                    databaseBackup: {
                        tablesProcessed: 25,
                        recordsBackedUp: 15420,
                        duration: '30000ms'
                    },
                    s3Backup: {
                        filesProcessed: 1250,
                        filesBackedUp: 1250,
                        filesSkipped: 0,
                        totalSizeBytes: 2147483648,
                        duration: '120000ms'
                    },
                    databaseDeletion: {
                        tablesProcessed: 25,
                        recordsDeleted: 15420,
                        duration: '15000ms'
                    }
                },
                backup: {
                    backupLocation: 'backups/clinic-deletion/clinic-456/2024-01-15T10-30-00Z',
                    backupSize: 2199912448,
                    estimatedRestoreTime: '15 minutes'
                },
                audit: {
                    executedBy: 'user-789',
                    executedAt: '2024-01-15T10:30:00Z'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid request parameters' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Insufficient permissions (SUPER_ADMIN required) or missing confirmation'
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Target clinic/brand not found' }),
    (0, track_method_decorator_1.TrackMethod)('clinic-deletion-execute'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [clinic_deletion_dto_1.DeletionRequestDto, Object]),
    __metadata("design:returntype", Promise)
], ClinicDeletionController.prototype, "executeDeletion", null);
__decorate([
    (0, common_1.Get)('backups'),
    (0, swagger_1.ApiOperation)({
        summary: 'List backups',
        description: 'Retrieve a list of backups with optional filtering'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'targetType',
        required: false,
        enum: clinic_deletion_dto_1.DeletionType,
        description: 'Filter by target type (clinic or brand)'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'targetId',
        required: false,
        description: 'Filter by target ID'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'status',
        required: false,
        description: 'Filter by backup status'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Number of results to return (default: 50)'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'offset',
        required: false,
        type: Number,
        description: 'Number of results to skip (default: 0)'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of backups retrieved successfully'
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [clinic_deletion_dto_1.BackupListRequestDto, Object]),
    __metadata("design:returntype", Promise)
], ClinicDeletionController.prototype, "listBackups", null);
__decorate([
    (0, common_1.Get)('backups/:backupId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get backup details',
        description: 'Retrieve detailed information about a specific backup'
    }),
    (0, swagger_1.ApiParam)({
        name: 'backupId',
        description: 'Backup ID (UUID)',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Backup details retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Backup not found'
    }),
    __param(0, (0, common_1.Param)('backupId', new common_1.ParseUUIDPipe())),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ClinicDeletionController.prototype, "getBackupDetails", null);
__decorate([
    (0, common_1.Delete)('backups/:backupId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete backup',
        description: 'Permanently delete a backup and all its associated files'
    }),
    (0, swagger_1.ApiParam)({
        name: 'backupId',
        description: 'Backup ID (UUID)',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Backup deleted successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Backup not found'
    }),
    __param(0, (0, common_1.Param)('backupId', new common_1.ParseUUIDPipe())),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ClinicDeletionController.prototype, "deleteBackup", null);
__decorate([
    (0, common_1.Post)('restore/impact-analysis'),
    (0, swagger_1.ApiOperation)({
        summary: 'Analyze restore impact',
        description: 'Analyze potential conflicts and impact of restoring from a backup'
    }),
    (0, swagger_1.ApiBody)({
        type: clinic_deletion_dto_1.RestoreRequestDto,
        description: 'Restore impact analysis request'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Restore impact analysis completed successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Backup not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid request parameters'
    }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [clinic_deletion_dto_1.RestoreRequestDto, Object]),
    __metadata("design:returntype", Promise)
], ClinicDeletionController.prototype, "analyzeRestoreImpact", null);
__decorate([
    (0, common_1.Post)('restore'),
    (0, swagger_1.ApiOperation)({
        summary: 'Execute restore from backup',
        description: 'Restore data from a backup with specified conflict resolution strategy'
    }),
    (0, swagger_1.ApiBody)({
        type: clinic_deletion_dto_1.RestoreRequestDto,
        description: 'Restore execution request'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Restore executed successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid request parameters or missing confirmation'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Backup not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Conflicts detected and resolution strategy is FAIL'
    }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [clinic_deletion_dto_1.RestoreRequestDto, Object]),
    __metadata("design:returntype", Promise)
], ClinicDeletionController.prototype, "executeRestore", null);
exports.ClinicDeletionController = ClinicDeletionController = __decorate([
    (0, swagger_1.ApiTags)('Clinic Deletion'),
    (0, common_1.Controller)('clinic-deletion'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [clinic_deletion_service_1.ClinicDeletionService,
        winston_logger_service_1.WinstonLogger])
], ClinicDeletionController);
//# sourceMappingURL=clinic-deletion.controller.js.map