import { <PERSON><PERSON>ogger } from '../../logger/winston-logger.service';
import { AIService } from './ai.service';
export declare class AIController {
    private readonly aiService;
    private readonly logger;
    constructor(aiService: AIService, logger: <PERSON>Logger);
    generateSOAPNotes(data: {
        clinicalNotes: string;
    }): Promise<import("../types/soap.types").SOAPResponse>;
    regenerateSOAPNotes(data: {
        clinicalNotes: string;
        previousNotes?: string;
    }): Promise<import("../types/soap.types").SOAPResponse>;
}
