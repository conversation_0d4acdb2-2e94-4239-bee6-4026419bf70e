"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseExcelImportController = void 0;
const read_service_1 = require("./read.service");
class BaseExcelImportController {
    async handleExcelImport(file, sheetName, formatFn, insertFn) {
        if (!file) {
            throw new Error('No file uploaded');
        }
        const sheetData = await read_service_1.ReadService.readExcelBuffer(file.buffer);
        const data = sheetData[sheetName];
        if (!data) {
            throw new Error(`Sheet "${sheetName}" not found in the uploaded file`);
        }
        const formattedData = formatFn(data);
        let insertResult;
        if (formattedData.insertArray.length > 0) {
            insertResult = await insertFn(formattedData.insertArray);
        }
        return {
            sheet: sheetName,
            inserted: formattedData.insertArray.length,
            errors: formattedData.errorArray,
            insertResult
        };
    }
}
exports.BaseExcelImportController = BaseExcelImportController;
//# sourceMappingURL=file-extract-sheets.js.map