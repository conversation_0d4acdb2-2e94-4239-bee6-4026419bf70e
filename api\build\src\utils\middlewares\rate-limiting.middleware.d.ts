import { NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';
export declare class RateLimitingMiddleware implements NestMiddleware {
    private configService;
    private readonly globalRateLimiter;
    private readonly blacklistedIPs;
    private readonly allowedRegions;
    private readonly healthCheckPaths;
    constructor(configService: ConfigService);
    use(req: Request, res: Response, next: NextFunction): Promise<void | Response<any, Record<string, any>>>;
}
