"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAppoinmtmentAssessmentsTable1723702684656 = void 0;
const typeorm_1 = require("typeorm");
class CreateAppoinmtmentAssessmentsTable1723702684656 {
    constructor() {
        this.name = 'CreateAppoinmtmentAssessmentsTable1723702684656';
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'appointment_assessments',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'name',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                }
            ]
        }), true);
        await queryRunner.createForeignKey('appointment_assessments', new typeorm_1.TableForeignKey({
            columnNames: ['created_by'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
        await queryRunner.createForeignKey('appointment_assessments', new typeorm_1.TableForeignKey({
            columnNames: ['updated_by'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable('appointment_assessments');
        if (table) {
            const foreignKeys = table.foreignKeys.filter(fk => ['created_by', 'updated_by'].includes(fk.columnNames[0]));
            for (const fk of foreignKeys) {
                await queryRunner.dropForeignKey('appointment_assessments', fk);
            }
        }
        await queryRunner.dropTable('appointment_assessments');
    }
}
exports.CreateAppoinmtmentAssessmentsTable1723702684656 = CreateAppoinmtmentAssessmentsTable1723702684656;
//# sourceMappingURL=1723702684656-CreateAppoinmtmentAssessmentsTable.js.map