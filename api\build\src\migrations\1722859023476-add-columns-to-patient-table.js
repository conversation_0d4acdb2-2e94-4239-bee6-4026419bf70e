"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddColumnsToPatientTable1722859023476 = void 0;
const typeorm_1 = require("typeorm");
class AddColumnsToPatientTable1722859023476 {
    constructor() {
        this.name = 'AddColumnsToPatientTable1722859023476';
    }
    async up(queryRunner) {
        await queryRunner.addColumns('patients', [
            new typeorm_1.TableColumn({
                name: 'weight',
                type: 'varchar',
                length: '50',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'is_deceased',
                type: 'boolean',
                default: false
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumns('patients', ['weight', 'is_deceased']);
    }
}
exports.AddColumnsToPatientTable1722859023476 = AddColumnsToPatientTable1722859023476;
//# sourceMappingURL=1722859023476-add-columns-to-patient-table.js.map