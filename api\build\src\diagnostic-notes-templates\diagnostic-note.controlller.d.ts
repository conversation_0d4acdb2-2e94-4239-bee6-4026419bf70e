import { CreateDiagnosticTemplateDto } from './dto/create-template.dto';
import { DiagnosticTemplatesService } from './diagnostic-note.service';
import { UpdateDiagnosticNoteDto, CreateDiagnosticNoteDto } from './dto/diagnostic-note.dto';
export declare class DiagnosticTemplatesController {
    private readonly diagnosticNoteService;
    constructor(diagnosticNoteService: DiagnosticTemplatesService);
    create(createDto: CreateDiagnosticTemplateDto, req: any): Promise<import("./entities/diagnostic-template.entity").DiagnosticTemplate>;
    findAll(clinicId: string): Promise<import("./entities/diagnostic-template.entity").DiagnosticTemplate[]>;
    findOne(id: string, clinicId: string): Promise<import("./entities/diagnostic-template.entity").DiagnosticTemplate>;
    update(id: string, updateDto: Partial<CreateDiagnosticTemplateDto>, clinicId: string, req: any): Promise<import("./entities/diagnostic-template.entity").DiagnosticTemplate>;
    remove(id: string, clinicId: string): Promise<void>;
    createNote(createNoteDto: CreateDiagnosticNoteDto, req: any): Promise<import("./entities/diagnostic-note.entity").DiagnosticNote>;
    getTemplatesForLabReport(labReportId: string, clinicId: string): Promise<import("./entities/diagnostic-template.entity").DiagnosticTemplate[]>;
    getNotesByLabReport(clinicLabReportId: string, clinicId: string): Promise<{
        status: boolean;
        data: import("./entities/diagnostic-template.entity").DiagnosticTemplate[];
    }>;
    getPatientNotes(patientId: string): Promise<import("./entities/diagnostic-note.entity").DiagnosticNote[]>;
    getNote(noteId: string): Promise<import("./entities/diagnostic-note.entity").DiagnosticNote[]>;
    updateNote(id: string, updateNoteDto: UpdateDiagnosticNoteDto, req: any): Promise<import("./entities/diagnostic-note.entity").DiagnosticNote>;
    deleteNote(noteId: string): Promise<{
        success: boolean;
    }>;
}
