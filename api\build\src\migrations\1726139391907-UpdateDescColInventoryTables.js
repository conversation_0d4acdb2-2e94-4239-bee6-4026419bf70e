"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AllowNullDescriptionForInventoryTables1726139391907 = void 0;
class AllowNullDescriptionForInventoryTables1726139391907 {
    async up(queryRunner) {
        const tables = [
            'clinic_consumables',
            'clinic_products',
            'clinic_services',
            'clinic_vaccinations',
            'clinic_medications',
            'clinic_lab_reports'
        ];
        for (const table of tables) {
            await queryRunner.query(`ALTER TABLE ${table} ALTER COLUMN description DROP NOT NULL`);
        }
    }
    async down(queryRunner) {
        const tables = [
            'clinic_consumables',
            'clinic_products',
            'clinic_services',
            'clinic_vaccinations',
            'clinic_medications',
            'clinic_lab_reports'
        ];
        for (const table of tables) {
            await queryRunner.query(`ALTER TABLE ${table} ALTER COLUMN description SET NOT NULL`);
        }
    }
}
exports.AllowNullDescriptionForInventoryTables1726139391907 = AllowNullDescriptionForInventoryTables1726139391907;
//# sourceMappingURL=1726139391907-UpdateDescColInventoryTables.js.map