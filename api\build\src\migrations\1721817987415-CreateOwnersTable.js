"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateOwnersTable1721817987415 = void 0;
const typeorm_1 = require("typeorm");
class CreateOwnersTable1721817987415 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'owners',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'first_name',
                    type: 'varchar',
                    length: '50',
                    isNullable: false
                },
                {
                    name: 'last_name',
                    type: 'varchar',
                    length: '50',
                    isNullable: false
                },
                {
                    name: 'email',
                    type: 'varchar',
                    length: '100',
                    isUnique: true,
                    isNullable: true
                },
                {
                    name: 'phone_number',
                    type: 'varchar',
                    length: '20',
                    isNullable: false
                },
                {
                    name: 'address',
                    type: 'text',
                    isNullable: true
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP'
                }
            ]
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('owners');
    }
}
exports.CreateOwnersTable1721817987415 = CreateOwnersTable1721817987415;
//# sourceMappingURL=1721817987415-CreateOwnersTable.js.map