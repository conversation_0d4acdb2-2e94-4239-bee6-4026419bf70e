export declare enum InventoryItemType {
    SERVICE = "service",
    PRODUCT = "product",
    MEDICATION = "medication",
    CONSUMABLE = "consumable",
    VACCINATION = "vaccination",
    LAB_REPORT = "lab_report",
    APPOINTMENT_ASSESSMENT = "appointment_assessment"
}
export declare class InventoryMapping {
    id: string;
    sourceItemId: string;
    sourceItemType: InventoryItemType;
    destinationItemId: string;
    destinationItemType: InventoryItemType;
    sourceClinicId: string;
    destinationClinicId: string;
    createdAt: Date;
    updatedAt: Date;
}
