"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateClinicsTable1726200032442 = void 0;
const typeorm_1 = require("typeorm");
class UpdateClinicsTable1726200032442 {
    constructor() {
        this.name = 'UpdateClinicsTable1726200032442';
    }
    async up(queryRunner) {
        await queryRunner.addColumns('clinics', [
            new typeorm_1.TableColumn({
                name: 'is_active',
                type: 'boolean',
                isNullable: true,
                default: true
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumns('clinics', ['is_active']);
    }
}
exports.UpdateClinicsTable1726200032442 = UpdateClinicsTable1726200032442;
//# sourceMappingURL=1726200032442-UpdateClinicsTable.js.map