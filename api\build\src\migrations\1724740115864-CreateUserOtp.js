"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUserOtp1724740115864 = void 0;
const typeorm_1 = require("typeorm");
class CreateUserOtp1724740115864 {
    constructor() {
        this.name = 'CreateUserOtp1724740115864';
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'user_otps',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'user_id',
                    type: 'uuid'
                },
                {
                    name: 'otp',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'otp_expires_at',
                    type: 'timestamp',
                    isNullable: false
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP'
                }
            ]
        }), true);
        await queryRunner.createForeignKey('user_otps', new typeorm_1.TableForeignKey({
            columnNames: ['user_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'CASCADE'
        }));
        await queryRunner.createForeignKey('user_otps', new typeorm_1.TableForeignKey({
            columnNames: ['created_by'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'SET NULL'
        }));
        await queryRunner.createForeignKey('user_otps', new typeorm_1.TableForeignKey({
            columnNames: ['updated_by'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'SET NULL'
        }));
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable('user_otps');
        if (table) {
            const userForeignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('user_id') !== -1);
            if (userForeignKey) {
                await queryRunner.dropForeignKey('user_otps', userForeignKey);
            }
            const createdByForeignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('created_by') !== -1);
            if (createdByForeignKey) {
                await queryRunner.dropForeignKey('user_otps', createdByForeignKey);
            }
            const updatedByForeignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('updated_by') !== -1);
            if (updatedByForeignKey) {
                await queryRunner.dropForeignKey('user_otps', updatedByForeignKey);
            }
            await queryRunner.dropTable('user_otps');
        }
    }
}
exports.CreateUserOtp1724740115864 = CreateUserOtp1724740115864;
//# sourceMappingURL=1724740115864-CreateUserOtp.js.map