{"version": 3, "file": "1735543219075-RemoveColumnsFromClinicVaccination.js", "sourceRoot": "", "sources": ["../../../src/migrations/1735543219075-RemoveColumnsFromClinicVaccination.ts"], "names": [], "mappings": ";;;AAAA,qCAAuE;AAEvE,MAAa,+CAA+C;IAA5D;QAGC,SAAI,GAAG,iDAAiD,CAAC;IAoD1D,CAAC;IAlDO,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,MAAM,WAAW,CAAC,UAAU,CAAC,qBAAqB,EAAE,gBAAgB,CAAC,CAAC;QACtE,MAAM,WAAW,CAAC,UAAU,CAAC,qBAAqB,EAAE,eAAe,CAAC,CAAC;QACrE,MAAM,WAAW,CAAC,UAAU,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC;QAC7D,MAAM,WAAW,CAAC,UAAU,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC;QAClE,MAAM,WAAW,CAAC,UAAU,CAAC,qBAAqB,EAAE,aAAa,CAAC,CAAC;IACpE,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,MAAM,WAAW,CAAC,SAAS,CAC1B,qBAAqB,EACrB,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;QACF,MAAM,WAAW,CAAC,SAAS,CAC1B,qBAAqB,EACrB,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,0BAA0B;YAChC,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;QACF,MAAM,WAAW,CAAC,SAAS,CAC1B,qBAAqB,EACrB,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;QACF,MAAM,WAAW,CAAC,SAAS,CAC1B,qBAAqB,EACrB,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;QACF,MAAM,WAAW,CAAC,SAAS,CAC1B,qBAAqB,EACrB,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,gBAAgB;YACtB,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;IACH,CAAC;CACD;AAvDD,0GAuDC"}