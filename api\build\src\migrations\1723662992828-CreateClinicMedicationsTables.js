"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateClinicMedicationsTables1723662992828 = void 0;
const typeorm_1 = require("typeorm");
class CreateClinicMedicationsTables1723662992828 {
    constructor() {
        this.name = 'CreateClinicMedicationsTables1723662992828';
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'clinic_medications',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'clinic_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'name',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                }
            ]
        }), true);
        await queryRunner.createForeignKey('clinic_medications', new typeorm_1.TableForeignKey({
            columnNames: ['clinic_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinics',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        }));
        await queryRunner.createForeignKey('clinic_medications', new typeorm_1.TableForeignKey({
            columnNames: ['created_by'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
        await queryRunner.createForeignKey('clinic_medications', new typeorm_1.TableForeignKey({
            columnNames: ['updated_by'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable('clinic_medications');
        if (table) {
            const foreignKeys = table.foreignKeys.filter(fk => ['clinic_id', 'created_by', 'updated_by'].includes(fk.columnNames[0]));
            for (const fk of foreignKeys) {
                await queryRunner.dropForeignKey('clinic_medications', fk);
            }
        }
        await queryRunner.dropTable('clinic_medications');
    }
}
exports.CreateClinicMedicationsTables1723662992828 = CreateClinicMedicationsTables1723662992828;
//# sourceMappingURL=1723662992828-CreateClinicMedicationsTables.js.map