"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BackupListRequestDto = exports.RestoreRequestDto = exports.DeletionRequestDto = exports.DeletionImpactRequestDto = exports.ConflictResolution = exports.RestoreMode = exports.BackupStatus = exports.DeletionMode = exports.DeletionType = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
var DeletionType;
(function (DeletionType) {
    DeletionType["CLINIC"] = "clinic";
    DeletionType["BRAND"] = "brand";
})(DeletionType || (exports.DeletionType = DeletionType = {}));
var DeletionMode;
(function (DeletionMode) {
    DeletionMode["DRY_RUN"] = "dry_run";
    DeletionMode["EXECUTE"] = "execute";
})(DeletionMode || (exports.DeletionMode = DeletionMode = {}));
var BackupStatus;
(function (BackupStatus) {
    BackupStatus["IN_PROGRESS"] = "in_progress";
    BackupStatus["COMPLETED"] = "completed";
    BackupStatus["FAILED"] = "failed";
})(BackupStatus || (exports.BackupStatus = BackupStatus = {}));
var RestoreMode;
(function (RestoreMode) {
    RestoreMode["DRY_RUN"] = "dry_run";
    RestoreMode["EXECUTE"] = "execute";
})(RestoreMode || (exports.RestoreMode = RestoreMode = {}));
var ConflictResolution;
(function (ConflictResolution) {
    ConflictResolution["SKIP"] = "skip";
    ConflictResolution["OVERWRITE"] = "overwrite";
    ConflictResolution["FAIL"] = "fail";
})(ConflictResolution || (exports.ConflictResolution = ConflictResolution = {}));
class DeletionImpactRequestDto {
}
exports.DeletionImpactRequestDto = DeletionImpactRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of deletion - clinic or brand',
        enum: DeletionType,
        example: DeletionType.CLINIC
    }),
    (0, class_validator_1.IsEnum)(DeletionType),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DeletionImpactRequestDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Clinic ID (required if type is clinic)',
        example: 'ea35c83a-9549-4fd7-99d6-27480c182164'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], DeletionImpactRequestDto.prototype, "clinicId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Brand ID (required if type is brand)',
        example: '98907c8e-704b-46d2-b2be-d45cfde88d2e'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], DeletionImpactRequestDto.prototype, "brandId", void 0);
class DeletionRequestDto extends DeletionImpactRequestDto {
}
exports.DeletionRequestDto = DeletionRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Deletion mode - dry_run or execute (now creates backup)',
        enum: DeletionMode,
        example: DeletionMode.DRY_RUN
    }),
    (0, class_validator_1.IsEnum)(DeletionMode),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DeletionRequestDto.prototype, "mode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether to skip S3 file backup',
        default: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], DeletionRequestDto.prototype, "skipS3Backup", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Confirmation phrase (required for execute mode)',
        example: 'BACKUP_CONFIRMED'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], DeletionRequestDto.prototype, "confirmationPhrase", void 0);
// New Backup & Restore DTOs
class RestoreRequestDto {
}
exports.RestoreRequestDto = RestoreRequestDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Backup ID to restore from',
        example: 'ea35c83a-9549-4fd7-99d6-27480c182164'
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], RestoreRequestDto.prototype, "backupId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Restore mode - dry_run or execute',
        enum: RestoreMode,
        example: RestoreMode.DRY_RUN
    }),
    (0, class_validator_1.IsEnum)(RestoreMode),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], RestoreRequestDto.prototype, "mode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'How to handle conflicts with existing data',
        enum: ConflictResolution,
        default: ConflictResolution.FAIL
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ConflictResolution),
    __metadata("design:type", String)
], RestoreRequestDto.prototype, "conflictResolution", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether to skip database restore',
        default: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], RestoreRequestDto.prototype, "skipDatabaseRestore", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether to skip S3 file restore',
        default: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], RestoreRequestDto.prototype, "skipFileRestore", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Confirmation phrase (required for execute mode)',
        example: 'RESTORE_CONFIRMED'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], RestoreRequestDto.prototype, "confirmationPhrase", void 0);
class BackupListRequestDto {
}
exports.BackupListRequestDto = BackupListRequestDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by target type',
        enum: DeletionType
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(DeletionType),
    __metadata("design:type", String)
], BackupListRequestDto.prototype, "targetType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by target ID (clinic or brand ID)'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], BackupListRequestDto.prototype, "targetId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by backup status',
        enum: BackupStatus
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(BackupStatus),
    __metadata("design:type", String)
], BackupListRequestDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of results to return',
        default: 50
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], BackupListRequestDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of results to skip',
        default: 0
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], BackupListRequestDto.prototype, "offset", void 0);
//# sourceMappingURL=clinic-deletion.dto.js.map