{"version": 3, "file": "1738566847167-AddBrandIdToEntities.js", "sourceRoot": "", "sources": ["../../../src/migrations/1738566847167-AddBrandIdToEntities.ts"], "names": [], "mappings": ";;;AAEA,MAAa,iCAAiC;IAA9C;QACC,SAAI,GAAG,mCAAmC,CAAC;IAoY5C,CAAC;IAlYO,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,gDAAgD;QAChD,MAAM,WAAW,CAAC,KAAK,CACtB,2DAA2D,CAC3D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,oLAAoL,CACpL,CAAC;QAEF,oCAAoC;QACpC,MAAM,WAAW,CAAC,KAAK,CACtB,+CAA+C,CAC/C,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,4JAA4J,CAC5J,CAAC;QAEF,sCAAsC;QACtC,MAAM,WAAW,CAAC,KAAK,CACtB,iDAAiD,CACjD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,gKAAgK,CAChK,CAAC;QAEI,yCAAyC;QAC/C,MAAM,WAAW,CAAC,KAAK,CACtB,oDAAoD,CACpD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,sKAAsK,CACtK,CAAC;QAEF,qCAAqC;QACrC,MAAM,WAAW,CAAC,KAAK,CACtB,gDAAgD,CAChD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,8JAA8J,CAC9J,CAAC;QAEF,4CAA4C;QAC5C,MAAM,WAAW,CAAC,KAAK,CACtB,uDAAuD,CACvD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,4KAA4K,CAC5K,CAAC;QAEF,qCAAqC;QACrC,MAAM,WAAW,CAAC,KAAK,CACtB,gDAAgD,CAChD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,8JAA8J,CAC9J,CAAC;QAEF,qCAAqC;QACrC,MAAM,WAAW,CAAC,KAAK,CACtB,gDAAgD,CAChD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,8JAA8J,CAC9J,CAAC;QAEF,4BAA4B;QAC5B,MAAM,WAAW,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;QACjE,MAAM,WAAW,CAAC,KAAK,CACtB,4IAA4I,CAC5I,CAAC;QAEF,8CAA8C;QAC9C,MAAM,WAAW,CAAC,KAAK,CACtB,yDAAyD,CACzD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,gLAAgL,CAChL,CAAC;QAEF,8CAA8C;QAC9C,MAAM,WAAW,CAAC,KAAK,CACtB,yDAAyD,CACzD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,gLAAgL,CAChL,CAAC;QAEF,0CAA0C;QAC1C,MAAM,WAAW,CAAC,KAAK,CACtB,qDAAqD,CACrD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,wKAAwK,CACxK,CAAC;QAEF,iCAAiC;QACjC,MAAM,WAAW,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;QACtE,MAAM,WAAW,CAAC,KAAK,CACtB,sJAAsJ,CACtJ,CAAC;QAEF,yCAAyC;QACzC,MAAM,WAAW,CAAC,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACvE,MAAM,WAAW,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;QACtE,MAAM,WAAW,CAAC,KAAK,CACtB,yJAAyJ,CACzJ,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,sJAAsJ,CACtJ,CAAC;QAEF,+CAA+C;QAC/C,MAAM,WAAW,CAAC,KAAK,CACtB,mDAAmD,CACnD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,kDAAkD,CAClD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,qKAAqK,CACrK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,kKAAkK,CAClK,CAAC;QAEF,gDAAgD;QAChD,MAAM,WAAW,CAAC,KAAK,CACtB,oDAAoD,CACpD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,mDAAmD,CACnD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,uKAAuK,CACvK,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,oKAAoK,CACpK,CAAC;QAEF,6FAA6F;QAC7F,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKjB,CAAC,CAAC;QAET,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKjB,CAAC,CAAC;QAET,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKjB,CAAC,CAAC;QAEH,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKvB,CAAC,CAAC;QAET,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKjB,CAAC,CAAC;QAET,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKjB,CAAC,CAAC;QAET,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKjB,CAAC,CAAC;QAET,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKjB,CAAC,CAAC;QAET,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKjB,CAAC,CAAC;QAET,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKjB,CAAC,CAAC;QAET,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKjB,CAAC,CAAC;QAET,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKjB,CAAC,CAAC;QAET,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;SAKjB,CAAC,CAAC;QAET,sEAAsE;QACtE,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;SAQjB,CAAC,CAAC;QAET,4EAA4E;QAC5E,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;SAQjB,CAAC,CAAC;QAET,6EAA6E;QAC7E,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;SAQjB,CAAC,CAAC;IACV,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,uCAAuC;QACvC,MAAM,WAAW,CAAC,KAAK,CACtB,4DAA4D,CAC5D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,8EAA8E,CAC9E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,sFAAsF,CACtF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,sFAAsF,CACtF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,kDAAkD,CAClD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,oEAAoE,CACpE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,oEAAoE,CACpE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,kFAAkF,CAClF,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,oEAAoE,CACpE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,0FAA0F,CAC1F,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,kEAAkE,CAClE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,4EAA4E,CAC5E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,sEAAsE,CACtE,CAAC;QAEF,0BAA0B;QAC1B,MAAM,WAAW,CAAC,KAAK,CACtB,+CAA+C,CAC/C,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,wDAAwD,CACxD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,4DAA4D,CAC5D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,4DAA4D,CAC5D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;QACpE,MAAM,WAAW,CAAC,KAAK,CACtB,mDAAmD,CACnD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,mDAAmD,CACnD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,0DAA0D,CAC1D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,mDAAmD,CACnD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,8DAA8D,CAC9D,CAAC;QAEF,oDAAoD;QACpD,MAAM,WAAW,CAAC,KAAK,CACtB,0EAA0E,CAC1E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,2EAA2E,CAC3E,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,wEAAwE,CACxE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,yEAAyE,CACzE,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,4DAA4D,CAC5D,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,6DAA6D,CAC7D,CAAC;QAEF,gDAAgD;QAChD,MAAM,WAAW,CAAC,KAAK,CACtB,sDAAsD,CACtD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,uDAAuD,CACvD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,qDAAqD,CACrD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,sDAAsD,CACtD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,+CAA+C,CAC/C,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,gDAAgD,CAChD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,uDAAuD,CACvD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,kDAAkD,CAClD,CAAC;QACF,MAAM,WAAW,CAAC,KAAK,CACtB,oDAAoD,CACpD,CAAC;IACH,CAAC;CACD;AArYD,8EAqYC"}