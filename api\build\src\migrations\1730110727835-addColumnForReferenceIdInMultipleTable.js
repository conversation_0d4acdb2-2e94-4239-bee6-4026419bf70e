"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddNewReferenceIdColumns1730110727835 = void 0;
const typeorm_1 = require("typeorm");
class AddNewReferenceIdColumns1730110727835 {
    async up(queryRunner) {
        await queryRunner.addColumn('invoices', new typeorm_1.TableColumn({
            name: 'reference_alpha_id',
            type: 'varchar',
            isNullable: true
        }));
        await queryRunner.addColumn('invoices', new typeorm_1.TableColumn({
            name: 'prescripiton_reference_alpha_id',
            type: 'varchar',
            isNullable: true
        }));
        await queryRunner.addColumn('payment_details', new typeorm_1.TableColumn({
            name: 'reference_alpha_id',
            type: 'varchar',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('invoices', 'reference_alpha_id');
        await queryRunner.dropColumn('invoices', 'prescripiton_reference_alpha_id');
        await queryRunner.dropColumn('payment_details', 'reference_alpha_id');
    }
}
exports.AddNewReferenceIdColumns1730110727835 = AddNewReferenceIdColumns1730110727835;
//# sourceMappingURL=1730110727835-addColumnForReferenceIdInMultipleTable.js.map