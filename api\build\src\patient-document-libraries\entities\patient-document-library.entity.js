"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientDocumentLibrary = void 0;
const typeorm_1 = require("typeorm");
const patient_entity_1 = require("../../patients/entities/patient.entity");
const document_library_entity_1 = require("../../document-library/entities/document-library.entity");
const user_entity_1 = require("../../users/entities/user.entity");
let PatientDocumentLibrary = class PatientDocumentLibrary {
};
exports.PatientDocumentLibrary = PatientDocumentLibrary;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PatientDocumentLibrary.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'patient_id', type: 'uuid' }),
    __metadata("design:type", String)
], PatientDocumentLibrary.prototype, "patientId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => patient_entity_1.Patient, patient => patient.id, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'patient_id' }),
    __metadata("design:type", patient_entity_1.Patient)
], PatientDocumentLibrary.prototype, "patient", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'document_id', type: 'uuid' }),
    __metadata("design:type", String)
], PatientDocumentLibrary.prototype, "documentId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => document_library_entity_1.DocumentLibrary, document => document.id, {
        onDelete: 'CASCADE'
    }),
    (0, typeorm_1.JoinColumn)({ name: 'document_id' }),
    __metadata("design:type", document_library_entity_1.DocumentLibrary)
], PatientDocumentLibrary.prototype, "document", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'doctor_id', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], PatientDocumentLibrary.prototype, "doctorId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, user => user.id, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'doctor_id' }),
    __metadata("design:type", user_entity_1.User)
], PatientDocumentLibrary.prototype, "doctor", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'doctor_name', type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], PatientDocumentLibrary.prototype, "doctorName", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'document_send_status',
        type: 'enum',
        enum: ['pending', 'completed']
    }),
    __metadata("design:type", String)
], PatientDocumentLibrary.prototype, "documentSendStatus", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'signed_recieved',
        type: 'enum',
        enum: ['pending', 'completed']
    }),
    __metadata("design:type", String)
], PatientDocumentLibrary.prototype, "signedRecieved", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'signed_by', type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PatientDocumentLibrary.prototype, "signedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'document_type',
        type: 'enum',
        enum: ['signable', 'notSignable']
    }),
    __metadata("design:type", String)
], PatientDocumentLibrary.prototype, "documentType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'file_key', type: 'varchar', nullable: true }),
    __metadata("design:type", String)
], PatientDocumentLibrary.prototype, "fileKey", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    __metadata("design:type", Date)
], PatientDocumentLibrary.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    __metadata("design:type", Date)
], PatientDocumentLibrary.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], PatientDocumentLibrary.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', type: 'uuid', nullable: true }),
    __metadata("design:type", String)
], PatientDocumentLibrary.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'document_body', type: 'json', nullable: true }),
    __metadata("design:type", Object)
], PatientDocumentLibrary.prototype, "documentBody", void 0);
exports.PatientDocumentLibrary = PatientDocumentLibrary = __decorate([
    (0, typeorm_1.Entity)('patient_document_libraries')
], PatientDocumentLibrary);
//# sourceMappingURL=patient-document-library.entity.js.map