"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddInvoiceidToPaymentDetails1727073340793 = void 0;
const typeorm_1 = require("typeorm");
class AddInvoiceidToPaymentDetails1727073340793 {
    async up(queryRunner) {
        await queryRunner.addColumns('payment_details', [
            new typeorm_1.TableColumn({
                name: 'invoice_id',
                type: 'uuid',
                isNullable: true,
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('payment_details', 'invoice_id');
    }
}
exports.AddInvoiceidToPaymentDetails1727073340793 = AddInvoiceidToPaymentDetails1727073340793;
//# sourceMappingURL=1727073340793-AddInvoiceidToPaymentDetails.js.map