import {
	Controller,
	Post,
	UseGuards,
	UsePipes,
	ValidationPipe,
	Get,
	Query,
	Param,
	DefaultValuePipe,
	ParseIntPipe,
	HttpException,
	HttpStatus
} from '@nestjs/common';
import { BrandService } from './brands.service';
import { CreateBrandDto } from './dto/create-brand.dto';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../roles/roles.decorator';
import { Role } from '../roles/role.enum';
import {
	ApiBearerAuth,
	ApiBody,
	ApiOperation,
	ApiResponse,
	ApiTags
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { TrackMethod } from '../utils/new-relic/decorators/track-method.decorator';
import { BrandWithSettingsDto } from './dto/brand-with-settings.dto';

@ApiTags('Brands')
// @ApiBearerAuth()
// @UseGuards(JwtAuthGuard, RolesGuard)
@Controller('brands')
export class BrandController {
	constructor(
		private readonly brandService: BrandService,
		private readonly logger: WinstonLogger
	) {}

	@Post()
	@ApiOperation({ summary: 'Creating a new Brand' })
	@ApiBody({ type: CreateBrandDto })
	@ApiResponse({
		status: 201,
		description: 'The Brand has been successfully created.',
		type: CreateBrandDto
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@Roles(Role.SUPER_ADMIN)
	@UsePipes(ValidationPipe)
	@TrackMethod('create-brands')
	async create(@Query() createBrandDto: CreateBrandDto) {
		return await this.brandService.createBrand(createBrandDto);
	}

	@Get(':id')
	@ApiOperation({ summary: 'Getting a brand by Id' })
	@ApiResponse({
		status: 200,
		description: 'Successfully retrieved Brand.',
		type: [CreateBrandDto]
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@Roles(Role.SUPER_ADMIN)
	@TrackMethod('findbyId-brands')
	async findbyId(@Param('id') id: string) {
		return this.brandService.getBrandById(id);
	}

	@Get()
	@ApiOperation({ summary: 'Getting all Brands' })
	@ApiResponse({
		status: 200,
		description: 'Successfully retrieved all Brands.',
		type: [CreateBrandDto]
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@Roles(Role.SUPER_ADMIN)
	@TrackMethod('findAll-brands')
	async findAll(
		@Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number = 1,
		@Query('limit', new DefaultValuePipe(10), ParseIntPipe)
		limit: number = 10,
		@Query('search', new DefaultValuePipe('')) search: string = '',
		@Query('orderBy', new DefaultValuePipe('DESC')) orderBy: string = 'DESC'
	) {
		try {
			this.logger.log('Fetching all brands', {
				page,
				limit,
				search,
				orderBy
			});

			return await this.brandService.getAllBrands(page, limit, search, orderBy);
		} catch (error) {
			this.logger.error('Error fetching brands', { error });

			throw new HttpException(
				'Error fetching all the brands',
				HttpStatus.BAD_REQUEST
			);
		}
	}

	@Get('slug/:slug')
	@ApiOperation({
		summary: 'Getting a brand by slug with client booking settings info'
	})
	@ApiResponse({
		status: 200,
		description:
			'Successfully retrieved Brand by slug with client booking settings status.',
		type: BrandWithSettingsDto
	})
	@ApiResponse({ status: 403, description: 'Forbidden.' })
	@TrackMethod('findBySlug-brands')
	async findBySlug(@Param('slug') slug: string) {
		return this.brandService.getBrandBySlug(slug);
	}
}
