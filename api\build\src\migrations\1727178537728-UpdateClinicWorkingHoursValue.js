"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateWorkingHoursDefaultValue1727178537728 = void 0;
class UpdateWorkingHoursDefaultValue1727178537728 {
    async up(queryRunner) {
        // First, remove the old default value
        await queryRunner.query(`
            ALTER TABLE clinics
            ALTER COLUMN working_hours DROP DEFAULT;
        `);
        // Then, set the new default value
        await queryRunner.query(`
            ALTER TABLE clinics
            ALTER COLUMN working_hours SET DEFAULT '{
                "workingHours": {
                  "friday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": true
                    }
                  ],
                  "monday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": true
                    }
                  ],
                  "sunday": [
                    {
                      "endTime": null,
                      "startTime": null,
                      "isWorkingDay": false
                    }
                  ],
                  "tuesday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": true
                    }
                  ],
                  "saturday": [
                    {
                      "endTime": null,
                      "startTime": null,
                      "isWorkingDay": false
                    }
                  ],
                  "thursday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": true
                    }
                  ],
                  "wednesday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": true
                    }
                  ]
                }
              }'::jsonb;
        `);
        // Update existing rows with the new structure
        await queryRunner.query(`
            UPDATE clinics
            SET working_hours = '{
                "workingHours": {
                  "friday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": true
                    }
                  ],
                  "monday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": true
                    }
                  ],
                  "sunday": [
                    {
                      "endTime": null,
                      "startTime": null,
                      "isWorkingDay": false
                    }
                  ],
                  "tuesday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": true
                    }
                  ],
                  "saturday": [
                    {
                      "endTime": null,
                      "startTime": null,
                      "isWorkingDay": false
                    }
                  ],
                  "thursday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": true
                    }
                  ],
                  "wednesday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": true
                    }
                  ]
                }
              }'::jsonb
            WHERE working_hours IS NULL OR working_hours->'workingHours' IS NOT NULL;
        `);
    }
    async down(queryRunner) {
        // Revert to the old default value
        await queryRunner.query(`
            ALTER TABLE clinics
            ALTER COLUMN working_hours SET DEFAULT '{
                "workingHours": {
                    "monday": {"startTime": "09:00", "endTime": "17:00", "isWorkingDay": true},
                    "tuesday": {"startTime": "09:00", "endTime": "17:00", "isWorkingDay": true},
                    "wednesday": {"startTime": "09:00", "endTime": "17:00", "isWorkingDay": true},
                    "thursday": {"startTime": "09:00", "endTime": "17:00", "isWorkingDay": true},
                    "friday": {"startTime": "09:00", "endTime": "17:00", "isWorkingDay": true},
                    "saturday": {"startTime": null, "endTime": null, "isWorkingDay": false},
                    "sunday": {"startTime": null, "endTime": null, "isWorkingDay": false}
                }
            }'::jsonb;
        `);
        // Revert existing rows to the old structure
        await queryRunner.query(`
            UPDATE clinics
            SET working_hours = jsonb_build_object('workingHours', jsonb_build_object(
                'monday', jsonb_build_object('startTime', working_hours->'monday'->0->>'startTime', 'endTime', working_hours->'monday'->0->>'endTime', 'isWorkingDay', true),
                'tuesday', jsonb_build_object('startTime', working_hours->'tuesday'->0->>'startTime', 'endTime', working_hours->'tuesday'->0->>'endTime', 'isWorkingDay', true),
                'wednesday', jsonb_build_object('startTime', working_hours->'wednesday'->0->>'startTime', 'endTime', working_hours->'wednesday'->0->>'endTime', 'isWorkingDay', true),
                'thursday', jsonb_build_object('startTime', working_hours->'thursday'->0->>'startTime', 'endTime', working_hours->'thursday'->0->>'endTime', 'isWorkingDay', true),
                'friday', jsonb_build_object('startTime', working_hours->'friday'->0->>'startTime', 'endTime', working_hours->'friday'->0->>'endTime', 'isWorkingDay', true),
                'saturday', jsonb_build_object('startTime', null, 'endTime', null, 'isWorkingDay', false),
                'sunday', jsonb_build_object('startTime', null, 'endTime', null, 'isWorkingDay', false)
            ))
            WHERE working_hours->>'monday' IS NOT NULL;
        `);
    }
}
exports.UpdateWorkingHoursDefaultValue1727178537728 = UpdateWorkingHoursDefaultValue1727178537728;
//# sourceMappingURL=1727178537728-UpdateClinicWorkingHoursValue.js.map