"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecalculatePreviousBalances1738749983423 = void 0;
const decimal_js_1 = require("decimal.js");
class RecalculatePreviousBalances1738749983423 {
    async up(queryRunner) {
        // Step 1: Create temporary calculation table that includes ALL owner brands and payment details
        await queryRunner.query(`
        CREATE TEMP TABLE balance_calculation AS
        SELECT 
            pd.id,
            pd.type,
            pd.amount::numeric AS amount,
            pd.transaction_amount::numeric AS transaction_amount,
            ob.opening_balance::numeric,
            pd.created_at,
            pd.owner_id,
            ob.created_at AS owner_created_at
        FROM owner_brands ob
        LEFT JOIN payment_details pd ON ob.id = pd.owner_id 
            AND pd.created_at >= ob.created_at
        ORDER BY ob.id, pd.created_at ASC, pd.id ASC;
        `);
        // Step 2: Get all owner brands, including those without payment_details
        const ownerBrands = await queryRunner.query(`
            SELECT DISTINCT ob.id as owner_id, ob.opening_balance 
            FROM owner_brands ob
        `);
        for (const ownerBrand of ownerBrands) {
            let currentBalance = new decimal_js_1.default(ownerBrand.opening_balance || 0);
            const transactions = await queryRunner.query(`SELECT * FROM balance_calculation 
                WHERE owner_id = $1 AND id IS NOT NULL
                ORDER BY created_at ASC, id ASC`, [ownerBrand.owner_id]);
            // Process transactions if they exist
            for (const tx of transactions) {
                const amount = new decimal_js_1.default(tx.amount || 0);
                const txAmount = new decimal_js_1.default(tx.transaction_amount || 0);
                const previousBalance = currentBalance;
                // Update current balance based on transaction type
                switch (tx.type) {
                    case 'Invoice':
                        currentBalance = currentBalance.plus(amount.minus(txAmount));
                        break;
                    case 'Collect':
                        currentBalance = currentBalance.plus(amount);
                        break;
                    case 'Return':
                        currentBalance = currentBalance.minus(amount);
                        break;
                    case 'Credit Note':
                        currentBalance = currentBalance.minus(txAmount.minus(amount));
                        break;
                    default:
                        console.warn(`Unknown transaction type: ${tx.type}`);
                }
                // Update previous_balance for this transaction
                await queryRunner.query(`UPDATE payment_details 
                    SET previous_balance = $1::numeric(10,2) 
                    WHERE id = $2`, [previousBalance.toDecimalPlaces(2).toNumber(), tx.id]);
            }
            // Update owner brand's balance using the correct column name
            await queryRunner.query(`UPDATE owner_brands 
                SET owner_balance = $1::numeric(10,2)
                WHERE id = $2`, [
                currentBalance.toDecimalPlaces(2).toNumber(),
                ownerBrand.owner_id
            ]);
        }
        // Step 3: Cleanup
        await queryRunner.query(`DROP TABLE balance_calculation;`);
        // Verify the updates
        const verification = await queryRunner.query(`
            SELECT COUNT(*) 
            FROM owner_brands 
            WHERE owner_balance != 0
        `);
        console.log('Updated owner_brands count:', verification[0].count);
    }
    async down(_queryRunner) {
        // No down migration needed as this is a one-time balance calculation
    }
}
exports.RecalculatePreviousBalances1738749983423 = RecalculatePreviousBalances1738749983423;
//# sourceMappingURL=1738749983423-RecalculatePreviousBalances.js.map