"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddColumnToLabReportsTable1736088073060 = void 0;
const typeorm_1 = require("typeorm");
class AddColumnToLabReportsTable1736088073060 {
    async up(queryRunner) {
        await queryRunner.addColumn('lab_reports', new typeorm_1.TableColumn({
            name: 'diagnostic_notes',
            type: 'json',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('lab_reports', 'diagnostic_notes');
    }
}
exports.AddColumnToLabReportsTable1736088073060 = AddColumnToLabReportsTable1736088073060;
//# sourceMappingURL=1736088073060-addColumnToLabReportsTable.js.map