"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrimaryUUID7Column = PrimaryUUID7Column;
exports.generateUUID7 = generateUUID7;
const typeorm_1 = require("typeorm");
const uuidv7_1 = require("uuidv7");
function PrimaryUUID7Column(options = {}) {
    return (0, typeorm_1.PrimaryColumn)('uuid', {
        name: options.name || 'id',
        default: () => `'${generateUUID7()}'`
    });
}
function generateUUID7() {
    return (0, uuidv7_1.uuidv7)();
}
//# sourceMappingURL=typeorm-uuid7.utils.js.map