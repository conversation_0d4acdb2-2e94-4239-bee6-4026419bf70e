"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppointmentSessionsUserInitials1735032704369 = void 0;
const typeorm_1 = require("typeorm");
class AppointmentSessionsUserInitials1735032704369 {
    async up(queryRunner) {
        await queryRunner.addColumn('appointment_sessions', new typeorm_1.TableColumn({
            name: 'user_initials',
            type: 'varchar',
            length: '2',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('appointment_sessions', 'user_initials');
    }
}
exports.AppointmentSessionsUserInitials1735032704369 = AppointmentSessionsUserInitials1735032704369;
//# sourceMappingURL=1735032704369-AppointmentSessionsUserInitials.js.map