"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddingFileUrlColumnToInvoiceTable1726657032527 = void 0;
const typeorm_1 = require("typeorm");
class AddingFileUrlColumnToInvoiceTable1726657032527 {
    async up(queryRunner) {
        await queryRunner.addColumn('invoices', new typeorm_1.TableColumn({
            name: 'file_url',
            type: 'varchar',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('invoices', 'file_url');
    }
}
exports.AddingFileUrlColumnToInvoiceTable1726657032527 = AddingFileUrlColumnToInvoiceTable1726657032527;
//# sourceMappingURL=1726657032527-adding_fileUrl_column_to_invoice_table.js.map