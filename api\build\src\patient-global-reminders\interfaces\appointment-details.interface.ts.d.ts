interface PlanItem {
    id: string;
    value: string;
    label: string;
}
interface AssessmentItem {
    id: string;
    value: string;
    label: string;
}
interface PrescriptionItem {
    id: string;
    value: string;
    label: string;
    isLongTerm?: boolean;
}
export interface AppointmentDetailsContent {
    details: any;
    subjective?: string;
    objective?: {
        notes?: string;
        vitals?: any[];
        physicalExam?: any[];
        bodyMaps?: any[];
        labReports?: any[];
    };
    assessment?: {
        notes?: string;
        list?: AssessmentItem[];
    };
    plans?: {
        notes?: string;
        list?: PlanItem[];
    };
    prescription?: {
        notes?: string;
        list?: PrescriptionItem[];
    };
}
export interface AppointmentDetails {
    appointmentId: string;
    details: AppointmentDetailsContent;
}
export {};
