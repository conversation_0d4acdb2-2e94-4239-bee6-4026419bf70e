"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAppointmentAuditLogTable1745557427917 = exports.AppointmentAuditLogAction = void 0;
const typeorm_1 = require("typeorm");
// Enum definition for action types
// Note: TypeORM migrations don't directly handle native PG enums easily without custom SQL.
// We'll use a CHECK constraint for simplicity here, or you could define a custom type.
var AppointmentAuditLogAction;
(function (AppointmentAuditLogAction) {
    AppointmentAuditLogAction["CREATE"] = "CREATE";
    AppointmentAuditLogAction["UPDATE"] = "UPDATE";
    AppointmentAuditLogAction["CANCEL"] = "CANCEL"; // Using CANCEL as it reflects the user action better than DELETE
})(AppointmentAuditLogAction || (exports.AppointmentAuditLogAction = AppointmentAuditLogAction = {}));
class CreateAppointmentAuditLogTable1745557427917 {
    async up(queryRunner) {
        // Create the appointment_audit_log table
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'appointment_audit_log',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    default: 'uuid_generate_v4()' // Assumes uuid-ossp extension is enabled
                },
                {
                    name: 'appointmentId',
                    type: 'uuid',
                    isNullable: false,
                    comment: 'ID of the appointment being audited'
                },
                {
                    name: 'userId',
                    type: 'uuid',
                    isNullable: false,
                    comment: 'ID of the user (owner) performing the action'
                },
                {
                    name: 'action',
                    type: 'varchar', // Could use an ENUM type if preferred and managed outside migration
                    isNullable: false,
                    comment: 'The action performed (CREATE, UPDATE, CANCEL)'
                },
                {
                    name: 'changedFields',
                    type: 'jsonb', // Use JSONB for efficient querying of structured data
                    isNullable: true, // Nullable for CREATE/CANCEL actions where specific field changes might not apply
                    comment: 'JSON object detailing the changes made (e.g., { field: { old: value, new: value } })'
                },
                {
                    name: 'context',
                    type: 'text',
                    isNullable: true,
                    comment: 'Optional additional context for the audit entry'
                },
                {
                    name: 'timestamp',
                    type: 'timestamp with time zone',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false,
                    comment: 'Timestamp when the action occurred'
                }
            ],
            // Add a CHECK constraint for the action column
            checks: [
                {
                    name: 'CHK_appointment_audit_log_action',
                    columnNames: ['action'],
                    expression: `action IN ('${AppointmentAuditLogAction.CREATE}', '${AppointmentAuditLogAction.UPDATE}', '${AppointmentAuditLogAction.CANCEL}')`
                }
            ]
        }));
    }
    async down(queryRunner) {
        // Drop the table
        await queryRunner.dropTable('appointment_audit_log');
    }
}
exports.CreateAppointmentAuditLogTable1745557427917 = CreateAppointmentAuditLogTable1745557427917;
//# sourceMappingURL=1745557427917-CreateAppointmentAuditLogTable.js.map