{"version": 3, "file": "1735280755686-RemoveColumnsFromClinicConsumables.js", "sourceRoot": "", "sources": ["../../../src/migrations/1735280755686-RemoveColumnsFromClinicConsumables.ts"], "names": [], "mappings": ";;;AAAA,qCAAuE;AAEvE,MAAa,+CAA+C;IAGpD,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,MAAM,WAAW,CAAC,WAAW,CAAC,oBAAoB,EAAE;YACnD,eAAe;YACf,aAAa;YACb,gBAAgB;SAChB,CAAC,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,MAAM,WAAW,CAAC,SAAS,CAC1B,oBAAoB,EACpB,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,KAAK;YACX,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;QAEF,MAAM,WAAW,CAAC,SAAS,CAC1B,oBAAoB,EACpB,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;QAEF,MAAM,WAAW,CAAC,SAAS,CAC1B,oBAAoB,EACpB,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,gBAAgB;YACtB,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,EAAE;YACb,KAAK,EAAE,CAAC;YACR,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;IACH,CAAC;CACD;AAzCD,0GAyCC"}