"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const client_sqs_1 = require("@aws-sdk/client-sqs");
const sqs_queue_config_1 = require("./sqs-queue.config");
require('dotenv').config({ path: '.env' });
const env = process.env.NODE_ENV || 'development';
// dotenv.config({
// 	path: `${__dirname}/../../../../envs/.env.${env}`
// });
async function createDLQueue() {
    const region = process.env.AWS_SQS_REGION;
    const accessKeyId = process.env.AWS_SQS_ACCESS_KEY_ID;
    const secretAccessKey = process.env.AWS_SQS_SECRET_KEY;
    if (!region || !accessKeyId || !secretAccessKey) {
        throw new Error('Missing AWS configuration: Ensure aws.sqs.region, aws.sqs.accessKeyId, and aws.sqs.secretKey are defined in your environment variables.');
    }
    const sqsClient = new client_sqs_1.SQSClient({
        region,
        credentials: {
            accessKeyId,
            secretAccessKey
        }
    });
    const queues = (0, sqs_queue_config_1.getEnvSpecificQueues)(env);
    for (const queue of Object.values(queues)) {
        try {
            const dlqName = `${queue.dlqName}`;
            const dlqArn = await checkOrCreateDLQ(sqsClient, dlqName);
            console.log(`Dead Letter Queue ARN for ${queue.name}: ${dlqArn}`);
        }
        catch (error) {
            console.error(`Failed to process queue: ${queue.name}`, error);
        }
    }
}
async function checkOrCreateDLQ(sqsClient, dlqName) {
    var _a;
    // List existing queues to check if the DLQ already exists
    const listQueuesCommand = new client_sqs_1.ListQueuesCommand({
        QueueNamePrefix: dlqName
    });
    const listResponse = await sqsClient.send(listQueuesCommand);
    if (listResponse.QueueUrls && listResponse.QueueUrls.length > 0) {
        // Get the ARN of the existing DLQ
        const dlqUrl = listResponse.QueueUrls[0];
        const getAttributesCommand = new client_sqs_1.GetQueueAttributesCommand({
            QueueUrl: dlqUrl,
            AttributeNames: ['QueueArn']
        });
        const attributesResponse = await sqsClient.send(getAttributesCommand);
        const dlqArn = (_a = attributesResponse.Attributes) === null || _a === void 0 ? void 0 : _a.QueueArn;
        if (!dlqArn) {
            throw new Error(`Failed to retrieve DLQ ARN for existing queue: ${dlqName}`);
        }
        console.log(`DLQ already exists: ${dlqName}`);
        return dlqArn;
    }
    // Create the DLQ if it does not exist
    return await createDLQ(sqsClient, dlqName);
}
async function createDLQ(sqsClient, dlqName) {
    var _a;
    const command = new client_sqs_1.CreateQueueCommand({
        QueueName: dlqName,
        Attributes: {
            MessageRetentionPeriod: '1209600' // 14 days (Maximum retention period for SQS messages)
        }
    });
    try {
        const response = await sqsClient.send(command);
        console.log(`DLQ created: ${dlqName}`);
        const dlqUrl = response.QueueUrl;
        if (!dlqUrl) {
            throw new Error(`Failed to retrieve DLQ URL for queue: ${dlqName}`);
        }
        const getAttributesCommand = new client_sqs_1.GetQueueAttributesCommand({
            QueueUrl: dlqUrl,
            AttributeNames: ['QueueArn']
        });
        const attributesResponse = await sqsClient.send(getAttributesCommand);
        const dlqArn = (_a = attributesResponse.Attributes) === null || _a === void 0 ? void 0 : _a.QueueArn;
        if (!dlqArn) {
            throw new Error(`Failed to retrieve DLQ ARN for queue: ${dlqName}`);
        }
        return dlqArn;
    }
    catch (error) {
        console.error(`Failed to create DLQ: ${dlqName}`, error);
        throw error;
    }
}
createDLQueue()
    .then(() => {
    console.log('DLQ check and creation completed successfully');
})
    .catch(error => {
    console.error('Error creating or checking DLQ:', error);
});
//# sourceMappingURL=create-dead-letter-queue.js.map