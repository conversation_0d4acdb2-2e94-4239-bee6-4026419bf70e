interface VaccinationCertificateData {
    vaccineName: string;
    vaccinationDate: string;
    petName: string;
    ownerName: string;
    clinicName: string;
    clinicAddress: string;
    clinicPhone: string;
    clinicEmail: string;
    clinicWebsite: string;
    vetName: string;
    vetLicense: string;
    species: string;
    breed: string;
    color: string;
    weight: string;
    reproductiveStatus: string;
    dob: string;
    age: number;
    digitalSignature: string;
    ownerEmail: string;
    ownerPhone: string;
    gender: string;
    vaccineId: string;
}
export declare const generateVaccinationCertificate: ({ vaccineName, vaccinationDate, petName, ownerName, clinicName, clinicAddress, clinicPhone, clinicEmail, clinicWebsite, vetName, vetLicense, species, breed, color, weight, reproductiveStatus, dob, age, digitalSignature, ownerEmail, ownerPhone, gender, vaccineId }: VaccinationCertificateData) => string;
export {};
