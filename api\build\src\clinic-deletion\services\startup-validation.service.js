"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StartupValidationService = void 0;
const common_1 = require("@nestjs/common");
const query_manager_service_1 = require("./query-manager.service");
const clinic_deletion_dto_1 = require("../dto/clinic-deletion.dto");
const winston_logger_service_1 = require("../../utils/logger/winston-logger.service");
/**
 * Service that performs startup validation of QueryManagerService consistency
 * Only runs in development environment to provide immediate feedback to developers
 */
let StartupValidationService = class StartupValidationService {
    constructor(queryManagerService, logger) {
        this.queryManagerService = queryManagerService;
        this.logger = logger;
    }
    async onModuleInit() {
        // Only run validation in development environment
        if (process.env.NODE_ENV === 'development') {
            this.validateQueryConsistency();
        }
    }
    validateQueryConsistency() {
        this.logger.log('Running QueryManagerService consistency validation at startup...');
        try {
            // Test both deletion types with dummy IDs
            const testCases = [
                { type: clinic_deletion_dto_1.DeletionType.CLINIC, id: 'test-clinic-validation' },
                { type: clinic_deletion_dto_1.DeletionType.BRAND, id: 'test-brand-validation' }
            ];
            let hasErrors = false;
            let totalWarnings = 0;
            for (const testCase of testCases) {
                const result = this.queryManagerService.validateQueryConsistency(testCase.type, testCase.id);
                if (!result.valid) {
                    hasErrors = true;
                    this.logger.error(`Query consistency validation FAILED for ${testCase.type} deletion:`, { errors: result.errors });
                }
                if (result.warnings.length > 0) {
                    totalWarnings += result.warnings.length;
                    this.logger.warn(`Query consistency warnings for ${testCase.type} deletion:`, { warnings: result.warnings });
                }
            }
            if (hasErrors) {
                this.logger.error('❌ QueryManagerService validation FAILED! Please fix the errors above.');
            }
            else if (totalWarnings > 0) {
                this.logger.warn(`⚠️  QueryManagerService validation passed with ${totalWarnings} warnings. Consider reviewing the warnings above.`);
            }
            else {
                this.logger.log('✅ QueryManagerService validation passed successfully!');
            }
        }
        catch (error) {
            this.logger.error('Error during QueryManagerService validation:', error);
        }
    }
};
exports.StartupValidationService = StartupValidationService;
exports.StartupValidationService = StartupValidationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [query_manager_service_1.QueryManagerService,
        winston_logger_service_1.WinstonLogger])
], StartupValidationService);
//# sourceMappingURL=startup-validation.service.js.map