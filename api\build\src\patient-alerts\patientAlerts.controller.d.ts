import { PatientAlertsService } from './patientAlerts.service';
import { CreatePatientAlertDto } from './dto/create-patientAlert.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ApiDocumentationBase } from '../base/api-documentation-base';
import { PatientAlertsEntity } from './entities/patientAlerts.entity';
import { UpdatePatientAlertDto } from './dto/update-patientAlert.dto';
export declare class PatientAlertController extends ApiDocumentationBase {
    private readonly logger;
    private readonly patientAlertsService;
    constructor(logger: WinstonLogger, patientAlertsService: PatientAlertsService);
    createPatientAlert(createPatientAlertDto: CreatePatientAlertDto): Promise<PatientAlertsEntity>;
    getPatientAlert(patientId: string): Promise<PatientAlertsEntity[]>;
    deletePatientAlert(id: string, all?: string): Promise<void>;
    updatePatientAlert(id: string, updatePatientAlertDto: UpdatePatientAlertDto): Promise<PatientAlertsEntity>;
}
