import { PatientDocumentLibrariesService } from './patient-document-libraries.service';
import { CreatePatientDocumentLibraryDto } from './dto/create-patient-document-library.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { PatientDocumentLibrary } from './entities/patient-document-library.entity';
import { UpdateSignedDocumentDto } from './dto/update-patient-document-library.dto';
export declare class PatientDocumentLibrariesController {
    private readonly patientDocumentLibrariesService;
    private readonly logger;
    constructor(patientDocumentLibrariesService: PatientDocumentLibrariesService, logger: WinstonLogger);
    create(createPatientDocumentLibraryDto: CreatePatientDocumentLibraryDto): Promise<any>;
    findAll(patientId: string, page?: number, limit?: number, search?: string): Promise<{
        data: any;
        total: number;
        page: number;
        pageCount: number;
    }>;
    findOne(id: string): Promise<PatientDocumentLibrary>;
    sendSignedDocument(id: string, body: UpdateSignedDocumentDto): Promise<import("typeorm").UpdateResult | null>;
}
