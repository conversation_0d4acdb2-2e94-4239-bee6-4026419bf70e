"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataController = void 0;
const common_1 = require("@nestjs/common");
const data_service_1 = require("./data.service");
const data_dto_1 = require("./dto/data.dto");
const s3_service_1 = require("../utils/aws/s3/s3.service");
const axios_1 = require("axios");
const uuidv7_1 = require("uuidv7");
const rxjs_1 = require("rxjs");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const platform_express_1 = require("@nestjs/platform-express");
const typeorm_1 = require("typeorm");
let DataController = class DataController {
    constructor(dataService, s3Service, logger, dataSource) {
        this.dataService = dataService;
        this.s3Service = s3Service;
        this.logger = logger;
        this.dataSource = dataSource;
    }
    getHello() {
        return this.dataService.getHello();
    }
    async createOwner(createOwnerDto) {
        return this.dataService.createOwner(createOwnerDto);
    }
    async createPatient(createPatientDto) {
        return this.dataService.createPatient(createPatientDto);
    }
    async getPatients(clinicId, patientId) {
        try {
            const patient = await this.dataService.findByOldPatientId(patientId, clinicId);
            if (!patient) {
                throw new common_1.HttpException(`Patient with old ID "${patientId}" not found`, common_1.HttpStatus.NOT_FOUND);
            }
            return {
                id: patient.id,
                oldPatientId: patientId,
                clinicId: patient.clinicId,
                status: 'found'
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Error finding patient', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createPatientOwner(createPatientOwnerDto) {
        return this.dataService.createPatientOwner(createPatientOwnerDto);
    }
    async createAppointment(createAppointmentDto) {
        return this.dataService.createAppointment(createAppointmentDto);
    }
    // @Post('appointment-details')
    // async createAppointmentDetails(@Body() createAppointmentDetailsDto: CreateAppointmentDetailsDto) {
    //   console.log("App det",createAppointmentDetailsDto)
    //   return this.dataService.createAppointmentDetails(createAppointmentDetailsDto);
    // }
    async createAppointmentDetails(createAppointmentDetailsDto) {
        var _a, _b, _c;
        try {
            // Process attachments if they exist
            if (((_c = (_b = (_a = createAppointmentDetailsDto.details) === null || _a === void 0 ? void 0 : _a.attachments) === null || _b === void 0 ? void 0 : _b.list) === null || _c === void 0 ? void 0 : _c.length) >
                0) {
                const processedAttachments = await this.processAttachments(createAppointmentDetailsDto.details.attachments.list, createAppointmentDetailsDto.appointmentId);
                createAppointmentDetailsDto.details.attachments.list =
                    processedAttachments;
            }
            return await this.dataService.createAppointmentDetails(createAppointmentDetailsDto);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException('Failed to process appointment details');
        }
    }
    async processAttachments(attachments, appointmentId) {
        const processedAttachments = [];
        for (const attachment of attachments) {
            try {
                console.log('---------------------------------', attachment);
                // Skip processing for EMR Documents - use S3 key directly
                if (attachment.attachementName === 'EMR Document') {
                    this.logger.log(`Using existing S3 key for EMR Document: ${attachment.fileKey}`);
                    processedAttachments.push(attachment);
                    continue;
                }
                // Add delay to prevent rate limiting
                (0, rxjs_1.delay)(500);
                // Download file
                const fileBuffer = await this.downloadFile(attachment.fileKey);
                // Generate UUID for unique S3 key
                const fileUUID = (0, uuidv7_1.uuidv4)();
                const fileExtension = attachment.fileName.split('.').pop();
                const s3Key = `appointment_attachments/${appointmentId}/${fileUUID}.${fileExtension}`;
                // Upload image attachment
                this.logger.log(`Processing image attachment for appointment ${appointmentId}`);
                await this.s3Service.uploadBuffer(fileBuffer, s3Key, 'image/jpg');
                // Add processed attachment
                processedAttachments.push({
                    ...attachment,
                    fileKey: s3Key
                });
                this.logger.log(`Successfully processed attachment: ${attachment.attachementName} for appointment ${appointmentId}`);
            }
            catch (error) {
                this.logger.error('Attachment processing failed', {
                    appointmentId,
                    attachmentName: attachment.attachementName
                });
                if (attachment.attachementName === 'EMR Document') {
                    // For EMR documents, keep the original S3 key even if validation fails
                    processedAttachments.push(attachment);
                }
                else {
                    // For other attachments, throw error to handle upstream
                    throw new Error(`Failed to process ${attachment.attachementName} for appointment ${appointmentId}`);
                }
            }
        }
        return processedAttachments;
    }
    async downloadFile(url) {
        try {
            const response = await axios_1.default.get(url, {
                responseType: 'arraybuffer',
                timeout: 30000, // 30 second timeout
                maxContentLength: 50 * 1024 * 1024 // 50MB max
            });
            return Buffer.from(response.data);
        }
        catch (error) {
            throw new Error(`Failed to download file: ${error}`);
        }
    }
    /**
     * Process invoices with either a PDF URL or an existing S3 key
     *
     * @example With PDF URL:
     * {
     *   "patientId": "...",
     *   "date": "...",
     *   "pdfUrl": "...",
     *   "clinicId": "...",
     *   "brandId": "..."
     * }
     *
     * @example With existing S3 key:
     * {
     *   "patientId": "...",
     *   "date": "...",
     *   "s3Key": "...",
     *   "clinicId": "...",
     *   "brandId": "..."
     * }
     */
    async processInvoices(invoiceData) {
        this.logger.log('Processing invoices', { count: invoiceData.length });
        const results = [];
        for (const data of invoiceData) {
            try {
                // Check if s3Key is provided directly
                if (!data.s3Key && data.pdfUrl) {
                    // If no s3Key is provided but we have a pdfUrl, download and upload
                    this.logger.log('Downloading and uploading PDF to S3', {
                        pdfUrl: data.pdfUrl
                    });
                    // Download PDF
                    const pdfBuffer = await this.downloadPDF(data.pdfUrl);
                    // Upload to S3
                    const invoiceUUID = (0, uuidv7_1.uuidv4)();
                    const s3Key = `invoice/${invoiceUUID}`;
                    await this.s3Service.uploadPdfToS3(pdfBuffer, s3Key);
                    // Update invoiceData with S3 key
                    data.s3Key = s3Key;
                    this.logger.log('PDF uploaded successfully', { s3Key });
                }
                else if (!data.s3Key) {
                    // If neither s3Key nor pdfUrl is provided, we can't proceed
                    const error = 'Either s3Key or pdfUrl must be provided';
                    this.logger.error(error, { data });
                    results.push({ status: 'error', message: error, data });
                    continue;
                }
                else {
                    // Log that we're using the provided S3 key
                    this.logger.log(`Using provided S3 key: ${data.s3Key}`);
                }
                // Process invoice using DataService
                const result = await this.dataService.processInvoices([data]);
                results.push(...result);
            }
            catch (error) {
                this.logger.error('Error processing invoice', {
                    error,
                    patientId: data.patientId,
                    pdfUrl: data.pdfUrl,
                    s3Key: data.s3Key
                });
                results.push({ status: 'error', message: error, data });
            }
        }
        // Calculate success and error counts
        const successCount = results.filter(r => r.status === 'success').length;
        const failedCount = results.filter(r => r.status === 'error').length;
        this.logger.log('Invoice processing completed', {
            totalProcessed: results.length,
            successful: successCount,
            failed: failedCount
        });
        // Return structured response
        return {
            success: failedCount === 0, // true if no errors
            results,
            summary: {
                total: results.length,
                successful: successCount,
                failed: failedCount
            }
        };
    }
    // @Post('process-diagnostics')
    // async processDiagnostics(@Body() diagnosticsData: DiagnosticsDataDto[]) {
    //   const results = [];
    //   console.log(diagnosticsData)
    //   for (const data of diagnosticsData) {
    //     try {
    //       // Download PDF
    //       console.log(data.pdfUrl)
    //       const pdfBuffer = await this.downloadPDF(data.pdfUrl);
    //       console.log(pdfBuffer)
    //       // Upload to S3
    //       const fileUUID = uuidv4();
    //       const s3Key = `labReports/${fileUUID}/${data.fileName}`;
    //       const res = await this.s3Service.uploadPdfToS3(pdfBuffer, s3Key);
    //       // Create file metadata
    //       const fileData = {
    //         id: uuidv4(),
    //         fileKey: s3Key,
    //         fileName: data.fileName,
    //         fileSize: pdfBuffer.length,
    //         uploadDate: new Date().toISOString()
    //       };
    //       console.log(fileData)
    //       const result = await this.dataService.processDiagnostics({
    //         ...data,
    //         fileData
    //       });
    //       results.push(result);
    //     } catch (error) {
    //       results.push({
    //         status: 'error',
    //         message: error,
    //         data: data
    //       });
    //     }
    //   }
    //   return results;
    // }
    async uploadEmr(file, patientId) {
        if (!file || !patientId) {
            throw new common_1.BadRequestException('File and patientId are required');
        }
        console.log(file, patientId);
        try {
            this.logger.log('Uploading EMR file', {
                patientId,
                fileName: file.originalname
            });
            // Generate unique S3 key
            const fileUUID = (0, uuidv7_1.uuidv4)();
            const s3Key = `appointment_attachments/migrated_data/emr_documents/${patientId}/${fileUUID}.pdf`;
            console.log(s3Key);
            // Upload to S3
            await this.s3Service.uploadBuffer(file.buffer, s3Key, 'application/pdf');
            this.logger.log('EMR file uploaded successfully', {
                patientId,
                s3Key
            });
            return {
                status: 'success',
                patientId,
                s3Key
            };
        }
        catch (error) {
            this.logger.error('Error uploading EMR file', {
                patientId,
                error
            });
            throw error;
        }
    }
    // @Post('appointments/calendar')
    // async migrateAppointments(
    //   @Body() appointments: CreateAppointmentDto[]
    // ) {
    //   try {
    //     this.logger.log('Starting bulk appointment migration', {
    //       appointmentCount: appointments.length
    //     });
    //     const results = [];
    //     for (const appointment of appointments) {
    //       try {
    //         // Get real patient ID using old ID
    //         const patient = await this.dataService.findByOldId(appointment.patientId);
    //         if (!patient) {
    //           results.push({
    //             status: 'error',
    //             oldPatientId: appointment.patientId,
    //             error: 'Patient not found'
    //           });
    //           continue;
    //         }
    //         // Create appointment with real patient ID
    //         const formattedAppointment = {
    //           ...appointment,
    //           patientId: patient.id
    //         };
    //         const createdAppointment = await this.dataService.createAppointment(formattedAppointment);
    //         results.push({
    //           status: 'success',
    //           oldPatientId: appointment.patientId,
    //           newPatientId: patient.id,
    //           appointmentId: createdAppointment.id,
    //           date: appointment.date
    //         });
    //         this.logger.log('Appointment created successfully', {
    //           oldPatientId: appointment.patientId,
    //           newPatientId: patient.id,
    //           appointmentId: createdAppointment.id
    //         });
    //       } catch (error) {
    //         this.logger.error('Error processing appointment', {
    //           oldPatientId: appointment.patientId,
    //           error: error
    //         });
    //         results.push({
    //           status: 'error',
    //           oldPatientId: appointment.patientId,
    //           error: error
    //         });
    //       }
    //     }
    //     return results;
    //   } catch (error) {
    //     this.logger.error('Bulk appointment migration failed', { error });
    //     throw new InternalServerErrorException('Failed to process appointments');
    //   }
    // }
    async downloadPDF(url) {
        const response = await axios_1.default.get(url, { responseType: 'arraybuffer' });
        return Buffer.from(response.data);
    }
    //   @Post('appointments-future/calendar')
    //   async migrateAppointmentsFuture(
    //   @Body() appointments: CreateAppointmentDto[]
    // ) {
    //   try {
    //     this.logger.log('Starting bulk appointment migration', {
    //       appointmentCount: appointments.length
    //     });
    //     const results = [];
    //     for (const appointment of appointments) {
    //       try {
    //         // Start transaction
    //         const queryRunner = this.dataSource.createQueryRunner();
    //         await queryRunner.connect();
    //         await queryRunner.startTransaction();
    //         try {
    //           // Get real patient ID using old ID
    //           const patient = await this.dataService.findByOldId(appointment.patientId);
    //           if (!patient) {
    //             results.push({
    //               status: 'error',
    //               oldPatientId: appointment.patientId,
    //               error: 'Patient not found'
    //             });
    //             continue;
    //           }
    //           // Create appointment with real patient ID
    //           const formattedAppointment = {
    //             ...appointment,
    //             patientId: patient.id
    //           };
    //           const createdAppointment = await this.dataService.createAppointment(formattedAppointment);
    //           // Create appointment details
    //           const appointmentDetails = queryRunner.manager.create(AppointmentDetailsEntity, {
    //             appointmentId: createdAppointment?.id
    //           });
    //           await queryRunner.manager.save(AppointmentDetailsEntity, appointmentDetails);
    //           // Commit transaction
    //           await queryRunner.commitTransaction();
    //           results.push({
    //             status: 'success',
    //             oldPatientId: appointment.patientId,
    //             newPatientId: patient.id,
    //             appointmentId: createdAppointment.id,
    //             date: appointment.date,
    //             detailsId: appointmentDetails.id
    //           });
    //           this.logger.log('Appointment and details created successfully', {
    //             oldPatientId: appointment.patientId,
    //             newPatientId: patient.id,
    //             appointmentId: createdAppointment.id,
    //             detailsId: appointmentDetails.id
    //           });
    //         } catch (error) {
    //           // Rollback transaction on error
    //           await queryRunner.rollbackTransaction();
    //           throw error;
    //         } finally {
    //           // Release query runner
    //           await queryRunner.release();
    //         }
    //       } catch (error) {
    //         this.logger.error('Error processing appointment', {
    //           oldPatientId: appointment.patientId,
    //           error
    //         });
    //       }
    //     }
    // const successCount = results.filter(r => r.status === 'success').length;
    // const errorCount = results.filter(r => r.status === 'error').length;
    // this.logger.log('Bulk appointment migration completed', {
    //   total: appointments.length,
    //   successful: successCount,
    //   failed: errorCount
    // });
    //     return results;
    //   } catch (error) {
    //     this.logger.error('Bulk appointment migration failed', {
    //       error
    //     });
    //     throw new InternalServerErrorException('Failed to process appointments');
    //   }
    // }
    async updateOwnersOpeningBalance(bulkUpdateDto) {
        try {
            this.logger.log('Starting bulk opening balance update', {
                ownerCount: bulkUpdateDto.owners.length
            });
            const results = await this.dataService.bulkUpdateOpeningBalance(bulkUpdateDto.owners);
            const successCount = results.filter(r => r.status === 'success').length;
            const failedCount = results.filter(r => r.status === 'error').length;
            this.logger.log('Bulk opening balance update completed', {
                totalProcessed: results.length,
                successful: successCount,
                failed: failedCount
            });
            return {
                success: true,
                results,
                summary: {
                    total: results.length,
                    successful: successCount,
                    failed: failedCount
                }
            };
        }
        catch (error) {
            this.logger.error('Bulk opening balance update failed', {
                error: error instanceof Error ? error.message : 'Unknown error'
            });
            throw new common_1.InternalServerErrorException('Failed to update opening balances', error instanceof Error ? error.message : undefined);
        }
    }
    async updateMicrochipId(id, updateData) {
        try {
            this.logger.log('Updating patient microchip ID', {
                patientId: id,
                microchipId: updateData.microchipId
            });
            const updatedPatient = await this.dataService.updateMicrochipId(id, updateData.microchipId);
            this.logger.log('Microchip ID updated successfully', {
                patientId: id
            });
            return {
                success: true,
                data: updatedPatient
            };
        }
        catch (error) {
            this.logger.log('Error updating microchip ID', {
                error,
                patientId: id
            });
            throw new common_1.HttpException('Failed to update microchip ID', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.DataController = DataController;
__decorate([
    (0, common_1.Get)('/health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", String)
], DataController.prototype, "getHello", null);
__decorate([
    (0, common_1.Post)('owners'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [data_dto_1.CreateOwnerDto]),
    __metadata("design:returntype", Promise)
], DataController.prototype, "createOwner", null);
__decorate([
    (0, common_1.Post)('patients'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [data_dto_1.CreatePatientDto]),
    __metadata("design:returntype", Promise)
], DataController.prototype, "createPatient", null);
__decorate([
    (0, common_1.Get)('patients/lookup'),
    __param(0, (0, common_1.Query)('clinicId')),
    __param(1, (0, common_1.Query)('dummyData.patientId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], DataController.prototype, "getPatients", null);
__decorate([
    (0, common_1.Post)('patient-owners'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [data_dto_1.CreatePatientOwnerDto]),
    __metadata("design:returntype", Promise)
], DataController.prototype, "createPatientOwner", null);
__decorate([
    (0, common_1.Post)('appointments'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [data_dto_1.CreateAppointmentDto]),
    __metadata("design:returntype", Promise)
], DataController.prototype, "createAppointment", null);
__decorate([
    (0, common_1.Post)('appointment-details'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [data_dto_1.CreateAppointmentDetailsDto]),
    __metadata("design:returntype", Promise)
], DataController.prototype, "createAppointmentDetails", null);
__decorate([
    (0, common_1.Post)('process'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], DataController.prototype, "processInvoices", null);
__decorate([
    (0, common_1.Post)('upload-emr'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)('patientId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], DataController.prototype, "uploadEmr", null);
__decorate([
    (0, common_1.Post)('owners/opening-balance/bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [data_dto_1.BulkUpdateOpeningBalanceDto]),
    __metadata("design:returntype", Promise)
], DataController.prototype, "updateOwnersOpeningBalance", null);
__decorate([
    (0, common_1.Put)('patients/:id/microchip'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], DataController.prototype, "updateMicrochipId", null);
exports.DataController = DataController = __decorate([
    (0, common_1.Controller)('data-migration'),
    __metadata("design:paramtypes", [data_service_1.DataService,
        s3_service_1.S3Service,
        winston_logger_service_1.WinstonLogger,
        typeorm_1.DataSource])
], DataController);
//# sourceMappingURL=data.controller.js.map