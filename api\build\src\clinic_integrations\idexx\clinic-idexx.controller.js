"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicIdexxController = void 0;
const common_1 = require("@nestjs/common");
const clinic_idexx_service_1 = require("./clinic-idexx.service");
const winston_logger_service_1 = require("../../utils/logger/winston-logger.service");
const swagger_1 = require("@nestjs/swagger");
const create_clinic_idexx_dto_1 = require("./dto/create-clinic-idexx.dto");
const create_clinic_idexx_entity_1 = require("./entities/create-clinic-idexx.entity");
const create_clinic_idexx_test_item_dto_1 = require("./dto/create_clinic-idexx-test-item.dto");
const create_idexx_order_dto_1 = require("./dto/create-idexx-order.dto");
const create_idexx_order_dto_2 = require("./dto/create-idexx-order.dto");
const track_method_decorator_1 = require("../../utils/new-relic/decorators/track-method.decorator");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../auth/guards/roles.guard");
const role_enum_1 = require("../../roles/role.enum");
const roles_decorator_1 = require("../../roles/roles.decorator");
let ClinicIdexxController = class ClinicIdexxController {
    constructor(logger, clinicIdexxService) {
        this.logger = logger;
        this.clinicIdexxService = clinicIdexxService;
    }
    createIdexxEntry(createClinicIdexxDto, req) {
        try {
            this.logger.log('Creating new IDEXX entry', {
                dto: createClinicIdexxDto
            });
            return this.clinicIdexxService.createIdexxEntry(createClinicIdexxDto, req.user.brandId);
        }
        catch (error) {
            this.logger.error('Error creating new IDEXX entry', {
                error,
                createClinicIdexxDto
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    getIdexxEntries(clinicId) {
        try {
            this.logger.log('Fetching IDEXX entry list by clinic id', {
                clinicId
            });
            return this.clinicIdexxService.getIdexxEntries(clinicId);
        }
        catch (error) {
            this.logger.error('Error fetching IDEXX entry list by clinic id', {
                error
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
        }
    }
    deletIdexxEntry(clinicIdexxId) {
        try {
            this.logger.log('Deleting IDEXX entry item by clinic id', {
                clinicIdexxId
            });
            return this.clinicIdexxService.deletIdexxEntry(clinicIdexxId);
        }
        catch (error) {
            this.logger.error('Error deleting IDEXX entry item by clinic id', {
                error
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
        }
    }
    getAllIDexxTestsList(clinicId) {
        try {
            this.logger.log('Fetching fetching IDEXX tests list', { clinicId });
            return this.clinicIdexxService.getAllIDexxTestsList(clinicId);
        }
        catch (error) {
            this.logger.error('Error fetching IDEXX tests list', { error });
            throw new common_1.HttpException('Error fetching IDEXX tests list', common_1.HttpStatus.BAD_REQUEST);
        }
    }
    createIdexxTestItem(createClinicIdexxTestItemDto, req) {
        try {
            this.logger.log('Creating IDEXX Test list entry', {
                dto: createClinicIdexxTestItemDto
            });
            return this.clinicIdexxService.createIdexxTestItem(createClinicIdexxTestItemDto, req.user.brandId);
        }
        catch (error) {
            this.logger.error('Error creating IDEXX Test list entry', {
                error
            });
            throw new common_1.HttpException('Error creating IDEXX Test list entry', common_1.HttpStatus.BAD_REQUEST);
        }
    }
    deleteIdexxTestItem(clinicLabReportEntryId) {
        try {
            this.logger.log('Deleting IDEXX test list item by id', {
                clinicLabReportEntryId
            });
            return this.clinicIdexxService.deleteIdexxTestItem(clinicLabReportEntryId);
        }
        catch (error) {
            this.logger.error('Error deleting IDEXX test list item by id', {
                error
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
        }
    }
    async createIdexxOrder(createIdexxOrderDto, req) {
        this.logger.log('Creating an IDEXX order', {
            dto: createIdexxOrderDto
        });
        try {
            const result = await this.clinicIdexxService.createIdexxOrder(createIdexxOrderDto, req.user.brandId);
            // Add detailed logging for the result
            if (result.automationFailed) {
                this.logger.log('IDEXX order creation succeeded but automation failed', {
                    orderId: result.orderId,
                    labReportId: result.labReportId,
                    requiresManualCompletion: true
                });
            }
            else if (result.orderId) {
                this.logger.log('IDEXX order creation and automation succeeded', {
                    orderId: result.orderId,
                    labReportId: result.labReportId
                });
            }
            return result;
        }
        catch (error) {
            this.logger.error('Error creating or automating IDEXX order', {
                error,
                dto: createIdexxOrderDto
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async checkIdexxOrdersDeletion(clinicId, checkIdexxOrdersDeletionDto) {
        try {
            this.logger.log('Checking IDEXX orders deletion eligibility', {
                clinicId,
                labReportIds: checkIdexxOrdersDeletionDto.labReportIds
            });
            return await this.clinicIdexxService.checkIdexxOrdersCanBeDeleted(clinicId, checkIdexxOrdersDeletionDto.labReportIds);
        }
        catch (error) {
            this.logger.error('Error checking IDEXX orders deletion eligibility', {
                error,
                clinicId,
                dto: checkIdexxOrdersDeletionDto
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    cancelIdexxOrder(clinicId, idexxOrderId) {
        try {
            this.logger.log('Cancelling an IDEXX order', {
                clinicId,
                idexxOrderId
            });
            return this.clinicIdexxService.cancelIdexxOrder(clinicId, idexxOrderId);
        }
        catch (error) {
            this.logger.error('Error cancelling new IDEXX order', {
                error
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
};
exports.ClinicIdexxController = ClinicIdexxController;
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Creates a IDEXX entry for a clinic',
        type: create_clinic_idexx_entity_1.CreateClinicIdexxEntity
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, common_1.Post)(),
    (0, track_method_decorator_1.TrackMethod)('createIdexxEntry-clinic-integration'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_clinic_idexx_dto_1.CreateClinicIdexxDto, Object]),
    __metadata("design:returntype", void 0)
], ClinicIdexxController.prototype, "createIdexxEntry", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Gets the list of entries for a clinic',
        type: create_clinic_idexx_entity_1.CreateClinicIdexxEntity,
        isArray: true
    }),
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('getIdexxEntries-clinic-integration'),
    __param(0, (0, common_1.Query)('clinicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ClinicIdexxController.prototype, "getIdexxEntries", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Deletes the IDEXX entry for a clinic id and returns true else returns false'
    }),
    (0, common_1.Delete)(':clinicIdexxId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('deletIdexxEntry-clinic-integration'),
    __param(0, (0, common_1.Param)('clinicIdexxId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ClinicIdexxController.prototype, "deletIdexxEntry", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Gets the list of tests supported by IDEXX'
    }),
    (0, common_1.Get)(':clinicId/testsList'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('getAllIDexxTestsList-clinic-integration'),
    __param(0, (0, common_1.Param)('clinicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ClinicIdexxController.prototype, "getAllIDexxTestsList", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Creates a IDEXX Test list item for a clinic',
        type: create_clinic_idexx_entity_1.CreateClinicIdexxEntity
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, common_1.Post)(':clinicId/create'),
    (0, track_method_decorator_1.TrackMethod)('createIdexxTestItem-clinic-integration'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_clinic_idexx_test_item_dto_1.CreateClinicIdexxTestItemDto, Object]),
    __metadata("design:returntype", void 0)
], ClinicIdexxController.prototype, "createIdexxTestItem", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Deletes the IDEXX Test item entry from the clinic lab reports table id and returns true else returns false'
    }),
    (0, common_1.Delete)('/labReport/:clinicLabReportEntryId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('deleteIdexxTestItem-clinic-integration'),
    __param(0, (0, common_1.Param)('clinicLabReportEntryId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ClinicIdexxController.prototype, "deleteIdexxTestItem", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Creates a IDEXX order'
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, common_1.Post)('/create'),
    (0, track_method_decorator_1.TrackMethod)('createIdexxOrder-clinic-integration'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_idexx_order_dto_1.CreateIdexxOrderDto, Object]),
    __metadata("design:returntype", Promise)
], ClinicIdexxController.prototype, "createIdexxOrder", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Checks if IDEXX orders can be deleted by verifying their status',
        schema: {
            type: 'object',
            properties: {
                canBeDeleted: {
                    type: 'boolean',
                    description: 'Whether all IDEXX orders can be deleted'
                },
                details: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            labReportId: { type: 'string' },
                            idexxOrderId: { type: 'string', nullable: true },
                            status: { type: 'string', nullable: true },
                            canDelete: { type: 'boolean' },
                            error: { type: 'string', nullable: true }
                        }
                    }
                }
            }
        }
    }),
    (0, common_1.Post)('clinic/:clinicId/check-deletion-eligibility'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('checkIdexxOrdersDeletion-clinic-integration'),
    __param(0, (0, common_1.Param)('clinicId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_idexx_order_dto_2.CheckIdexxOrdersDeletionDto]),
    __metadata("design:returntype", Promise)
], ClinicIdexxController.prototype, "checkIdexxOrdersDeletion", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Cancels an IDEXX order'
    }),
    (0, common_1.Delete)('clinic/:clinicId/cancel/:idexxOrderId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('cancelIdexxOrder-clinic-integration'),
    __param(0, (0, common_1.Param)('clinicId')),
    __param(1, (0, common_1.Param)('idexxOrderId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], ClinicIdexxController.prototype, "cancelIdexxOrder", null);
exports.ClinicIdexxController = ClinicIdexxController = __decorate([
    (0, swagger_1.ApiTags)('Clinic Integrations'),
    (0, common_1.Controller)('clinic-integrations'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        clinic_idexx_service_1.ClinicIdexxService])
], ClinicIdexxController);
//# sourceMappingURL=clinic-idexx.controller.js.map