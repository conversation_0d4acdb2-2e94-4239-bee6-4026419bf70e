{"version": 3, "file": "create-dead-letter-queue.js", "sourceRoot": "", "sources": ["../../../../../src/utils/aws/sqs/create-dead-letter-queue.ts"], "names": [], "mappings": ";;AACA,oDAK6B;AAC7B,yDAA0D;AAE1D,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;AAC3C,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa,CAAC;AAClD,kBAAkB;AAClB,qDAAqD;AACrD,MAAM;AAEN,KAAK,UAAU,aAAa;IAC3B,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;IAC1C,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;IACtD,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;IAEvD,IAAI,CAAC,MAAM,IAAI,CAAC,WAAW,IAAI,CAAC,eAAe,EAAE,CAAC;QACjD,MAAM,IAAI,KAAK,CACd,yIAAyI,CACzI,CAAC;IACH,CAAC;IAED,MAAM,SAAS,GAAG,IAAI,sBAAS,CAAC;QAC/B,MAAM;QACN,WAAW,EAAE;YACZ,WAAW;YACX,eAAe;SACf;KACD,CAAC,CAAC;IAEH,MAAM,MAAM,GAAG,IAAA,uCAAoB,EAAC,GAAG,CAAC,CAAC;IACzC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC3C,IAAI,CAAC;YACJ,MAAM,OAAO,GAAG,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAC1D,OAAO,CAAC,GAAG,CAAC,6BAA6B,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,4BAA4B,KAAK,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;IACF,CAAC;AACF,CAAC;AAED,KAAK,UAAU,gBAAgB,CAC9B,SAAoB,EACpB,OAAe;;IAEf,0DAA0D;IAC1D,MAAM,iBAAiB,GAAG,IAAI,8BAAiB,CAAC;QAC/C,eAAe,EAAE,OAAO;KACxB,CAAC,CAAC;IACH,MAAM,YAAY,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC7D,IAAI,YAAY,CAAC,SAAS,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACjE,kCAAkC;QAClC,MAAM,MAAM,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM,oBAAoB,GAAG,IAAI,sCAAyB,CAAC;YAC1D,QAAQ,EAAE,MAAM;YAChB,cAAc,EAAE,CAAC,UAAU,CAAC;SAC5B,CAAC,CAAC;QACH,MAAM,kBAAkB,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAEtE,MAAM,MAAM,GAAG,MAAA,kBAAkB,CAAC,UAAU,0CAAE,QAAQ,CAAC;QACvD,IAAI,CAAC,MAAM,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CACd,kDAAkD,OAAO,EAAE,CAC3D,CAAC;QACH,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,uBAAuB,OAAO,EAAE,CAAC,CAAC;QAC9C,OAAO,MAAM,CAAC;IACf,CAAC;IAED,sCAAsC;IACtC,OAAO,MAAM,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAC5C,CAAC;AAED,KAAK,UAAU,SAAS,CACvB,SAAoB,EACpB,OAAe;;IAEf,MAAM,OAAO,GAAG,IAAI,+BAAkB,CAAC;QACtC,SAAS,EAAE,OAAO;QAClB,UAAU,EAAE;YACX,sBAAsB,EAAE,SAAS,CAAC,sDAAsD;SACxF;KACD,CAAC,CAAC;IAEH,IAAI,CAAC;QACJ,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,OAAO,CAAC,GAAG,CAAC,gBAAgB,OAAO,EAAE,CAAC,CAAC;QAEvC,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,MAAM,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,yCAAyC,OAAO,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,MAAM,oBAAoB,GAAG,IAAI,sCAAyB,CAAC;YAC1D,QAAQ,EAAE,MAAM;YAChB,cAAc,EAAE,CAAC,UAAU,CAAC;SAC5B,CAAC,CAAC;QACH,MAAM,kBAAkB,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAEtE,MAAM,MAAM,GAAG,MAAA,kBAAkB,CAAC,UAAU,0CAAE,QAAQ,CAAC;QACvD,IAAI,CAAC,MAAM,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,yCAAyC,OAAO,EAAE,CAAC,CAAC;QACrE,CAAC;QAED,OAAO,MAAM,CAAC;IACf,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,yBAAyB,OAAO,EAAE,EAAE,KAAK,CAAC,CAAC;QACzD,MAAM,KAAK,CAAC;IACb,CAAC;AACF,CAAC;AAED,aAAa,EAAE;KACb,IAAI,CAAC,GAAG,EAAE;IACV,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;AAC9D,CAAC,CAAC;KACD,KAAK,CAAC,KAAK,CAAC,EAAE;IACd,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;AACzD,CAAC,CAAC,CAAC"}