{"version": 3, "file": "1721818292453-CreatePatientsTable.js", "sourceRoot": "", "sources": ["../../../src/migrations/1721818292453-CreatePatientsTable.ts"], "names": [], "mappings": ";;;AAAA,qCAAiE;AAEjE,MAAa,gCAAgC;IACrC,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,MAAM,WAAW,CAAC,WAAW,CAC5B,IAAI,eAAK,CAAC;YACT,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE;gBACR;oBACC,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,IAAI;oBACf,kBAAkB,EAAE,MAAM;oBAC1B,OAAO,EAAE,oBAAoB;iBAC7B;gBACD;oBACC,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,KAAK;iBACjB;gBACD;oBACC,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,KAAK;oBACX,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC;oBACnC,OAAO,EAAE,WAAW;oBACpB,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,qBAAqB;oBAC3B,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC;oBACtC,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,gBAAgB;oBACtB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,IAAI;oBACb,UAAU,EAAE,KAAK;iBACjB;gBACD;oBACC,IAAI,EAAE,kBAAkB;oBACxB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,KAAK;iBACjB;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;iBAC5B;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;oBAC5B,QAAQ,EAAE,mBAAmB;iBAC7B;aACD;YACD,WAAW,EAAE;gBACZ;oBACC,WAAW,EAAE,CAAC,kBAAkB,CAAC;oBACjC,mBAAmB,EAAE,QAAQ;oBAC7B,qBAAqB,EAAE,CAAC,IAAI,CAAC;oBAC7B,QAAQ,EAAE,SAAS;iBACnB;aACD;SACD,CAAC,EACF,IAAI,CACJ,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,MAAM,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACzC,CAAC;CACD;AAzGD,4EAyGC"}