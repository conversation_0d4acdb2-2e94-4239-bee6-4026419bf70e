export declare enum AnalyticsTimeFrame {
    ONE_DAY = "1D",
    ONE_WEEK = "1W",
    ONE_MONTH = "1M",
    ONE_YEAR = "1Y"
}
export declare enum AnalyticsType {
    REVENUE = "REVENUE",
    APPOINTMENTS = "APPOINTMENTS",
    DOCTOR_PERFORMANCE = "DOCTOR_PERFORMANCE",
    OUTSTANDING_BALANCE = "OUTSTANDING_BALANCE",
    COLLECTED_PAYMENTS = "COLLECTED_PAYMENTS"
}
export declare enum AnalyticsReportType {
    BY_BILLING = "by-billing",
    BY_PATIENT = "by-patient"
}
export declare enum AppointmentAnalyticsType {
    ALL = "All",
    BUSIEST_DAYS = "BusiestDays",
    BUSIEST_HOURS = "BusiestHours",
    AVERAGE_DURATION = "AverageDuration"
}
export declare class GetRevenueChartDataDto {
    startDate: string;
    endDate: string;
    clinicId: string;
}
export interface RevenueChartDataPoint {
    date: string;
    products: number;
    services: number;
    diagnostics: number;
    medications: number;
    vaccinations: number;
}
export interface CollectedPaymentsChartDataPoint {
    date: string;
    cash: number;
    card: number;
    wallet: number;
    cheque: number;
    bankTransfer: number;
}
export declare class DownloadAnalyticsReportDto {
    type: AnalyticsType;
    startDate: string;
    endDate: string;
    clinicId: string;
    reportType?: AnalyticsReportType;
}
export declare class GetAppointmentsChartDataDto {
    startDate: string;
    endDate: string;
    clinicId: string;
    type: AppointmentAnalyticsType;
}
export interface AppointmentsChartDataPoint {
    date: string;
    total?: number;
    missed?: number;
    averageDuration?: number;
}
export interface AppointmentDurationDataPoint {
    date: string;
    checkinDuration: number;
    receivingCareDuration: number;
    checkoutDuration: number;
    totalDuration: number;
}
export interface AppointmentsChartResponse {
    total: AppointmentsChartDataPoint[];
    missed: AppointmentsChartDataPoint[];
    busiestDays?: {
        day: string;
        count: number;
        weeksCount: number;
        total: number;
    }[];
    busiestHours?: {
        hour: string;
        count: number;
        daysCount: number;
        total: number;
    }[];
    averageDuration?: AppointmentDurationDataPoint[];
}
export declare class GetDoctorSummaryDto {
    startDate: string;
    endDate: string;
    clinicId: string;
}
export interface DoctorSummaryResponseDto {
    doctorName: string;
    numAppointments: number;
    totalRevenue: number;
    revenuePerAppointment: number;
    avgAppointmentDurationMinutes: number;
}
export declare class GetSummaryDto {
    startDate: string;
    endDate: string;
    clinicId: string;
}
export interface SummaryResponseDto {
    appointmentsCompleted: number;
    invoicesGenerated: number;
    totalBilling: number;
    creditNotesGenerated: number;
    totalCreditNotes: number;
    amountCollected: {
        cash: number;
        card: number;
        wallet: number;
        cheque: number;
        bankTransfer: number;
        total: number;
    };
    amountRefunded: {
        cash: number;
        card: number;
        wallet: number;
        cheque: number;
        bankTransfer: number;
        total: number;
    };
    badDebts: number;
}
