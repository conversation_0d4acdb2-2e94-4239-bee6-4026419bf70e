"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddColumnToPatientDocumentLibrary1737016067507 = void 0;
const typeorm_1 = require("typeorm");
class AddColumnToPatientDocumentLibrary1737016067507 {
    async up(queryRunner) {
        await queryRunner.addColumn('patient_document_libraries', new typeorm_1.TableColumn({
            name: 'document_body',
            type: 'json',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('patient_document_libraries', 'document_body');
    }
}
exports.AddColumnToPatientDocumentLibrary1737016067507 = AddColumnToPatientDocumentLibrary1737016067507;
//# sourceMappingURL=1737016067507-addColumnToPatientDocumentLibrary.js.map