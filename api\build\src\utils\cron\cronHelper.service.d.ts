import { Repository } from 'typeorm';
import { PatientReminder } from '../../patient-reminders/entities/patient-reminder.entity';
import { SqsService } from '../aws/sqs/sqs.service';
import { AppointmentEntity } from '../../appointments/entities/appointment.entity';
import { WhatsappService } from '../whatsapp-integration/whatsapp.service';
import { SESMailService } from '../aws/ses/send-mail-service';
import { WinstonLogger } from '../logger/winston-logger.service';
import { RedisService } from '../redis/redis.service';
import { AppointmentSessionChange } from '../../socket/appointment-session-changes.entity';
import { AppointmentSessions } from '../../socket/appointment-sessions.entity';
export declare class CronHelperService {
    private readonly reminderRepository;
    private readonly sqsService;
    private appointmentRepository;
    private readonly sessionChangeRepository;
    private readonly appointmentSessionsRepository;
    private readonly mailService;
    private readonly whatsappService;
    private readonly redisService;
    private readonly logger;
    constructor(winstonLogger: <PERSON><PERSON>ogger, reminderRepository: Repository<PatientReminder>, sqsService: SqsService, appointmentRepository: Repository<AppointmentEntity>, sessionChangeRepository: Repository<AppointmentSessionChange>, appointmentSessionsRepository: Repository<AppointmentSessions>, mailService: SESMailService, whatsappService: WhatsappService, redisService: RedisService);
    private getRedisClient;
    private updateReminderTracking;
    private allOwnersNotified;
    sendReminderNotifications(): Promise<void>;
    sendAppointmentMailBy24thHour(): Promise<void>;
    /**
     * Schedule daily availability validation tasks
     * Runs at 2 AM every day to minimize impact on system performance
     */
    scheduleAvailabilityDailyValidation(): Promise<void>;
    /**
     * Schedule weekly availability defragmentation tasks
     * Runs at 3 AM every Sunday to optimize slots for the upcoming week
     */
    scheduleAvailabilityWeeklyDefragmentation(): Promise<void>;
    /**
     * Schedule monthly availability cleanup tasks
     * Runs at 4 AM on the first day of each month
     */
    scheduleAvailabilityMonthlyCleanup(): Promise<void>;
    /**
     * Cleanup stale appointment session changes
     * Runs every 6 hours to remove session changes older than 24 hours
     */
    cleanupStaleAppointmentSessionChanges(): Promise<void>;
    /**
     * Cleanup stale appointment sessions
     * Runs every 4 hours to remove sessions older than 12 hours
     */
    cleanupStaleAppointmentSessions(): Promise<void>;
}
