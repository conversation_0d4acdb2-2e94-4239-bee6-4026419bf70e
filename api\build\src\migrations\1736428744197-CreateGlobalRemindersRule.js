"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateGlobalRemindersRule1736428744197 = void 0;
const typeorm_1 = require("typeorm");
const reminder_trigger_enum_1 = require("../patient-global-reminders/enums/reminder-trigger.enum");
const reminder_enum_1 = require("../patient-reminders/enums/reminder.enum");
class CreateGlobalRemindersRule1736428744197 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: "global_reminder_rules",
            columns: [
                {
                    name: "id",
                    type: "uuid",
                    isPrimary: true,
                    generationStrategy: "uuid",
                    default: "uuid_generate_v4()"
                },
                {
                    name: "clinic_id",
                    type: "uuid"
                },
                {
                    name: "trigger_type",
                    type: "enum",
                    enum: Object.values(reminder_trigger_enum_1.ReminderTriggerType)
                },
                {
                    name: "condition",
                    type: "jsonb",
                    isNullable: false
                },
                {
                    name: "set_reminder_for",
                    type: "jsonb",
                    isNullable: false
                },
                {
                    name: "species_breed_age",
                    type: "jsonb",
                    isNullable: true
                },
                {
                    name: "other_condition",
                    type: "jsonb",
                    isNullable: true
                },
                {
                    name: "recurrence_frequency",
                    type: "integer",
                    isNullable: true
                },
                {
                    name: "recurrence_unit",
                    type: "enum",
                    enum: Object.values(reminder_enum_1.RecurrenceUnit),
                    isNullable: true
                },
                {
                    name: "recurrence_end_date",
                    type: "timestamp",
                    isNullable: true
                },
                {
                    name: "recurrence_end_occurrences",
                    type: "integer",
                    isNullable: true
                },
                {
                    name: "recurrence_end_type",
                    type: "varchar",
                    isNullable: true
                },
                {
                    name: "is_active",
                    type: "boolean",
                    default: true
                },
                {
                    name: "created_by",
                    type: "uuid",
                    isNullable: true
                },
                {
                    name: "created_at",
                    type: "timestamp",
                    default: "CURRENT_TIMESTAMP"
                },
                {
                    name: "updated_at",
                    type: "timestamp",
                    default: "CURRENT_TIMESTAMP",
                    onUpdate: "CURRENT_TIMESTAMP"
                }
            ]
        }), true);
        // Add foreign key for clinic
        await queryRunner.createForeignKey('global_reminder_rules', new typeorm_1.TableForeignKey({
            columnNames: ['clinic_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinics',
            onDelete: 'CASCADE'
        }));
        // Add foreign key for created_by user
        await queryRunner.createForeignKey('global_reminder_rules', new typeorm_1.TableForeignKey({
            columnNames: ['created_by'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'SET NULL'
        }));
        // Add created_from_rule_id column to patient_reminders table
        await queryRunner.addColumn('patient_reminders', new typeorm_1.TableColumn({
            name: 'created_from_rule_id',
            type: 'uuid',
            isNullable: true
        }));
        // Add foreign key for created_from_rule_id
        await queryRunner.createForeignKey('patient_reminders', new typeorm_1.TableForeignKey({
            columnNames: ['created_from_rule_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'global_reminder_rules',
            onDelete: 'SET NULL'
        }));
    }
    async down(queryRunner) {
        const patientRemindersTable = await queryRunner.getTable('patient_reminders');
        const globalRulesTable = await queryRunner.getTable('global_reminder_rules');
        if (patientRemindersTable) {
            const ruleIdForeignKey = patientRemindersTable.foreignKeys.find(fk => fk.columnNames.indexOf('created_from_rule_id') !== -1);
            if (ruleIdForeignKey) {
                await queryRunner.dropForeignKey('patient_reminders', ruleIdForeignKey);
            }
            await queryRunner.dropColumn('patient_reminders', 'created_from_rule_id');
            if (globalRulesTable) {
                const foreignKeys = globalRulesTable.foreignKeys;
                for (const foreignKey of foreignKeys) {
                    await queryRunner.dropForeignKey('global_reminder_rules', foreignKey);
                }
            }
            await queryRunner.dropTable('global_reminder_rules');
        }
    }
}
exports.CreateGlobalRemindersRule1736428744197 = CreateGlobalRemindersRule1736428744197;
//# sourceMappingURL=1736428744197-CreateGlobalRemindersRule.js.map