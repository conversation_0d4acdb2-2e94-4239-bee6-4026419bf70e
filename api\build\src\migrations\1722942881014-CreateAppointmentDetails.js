"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAppointmentDetails1722942881014 = void 0;
const typeorm_1 = require("typeorm");
class CreateAppointmentDetails1722942881014 {
    constructor() {
        this.name = 'CreateAppointmentDetails1722942881014';
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'appointment_details',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'appointment_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'details',
                    type: 'jsonb',
                    isNullable: true
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'now()',
                    onUpdate: 'now()'
                }
            ]
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('appointment_details');
    }
}
exports.CreateAppointmentDetails1722942881014 = CreateAppointmentDetails1722942881014;
//# sourceMappingURL=1722942881014-CreateAppointmentDetails.js.map