import { Patient } from '../../patients/entities/patient.entity';
import { User } from '../../users/entities/user.entity';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';
export declare enum SignatureStatus {
    PENDING = "pending",
    COMPLETED = "completed"
}
export interface TreatmentPlanItem {
    itemId: string;
    itemType: string;
    itemName: string;
    unitPrice: number;
    quantity: number;
    itemTotal: number;
}
export declare class PatientEstimate {
    id: string;
    clinicId: string;
    patientId: string;
    doctorId: string;
    estimateTotal: number;
    signatureRequired: boolean;
    signatureStatus: SignatureStatus;
    treatmentPlan: TreatmentPlanItem[];
    signaturedBy: string;
    fileKey: string;
    documentId: string;
    clinic: ClinicEntity;
    patient: Patient;
    doctor: User;
    createdAt?: Date;
    updatedAt?: Date;
    createdBy?: string;
    updatedBy?: string;
}
