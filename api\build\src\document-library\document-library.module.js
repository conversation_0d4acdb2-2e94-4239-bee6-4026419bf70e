"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentLibraryModule = void 0;
const common_1 = require("@nestjs/common");
const document_library_service_1 = require("./document-library.service");
const document_library_controller_1 = require("./document-library.controller");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const typeorm_1 = require("@nestjs/typeorm");
const document_library_entity_1 = require("./entities/document-library.entity");
const s3_service_1 = require("../utils/aws/s3/s3.service");
const role_module_1 = require("../roles/role.module");
let DocumentLibraryModule = class DocumentLibraryModule {
};
exports.DocumentLibraryModule = DocumentLibraryModule;
exports.DocumentLibraryModule = DocumentLibraryModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([document_library_entity_1.DocumentLibrary]), role_module_1.RoleModule],
        controllers: [document_library_controller_1.DocumentLibraryController],
        providers: [document_library_service_1.DocumentLibraryService, winston_logger_service_1.WinstonLogger, s3_service_1.S3Service]
    })
], DocumentLibraryModule);
//# sourceMappingURL=document-library.module.js.map