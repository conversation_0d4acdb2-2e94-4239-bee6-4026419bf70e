import { Repository } from 'typeorm';
import { Brand } from './entities/brand.entity';
import { CreateBrandDto } from './dto/create-brand.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { BrandWithSettingsDto } from './dto/brand-with-settings.dto';
export declare class BrandService {
    private brandRepository;
    private readonly logger;
    constructor(brandRepository: Repository<Brand>, logger: WinstonLogger);
    createBrand(createBrandDto: CreateBrandDto): Promise<Brand>;
    getAllBrands(page?: number, limit?: number, search?: string, orderBy?: string): Promise<{
        brands: Brand[];
        total: number;
    }>;
    getAllBrandsSimple(): Promise<Brand[]>;
    getBrandById(id: string): Promise<Brand | null>;
    getBrandBySlug(slug: string): Promise<BrandWithSettingsDto | null>;
}
