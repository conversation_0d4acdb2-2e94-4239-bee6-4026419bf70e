"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddBrandIdToClinicUsers1726862786109 = void 0;
const typeorm_1 = require("typeorm");
class AddBrandIdToClinicUsers1726862786109 {
    async up(queryRunner) {
        await queryRunner.addColumn('clinic_users', new typeorm_1.TableColumn({
            name: 'brand_id',
            type: 'uuid',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('clinic_users', 'brand_id');
    }
}
exports.AddBrandIdToClinicUsers1726862786109 = AddBrandIdToClinicUsers1726862786109;
//# sourceMappingURL=1726862786109-UpdateClinicUsersTableBrandId.js.map