"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileBackupService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const winston_logger_service_1 = require("../../utils/logger/winston-logger.service");
const s3_service_1 = require("../../utils/aws/s3/s3.service");
const query_manager_service_1 = require("./query-manager.service");
let FileBackupService = class FileBackupService {
    constructor(dataSource, logger, s3Service, queryManagerService) {
        this.dataSource = dataSource;
        this.logger = logger;
        this.s3Service = s3Service;
        this.queryManagerService = queryManagerService;
    }
    /**
     * Backup all S3 files for a clinic or brand
     */
    async backupS3Files(targetType, targetId, backupId, backupBasePath) {
        const startTime = Date.now();
        // Declare backupPaths outside the try block so it's available in the catch block
        const backupPaths = []; // Track backup paths for rollback
        try {
            this.logger.log('Starting S3 file backup', {
                targetType,
                targetId,
                backupId
            });
            // Get all file references
            const fileReferences = await this.getFileReferences(targetType, targetId);
            const manifest = {
                backupId,
                createdAt: new Date(),
                files: [],
                totalFiles: 0,
                totalSizeBytes: 0
            };
            let filesBackedUp = 0;
            let filesSkipped = 0;
            let totalSizeBytes = 0;
            // Process each file
            for (const fileRef of fileReferences) {
                try {
                    const backupResult = await this.backupSingleFile(fileRef, backupBasePath, backupId);
                    if (backupResult.success) {
                        manifest.files.push({
                            originalPath: fileRef.originalPath,
                            backupPath: backupResult.backupPath,
                            sourceTable: fileRef.sourceTable,
                            sizeBytes: backupResult.sizeBytes,
                            lastModified: backupResult.lastModified,
                            checksum: backupResult.checksum
                        });
                        // Track backup path for potential rollback
                        if (backupResult.backupPath) {
                            backupPaths.push(backupResult.backupPath);
                        }
                        filesBackedUp++;
                        totalSizeBytes += backupResult.sizeBytes || 0;
                    }
                    else {
                        filesSkipped++;
                        this.logger.warn(`Skipped file backup: ${fileRef.originalPath}`, {
                            reason: backupResult.reason
                        });
                    }
                }
                catch (error) {
                    filesSkipped++;
                    const errorMessage = error instanceof Error
                        ? error.message
                        : 'Unknown error';
                    this.logger.error(`Failed to backup file: ${fileRef.originalPath}`, {
                        error: errorMessage
                    });
                }
            }
            manifest.totalFiles = filesBackedUp;
            manifest.totalSizeBytes = totalSizeBytes;
            // Upload manifest to S3
            const manifestPath = `${backupBasePath}/files/file-manifest.json`;
            await this.s3Service.uploadBuffer(Buffer.from(JSON.stringify(manifest, null, 2)), manifestPath, 'application/json');
            // Track manifest path for potential rollback
            backupPaths.push(manifestPath);
            const duration = Date.now() - startTime;
            this.logger.log('S3 file backup completed', {
                backupId,
                totalFiles: fileReferences.length,
                filesBackedUp,
                filesSkipped,
                totalSizeBytes,
                duration
            });
            return {
                manifest,
                totalFiles: fileReferences.length,
                totalSizeBytes,
                filesBackedUp,
                filesSkipped,
                duration
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('S3 file backup failed - initiating rollback', {
                error: errorMessage,
                backupId,
                targetType,
                targetId,
                uploadedFiles: backupPaths.length,
                duration: Date.now() - startTime
            });
            // Rollback: Clean up any uploaded files
            await this.rollbackFileBackup(backupPaths, backupId);
            // Re-throw with enhanced error context
            throw new Error(`File backup failed for ${targetType} ${targetId}: ${errorMessage}`);
        }
    }
    /**
     * Rollback file backup by cleaning up uploaded files
     */
    async rollbackFileBackup(uploadedFiles, backupId) {
        if (uploadedFiles.length === 0) {
            this.logger.log('No files to rollback for file backup', {
                backupId
            });
            return;
        }
        this.logger.warn('Rolling back file backup files', {
            backupId,
            filesToDelete: uploadedFiles.length
        });
        const rollbackErrors = [];
        for (const filePath of uploadedFiles) {
            try {
                await this.s3Service.deleteFile(filePath);
                this.logger.log('Rolled back file backup file', {
                    filePath,
                    backupId
                });
            }
            catch (rollbackError) {
                const rollbackErrorMessage = rollbackError instanceof Error
                    ? rollbackError.message
                    : 'Unknown rollback error';
                rollbackErrors.push(`Failed to delete ${filePath}: ${rollbackErrorMessage}`);
                this.logger.error('Failed to rollback file backup file', {
                    filePath,
                    backupId,
                    error: rollbackErrorMessage
                });
            }
        }
        if (rollbackErrors.length > 0) {
            this.logger.error('File backup rollback completed with errors', {
                backupId,
                errors: rollbackErrors,
                totalErrors: rollbackErrors.length,
                totalFiles: uploadedFiles.length
            });
        }
        else {
            this.logger.log('File backup rollback completed successfully', {
                backupId,
                filesDeleted: uploadedFiles.length
            });
        }
    }
    /**
     * Backup a single file
     */
    async backupSingleFile(fileRef, backupBasePath, _backupId) {
        var _a;
        try {
            // Clean the original path (remove leading slash if present)
            const cleanOriginalPath = fileRef.originalPath;
            // Check if file exists
            const fileExists = await this.s3Service.objectExists(cleanOriginalPath);
            if (!fileExists) {
                return {
                    success: false,
                    reason: 'File does not exist in S3'
                };
            }
            // Get file metadata
            const metadata = await this.s3Service.getObjectMetadata(cleanOriginalPath);
            // Create backup path preserving original structure
            const backupPath = `${backupBasePath}/files/${cleanOriginalPath}`;
            // Copy file to backup location
            await this.s3Service.copyObject(cleanOriginalPath, backupPath);
            return {
                success: true,
                backupPath,
                sizeBytes: metadata.ContentLength || 0,
                lastModified: metadata.LastModified || new Date(),
                checksum: (_a = metadata.ETag) === null || _a === void 0 ? void 0 : _a.replace(/"/g, '') // Remove quotes from ETag
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            return {
                success: false,
                reason: `Backup failed: ${errorMessage}`
            };
        }
    }
    /**
     * Get all file references for a clinic or brand
     */
    async getFileReferences(targetType, targetId) {
        // Get S3 file queries from centralized QueryManager
        const fileQueries = this.queryManagerService.getS3BackupQueries(targetType, targetId);
        const allFileReferences = [];
        for (const [sourceTable, query] of Object.entries(fileQueries)) {
            try {
                const result = await this.dataSource.query(query.sql, query.params);
                const fileRefs = result
                    .filter((row) => row.file_key &&
                    typeof row.file_key === 'string' &&
                    row.file_key.trim() !== '')
                    .map((row) => ({
                    originalPath: row.file_key,
                    sourceTable
                }));
                allFileReferences.push(...fileRefs);
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                this.logger.error(`Error getting file references for ${sourceTable}`, {
                    error: errorMessage
                });
            }
        }
        return allFileReferences;
    }
    /**
     * @deprecated Use QueryManagerService.getS3BackupQueries() instead
     * This method has been moved to centralized QueryManagerService for consistency
     */
    getS3FileQueries(targetType, targetId) {
        // DEPRECATED: This method is no longer used
        // Use this.queryManagerService.getS3BackupQueries() instead
        throw new Error('Deprecated method - use QueryManagerService.getS3BackupQueries() instead');
    }
};
exports.FileBackupService = FileBackupService;
exports.FileBackupService = FileBackupService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource,
        winston_logger_service_1.WinstonLogger,
        s3_service_1.S3Service,
        query_manager_service_1.QueryManagerService])
], FileBackupService);
//# sourceMappingURL=file-backup.service.js.map