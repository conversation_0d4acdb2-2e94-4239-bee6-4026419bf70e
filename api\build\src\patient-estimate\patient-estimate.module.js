"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientEstimateModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const patient_estimate_controller_1 = require("./patient-estimate.controller");
const patient_estimate_service_1 = require("./patient-estimate.service");
const patient_estimate_entity_1 = require("./entities/patient-estimate.entity");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const s3_service_1 = require("../utils/aws/s3/s3.service");
const ses_module_1 = require("../utils/aws/ses/ses.module");
const whatsapp_module_1 = require("../utils/whatsapp-integration/whatsapp.module");
let PatientEstimateModule = class PatientEstimateModule {
};
exports.PatientEstimateModule = PatientEstimateModule;
exports.PatientEstimateModule = PatientEstimateModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([patient_estimate_entity_1.PatientEstimate]),
            ses_module_1.SESModule,
            whatsapp_module_1.WhatsappModule
        ],
        controllers: [patient_estimate_controller_1.PatientEstimateController],
        providers: [patient_estimate_service_1.PatientEstimateService, winston_logger_service_1.WinstonLogger, s3_service_1.S3Service],
        exports: [patient_estimate_service_1.PatientEstimateService]
    })
], PatientEstimateModule);
//# sourceMappingURL=patient-estimate.module.js.map