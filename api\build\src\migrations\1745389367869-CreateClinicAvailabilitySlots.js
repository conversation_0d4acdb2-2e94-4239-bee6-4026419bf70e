"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateClinicAvailabilitySlots1745389367869 = void 0;
class CreateClinicAvailabilitySlots1745389367869 {
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TABLE "clinic_availability_slots" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "clinic_user_id" uuid NOT NULL,
        "date" date NOT NULL,
        "start_time" TIMESTAMPTZ NOT NULL,
        "end_time" TIMESTAMPTZ NOT NULL,
        "is_available" boolean NOT NULL DEFAULT true,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "pk_clinic_availability_slots" PRIMARY KEY ("id")
      )
    `);
        // Add foreign key constraint
        await queryRunner.query(`
      ALTER TABLE "clinic_availability_slots"
      ADD CONSTRAINT "fk_clinic_availability_slots_clinic_user"
      FOREIGN KEY ("clinic_user_id")
      REFERENCES "clinic_users"("id")
      ON DELETE CASCADE
    `);
    }
    async down(queryRunner) {
        // Drop foreign key constraint
        await queryRunner.query(`ALTER TABLE "clinic_availability_slots" DROP CONSTRAINT "fk_clinic_availability_slots_clinic_user"`);
        // Drop the table
        await queryRunner.query(`DROP TABLE "clinic_availability_slots"`);
    }
}
exports.CreateClinicAvailabilitySlots1745389367869 = CreateClinicAvailabilitySlots1745389367869;
//# sourceMappingURL=1745389367869-CreateClinicAvailabilitySlots.js.map