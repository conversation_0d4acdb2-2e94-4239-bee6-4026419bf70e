"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RemoveGSTNumberColumn1727001234567 = void 0;
class RemoveGSTNumberColumn1727001234567 {
    async up(queryRunner) {
        await queryRunner.query(`ALTER TABLE "clinics" DROP COLUMN IF EXISTS "gst_number"`);
    }
    async down(queryRunner) {
        await queryRunner.query(`ALTER TABLE "clinics" ADD COLUMN IF NOT EXISTS "gst_number" character varying`);
    }
}
exports.RemoveGSTNumberColumn1727001234567 = RemoveGSTNumberColumn1727001234567;
//# sourceMappingURL=1727001234567-RemoveGSTNumberColumn.js.map