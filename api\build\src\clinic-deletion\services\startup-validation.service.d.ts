import { OnModuleInit } from '@nestjs/common';
import { QueryManagerService } from './query-manager.service';
import { WinstonLogger } from '../../utils/logger/winston-logger.service';
/**
 * Service that performs startup validation of QueryManagerService consistency
 * Only runs in development environment to provide immediate feedback to developers
 */
export declare class StartupValidationService implements OnModuleInit {
    private readonly queryManagerService;
    private readonly logger;
    constructor(queryManagerService: QueryManagerService, logger: WinstonLogger);
    onModuleInit(): Promise<void>;
    private validateQueryConsistency;
}
