"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateDocumentLibrary = void 0;
const generateDocumentLibrary = ({ clinicName, clinicAddress, clinicPhone, clinicEmail, clinicWebsite, vetName, docDate, digitalSignature, title, bodyText, petName, ownerName, species, breed, color, weight, reproductiveStatus, dob, age }) => {
    return `
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vaccination Certificate</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Qwitcher+Grypen:wght@400;700&display=swap" rel="stylesheet">
    <style>
        @page {
            size: A4;
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            color: #000000;
            font-weight: 300;
            font-size: 12px;
            line-height: 14.52px;
        }

        * {
            box-sizing: border-box;
        }

        p {
            margin: 0;
        }

        .fw-400 {
            font-weight: 400;
        }

        .fw-500 {
            font-weight: 500;
        }

        .text-grey {
            color: #000000;
        }

        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        .pb-16 {
            padding-bottom: 16px;
        }

        .block {
            display: block;
        }

        .vertical_divider {
            border-left: 0.5px solid #BEBDBD;
            margin: 0 10px;
        }

        .invoice-container {
            max-width: 612px;
            margin: 0 auto;
        }

        .invoice-header h1 {
            font-size: 42px;
            font-weight: 100;
            line-height: 48px;
        }

        .invoice-header p {
            font-size: 18px;
            font-weight: 300;
            line-height: 24px;
            display: flex;
        }

        .invoice-header .vaccination-title {
            border-right: 0.3px solid #BEBDBD;
            padding-right: 10px;
            margin-right: 10px;
        }

        .invoice-header .date {
            flex-shrink: 0;
        }

        .invoice-details {
            border-top: 0.3px solid #BEBDBD;
            padding: 25px 0;
        }

        .invoice-details h5 {
            font-size: 18px;
            font-weight: 400;
            line-height: 24px;
            margin: 0;
            color: #000000;
        }

        .invoice-details h6 {
            font-size: 12px;
            font-weight: 400;
            line-height: 14.52px;
            margin: 0;
            color: #000000;
        }

        .invoice-details p {
            font-size: 10px;
            font-weight: 400;
            line-height: 18px;
            color: #000000;
        }

        .invoice-info {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin-top: 6px;
        }

        .invoice-info div:first-child {
            max-width: 170px;
        }

        .invoice-info div:last-child {
            max-width: 145px;
        }

        .vaccination-info {
            padding-bottom: 6px;
            margin: 0 0 13px;
            font-size: 14px;
            font-weight: 400;
            line-height: 16.94px;
            page-break-inside: avoid;
        }

        .vaccination-info:last-child {
            page-break-after: auto;
        }
        
        .vaccination-info-page {
                margin-top: 30px;
            }

        .vaccination-info p {
                margin-bottom: 1em;
        }

        /* Add styles for proper page breaks */
        .vaccination-info {
            break-inside: auto;
            white-space: pre-line;
            word-wrap: break-word;
        }

        .signature-content {
            display: flex;
            gap: 20px;
            margin-top: 40px;
            justify-content: flex-end;
            flex-direction: row;
            width: 100%;
            break-inside: avoid;
        }

        .signature {
           display: flex;
           flex-direction: column;
           align-items: center;
        }

        .simple-border {
            height: 80px;
            width: 280px;
            overflow: fit;
            display: flex;
            justify-content: center;
            align-items: center;
            border-bottom: 2px solid #BEBDBD;
        }

        .patient-info {
            display: flex;
            padding-top: 10px;
            margin-bottom: 16px;
        }

        .patient-info div p:first-child {
            font-size: 10px;
            font-weight: 300;
            line-height: 10.89px;
            margin-bottom: 2px;
            color: #000000;
        }

        .patient-info div p:last-child {
            font-size: 14px;
            font-weight: 400;
            line-height: 16.94px;
            color: #000000;
        }

        .patient-details {
            display: flex;
            flex-wrap: wrap;
            font-size: 12px;
            font-weight: 300;
            line-height: 24px;
            padding-bottom: 28px;
            max-width: 426px;
        }

        .patient-details p {
            border-right: 0.5px solid #BEBDBD;
            padding: 5px 20px 0;
            color: #000000;
            width: 33.3%;
        }

        .patient-details p:nth-child(3n+1) {
            padding-left: 0;
        }

        .patient-details p:nth-child(3n) {
            border: none;
        }

        .signature h6 {
            font-size: 14px;
            font-weight: 400;
            line-height: 16.94px;
            margin: 0px 0 3px
        }

        .signature p {
            font-size: 10px;
            font-weight: 400;
            line-height: 12.1px;
        }

        .font-Ridenation {
            font-size: 55px !important;
            font-family: "Qwitcher Grypen", cursive;
            font-weight: 700;
            font-style: normal;
            color: #504947
        }
        .horitontal-border {
            border-top:  0.3px solid #BEBDBD;
        }
        @page {
        margin: 47px 34px 47px 34px;
        }
        @media print {
            body {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                margin: 0;
            }
        }
    </style>
</head>

<body>
    <div class="invoice-container">
        <div class="invoice-header pb-16">
            <h1>${title}</h1>
        </div>

        <div class="invoice-details" style="border-bottom: 1px solid #BEBDBD; margin-bottom: 20px;">
            <h6>${clinicName}</h6>
            <div class="invoice-info">
                <div>
                    <p>${clinicAddress}</p>
                </div>
                <div>
                    <p>${clinicPhone}</p>
                    <p>
                        <span class="block">${clinicEmail}</span>
                        <span class="block">${clinicWebsite}</span>
                    </p>
                </div>
            </div>
        </div>

        ${petName
        ? `<div class="patient-info">
            <div>
                <p>Name</p>
                <p>${petName}</p>
            </div>
            <div class="vertical_divider"></div>
            <div>
                <p>Assigned Owner</p>
                <p>${ownerName}</p>
            </div>
        </div>
         <div class="patient-details">
               ${species ? `<p>${species}</p>` : ''} 
               ${breed ? `<p>${breed}</p>` : ''} 
               ${color ? `<p>${color}</p>` : ''} 
               ${weight ? `<p>${weight}</p>` : ''} 
               ${reproductiveStatus ? `<p>${reproductiveStatus}</p>` : ''} 
               ${dob ? `<p>${dob}</p>` : ''} 
               ${age ? `<p>${age}</p>` : ''} 
        </div>`
        : ''}

         ${petName ? `<hr class="horitontal-border">` : ''}

        <div class="vaccination-info">
            ${bodyText}
        </div>

      ${digitalSignature ? ` <div class="signature-content">
            <div class="signature">
                <div class="simple-border">
                    ${digitalSignature}
                </div>
                <h6>${vetName}</h6>
                ${docDate ? `<p class='text-grey'>${docDate}</p>` : ''}
            </div>` : ''}
    </div>
</body>

</html>
`;
};
exports.generateDocumentLibrary = generateDocumentLibrary;
//# sourceMappingURL=documentLibrary.js.map