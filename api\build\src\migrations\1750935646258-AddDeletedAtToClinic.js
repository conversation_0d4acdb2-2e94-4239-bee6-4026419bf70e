"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddDeletedAtToClinic1750935646258 = void 0;
const typeorm_1 = require("typeorm");
class AddDeletedAtToClinic1750935646258 {
    constructor() {
        this.name = 'AddDeletedAtToClinic1750935646258';
    }
    async up(queryRunner) {
        await queryRunner.addColumn('clinics', new typeorm_1.TableColumn({
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
            default: null
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('clinics', 'deleted_at');
    }
}
exports.AddDeletedAtToClinic1750935646258 = AddDeletedAtToClinic1750935646258;
//# sourceMappingURL=1750935646258-AddDeletedAtToClinic.js.map