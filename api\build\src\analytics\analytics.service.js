"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AnalyticsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
const payment_details_entity_1 = require("../payment-details/entities/payment-details.entity");
const patient_entity_1 = require("../patients/entities/patient.entity");
const owner_brand_entity_1 = require("../owners/entities/owner-brand.entity");
const analytics_dto_1 = require("./dto/analytics.dto");
const XLSX = require("xlsx");
const appointment_entity_1 = require("../appointments/entities/appointment.entity");
let AnalyticsService = AnalyticsService_1 = class AnalyticsService {
    constructor(invoiceRepository, paymentDetailsRepository, patientRepository, ownerBrandRepository, appointmentRepository) {
        this.invoiceRepository = invoiceRepository;
        this.paymentDetailsRepository = paymentDetailsRepository;
        this.patientRepository = patientRepository;
        this.ownerBrandRepository = ownerBrandRepository;
        this.appointmentRepository = appointmentRepository;
        this.logger = new common_1.Logger(AnalyticsService_1.name);
    }
    async getClinicInfo(clinicId) {
        const query = `
			SELECT 
				name,
				address_line_1 AS "addressLine1",
				address_line_2 AS "addressLine2",
				city,
				state,
				country,
				address_pincode AS pincode
			FROM public.clinics
			WHERE id = $1
		`;
        const result = await this.invoiceRepository.query(query, [clinicId]);
        return result[0];
    }
    async createMetadataSheet(clinicId, startDate, endDate, reportType) {
        // Get clinic information
        const clinicInfo = await this.getClinicInfo(clinicId);
        // Define styles
        const headerStyle = {
            font: { bold: true, sz: 14, color: { rgb: '000000' } },
            fill: { fgColor: { rgb: 'CCCCCC' } },
            border: {
                top: { style: 'medium', color: { rgb: '000000' } },
                bottom: { style: 'medium', color: { rgb: '000000' } },
                left: { style: 'medium', color: { rgb: '000000' } },
                right: { style: 'medium', color: { rgb: '000000' } }
            },
            alignment: { horizontal: 'center' }
        };
        const labelStyle = {
            font: { bold: true, color: { rgb: '000000' } },
            border: {
                top: { style: 'thin', color: { rgb: '000000' } },
                bottom: { style: 'thin', color: { rgb: '000000' } },
                left: { style: 'thin', color: { rgb: '000000' } },
                right: { style: 'thin', color: { rgb: '000000' } }
            },
            alignment: { horizontal: 'left' }
        };
        const valueStyle = {
            border: {
                top: { style: 'thin', color: { rgb: '000000' } },
                bottom: { style: 'thin', color: { rgb: '000000' } },
                left: { style: 'thin', color: { rgb: '000000' } },
                right: { style: 'thin', color: { rgb: '000000' } }
            },
            alignment: { horizontal: 'left' }
        };
        // Create metadata rows with proper styling
        const metadata = [
            [{ v: 'REPORT DETAILS', s: headerStyle }],
            [
                { v: 'Report Type:', s: labelStyle },
                { v: reportType, s: valueStyle }
            ],
            [''],
            [{ v: 'CLINIC INFORMATION', s: headerStyle }],
            [
                { v: 'Clinic Name:', s: labelStyle },
                { v: clinicInfo.name, s: valueStyle }
            ],
            [
                { v: 'Address Line 1:', s: labelStyle },
                { v: clinicInfo.addressLine1, s: valueStyle }
            ],
            [
                { v: 'Address Line 2:', s: labelStyle },
                { v: clinicInfo.addressLine2, s: valueStyle }
            ],
            [
                { v: 'City:', s: labelStyle },
                { v: clinicInfo.city, s: valueStyle }
            ],
            [
                { v: 'State:', s: labelStyle },
                { v: clinicInfo.state, s: valueStyle }
            ],
            [
                { v: 'Country:', s: labelStyle },
                { v: clinicInfo.country, s: valueStyle }
            ],
            [
                { v: 'Pincode:', s: labelStyle },
                { v: clinicInfo.pincode, s: valueStyle }
            ],
            [''],
            [{ v: 'REPORT PARAMETERS', s: headerStyle }],
            [
                { v: 'Date Range:', s: labelStyle },
                {
                    v: `${startDate.getDate()} ${['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'][startDate.getMonth()]} ${startDate.getFullYear()} - ${endDate.getDate()} ${['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'][endDate.getMonth()]} ${endDate.getFullYear()}`,
                    s: valueStyle
                }
            ],
            [
                { v: 'Generated on:', s: labelStyle },
                {
                    v: (() => {
                        const now = new Date();
                        const day = now.getDate();
                        const month = [
                            'January',
                            'February',
                            'March',
                            'April',
                            'May',
                            'June',
                            'July',
                            'August',
                            'September',
                            'October',
                            'November',
                            'December'
                        ][now.getMonth()];
                        const year = now.getFullYear();
                        const hours = now.getHours();
                        const minutes = now
                            .getMinutes()
                            .toString()
                            .padStart(2, '0');
                        const ampm = hours >= 12 ? 'pm' : 'am';
                        const formattedHours = (hours % 12 || 12)
                            .toString()
                            .padStart(2, '0');
                        return `${day} ${month} ${year} at ${formattedHours}:${minutes} ${ampm}`;
                    })(),
                    s: valueStyle
                }
            ]
        ];
        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet(metadata);
        // Set column widths
        ws['!cols'] = [
            { wch: 20 }, // Width for first column (labels)
            { wch: 60 } // Width for second column (values)
        ];
        // Merge cells for headers
        ws['!merges'] = [
            { s: { r: 0, c: 0 }, e: { r: 0, c: 1 } }, // REPORT DETAILS
            { s: { r: 3, c: 0 }, e: { r: 3, c: 1 } }, // CLINIC INFORMATION
            { s: { r: 12, c: 0 }, e: { r: 12, c: 1 } } // REPORT PARAMETERS
        ];
        return ws;
    }
    async createDataSheet(data) {
        if (!data || data.length === 0) {
            // Return an empty worksheet if no data
            const emptyWs = XLSX.utils.json_to_sheet([
                { message: 'No data available' }
            ]);
            return emptyWs;
        }
        // Clean the data to convert null/undefined to empty string for numeric columns
        const cleanedData = data.map(row => {
            const cleanRow = { ...row };
            Object.keys(cleanRow).forEach(key => {
                if (cleanRow[key] === null || cleanRow[key] === undefined) {
                    cleanRow[key] = '';
                }
            });
            return cleanRow;
        });
        // Create worksheet with just the data
        const ws = XLSX.utils.json_to_sheet(cleanedData);
        // Set column widths
        ws['!cols'] = Object.keys(cleanedData[0]).map(() => ({ wch: 15 }));
        // Define styles
        const headerStyle = {
            font: { bold: true, color: { rgb: '000000' } },
            fill: { fgColor: { rgb: 'CCCCCC' } },
            border: {
                top: { style: 'thin', color: { rgb: '000000' } },
                bottom: { style: 'thin', color: { rgb: '000000' } },
                left: { style: 'thin', color: { rgb: '000000' } },
                right: { style: 'thin', color: { rgb: '000000' } }
            },
            alignment: { horizontal: 'center' }
        };
        const numericStyle = {
            border: {
                top: { style: 'thin', color: { rgb: '000000' } },
                bottom: { style: 'thin', color: { rgb: '000000' } },
                left: { style: 'thin', color: { rgb: '000000' } },
                right: { style: 'thin', color: { rgb: '000000' } }
            },
            numFmt: '#,##0.00', // Excel number format
            alignment: { horizontal: 'right' }
        };
        const textStyle = {
            border: {
                top: { style: 'thin', color: { rgb: '000000' } },
                bottom: { style: 'thin', color: { rgb: '000000' } },
                left: { style: 'thin', color: { rgb: '000000' } },
                right: { style: 'thin', color: { rgb: '000000' } }
            },
            alignment: { horizontal: 'left' }
        };
        // Get the range of the worksheet
        const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
        // Style header row and identify numeric columns
        const headers = Object.keys(cleanedData[0]);
        const numericColumns = new Set();
        headers.forEach((header, idx) => {
            // Style header
            const cellRef = XLSX.utils.encode_cell({ r: 0, c: idx });
            if (!ws[cellRef])
                ws[cellRef] = { v: header };
            ws[cellRef].s = headerStyle;
            // Check if this column contains numeric data
            const isNumericColumn = cleanedData.some(row => {
                const value = row[header];
                return (value !== '' &&
                    (typeof value === 'number' ||
                        (typeof value === 'string' &&
                            !isNaN(Number(value)) &&
                            value.trim() !== '')));
            });
            if (isNumericColumn) {
                numericColumns.add(idx);
            }
        });
        // Style data cells and format numbers
        for (let R = 1; R <= range.e.r; ++R) {
            for (let C = 0; C <= range.e.c; ++C) {
                const cellRef = XLSX.utils.encode_cell({ r: R, c: C });
                if (!ws[cellRef])
                    continue;
                if (numericColumns.has(C)) {
                    // Handle empty cells and convert string numbers to actual numbers
                    if (ws[cellRef].v === '') {
                        ws[cellRef].t = 's'; // Set empty cells as string type
                        ws[cellRef].s = numericStyle;
                    }
                    else {
                        if (typeof ws[cellRef].v === 'string') {
                            const numValue = Number(ws[cellRef].v);
                            if (!isNaN(numValue)) {
                                ws[cellRef].v = numValue;
                            }
                        }
                        ws[cellRef].t = 'n'; // Set cell type to number
                        ws[cellRef].s = numericStyle;
                    }
                }
                else {
                    ws[cellRef].s = textStyle;
                }
            }
        }
        return ws;
    }
    async generateReport(dto) {
        try {
            // Adjust dates to include full day
            const startDateTime = new Date(dto.startDate);
            startDateTime.setHours(0, 0, 0, 0);
            const endDateTime = new Date(dto.endDate);
            endDateTime.setHours(23, 59, 59, 999);
            const workbook = XLSX.utils.book_new();
            if (dto.type === analytics_dto_1.AnalyticsType.REVENUE &&
                dto.reportType === analytics_dto_1.AnalyticsReportType.BY_BILLING) {
                // Add metadata sheet first
                try {
                    const metadataWs = await this.createMetadataSheet(dto.clinicId, startDateTime, endDateTime, 'Revenue By Billing Report');
                    XLSX.utils.book_append_sheet(workbook, metadataWs, 'Report Info');
                }
                catch (error) {
                    this.logger.error(`Error creating metadata sheet: ${error.message}`);
                    this.logger.error(error.stack);
                    throw error;
                }
                // Add data sheets with adjusted dates
                try {
                    const productsData = await this.getProductsBillingData(dto.clinicId, startDateTime, endDateTime);
                    if (productsData.length > 0) {
                        const ws = await this.createDataSheet(productsData);
                        XLSX.utils.book_append_sheet(workbook, ws, 'Products');
                    }
                }
                catch (error) {
                    this.logger.error(`Error processing products data: ${error.message}`);
                    this.logger.error(error.stack);
                    throw error;
                }
                try {
                    const servicesData = await this.getServicesBillingData(dto.clinicId, startDateTime, endDateTime);
                    if (servicesData.length > 0) {
                        const ws = await this.createDataSheet(servicesData);
                        XLSX.utils.book_append_sheet(workbook, ws, 'Services');
                    }
                }
                catch (error) {
                    this.logger.error(`Error processing services data: ${error.message}`);
                    this.logger.error(error.stack);
                    throw error;
                }
                try {
                    const vaccinationsData = await this.getVaccinationsBillingData(dto.clinicId, startDateTime, endDateTime);
                    if (vaccinationsData.length > 0) {
                        const ws = await this.createDataSheet(vaccinationsData);
                        XLSX.utils.book_append_sheet(workbook, ws, 'Vaccinations');
                    }
                }
                catch (error) {
                    this.logger.error(`Error processing vaccinations data: ${error.message}`);
                    this.logger.error(error.stack);
                    throw error;
                }
                try {
                    const medicationsData = await this.getMedicationsBillingData(dto.clinicId, startDateTime, endDateTime);
                    if (medicationsData.length > 0) {
                        const ws = await this.createDataSheet(medicationsData);
                        XLSX.utils.book_append_sheet(workbook, ws, 'Medications');
                    }
                }
                catch (error) {
                    this.logger.error(`Error processing medications data: ${error.message}`);
                    this.logger.error(error.stack);
                    throw error;
                }
                try {
                    const labReportsData = await this.getLabReportsBillingData(dto.clinicId, startDateTime, endDateTime);
                    if (labReportsData.length > 0) {
                        const ws = await this.createDataSheet(labReportsData);
                        XLSX.utils.book_append_sheet(workbook, ws, 'Diagnostics');
                    }
                }
                catch (error) {
                    this.logger.error(`Error processing lab reports data: ${error.message}`);
                    this.logger.error(error.stack);
                    throw error;
                }
                // Add Bad Debt (Write-offs) sheet to revenue reports
                try {
                    const badDebtData = await this.getBadDebtData(dto.clinicId, startDateTime, endDateTime);
                    if (badDebtData.length > 0) {
                        const ws = await this.createDataSheet(badDebtData);
                        XLSX.utils.book_append_sheet(workbook, ws, 'Bad Debt (Write-offs)');
                    }
                }
                catch (error) {
                    this.logger.error(`Error processing bad debt data: ${error.message}`);
                    this.logger.error(error.stack);
                    throw error;
                }
            }
            else if (dto.type === analytics_dto_1.AnalyticsType.REVENUE &&
                dto.reportType === analytics_dto_1.AnalyticsReportType.BY_PATIENT) {
                // Add metadata sheet first
                const metadataWs = await this.createMetadataSheet(dto.clinicId, startDateTime, endDateTime, 'Revenue By Patient Report');
                XLSX.utils.book_append_sheet(workbook, metadataWs, 'Report Info');
                // Add patient transactions sheet with adjusted dates
                try {
                    const patientsData = await this.getPatientsBillingData(dto.clinicId, startDateTime, endDateTime);
                    if (patientsData.length > 0) {
                        const ws = await this.createDataSheet(patientsData);
                        XLSX.utils.book_append_sheet(workbook, ws, 'Patient Transactions');
                    }
                }
                catch (error) {
                    this.logger.error(`Error processing patient billing data: ${error.message}`);
                    this.logger.error(error.stack);
                    throw error;
                }
                // Add Bad Debt (Write-offs) sheet to revenue by patient reports
                try {
                    const badDebtData = await this.getBadDebtData(dto.clinicId, startDateTime, endDateTime);
                    if (badDebtData.length > 0) {
                        const ws = await this.createDataSheet(badDebtData);
                        XLSX.utils.book_append_sheet(workbook, ws, 'Bad Debt (Write-offs)');
                    }
                }
                catch (error) {
                    this.logger.error(`Error processing bad debt data: ${error.message}`);
                    this.logger.error(error.stack);
                    throw error;
                }
            }
            else if (dto.type === analytics_dto_1.AnalyticsType.COLLECTED_PAYMENTS &&
                dto.reportType === analytics_dto_1.AnalyticsReportType.BY_PATIENT) {
                // Add summary sheet with payment statistics
                const summaryWs = await this.createPaymentsSummarySheet(dto.clinicId, startDateTime, endDateTime);
                XLSX.utils.book_append_sheet(workbook, summaryWs, 'Payment Summary');
                // Payment modes
                const paymentModes = [
                    'Wallet',
                    'Card',
                    'Cash',
                    'Cheque',
                    'Bank Transfer'
                ];
                // OPTIMIZATION: Fetch all collected payments by mode in a single query
                const collectedPaymentsByMode = await this.getAllPaymentsByMode(dto.clinicId, startDateTime, endDateTime, 'collect');
                // Add collected payments sheets for each payment mode
                for (const mode of paymentModes) {
                    const lowerMode = mode.toLowerCase();
                    const collectedData = collectedPaymentsByMode.get(lowerMode) || [];
                    if (collectedData.length > 0) {
                        const ws = await this.createPaymentModeSheet(collectedData, mode, 'collected');
                        XLSX.utils.book_append_sheet(workbook, ws, `${mode} (Collected)`);
                    }
                }
                // OPTIMIZATION: Fetch all returned payments by mode in a single query
                const returnedPaymentsByMode = await this.getAllPaymentsByMode(dto.clinicId, startDateTime, endDateTime, 'return');
                // Add returned payments sheets for each payment mode
                for (const mode of paymentModes) {
                    const lowerMode = mode.toLowerCase();
                    const returnedData = returnedPaymentsByMode.get(lowerMode) || [];
                    if (returnedData.length > 0) {
                        const ws = await this.createPaymentModeSheet(returnedData, mode, 'returned');
                        XLSX.utils.book_append_sheet(workbook, ws, `${mode} (Returned)`);
                    }
                }
                // Add Bad Debt (Write-offs) sheet
                try {
                    const badDebtData = await this.getBadDebtData(dto.clinicId, startDateTime, endDateTime);
                    if (badDebtData.length > 0) {
                        const ws = await this.createDataSheet(badDebtData);
                        XLSX.utils.book_append_sheet(workbook, ws, 'Bad Debt (Write-offs)');
                    }
                }
                catch (error) {
                    this.logger.error(`Error processing bad debt data: ${error.message}`);
                    this.logger.error(error.stack);
                    throw error;
                }
            }
            // Add collected payments ledger data
            const collectedPaymentsData = await this.getCollectedPaymentsLedgerData(dto.clinicId, startDateTime, endDateTime);
            if (collectedPaymentsData.length > 0) {
                // Create a new workbook
                const ws = XLSX.utils.aoa_to_sheet([]);
                // Add summary line at A1
                const startDateFormatted = startDateTime.toLocaleDateString('en-GB', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric'
                });
                const endDateFormatted = endDateTime.toLocaleDateString('en-GB', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric'
                });
                const summaryText = `A total of ${collectedPaymentsData.length} invoices were issued between ${startDateFormatted} and ${endDateFormatted}`;
                XLSX.utils.sheet_add_aoa(ws, [[summaryText]], {
                    origin: 'A1'
                });
                // Add a blank row at A2
                XLSX.utils.sheet_add_aoa(ws, [[]], { origin: 'A2' });
                // Add data starting at A3 (which will put headers at A3 and data at A4)
                XLSX.utils.sheet_add_json(ws, collectedPaymentsData, {
                    origin: 'A3',
                    skipHeader: false
                });
                // Merge cells for the summary
                if (!ws['!merges'])
                    ws['!merges'] = [];
                ws['!merges'].push({
                    s: { r: 0, c: 0 },
                    e: { r: 0, c: 10 }
                });
                // Set column widths
                ws['!cols'] = [
                    { wch: 20 }, // Patient Name
                    { wch: 20 }, // Owner Name
                    { wch: 25 }, // Owner Email
                    { wch: 15 }, // Owner Phone
                    { wch: 18 }, // Date
                    { wch: 15 }, // Invoice ID
                    { wch: 12 }, // Amount
                    { wch: 20 }, // Invoice Amount Cleared
                    { wch: 15 }, // Pending Balance
                    { wch: 20 } // Balance Pending Since
                ];
                // Style header cells
                // Get the range of header cells (row 3)
                const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
                const headerRow = 2; // 0-indexed, so row 3
                // Create a style for header cells if it doesn't exist
                if (!ws['!styles'])
                    ws['!styles'] = [];
                // Apply styles to header cells
                for (let col = range.s.c; col <= range.e.c; col++) {
                    const cellRef = XLSX.utils.encode_cell({
                        r: headerRow,
                        c: col
                    });
                    if (ws[cellRef]) {
                        ws[cellRef].s = {
                            fill: { fgColor: { rgb: 'FFFF00' } }, // Yellow background
                            font: { bold: true, sz: 12 } // Bold text, larger font
                        };
                    }
                }
                XLSX.utils.book_append_sheet(workbook, ws, 'Invoice Summary');
            }
            // Add returned payments ledger data
            const returnedPaymentsData = await this.getReturnedPaymentsLedgerData(dto.clinicId, startDateTime, endDateTime);
            if (returnedPaymentsData.length > 0) {
                // Create a new workbook
                const ws = XLSX.utils.aoa_to_sheet([]);
                // Add summary line at A1
                const startDateFormatted = startDateTime.toLocaleDateString('en-GB', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric'
                });
                const endDateFormatted = endDateTime.toLocaleDateString('en-GB', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric'
                });
                const summaryText = `A total of ${returnedPaymentsData.length} credit notes were issued between ${startDateFormatted} and ${endDateFormatted}`;
                XLSX.utils.sheet_add_aoa(ws, [[summaryText]], {
                    origin: 'A1'
                });
                // Add a blank row at A2
                XLSX.utils.sheet_add_aoa(ws, [[]], { origin: 'A2' });
                // Add data starting at A3 (which will put headers at A3 and data at A4)
                XLSX.utils.sheet_add_json(ws, returnedPaymentsData, {
                    origin: 'A3',
                    skipHeader: false
                });
                // Merge cells for the summary
                if (!ws['!merges'])
                    ws['!merges'] = [];
                ws['!merges'].push({
                    s: { r: 0, c: 0 },
                    e: { r: 0, c: 6 }
                });
                // Set column widths
                ws['!cols'] = [
                    { wch: 20 }, // Patient Name
                    { wch: 20 }, // Owner Name
                    { wch: 25 }, // Owner Email
                    { wch: 15 }, // Owner Phone
                    { wch: 18 }, // Date
                    { wch: 15 }, // Credit Note ID
                    { wch: 12 } // Amount
                ];
                // Style header cells
                // Get the range of header cells (row 3)
                const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
                const headerRow = 2; // 0-indexed, so row 3
                // Create a style for header cells if it doesn't exist
                if (!ws['!styles'])
                    ws['!styles'] = [];
                // Apply styles to header cells
                for (let col = range.s.c; col <= range.e.c; col++) {
                    const cellRef = XLSX.utils.encode_cell({
                        r: headerRow,
                        c: col
                    });
                    if (ws[cellRef]) {
                        ws[cellRef].s = {
                            fill: { fgColor: { rgb: 'FFFF00' } }, // Yellow background
                            font: { bold: true, sz: 12 } // Bold text, larger font
                        };
                    }
                }
                XLSX.utils.book_append_sheet(workbook, ws, 'Credit Notes Summary');
            }
            // If no data sheets were added (only metadata sheet exists)
            if (workbook.SheetNames.length === 1) {
                const noDataWs = await this.createDataSheet([
                    { message: 'No data found' }
                ]);
                XLSX.utils.book_append_sheet(workbook, noDataWs, 'No Data');
            }
            const buffer = XLSX.write(workbook, {
                type: 'buffer',
                bookType: 'xlsx',
                compression: true
            });
            return buffer;
        }
        catch (error) {
            this.logger.error(`Error in generateReport: ${error.message}`);
            this.logger.error(error.stack);
            throw error;
        }
    }
    async getProductsBillingData(clinicId, startDate, endDate) {
        try {
            const query = `
				WITH invoice_details AS (
					SELECT 
						items.value->>'name' AS product_name,
						CAST(items.value->>'quantity' AS INTEGER) AS quantity,
						CAST(items.value->>'actualPrice' AS NUMERIC) AS actual_price,
						CAST(items.value->>'finalPrice' AS NUMERIC) AS final_price,
						invoices.created_at,
						invoices.id as invoice_id,
						invoices.invoice_type,
						invoices.status,
						invoices.metadata,
						invoices.invoice_amount::numeric as total_invoice_amount
					FROM public.invoices
					CROSS JOIN LATERAL json_array_elements(
						CASE 
							WHEN invoices.details IS NULL THEN '[]'::json
							ELSE invoices.details::json
						END
					) AS items
					INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
						AND items.value->>'itemType' = 'Product'
						AND (items.value->>'isAddedToCart')::boolean = true
						-- Exclude canceled invoices completely
						AND NOT (invoices.invoice_type = 'Invoice' AND invoices.status = 'cancelled')
				),
				writeoff_calculations AS (
					SELECT 
						product_name,
						invoice_type,
						quantity,
						actual_price,
						-- Calculate the proportion of writeoff for this item
						CASE 
							WHEN invoice_type = 'Invoice' AND metadata->>'writeoff' IS NOT NULL THEN
								(
									(actual_price * quantity) / 
									NULLIF(total_invoice_amount, 0)
								) * (metadata->'writeoff'->>'amount')::numeric
							ELSE 0
						END AS item_writeoff_amount
					FROM invoice_details
				)
				SELECT
					SUBSTRING(product_name, 1, 256) AS "Name",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN quantity
						ELSE 0 
					END) AS "Total Quantity Sold",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Revenue",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Returned",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Refund Value",
					SUM(item_writeoff_amount) AS "Total Bad Debt"
				FROM writeoff_calculations
				GROUP BY product_name
				ORDER BY "Total Revenue" DESC
				LIMIT 1000;
			`;
            const result = await this.invoiceRepository.query(query, [
                clinicId,
                startDate,
                endDate
            ]);
            return result;
        }
        catch (error) {
            this.logger.error(`Error in getProductsBillingData: ${error.message}`);
            this.logger.error(error.stack);
            throw error;
        }
    }
    async getServicesBillingData(clinicId, startDate, endDate) {
        try {
            const query = `
				WITH invoice_details AS (
					SELECT 
						items.value->>'name' AS service_name,
						CAST(items.value->>'quantity' AS INTEGER) AS quantity,
						CAST(items.value->>'actualPrice' AS NUMERIC) AS actual_price,
						CAST(items.value->>'finalPrice' AS NUMERIC) AS final_price,
						invoices.created_at,
						invoices.id as invoice_id,
						invoices.invoice_type,
						invoices.status,
						invoices.metadata,
						invoices.invoice_amount::numeric as total_invoice_amount
					FROM public.invoices
					CROSS JOIN LATERAL json_array_elements(
						CASE 
							WHEN invoices.details IS NULL THEN '[]'::json
							ELSE invoices.details::json
						END
					) AS items
					INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
						AND items.value->>'itemType' = 'Service'
						AND (items.value->>'isAddedToCart')::boolean = true
						-- Exclude canceled invoices completely
						AND NOT (invoices.invoice_type = 'Invoice' AND invoices.status = 'cancelled')
				),
				writeoff_calculations AS (
					SELECT 
						service_name,
						invoice_type,
						quantity,
						actual_price,
						-- Calculate the proportion of writeoff for this item
						CASE 
							WHEN invoice_type = 'Invoice' AND metadata->>'writeoff' IS NOT NULL THEN
								(
									(actual_price * quantity) / 
									NULLIF(total_invoice_amount, 0)
								) * (metadata->'writeoff'->>'amount')::numeric
							ELSE 0
						END AS item_writeoff_amount
					FROM invoice_details
				)
				SELECT
					SUBSTRING(service_name, 1, 256) AS "Name",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Sold",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Revenue",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Returned",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Refund Value",
					SUM(item_writeoff_amount) AS "Total Bad Debt"
				FROM writeoff_calculations
				GROUP BY service_name
				ORDER BY "Total Revenue" DESC
				LIMIT 1000;
			`;
            const result = await this.invoiceRepository.query(query, [
                clinicId,
                startDate,
                endDate
            ]);
            return result;
        }
        catch (error) {
            this.logger.error(`Error in getServicesBillingData: ${error.message}`);
            this.logger.error(error.stack);
            throw error;
        }
    }
    async getVaccinationsBillingData(clinicId, startDate, endDate) {
        try {
            const query = `
				WITH invoice_details AS (
					SELECT 
						items.value->>'name' AS product_name,
						CAST(items.value->>'quantity' AS INTEGER) AS quantity,
						CAST(items.value->>'actualPrice' AS NUMERIC) AS actual_price,
						CAST(items.value->>'finalPrice' AS NUMERIC) AS final_price,
						invoices.created_at,
						invoices.id as invoice_id,
						invoices.invoice_type,
						invoices.status,
						invoices.metadata,
						invoices.invoice_amount::numeric as total_invoice_amount
					FROM public.invoices
					CROSS JOIN LATERAL json_array_elements(
						CASE 
							WHEN invoices.details IS NULL THEN '[]'::json
							ELSE invoices.details::json
						END
					) AS items
					INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
						AND items.value->>'itemType' = 'Vaccination'
						AND (items.value->>'isAddedToCart')::boolean = true
						-- Exclude canceled invoices completely
						AND NOT (invoices.invoice_type = 'Invoice' AND invoices.status = 'cancelled')
				),
				writeoff_calculations AS (
					SELECT 
						product_name,
						invoice_type,
						quantity,
						actual_price,
						-- Calculate the proportion of writeoff for this item
						CASE 
							WHEN invoice_type = 'Invoice' AND metadata->>'writeoff' IS NOT NULL THEN
								(
									(actual_price * quantity) / 
									NULLIF(total_invoice_amount, 0)
								) * (metadata->'writeoff'->>'amount')::numeric
							ELSE 0
						END AS item_writeoff_amount
					FROM invoice_details
				)
				SELECT
					SUBSTRING(product_name, 1, 256) AS "Name",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Sold",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Revenue",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Returned",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Refund Value",
					SUM(item_writeoff_amount) AS "Total Bad Debt"
				FROM writeoff_calculations
				GROUP BY product_name
				ORDER BY "Total Revenue" DESC
				LIMIT 1000;
			`;
            const result = await this.invoiceRepository.query(query, [
                clinicId,
                startDate,
                endDate
            ]);
            return result;
        }
        catch (error) {
            this.logger.error(`Error in getVaccinationsBillingData: ${error.message}`);
            this.logger.error(error.stack);
            throw error;
        }
    }
    async getMedicationsBillingData(clinicId, startDate, endDate) {
        try {
            const query = `
				WITH invoice_details AS (
					SELECT 
						items.value->>'name' AS name,
						CAST(items.value->>'quantity' AS INTEGER) AS quantity,
						CAST(items.value->>'actualPrice' AS NUMERIC) AS actual_price,
						CAST(items.value->>'finalPrice' AS NUMERIC) AS final_price,
						invoices.created_at,
						invoices.id as invoice_id,
						invoices.invoice_type,
						invoices.status,
						invoices.metadata,
						invoices.invoice_amount::numeric as total_invoice_amount
					FROM public.invoices
					CROSS JOIN LATERAL json_array_elements(
						CASE 
							WHEN invoices.details IS NULL THEN '[]'::json
							ELSE invoices.details::json
						END
					) AS items
					INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
						AND items.value->>'itemType' = 'Medication'
						AND (items.value->>'isAddedToCart')::boolean = true
						-- Exclude canceled invoices completely
						AND NOT (invoices.invoice_type = 'Invoice' AND invoices.status = 'cancelled')
				),
				writeoff_calculations AS (
					SELECT 
						name,
						invoice_type,
						quantity,
						actual_price,
						-- Calculate the proportion of writeoff for this item
						CASE 
							WHEN invoice_type = 'Invoice' AND metadata->>'writeoff' IS NOT NULL THEN
								(
									(actual_price * quantity) / 
									NULLIF(total_invoice_amount, 0)
								) * (metadata->'writeoff'->>'amount')::numeric
							ELSE 0
						END AS item_writeoff_amount
					FROM invoice_details
				)
				SELECT
					SUBSTRING(name, 1, 256) AS "Name",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Sold",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Revenue",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Returned",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Refund Value",
					SUM(item_writeoff_amount) AS "Total Bad Debt"
				FROM writeoff_calculations
				GROUP BY name
				ORDER BY "Total Revenue" DESC
				LIMIT 1000;
			`;
            const result = await this.invoiceRepository.query(query, [
                clinicId,
                startDate,
                endDate
            ]);
            return result;
        }
        catch (error) {
            this.logger.error(`Error in getMedicationsBillingData: ${error.message}`);
            this.logger.error(error.stack);
            throw error;
        }
    }
    async getLabReportsBillingData(clinicId, startDate, endDate) {
        try {
            const query = `
				WITH invoice_details AS (
					SELECT 
						items.value->>'name' AS name,
						CAST(items.value->>'quantity' AS INTEGER) AS quantity,
						CAST(items.value->>'actualPrice' AS NUMERIC) AS actual_price,
						CAST(items.value->>'finalPrice' AS NUMERIC) AS final_price,
						invoices.created_at,
						invoices.id as invoice_id,
						invoices.invoice_type,
						invoices.status,
						invoices.metadata,
						invoices.invoice_amount::numeric as total_invoice_amount
					FROM public.invoices
					CROSS JOIN LATERAL json_array_elements(
						CASE 
							WHEN invoices.details IS NULL THEN '[]'::json
							ELSE invoices.details::json
						END
					) AS items
					INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
						AND items.value->>'itemType' = 'Labreport'
						AND (items.value->>'isAddedToCart')::boolean = true
						-- Exclude canceled invoices completely
						AND NOT (invoices.invoice_type = 'Invoice' AND invoices.status = 'cancelled')
				),
				writeoff_calculations AS (
					SELECT 
						name,
						invoice_type,
						quantity,
						actual_price,
						-- Calculate the proportion of writeoff for this item
						CASE 
							WHEN invoice_type = 'Invoice' AND metadata->>'writeoff' IS NOT NULL THEN
								(
									(actual_price * quantity) / 
									NULLIF(total_invoice_amount, 0)
								) * (metadata->'writeoff'->>'amount')::numeric
							ELSE 0
						END AS item_writeoff_amount
					FROM invoice_details
				)
				SELECT
					SUBSTRING(name, 1, 256) AS "Name",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Sold",
					SUM(CASE 
						WHEN invoice_type = 'Invoice' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Revenue",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN quantity 
						ELSE 0 
					END) AS "Total Quantity Returned",
					SUM(CASE 
						WHEN invoice_type = 'Refund' THEN actual_price * quantity 
						ELSE 0 
					END) AS "Total Refund Value",
					SUM(item_writeoff_amount) AS "Total Bad Debt"
				FROM writeoff_calculations
				GROUP BY name
				ORDER BY "Total Revenue" DESC
				LIMIT 1000;
			`;
            const result = await this.invoiceRepository.query(query, [
                clinicId,
                startDate,
                endDate
            ]);
            return result;
        }
        catch (error) {
            this.logger.error(`Error in getLabReportsBillingData: ${error.message}`);
            this.logger.error(error.stack);
            throw error;
        }
    }
    async getPatientsBillingData(clinicId, startDate, endDate) {
        const query = `
			WITH filtered_invoices AS (
				SELECT 
					invoices.id,
					invoices.patient_id,
					invoices.owner_id,
					invoices.created_at,
					invoices.invoice_type,
					invoices.status,
					SUM(
						CASE 
							WHEN items.value->>'isAddedToCart' = 'true' THEN 
								CAST(items.value->>'quantity' AS INTEGER) * CAST(items.value->>'actualPrice' AS NUMERIC)
							ELSE 
								0
						END
					) as total_price,
					SUM(
						CASE 
							WHEN items.value->>'isAddedToCart' = 'true' THEN 
								CAST(items.value->>'quantity' AS INTEGER) * (CAST(items.value->>'actualPrice' AS NUMERIC) - CAST(items.value->>'finalPrice' AS NUMERIC) / CAST(items.value->>'quantity' AS NUMERIC))
							ELSE 
								0
						END
					) as total_discount,
					SUM(
						CASE 
							WHEN items.value->>'isAddedToCart' = 'true' THEN 
								CAST(items.value->>'taxedAmount' AS NUMERIC)
							ELSE 
								0
						END
					) as total_tax,
					SUM(
						CASE 
							WHEN items.value->>'isAddedToCart' = 'true' THEN 
								CAST(items.value->>'finalPrice' AS NUMERIC)
							ELSE 
								0
						END
					) as amount_payable
				FROM public.invoices
				CROSS JOIN LATERAL json_array_elements(
					CASE 
						WHEN invoices.details IS NULL THEN '[]'::json
						ELSE invoices.details::json
					END
				) AS items
				WHERE invoices.clinic_id = $1
					AND invoices.created_at BETWEEN $2 AND $3
					-- Exclude canceled invoices completely
					AND NOT (invoices.invoice_type = 'Invoice' AND invoices.status = 'cancelled')
				GROUP BY 
					invoices.id,
					invoices.patient_id,
					invoices.owner_id,
					invoices.created_at,
					invoices.invoice_type,
					invoices.status
			)
			SELECT 
				patients.patient_name AS "Patient Name",
				ob.first_name AS "Owner First Name",
					ob.last_name AS "Owner Last Name",
				go.phone_number AS "Owner Phone Number",
				ob.email AS "Owner Email",
				TO_CHAR(fi.created_at, 'DD/MM/YY') AS "Transaction Date",
				fi.invoice_type AS "Transaction Type",
				CASE 
					WHEN fi.invoice_type = 'Refund' THEN -fi.total_price::numeric
					ELSE fi.total_price::numeric
				END AS "Total Price",
				CASE 
					WHEN fi.invoice_type = 'Refund' THEN -fi.total_discount::numeric
					ELSE fi.total_discount::numeric
				END AS "Total Discount",
				CASE 
					WHEN fi.invoice_type = 'Refund' THEN -fi.total_tax::numeric
					ELSE fi.total_tax::numeric
				END AS "Total Tax",
				CASE 
					WHEN fi.invoice_type = 'Refund' THEN -fi.amount_payable::numeric
					ELSE fi.amount_payable::numeric
				END AS "Total Value"
			FROM filtered_invoices fi
			INNER JOIN public.patients
				ON fi.patient_id = patients.id
			INNER JOIN public.patient_owners
				ON patients.id = patient_owners.patient_id
			INNER JOIN public.owner_brands ob
				ON patient_owners.owner_id = ob.id
			INNER JOIN public.global_owners go
				ON ob.global_owner_id = go.id
			WHERE fi.amount_payable > 0
			ORDER BY 
				fi.created_at DESC,
				patients.patient_name ASC;
		`;
        const results = await this.invoiceRepository.query(query, [
            clinicId,
            startDate,
            endDate
        ]);
        return results.map((row) => ({
            'Patient Name': row['Patient Name'],
            'Owner First Name': row['Owner First Name'],
            'Owner Last Name': row['Owner Last Name'],
            'Owner Phone Number': row['Owner Phone Number'],
            'Owner Email': row['Owner Email'],
            'Transaction Date': row['Transaction Date'],
            'Transaction Type': row['Transaction Type'],
            'Total Price': Number(row['Total Price'] || 0).toFixed(2),
            'Total Discount': Number(row['Total Discount'] || 0).toFixed(2),
            'Total Tax': Number(row['Total Tax'] || 0).toFixed(2),
            'Total Value': Number(row['Total Value'] || 0).toFixed(2)
        }));
    }
    async getBadDebtData(clinicId, startDate, endDate) {
        try {
            const query = `
				SELECT 
					COALESCE(p.patient_name, '') AS "Patient Name",
					CONCAT(ob.first_name, ' ', ob.last_name) AS "Owner Name",
					ob.email AS "Owner Email",
					go.phone_number AS "Owner Phone",
					TO_CHAR(i.created_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'DD/MM/YY') AS "Invoice Date",
					i.reference_alpha_id AS "Invoice ID",
					i.invoice_amount::numeric AS "Total Invoice Amount",
					COALESCE(i.paid_amount::numeric, 0) AS "Amount Paid",
					CASE 
						WHEN i.metadata->>'writeoff' IS NOT NULL THEN 
							(i.metadata->'writeoff'->>'amount')::numeric
						ELSE 0
					END AS "Write-off Amount",
					CASE 
						WHEN i.metadata->>'writeoff' IS NOT NULL THEN 
							TO_CHAR((i.metadata->'writeoff'->>'date')::timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'DD/MM/YY')
						ELSE ''
					END AS "Write-off Date",
					CASE 
						WHEN i.metadata->>'writeoff' IS NOT NULL THEN 
							COALESCE(i.metadata->'writeoff'->>'reason', '')
						ELSE ''
					END AS "Write-off Reason",
					CASE 
						WHEN i.metadata->>'writeoff' IS NOT NULL THEN 
							COALESCE(i.metadata->'writeoff'->>'by', '')
						ELSE ''
					END AS "Write-off By"
				FROM public.invoices i
				LEFT JOIN public.patients p ON i.patient_id = p.id
				JOIN public.owner_brands ob ON i.owner_id = ob.id
				JOIN public.global_owners go ON ob.global_owner_id = go.id
				WHERE i.invoice_type = 'Invoice'
				AND i.clinic_id = $1
				AND i.deleted_at IS NULL
				AND (
					i.status = 'written_off'
					OR (
						i.metadata->>'writeoff' IS NOT NULL 
						AND (i.metadata->'writeoff'->>'amount')::numeric > 0
					)
				)
				AND (
					-- Filter by write-off date if writeoff metadata exists
					CASE 
						WHEN i.metadata->>'writeoff' IS NOT NULL THEN 
							(i.metadata->'writeoff'->>'date')::timestamp BETWEEN $2 AND $3
						ELSE 
							-- Fallback to invoice creation date for old data without writeoff date
							i.created_at BETWEEN $2 AND $3
					END
				)
				ORDER BY COALESCE((i.metadata->'writeoff'->>'date')::timestamp, i.created_at) DESC;
			`;
            const results = await this.invoiceRepository.query(query, [
                clinicId,
                startDate,
                endDate
            ]);
            return results.map((row) => ({
                'Patient Name': row['Patient Name'] || '',
                'Owner Name': row['Owner Name'] || '',
                'Owner Email': row['Owner Email'] || '',
                'Owner Phone': row['Owner Phone'] || '',
                'Invoice Date': row['Invoice Date'] || '',
                'Invoice ID': row['Invoice ID'] || '',
                'Total Invoice Amount': Number(row['Total Invoice Amount'] || 0),
                'Amount Paid': Number(row['Amount Paid'] || 0),
                'Write-off Amount': Number(row['Write-off Amount'] || 0),
                'Write-off Date': row['Write-off Date'] || '',
                'Write-off Reason': row['Write-off Reason'] || '',
                'Write-off By': row['Write-off By'] || ''
            }));
        }
        catch (error) {
            this.logger.error(`Error in getBadDebtData: ${error.message}`);
            this.logger.error(error.stack);
            throw error;
        }
    }
    async getCollectedPaymentsLedgerData(clinicId, startDate, endDate) {
        const query = `
			SELECT 
				COALESCE(p.patient_name, '') AS "Patient Name",
				CONCAT(ob.first_name, ' ', ob.last_name) AS "Owner Name",
				ob.email AS "Owner Email",
				go.phone_number AS "Owner Phone",
				TO_CHAR(i.created_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'DD/MM/YY HH24:MI:SS') AS "Date",
				i.reference_alpha_id AS "Invoice ID",
				i.invoice_amount::numeric AS "Amount",
				CASE 
					WHEN i.status = 'unknown' THEN NULL
					ELSE i.paid_amount::numeric
				END AS "Invoice Amount Cleared",
				CASE 
					WHEN i.status = 'unknown' THEN NULL
					ELSE i.balance_due::numeric
				END AS "Pending Balance",
				CASE 
					WHEN i.balance_due::numeric > 0 THEN 
						TO_CHAR(i.updated_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'DD/MM/YY')
					ELSE ''
				END AS "Balance Pending Since"
			FROM public.invoices i
			LEFT JOIN public.patients p ON i.patient_id = p.id
			JOIN public.owner_brands ob ON i.owner_id = ob.id
			JOIN public.global_owners go ON ob.global_owner_id = go.id
			WHERE i.invoice_type = 'Invoice'
			AND i.created_at BETWEEN $2 AND $3
			AND i.clinic_id = $1
			AND i.deleted_at IS NULL
			AND i.status != 'cancelled'  -- Exclude canceled invoices
			AND (i.metadata->>'source' IS NULL OR i.metadata->>'source' != 'old_balance_migration')
			ORDER BY i.created_at DESC;
		`;
        const results = await this.invoiceRepository.query(query, [
            clinicId,
            startDate,
            endDate
        ]);
        return results.map((row) => ({
            'Patient Name': row['Patient Name'] || '',
            'Owner Name': row['Owner Name'] || '',
            'Owner Email': row['Owner Email'] || '',
            'Owner Phone': row['Owner Phone'] || '',
            Date: row['Date'] || '',
            'Invoice ID': row['Invoice ID'] || '',
            Amount: Number(row['Amount'] || 0),
            'Invoice Amount Cleared': row['Invoice Amount Cleared'] === null
                ? '-'
                : Number(row['Invoice Amount Cleared'] || 0),
            'Pending Balance': row['Pending Balance'] === null
                ? '-'
                : Number(row['Pending Balance'] || 0),
            'Balance Pending Since': row['Balance Pending Since'] || ''
        }));
    }
    async getReturnedPaymentsLedgerData(clinicId, startDate, endDate) {
        const query = `
			SELECT 
				COALESCE(p.patient_name, '') AS "Patient Name",
				CONCAT(ob.first_name, ' ', ob.last_name) AS "Owner Name",
				ob.email AS "Owner Email",
				go.phone_number AS "Owner Phone",
				TO_CHAR(i.created_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'DD/MM/YY HH24:MI:SS') AS "Date",
				i.reference_alpha_id AS "Credit Note ID",
				i.invoice_amount::numeric AS "Amount"
			FROM public.invoices i
			LEFT JOIN public.patients p ON i.patient_id = p.id
			JOIN public.owner_brands ob ON i.owner_id = ob.id
			JOIN public.global_owners go ON ob.global_owner_id = go.id
			WHERE i.invoice_type = 'Refund'
			AND i.created_at BETWEEN $2 AND $3
			AND i.clinic_id = $1
			AND i.deleted_at IS NULL
			ORDER BY i.created_at DESC;
		`;
        const results = await this.invoiceRepository.query(query, [
            clinicId,
            startDate,
            endDate
        ]);
        return results.map((row) => ({
            'Patient Name': row['Patient Name'] || '',
            'Owner Name': row['Owner Name'] || '',
            'Owner Email': row['Owner Email'] || '',
            'Owner Phone': row['Owner Phone'] || '',
            Date: row['Date'] || '',
            'Credit Note ID': row['Credit Note ID'] || '',
            Amount: Number(row['Amount'] || 0)
        }));
    }
    async createPaymentsSummarySheet(clinicId, startDate, endDate) {
        // Get clinic information
        const clinicInfo = await this.getClinicInfo(clinicId);
        // Get payment stats
        const paymentStats = await this.getPaymentSummaryStats(clinicId, startDate, endDate);
        // Create the data array for the summary sheet
        const data = [];
        // REPORT DETAILS
        data.push(['REPORT DETAILS', '', '']);
        data.push(['Report Type:', 'Collected Payments Report', '']);
        data.push(['', '', '']);
        // CLINIC INFORMATION
        data.push(['CLINIC INFORMATION', '', '']);
        data.push(['Clinic Name:', clinicInfo.name, '']);
        data.push(['Address Line 1:', clinicInfo.addressLine1, '']);
        data.push(['Address Line 2:', clinicInfo.addressLine2, '']);
        data.push(['City:', clinicInfo.city, '']);
        data.push(['State:', clinicInfo.state, '']);
        data.push(['Country:', clinicInfo.country, '']);
        data.push(['Pincode:', clinicInfo.pincode, '']);
        data.push(['', '', '']);
        // REPORT PARAMETERS
        data.push(['REPORT PARAMETERS', '', '']);
        data.push([
            'Date Range:',
            `${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}`,
            ''
        ]);
        data.push(['Generated At:', new Date().toLocaleString(), '']);
        data.push(['', '', '']);
        // PAYMENTS COLLECTED
        data.push(['PAYMENTS COLLECTED', '', '']);
        // Payment modes for display
        const paymentModes = [
            { key: 'wallet', label: 'wallet' },
            { key: 'card', label: 'card' },
            { key: 'cash', label: 'cash' },
            { key: 'cheque', label: 'cheque' },
            { key: 'bank_transfer', label: 'bank transfer' }
        ];
        let totalCollected = 0;
        let totalCollectedTransactions = 0;
        // Add collected payments by mode
        for (const mode of paymentModes) {
            const collectedAmount = paymentStats.collectedByMode[mode.key];
            const collectedCount = paymentStats.collectedCountByMode[mode.key];
            data.push([
                `Total amount collected through ${mode.label}`,
                collectedAmount,
                `${collectedCount} Transactions`
            ]);
            totalCollected += collectedAmount;
            totalCollectedTransactions += collectedCount;
        }
        // Add total collected
        data.push([
            'Total Payments Collected',
            totalCollected,
            `${totalCollectedTransactions} Transactions`
        ]);
        data.push(['', '', '']);
        // PAYMENTS RETURNED
        data.push(['PAYMENTS RETURNED', '', '']);
        let totalReturned = 0;
        let totalReturnedTransactions = 0;
        // Add returned payments by mode
        for (const mode of paymentModes) {
            const returnedAmount = paymentStats.returnedByMode[mode.key];
            const returnedCount = paymentStats.returnedCountByMode[mode.key];
            data.push([
                `Total amount returned through ${mode.label}`,
                returnedAmount,
                `${returnedCount} Transactions`
            ]);
            totalReturned += returnedAmount;
            totalReturnedTransactions += returnedCount;
        }
        // Add total returned
        data.push([
            'Total Payments Returned',
            totalReturned,
            `${totalReturnedTransactions} Transactions`
        ]);
        data.push(['', '', '']);
        // NET AMOUNT
        data.push(['NET AMOUNT', '', '']);
        let totalNet = 0;
        let totalNetTransactions = 0;
        // Add net amounts by mode
        for (const mode of paymentModes) {
            const collectedAmount = paymentStats.collectedByMode[mode.key];
            const returnedAmount = paymentStats.returnedByMode[mode.key];
            const netAmount = collectedAmount - returnedAmount;
            const collectedCount = paymentStats.collectedCountByMode[mode.key];
            const returnedCount = paymentStats.returnedCountByMode[mode.key];
            const netCount = collectedCount + returnedCount;
            data.push([
                `Net amount through ${mode.label}`,
                netAmount,
                `${netCount} Transactions`
            ]);
            totalNet += netAmount;
            totalNetTransactions += netCount;
        }
        // Add total net
        data.push([
            'Total (net)',
            totalNet,
            `${totalNetTransactions} Transactions`
        ]);
        // Add extra empty rows at the end
        data.push(['', '', '']);
        data.push(['', '', '']);
        data.push(['', '', '']);
        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet(data);
        // Set column widths
        ws['!cols'] = [
            { wch: 40 }, // Column A
            { wch: 20 }, // Column B
            { wch: 20 } // Column C
        ];
        // Apply formatting to cells
        for (let i = 0; i < data.length; i++) {
            // Format section headers (rows that have content in column A but not B or C)
            if (data[i][0] && !data[i][1] && !data[i][2]) {
                const cellRef = XLSX.utils.encode_cell({ r: i, c: 0 });
                if (!ws[cellRef].s)
                    ws[cellRef].s = {};
                ws[cellRef].s = {
                    font: { bold: true, sz: 12 },
                    fill: { fgColor: { rgb: 'DDDDDD' } } // Light gray background
                };
            }
            // Format amounts in column B (only for rows that have numeric values)
            if (typeof data[i][1] === 'number') {
                const cellRef = XLSX.utils.encode_cell({ r: i, c: 1 });
                if (!ws[cellRef].s)
                    ws[cellRef].s = {};
                ws[cellRef].s = { numFmt: '#,##0.00' }; // Format as currency
            }
            // Bold the total rows
            if (data[i][0]) {
                const cellText = String(data[i][0]);
                if (cellText.startsWith('Total Payments Collected') ||
                    cellText.startsWith('Total Payments Returned') ||
                    cellText.startsWith('Total (net)')) {
                    for (let j = 0; j < 3; j++) {
                        const cellRef = XLSX.utils.encode_cell({ r: i, c: j });
                        if (ws[cellRef]) {
                            if (!ws[cellRef].s)
                                ws[cellRef].s = {};
                            ws[cellRef].s.font = { bold: true };
                        }
                    }
                }
            }
        }
        return ws;
    }
    async getPaymentSummaryStats(clinicId, startDate, endDate) {
        // SQL query to get payment statistics grouped by mode
        const query = `
			SELECT
				payment_type,
				type,
				 SUM(amount::numeric) as total_amount,
				COUNT(*) as transaction_count
			FROM public.payment_details pd
			WHERE pd.clinic_id = $1
			AND pd.created_at BETWEEN $2 AND $3
			AND pd.deleted_at IS NULL
			GROUP BY payment_type, type
		`;
        const results = await this.paymentDetailsRepository.query(query, [
            clinicId,
            startDate,
            endDate
        ]);
        // Initialize objects to store payment stats with proper typing
        const collectedByMode = {
            wallet: 0,
            card: 0,
            cash: 0,
            cheque: 0,
            bank_transfer: 0
        };
        const collectedCountByMode = {
            wallet: 0,
            card: 0,
            cash: 0,
            cheque: 0,
            bank_transfer: 0
        };
        const returnedByMode = {
            wallet: 0,
            card: 0,
            cash: 0,
            cheque: 0,
            bank_transfer: 0
        };
        const returnedCountByMode = {
            wallet: 0,
            card: 0,
            cash: 0,
            cheque: 0,
            bank_transfer: 0
        };
        // Process the results and populate the stats objects
        for (const row of results) {
            const paymentType = (row.payment_type || '').toLowerCase();
            const type = row.type;
            const amount = Number(row.total_amount);
            const count = Number(row.transaction_count);
            // Map payment types to our standard keys
            let mappedType;
            if (paymentType === 'wallet') {
                mappedType = 'wallet';
            }
            else if (paymentType === 'card') {
                mappedType = 'card';
            }
            else if (paymentType === 'cash') {
                mappedType = 'cash';
            }
            else if (paymentType === 'cheque') {
                mappedType = 'cheque';
            }
            else if (paymentType === 'bank transfer') {
                mappedType = 'bank_transfer';
            }
            // Only proceed if we have a valid payment mode
            if (mappedType) {
                // Check if it's a collected payment or a returned payment
                if ([
                    'Invoice',
                    'Reconcile Invoice',
                    'Bulk Reconcile Invoice',
                    'Collect'
                ].includes(type)) {
                    // Collected payment
                    collectedByMode[mappedType] += amount;
                    collectedCountByMode[mappedType] += count;
                }
                else if (['Credit Note', 'Return'].includes(type)) {
                    // Returned payment
                    returnedByMode[mappedType] += amount;
                    returnedCountByMode[mappedType] += count;
                }
            }
        }
        // Calculate totals
        const totalCollected = Object.values(collectedByMode).reduce((sum, val) => sum + val, 0);
        const totalCollectedCount = Object.values(collectedCountByMode).reduce((sum, val) => sum + val, 0);
        const totalReturned = Object.values(returnedByMode).reduce((sum, val) => sum + val, 0);
        const totalReturnedCount = Object.values(returnedCountByMode).reduce((sum, val) => sum + val, 0);
        return {
            collectedByMode,
            collectedCountByMode,
            returnedByMode,
            returnedCountByMode,
            totalCollected,
            totalCollectedCount,
            totalReturned,
            totalReturnedCount
        };
    }
    async getOwnerSummaryData(clinicId, startDate, endDate) {
        this.logger.log('Getting owner summary data...');
        const query = `
			WITH FilteredOwners AS (
				SELECT 
					ob.id as owner_id,
					ob.first_name,
					ob.last_name,
					ob.email,
					ob.owner_balance,
					go.phone_number
				FROM public.owner_brands ob
				JOIN public.global_owners go ON ob.global_owner_id = go.id
				WHERE EXISTS (
					SELECT 1
					FROM public.patient_owners po
					JOIN public.patients p ON po.patient_id = p.id
					WHERE po.owner_id = ob.id
					AND p.clinic_id = $1
				)
				AND ob.updated_at BETWEEN $2 AND $3
			),
			OwnerPets AS (
				SELECT 
					fo.owner_id,
					COUNT(DISTINCT po.patient_id) as pet_count
				FROM FilteredOwners fo
				LEFT JOIN public.patient_owners po ON fo.owner_id = po.owner_id
				GROUP BY fo.owner_id
			)
			SELECT 
				fo.first_name AS "Owner First Name",
				fo.last_name AS "Owner Last Name",
				COALESCE(op.pet_count, 0) AS "Number of Pets",
				fo.email AS "Owner Email",
				fo.phone_number AS "Owner Phone",
				fo.owner_balance AS "Total Current Balance"
			FROM FilteredOwners fo
			LEFT JOIN OwnerPets op ON fo.owner_id = op.owner_id
			ORDER BY fo.first_name ASC, fo.last_name ASC;
		`;
        const results = await this.ownerBrandRepository.query(query, [
            clinicId,
            startDate,
            endDate
        ]);
        return results.map((row) => ({
            'Owner First Name': row['Owner First Name'],
            'Owner Last Name': row['Owner Last Name'],
            'Number of Pets': Number(row['Number of Pets']),
            'Owner Email': row['Owner Email'],
            'Owner Phone': row['Owner Phone'],
            'Total Current Balance': Number(row['Total Current Balance'])
        }));
    }
    async getRevenueChartData(dto) {
        try {
            // Convert dates to start and end of day
            const startDateTime = new Date(dto.startDate);
            startDateTime.setHours(0, 0, 0, 0);
            const endDateTime = new Date(dto.endDate);
            endDateTime.setHours(23, 59, 59, 999);
            // Calculate date difference in days
            const diffTime = Math.abs(endDateTime.getTime() - startDateTime.getTime());
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            // Determine grouping based on date difference
            let grouping;
            let dateFormat;
            let interval;
            let truncPart;
            if (diffDays <= 35) {
                grouping = 'DATE(invoices.created_at)';
                dateFormat = 'YYYY-MM-DD';
                interval = '1 day';
                truncPart = 'day';
            }
            else if (diffDays <= 210) {
                grouping = "DATE_TRUNC('week', invoices.created_at)";
                dateFormat = 'YYYY-"W"IW';
                interval = '1 week';
                truncPart = 'week';
            }
            else if (diffDays <= 730) {
                grouping = "DATE_TRUNC('month', invoices.created_at)";
                dateFormat = 'YYYY-MM';
                interval = '1 month';
                truncPart = 'month';
            }
            else {
                grouping = "DATE_TRUNC('year', invoices.created_at)";
                dateFormat = 'YYYY';
                interval = '1 year';
                truncPart = 'year';
            }
            const query = `
				WITH dates AS (
					SELECT generate_series(
						DATE_TRUNC($4, $2::timestamp),
						DATE_TRUNC($4, $3::timestamp),
						$5::interval
					)::date AS date
				),
				products_revenue AS (
					SELECT 
						${grouping} as date,
						SUM(
							CASE 
								WHEN invoices.invoice_type = 'Invoice' AND invoices.status NOT IN ('cancelled') AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Product' AND (items.value->>'isAddedToCart')::boolean = true
								)
								WHEN invoices.invoice_type = 'Refund' AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(-SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Product' AND (items.value->>'isAddedToCart')::boolean = true
								)
								ELSE 0
							END
						) as revenue
					FROM public.invoices
						INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
					GROUP BY ${grouping}
				),
				services_revenue AS (
					SELECT 
						${grouping} as date,
						SUM(
							CASE 
								WHEN invoices.invoice_type = 'Invoice' AND invoices.status NOT IN ('cancelled') AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Service' AND (items.value->>'isAddedToCart')::boolean = true
								)
								WHEN invoices.invoice_type = 'Refund' AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(-SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Service' AND (items.value->>'isAddedToCart')::boolean = true
								)
								ELSE 0
							END
						) as revenue
					FROM public.invoices
						INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
					GROUP BY ${grouping}
				),
				diagnostics_revenue AS (
					SELECT 
						${grouping} as date,
						SUM(
							CASE 
								WHEN invoices.invoice_type = 'Invoice' AND invoices.status NOT IN ('cancelled') AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Labreport' AND (items.value->>'isAddedToCart')::boolean = true
								)
								WHEN invoices.invoice_type = 'Refund' AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(-SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Labreport' AND (items.value->>'isAddedToCart')::boolean = true
								)
								ELSE 0
							END
						) as revenue
					FROM public.invoices
						INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
					GROUP BY ${grouping}
				),
				medications_revenue AS (
					SELECT 
						${grouping} as date,
						SUM(
							CASE 
								WHEN invoices.invoice_type = 'Invoice' AND invoices.status NOT IN ('cancelled') AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Medication' AND (items.value->>'isAddedToCart')::boolean = true
								)
								WHEN invoices.invoice_type = 'Refund' AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(-SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Medication' AND (items.value->>'isAddedToCart')::boolean = true
								)
								ELSE 0
							END
						) as revenue
					FROM public.invoices
						INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
					GROUP BY ${grouping}
				),
				vaccinations_revenue AS (
					SELECT 
						${grouping} as date,
						SUM(
							CASE 
								WHEN invoices.invoice_type = 'Invoice' AND invoices.status NOT IN ('cancelled') AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Vaccination' AND (items.value->>'isAddedToCart')::boolean = true
								)
								WHEN invoices.invoice_type = 'Refund' AND invoices.details IS NOT NULL THEN (
									SELECT COALESCE(-SUM(CAST(items.value->>'quantity' AS integer) * CAST(items.value->>'actualPrice' AS numeric)), 0)
									FROM json_array_elements(invoices.details::json) AS items
									WHERE items.value->>'itemType' = 'Vaccination' AND (items.value->>'isAddedToCart')::boolean = true
								)
								ELSE 0
							END
						) as revenue
					FROM public.invoices
						INNER JOIN public.patients ON invoices.patient_id = patients.id
					WHERE patients.clinic_id = $1
						AND invoices.created_at BETWEEN $2 AND $3
						AND invoices.details IS NOT NULL
					GROUP BY ${grouping}
				)
				SELECT 
					TO_CHAR(d.date, $6) as date,
					COALESCE(pr.revenue, 0) as products,
					COALESCE(sr.revenue, 0) as services,
					COALESCE(dr.revenue, 0) as diagnostics,
					COALESCE(mr.revenue, 0) as medications,
					COALESCE(vr.revenue, 0) as vaccinations
				FROM dates d
				LEFT JOIN products_revenue pr ON DATE_TRUNC($4, d.date) = pr.date
				LEFT JOIN services_revenue sr ON DATE_TRUNC($4, d.date) = sr.date
				LEFT JOIN diagnostics_revenue dr ON DATE_TRUNC($4, d.date) = dr.date
				LEFT JOIN medications_revenue mr ON DATE_TRUNC($4, d.date) = mr.date
				LEFT JOIN vaccinations_revenue vr ON DATE_TRUNC($4, d.date) = vr.date
				ORDER BY d.date;
			`;
            const queryParams = [
                dto.clinicId,
                startDateTime,
                endDateTime,
                truncPart,
                interval,
                dateFormat
            ];
            const result = await this.invoiceRepository.query(query, queryParams);
            const mappedResult = result.map((row) => ({
                date: row.date,
                products: Number(row.products),
                services: Number(row.services),
                diagnostics: Number(row.diagnostics),
                medications: Number(row.medications),
                vaccinations: Number(row.vaccinations)
            }));
            return mappedResult;
        }
        catch (error) {
            this.logger.error(`Error in getRevenueChartData: ${error.message}`);
            this.logger.error(error.stack);
            throw error;
        }
    }
    async getCollectedPaymentsChartData(dto) {
        // Convert dates to start and end of day
        const startDateTime = new Date(dto.startDate);
        startDateTime.setHours(0, 0, 0, 0);
        const endDateTime = new Date(dto.endDate);
        endDateTime.setHours(23, 59, 59, 999);
        // Calculate date difference in days
        const diffTime = Math.abs(endDateTime.getTime() - startDateTime.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        // Determine grouping based on date difference
        let dateFormat;
        let interval;
        let truncPart;
        if (diffDays <= 35) {
            dateFormat = 'YYYY-MM-DD';
            interval = '1 day';
            truncPart = 'day';
        }
        else if (diffDays <= 210) {
            dateFormat = 'YYYY-"W"IW';
            interval = '1 week';
            truncPart = 'week';
        }
        else if (diffDays <= 730) {
            dateFormat = 'YYYY-MM';
            interval = '1 month';
            truncPart = 'month';
        }
        else {
            dateFormat = 'YYYY';
            interval = '1 year';
            truncPart = 'year';
        }
        const query = `
			WITH dates AS (
				SELECT generate_series(
					DATE_TRUNC($4, $2::timestamp),  -- Truncate start date to the provided interval (e.g., day, week, month)
					DATE_TRUNC($4, $3::timestamp),  -- Truncate end date to the provided interval
					$5::interval  -- The interval for grouping (e.g., '1 day', '1 week', '1 month')
				)::date AS date
			),
			CollectedPayments AS (
				SELECT DISTINCT ON (pd.id) 
					pd.id,
					pd.payment_type,
					pd.amount,
					DATE_TRUNC($4, pd.created_at) AS date  -- Truncate the payment date to the given interval
				FROM public.payment_details pd
				JOIN public.owner_brands ob ON pd.owner_id = ob.id
				JOIN public.patient_owners po ON ob.id = po.owner_id
				JOIN public.patients p ON po.patient_id = p.id
				WHERE pd.created_at BETWEEN $2 AND $3
					AND p.clinic_id = $1
					AND pd.type IN ('Invoice', 'Reconcile Invoice', 'Bulk Reconcile Invoice', 'Collect')
					AND pd.deleted_at IS NULL
			),
			RefundedPayments AS (
				SELECT DISTINCT ON (pd.id) 
					pd.id,
					pd.payment_type,
					pd.amount,
					DATE_TRUNC($4, pd.created_at) AS date  -- Truncate the payment date to the given interval
				FROM public.payment_details pd
				JOIN public.owner_brands ob ON pd.owner_id = ob.id
				JOIN public.patient_owners po ON ob.id = po.owner_id
				JOIN public.patients p ON po.patient_id = p.id
				WHERE pd.created_at BETWEEN $2 AND $3
					AND p.clinic_id = $1
					AND pd.type IN ('Return', 'Credit Note')
					AND pd.deleted_at IS NULL
			),
			collected_data AS (
				SELECT 
					date,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Cash') THEN amount ELSE 0 END) AS cash,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Card') THEN amount ELSE 0 END) AS card,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Wallet') THEN amount ELSE 0 END) AS wallet,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Cheque') THEN amount ELSE 0 END) AS cheque,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Bank Transfer') THEN amount ELSE 0 END) AS bankTransfer
				FROM CollectedPayments
				GROUP BY date
			),
			refunded_data AS (
				SELECT 
					date,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Cash') THEN amount ELSE 0 END) AS cash,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Card') THEN amount ELSE 0 END) AS card,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Wallet') THEN amount ELSE 0 END) AS wallet,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Cheque') THEN amount ELSE 0 END) AS cheque,
					SUM(CASE WHEN LOWER(payment_type) = LOWER('Bank Transfer') THEN amount ELSE 0 END) AS bankTransfer
				FROM RefundedPayments
				GROUP BY date
			)
			SELECT 
				TO_CHAR(d.date, $6) AS date,  -- Format the date for the chart (e.g., 'DD/MM/YY')
				COALESCE(cd.cash, 0) - COALESCE(rd.cash, 0) AS cash,
				COALESCE(cd.card, 0) - COALESCE(rd.card, 0) AS card,
				COALESCE(cd.wallet, 0) - COALESCE(rd.wallet, 0) AS wallet,
				COALESCE(cd.cheque, 0) - COALESCE(rd.cheque, 0) AS cheque,
				COALESCE(cd.bankTransfer, 0) - COALESCE(rd.bankTransfer, 0) AS bankTransfer
			FROM dates d
			LEFT JOIN collected_data cd ON d.date = cd.date
			LEFT JOIN refunded_data rd ON d.date = rd.date
			ORDER BY d.date;
		`;
        const result = await this.paymentDetailsRepository.query(query, [
            dto.clinicId,
            startDateTime,
            endDateTime,
            truncPart,
            interval,
            dateFormat
        ]);
        return result.map((row) => ({
            date: row.date,
            cash: Number(row.cash),
            card: Number(row.card),
            wallet: Number(row.wallet),
            cheque: Number(row.cheque),
            bankTransfer: Number(row.banktransfer)
        }));
    }
    async getAppointmentsChartData(dto) {
        this.logger.log('Getting appointments chart data...');
        // Convert dates to start and end of day
        const startDateTime = new Date(dto.startDate);
        startDateTime.setHours(0, 0, 0, 0);
        const endDateTime = new Date(dto.endDate);
        endDateTime.setHours(23, 59, 59, 999);
        // Calculate date difference in days
        const diffTime = Math.abs(endDateTime.getTime() - startDateTime.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        // Determine grouping based on date difference
        let dateFormat;
        let interval;
        let truncPart;
        if (diffDays <= 35) {
            dateFormat = 'YYYY-MM-DD';
            interval = '1 day';
            truncPart = 'day';
        }
        else if (diffDays <= 210) {
            dateFormat = 'YYYY-"W"IW';
            interval = '1 week';
            truncPart = 'week';
        }
        else if (diffDays <= 730) {
            dateFormat = 'YYYY-MM';
            interval = '1 month';
            truncPart = 'month';
        }
        else {
            dateFormat = 'YYYY';
            interval = '1 year';
            truncPart = 'year';
        }
        const response = {
            total: [],
            missed: [],
            busiestDays: [],
            busiestHours: [],
            averageDuration: []
        };
        // Get base data for all types
        if (dto.type === analytics_dto_1.AppointmentAnalyticsType.ALL) {
            const baseQuery = `
				WITH dates AS (
					SELECT generate_series(
						DATE_TRUNC($4, $2::timestamp),
						DATE_TRUNC($4, $3::timestamp),
						$5::interval
					)::date AS date
				),
				total_appointments AS (
					SELECT 
						DATE_TRUNC($4, appointments.date) as date,
						COUNT(*) as count
					FROM public.appointments
					WHERE clinic_id = $1
						AND date BETWEEN $2 AND $3
						AND deleted_at IS NULL
						AND status NOT IN ('Missed', 'MISSED')
					GROUP BY DATE_TRUNC($4, appointments.date)
				),
				missed_appointments AS (
					SELECT 
						DATE_TRUNC($4, appointments.date) as date,
						COUNT(*) as count
					FROM public.appointments
					WHERE clinic_id = $1
						AND date BETWEEN $2 AND $3
						AND deleted_at IS NULL
						AND status IN ('Missed', 'MISSED')
					GROUP BY DATE_TRUNC($4, appointments.date)
				)
				SELECT 
					TO_CHAR(d.date, $6) as date,
					COALESCE(ta.count, 0) as total,
					COALESCE(ma.count, 0) as missed
				FROM dates d
				LEFT JOIN total_appointments ta ON DATE_TRUNC($4, d.date) = ta.date
				LEFT JOIN missed_appointments ma ON DATE_TRUNC($4, d.date) = ma.date
				ORDER BY d.date;
			`;
            const result = await this.appointmentRepository.query(baseQuery, [
                dto.clinicId,
                startDateTime,
                endDateTime,
                truncPart,
                interval,
                dateFormat
            ]);
            response.total = result.map((row) => ({
                date: row.date,
                total: Number(row.total)
            }));
            response.missed = result.map((row) => ({
                date: row.date,
                missed: Number(row.missed)
            }));
        }
        // Get busiest days data
        if (dto.type === analytics_dto_1.AppointmentAnalyticsType.BUSIEST_DAYS) {
            const busiestDaysQuery = `
				WITH WeekBoundaries AS (
					-- Get the start of the week for startDate (previous Saturday)
					SELECT 
						CASE 
							WHEN EXTRACT(DOW FROM $2::timestamp) = 6 THEN $2::date
							ELSE $2::date - INTERVAL '1 day' * EXTRACT(DOW FROM $2::timestamp + INTERVAL '1 day')
						END AS week_start,
						CASE 
							WHEN EXTRACT(DOW FROM $3::timestamp) = 5 THEN $3::date
							ELSE $3::date + INTERVAL '1 day' * (5 - EXTRACT(DOW FROM $3::timestamp))
						END AS week_end
				),
				AllDays AS (
					-- Generate all days of the week
					SELECT 
						generate_series(0, 6) as day_number,
						TO_CHAR(
							'2000-01-02'::date + (generate_series(0, 6) || ' days')::interval, 
							'DY'
						) as day_name
				),
				WeekCount AS (
					-- Calculate number of weeks, considering only the actual date range
					SELECT 
						CEIL(
							EXTRACT(EPOCH FROM ($3::timestamp - $2::timestamp)) / 
							(24 * 60 * 60 * 7)
						)::int as total_weeks
				),
				DayStats AS (
					SELECT 
						ad.day_number,
						ad.day_name,
						COUNT(a.id) as total_appointments,
						wc.total_weeks as week_count
					FROM AllDays ad
					CROSS JOIN WeekCount wc
					LEFT JOIN public.appointments a ON 
						EXTRACT(DOW FROM a.date) = ad.day_number
						AND a.clinic_id = $1
						AND a.deleted_at IS NULL
						AND a.status NOT IN ('Missed', 'MISSED')
						AND a.date BETWEEN $2 AND $3
					GROUP BY 
						ad.day_number,
						ad.day_name,
						wc.total_weeks
				)
				SELECT 
					day_name as day,
					TRUNC(CAST(CAST(total_appointments AS DECIMAL) / GREATEST(week_count, 1) AS NUMERIC), 2) as count,
					week_count as weeks_counted,
					total_appointments as total
				FROM DayStats
				ORDER BY 
					day_number;
			`;
            const busiestDaysResult = await this.appointmentRepository.query(busiestDaysQuery, [dto.clinicId, startDateTime, endDateTime]);
            response.busiestDays = busiestDaysResult.map((row) => ({
                day: row.day.trim(),
                count: Number(row.count),
                weeksCount: Number(row.weeks_counted),
                total: Number(row.total)
            }));
        }
        // Get busiest hours data
        if (dto.type === analytics_dto_1.AppointmentAnalyticsType.BUSIEST_HOURS) {
            const busiestHoursQuery = `
				WITH AppointmentHours AS (
					SELECT 
						EXTRACT(HOUR FROM (start_time AT TIME ZONE 'Asia/Kolkata'))::integer as hour_number,
						date,
						COUNT(*) as daily_count
					FROM public.appointments
					WHERE clinic_id = $1
						AND date BETWEEN $2 AND $3
						AND deleted_at IS NULL
						AND status NOT IN ('Missed', 'MISSED')
					GROUP BY 
						EXTRACT(HOUR FROM (start_time AT TIME ZONE 'Asia/Kolkata')),
						date
				),
				HourStats AS (
					SELECT 
						hour_number,
						COUNT(DISTINCT date) as days_with_appointments,
						SUM(daily_count) as total_appointments
					FROM AppointmentHours
					GROUP BY hour_number
				),
				HourRange AS (
					SELECT 
						MIN(hour_number) as min_hour,
						MAX(hour_number) as max_hour
					FROM HourStats
					WHERE total_appointments > 0
				),
				AllHours AS (
					SELECT 
						generate_series(
							(SELECT min_hour FROM HourRange),
							(SELECT max_hour FROM HourRange)
						) as hour_number
				),
				DateRange AS (
					SELECT COUNT(DISTINCT date)::float as total_days
					FROM generate_series(
						$2::date,
						$3::date,
						'1 day'::interval
					) as date
				)
				SELECT 
					TO_CHAR(
						'2000-01-01 '::timestamp + (ah.hour_number || ' hours')::interval,
						'HH24:00'
					) as hour,
					COALESCE(hs.total_appointments, 0) as total,
					COALESCE(hs.days_with_appointments, 0) as days_count,
					TRUNC(CAST(CAST(COALESCE(hs.total_appointments, 0) AS DECIMAL) / 
						GREATEST(dr.total_days, 1) AS NUMERIC), 2) as count
				FROM AllHours ah
				CROSS JOIN DateRange dr
				LEFT JOIN HourStats hs ON ah.hour_number = hs.hour_number
				ORDER BY ah.hour_number;
			`;
            const busiestHoursResult = await this.appointmentRepository.query(busiestHoursQuery, [dto.clinicId, startDateTime, endDateTime]);
            response.busiestHours = busiestHoursResult.map((row) => ({
                hour: row.hour,
                count: Number(row.count),
                daysCount: Number(row.days_count),
                total: Number(row.total)
            }));
        }
        // Get average duration data
        if (dto.type === analytics_dto_1.AppointmentAnalyticsType.AVERAGE_DURATION) {
            const avgDurationQuery = `
				WITH dates AS (
					SELECT generate_series(
						DATE_TRUNC($4, $2::timestamp),
						DATE_TRUNC($4, $3::timestamp),
						$5::interval
					)::date AS date
				),
				appointment_durations AS (
					SELECT 
						DATE_TRUNC($4, appointments.date) as date,
						AVG(
							CASE 
								WHEN EXTRACT(EPOCH FROM (receiving_care_time - checkin_time))/60 <= 1440 
								THEN EXTRACT(EPOCH FROM (receiving_care_time - checkin_time))/60 
							END
						) as avg_checkin_duration,
						AVG(
							CASE 
								WHEN EXTRACT(EPOCH FROM (checkout_time - receiving_care_time))/60 <= 1440 
								THEN EXTRACT(EPOCH FROM (checkout_time - receiving_care_time))/60 
							END
						) as avg_receiving_care_duration,
						AVG(
							CASE 
								WHEN EXTRACT(EPOCH FROM (updated_at - checkout_time))/60 <= 1440 
								THEN EXTRACT(EPOCH FROM (updated_at - checkout_time))/60 
							END
						) as avg_checkout_duration,
						AVG(
							CASE 
								WHEN EXTRACT(EPOCH FROM (updated_at - checkin_time))/60 <= 1440 
								THEN EXTRACT(EPOCH FROM (updated_at - checkin_time))/60 
							END
						) as avg_total_duration,
						COUNT(*) as total_appointments,
						COUNT(
							CASE 
								WHEN EXTRACT(EPOCH FROM (receiving_care_time - checkin_time))/60 <= 1440 
								AND EXTRACT(EPOCH FROM (checkout_time - receiving_care_time))/60 <= 1440
								AND EXTRACT(EPOCH FROM (updated_at - checkout_time))/60 <= 1440
								AND EXTRACT(EPOCH FROM (updated_at - checkin_time))/60 <= 1440
								THEN 1 
							END
						) as valid_appointments
					FROM public.appointments
					WHERE clinic_id = $1
						AND appointments.date BETWEEN $2 AND $3
						AND appointments.deleted_at IS NULL
						AND checkin_time IS NOT NULL 
						AND receiving_care_time IS NOT NULL 
						AND checkout_time IS NOT NULL
						-- Exclude appointments where any duration is negative
						AND receiving_care_time > checkin_time
						AND checkout_time > receiving_care_time
						AND updated_at > checkout_time
					GROUP BY DATE_TRUNC($4, appointments.date)
				)
				SELECT 
					TO_CHAR(d.date, $6) as date,
					COALESCE(ROUND(ad.avg_checkin_duration::numeric, 2), 0) as checkinDuration,
					COALESCE(ROUND(ad.avg_receiving_care_duration::numeric, 2), 0) as receivingCareDuration,
					COALESCE(ROUND(ad.avg_checkout_duration::numeric, 2), 0) as checkoutDuration,
					COALESCE(ROUND(ad.avg_total_duration::numeric, 2), 0) as totalDuration,
					COALESCE(ad.total_appointments, 0) as totalAppointments,
					COALESCE(ad.valid_appointments, 0) as validAppointments
				FROM dates d
				LEFT JOIN appointment_durations ad ON DATE_TRUNC($4, d.date) = ad.date
				ORDER BY d.date;
			`;
            const avgDurationResult = await this.appointmentRepository.query(avgDurationQuery, [
                dto.clinicId,
                startDateTime,
                endDateTime,
                truncPart,
                interval,
                dateFormat
            ]);
            response.averageDuration = avgDurationResult.map((row) => ({
                date: row.date,
                checkinDuration: Number(row.checkinduration),
                receivingCareDuration: Number(row.receivingcareduration),
                checkoutDuration: Number(row.checkoutduration),
                totalDuration: Number(row.totalduration),
                validAppointments: Number(row.validappointments)
            }));
        }
        return response;
    }
    async getDoctorSummary(dto) {
        try {
            this.logger.log('Getting doctor summary data...');
            // Convert dates to start and end of day
            const startDateTime = new Date(dto.startDate);
            startDateTime.setHours(0, 0, 0, 0);
            const endDateTime = new Date(dto.endDate);
            endDateTime.setHours(23, 59, 59, 999);
            const query = `
				WITH invoice_summary AS (
					SELECT 
						c.appointment_id,
						SUM(i.amount_payable::numeric) AS total_invoice_amount
					FROM public.carts c
					LEFT JOIN public.invoices i ON i.cart_id = c.id
					GROUP BY c.appointment_id
				),
				completed_appointments AS (
					SELECT 
						a.id,
						a.receiving_care_time,
						a.checkout_time,
						a.end_time,
						EXTRACT(EPOCH FROM (COALESCE(a.checkout_time, a.end_time) - a.receiving_care_time)) / 60 as duration_minutes
					FROM public.appointments a
					WHERE a.status = 'Completed'
					AND a.deleted_at IS NULL
					AND a.receiving_care_time IS NOT NULL
					AND (a.checkout_time IS NOT NULL OR a.end_time IS NOT NULL)
				)
				SELECT 
					u.first_name || ' ' || u.last_name AS doctor_name,
					COUNT(DISTINCT ca.id) AS num_appointments,
					COALESCE(SUM(inv.total_invoice_amount), 0) AS total_revenue,
					COALESCE(SUM(inv.total_invoice_amount) / NULLIF(COUNT(DISTINCT ca.id), 0), 0) AS revenue_per_appointment,
					COALESCE(AVG(ca.duration_minutes), 0) AS avg_appointment_duration_minutes
				FROM public.appointment_doctors ad
				JOIN public.clinic_users cu ON ad.clinic_user_id = cu.id
				JOIN public.users u ON cu.user_id = u.id
				JOIN completed_appointments ca ON ad.appointment_id = ca.id
				LEFT JOIN invoice_summary inv ON inv.appointment_id = ca.id
				WHERE (u.role_id = 'ea907601-9a89-4551-94a6-8e58bbeeb0c4' OR u.role_id = '3623c5c1-7621-473e-a2b8-667df02200e3')
				AND ad.appointment_id IN (
					SELECT id FROM public.appointments 
					WHERE clinic_id = $1 
					AND date BETWEEN $2 AND $3
				)
				GROUP BY u.id, u.first_name, u.last_name
				ORDER BY total_revenue DESC;
			`;
            const result = await this.appointmentRepository.query(query, [
                dto.clinicId,
                startDateTime,
                endDateTime
            ]);
            return result.map((row) => ({
                doctorName: row.doctor_name,
                numAppointments: Number(row.num_appointments),
                totalRevenue: Number(row.total_revenue),
                revenuePerAppointment: Number(row.revenue_per_appointment),
                avgAppointmentDurationMinutes: Number(row.avg_appointment_duration_minutes)
            }));
        }
        catch (error) {
            this.logger.error('Error in getDoctorSummary:', error);
            throw error;
        }
    }
    async getSummary(dto) {
        var _a, _b, _c, _d, _e, _f;
        try {
            this.logger.log('Getting summary data...');
            // Convert dates to start and end of day
            const startDateTime = new Date(dto.startDate);
            startDateTime.setHours(0, 0, 0, 0);
            const endDateTime = new Date(dto.endDate);
            endDateTime.setHours(23, 59, 59, 999);
            // Get appointments completed
            const appointmentsQuery = `
				SELECT COUNT(*) as count
				FROM public.appointments
				WHERE clinic_id = $1
				AND date BETWEEN $2 AND $3
				AND status = 'Completed'
				AND deleted_at IS NULL
			`;
            const appointmentsResult = await this.appointmentRepository.query(appointmentsQuery, [dto.clinicId, startDateTime, endDateTime]);
            const appointmentsCompleted = Number(((_a = appointmentsResult[0]) === null || _a === void 0 ? void 0 : _a.count) || 0);
            // ------------------------------------------------------------------
            // INVOICES GENERATED (exclude cancelled invoices)
            // ------------------------------------------------------------------
            const invoicesQuery = `
				SELECT COUNT(*) AS count
				FROM public.invoices i
				WHERE i.clinic_id = $1
				AND i.created_at BETWEEN $2 AND $3
				AND i.invoice_type = 'Invoice'
				AND i.status <> 'cancelled'
			`;
            const invoicesResult = await this.invoiceRepository.query(invoicesQuery, [dto.clinicId, startDateTime, endDateTime]);
            const invoicesGenerated = Number(((_b = invoicesResult[0]) === null || _b === void 0 ? void 0 : _b.count) || 0);
            // ------------------------------------------------------------------
            // TOTAL BILLING
            // For all invoices (fully paid, partially paid, written off) – include full invoice_amount
            // Only cancelled invoices contribute 0
            // ------------------------------------------------------------------
            const billingQuery = `
				SELECT COALESCE(SUM(
					CASE 
						WHEN i.status = 'cancelled' THEN 0
						ELSE COALESCE(i.invoice_amount::numeric,0)
					END
				),0) AS total
				FROM public.invoices i
					WHERE i.clinic_id = $1
					AND i.created_at BETWEEN $2 AND $3
					AND i.invoice_type = 'Invoice'
			`;
            const billingResult = await this.invoiceRepository.query(billingQuery, [dto.clinicId, startDateTime, endDateTime]);
            const totalBilling = Number(((_c = billingResult[0]) === null || _c === void 0 ? void 0 : _c.total) || 0);
            // Get credit notes generated
            const creditNotesQuery = `
				SELECT COUNT(*) as count
				FROM public.invoices i
				WHERE i.clinic_id = $1
				AND i.created_at BETWEEN $2 AND $3
				AND i.invoice_type = 'Refund'	
			`;
            const creditNotesResult = await this.invoiceRepository.query(creditNotesQuery, [dto.clinicId, startDateTime, endDateTime]);
            const creditNotesGenerated = Number(((_d = creditNotesResult[0]) === null || _d === void 0 ? void 0 : _d.count) || 0);
            // Get total credit notes amount
            const creditNotesTotalQuery = `
				SELECT COALESCE(SUM(i.invoice_amount::numeric), 0) as total
				FROM public.invoices i
				WHERE i.clinic_id = $1
				AND i.created_at BETWEEN $2 AND $3
				AND i.invoice_type = 'Refund'
			`;
            const creditNotesTotalResult = await this.invoiceRepository.query(creditNotesTotalQuery, [dto.clinicId, startDateTime, endDateTime]);
            const totalCreditNotes = Number(((_e = creditNotesTotalResult[0]) === null || _e === void 0 ? void 0 : _e.total) || 0);
            // Get amount collected by payment type - revised to only include actual collections
            const paymentsQuery = `
				SELECT 
					pd.payment_type as payment_mode,
					COALESCE(SUM(pd.amount::numeric), 0) as total
				FROM public.payment_details pd
				WHERE pd.clinic_id = $1
				AND pd.created_at BETWEEN $2 AND $3
				AND pd.type IN ('Invoice', 'Reconcile Invoice', 'Bulk Reconcile Invoice', 'Collect')
				AND pd.payment_type IN ('Cash', 'Card', 'Wallet', 'Cheque', 'Bank Transfer')
				AND pd.amount > 0
				AND pd.deleted_at IS NULL
				GROUP BY pd.payment_type
			`;
            const paymentsResult = await this.paymentDetailsRepository.query(paymentsQuery, [dto.clinicId, startDateTime, endDateTime]);
            const amountCollected = {
                cash: 0,
                card: 0,
                wallet: 0,
                cheque: 0,
                bankTransfer: 0,
                total: 0
            };
            paymentsResult.forEach((row) => {
                var _a;
                const amount = Number(row.total || 0);
                switch ((_a = row.payment_mode) === null || _a === void 0 ? void 0 : _a.toLowerCase()) {
                    case 'cash':
                        amountCollected.cash = amount;
                        break;
                    case 'card':
                        amountCollected.card = amount;
                        break;
                    case 'wallet':
                        amountCollected.wallet = amount;
                        break;
                    case 'cheque':
                        amountCollected.cheque = amount;
                        break;
                    case 'bank transfer':
                        amountCollected.bankTransfer = amount;
                        break;
                }
            });
            // Calculate total amount collected
            amountCollected.total =
                amountCollected.cash +
                    amountCollected.card +
                    amountCollected.wallet +
                    amountCollected.cheque +
                    amountCollected.bankTransfer;
            // Get amount refunded by payment type
            const refundsQuery = `
				SELECT 
					pd.payment_type as payment_mode,
					COALESCE(SUM(pd.amount::numeric), 0) as total
				FROM public.payment_details pd
				WHERE pd.clinic_id = $1
				AND pd.created_at BETWEEN $2 AND $3
				AND pd.type IN ('Credit Note', 'Return')
				AND pd.payment_type IN ('Cash', 'Card', 'Wallet', 'Cheque', 'Bank Transfer')
				AND pd.amount > 0
				AND pd.deleted_at IS NULL
				GROUP BY pd.payment_type
			`;
            const refundsResult = await this.paymentDetailsRepository.query(refundsQuery, [dto.clinicId, startDateTime, endDateTime]);
            const amountRefunded = {
                cash: 0,
                card: 0,
                wallet: 0,
                cheque: 0,
                bankTransfer: 0,
                total: 0
            };
            refundsResult.forEach((row) => {
                var _a;
                const amount = Number(row.total || 0);
                switch ((_a = row.payment_mode) === null || _a === void 0 ? void 0 : _a.toLowerCase()) {
                    case 'cash':
                        amountRefunded.cash = amount;
                        break;
                    case 'card':
                        amountRefunded.card = amount;
                        break;
                    case 'wallet':
                        amountRefunded.wallet = amount;
                        break;
                    case 'cheque':
                        amountRefunded.cheque = amount;
                        break;
                    case 'bank transfer':
                        amountRefunded.bankTransfer = amount;
                        break;
                }
            });
            // Calculate total amount refunded
            amountRefunded.total =
                amountRefunded.cash +
                    amountRefunded.card +
                    amountRefunded.wallet +
                    amountRefunded.cheque +
                    amountRefunded.bankTransfer;
            // ------------------------------------------------------------------
            // BAD DEBTS (WRITE OFF) CALCULATION
            // Only use payment details with type 'Write Off Invoice'
            // ------------------------------------------------------------------
            const badDebtsQuery = `
				SELECT COALESCE(SUM(pd.amount::numeric), 0) as total
				FROM public.payment_details pd
				WHERE pd.clinic_id = $1
				AND pd.created_at BETWEEN $2 AND $3
				AND pd.type = 'Write Off Invoice'
				AND pd.deleted_at IS NULL
			`;
            const badDebtsResult = await this.paymentDetailsRepository.query(badDebtsQuery, [dto.clinicId, startDateTime, endDateTime]);
            const badDebts = Number(((_f = badDebtsResult[0]) === null || _f === void 0 ? void 0 : _f.total) || 0);
            return {
                appointmentsCompleted,
                invoicesGenerated,
                totalBilling,
                creditNotesGenerated,
                totalCreditNotes,
                amountCollected,
                amountRefunded,
                badDebts
            };
        }
        catch (error) {
            this.logger.error('Error in getSummary:', error);
            throw error;
        }
    }
    async getPaymentsByMode(clinicId, startDate, endDate, paymentMode, type) {
        const query = `
			WITH PaymentData AS (
				SELECT 
					pd.id,
					pd.owner_id,
					pd.reference_alpha_id as receipt_number,
					pd.amount::numeric as amount,
					pd.type,
					pd.payment_type,
					pd.patient_id,
					pd.invoice_id,
					TO_CHAR(
						pd.created_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata',
						'DD/MM/YY HH24:MI:SS'
					) AS payment_date,
					CONCAT(ob.first_name, ' ', ob.last_name) as owner_name,
					CASE 
						WHEN pd.patient_id IS NOT NULL THEN 
							(SELECT p.patient_name 
							FROM public.patients p 
							WHERE p.id = pd.patient_id)
						ELSE NULL
					END as patient_name,
					CASE 
						WHEN pd.invoice_id IS NOT NULL THEN 
							(SELECT i.reference_alpha_id 
							FROM public.invoices i 
							WHERE i.id = pd.invoice_id)
						ELSE NULL
					END as invoice_reference,
					CASE 
						WHEN pd.type IN ('Invoice', 'Reconcile Invoice', 'Bulk Reconcile Invoice') THEN 'Clear Invoice Balance'
						WHEN pd.type = 'Collect' THEN 'Add Credits'
					END as action
				FROM public.payment_details pd
				JOIN public.owner_brands ob ON pd.owner_id = ob.id
				WHERE pd.clinic_id = $1
				AND pd.created_at BETWEEN $2 AND $3
				AND LOWER(pd.payment_type) = LOWER($4)
				AND pd.amount > 0
				AND pd.deleted_at IS NULL
				AND (
					CASE 
						WHEN $5 = 'collect' THEN pd.type IN ('Invoice', 'Reconcile Invoice', 'Bulk Reconcile Invoice', 'Collect')
							ELSE pd.type IN ('Credit Note', 'Return')
					END
				)
			),
			GroupedPayments AS (
				SELECT 
					owner_name,
					payment_type,
						receipt_number,
					MIN(payment_date) as payment_date,
					SUM(amount) as total_amount,
					STRING_AGG(DISTINCT action, ', ' ORDER BY action) as actions,
						STRING_AGG(DISTINCT patient_name, ', ' ORDER BY patient_name) FILTER (WHERE patient_name IS NOT NULL) as pets,
					STRING_AGG(DISTINCT invoice_reference, ', ' ORDER BY invoice_reference) FILTER (WHERE invoice_reference IS NOT NULL) as invoice_references
				FROM PaymentData
				GROUP BY 
					owner_name,
					payment_type,
					receipt_number
			)
			SELECT 
				owner_name as "Owner Name",
				payment_type as "Payment Mode",
				receipt_number as "Receipt Number",
				payment_date as "Payment Date",
				total_amount as "Amount",
				actions as "Action",
				COALESCE(pets, '-') as "Pet",
				COALESCE(invoice_references, '-') as "Invoice Cleared"
			FROM GroupedPayments
			ORDER BY payment_date DESC;
		`;
        const results = await this.paymentDetailsRepository.query(query, [
            clinicId,
            startDate,
            endDate,
            paymentMode,
            type
        ]);
        return results;
    }
    /**
     * Optimized method that fetches payment data for all modes in a single query
     * Replaces the need to call getPaymentsByMode multiple times
     */
    async getAllPaymentsByMode(clinicId, startDate, endDate, type) {
        this.logger.log('Fetching all payments by mode in a single query', {
            clinicId,
            startDate,
            endDate,
            type
        });
        // This query retrieves payment data for all payment modes in one go
        // We use CASE statements to categorize each payment by its payment_type
        const query = `
			WITH PaymentData AS (
				SELECT 
					pd.id,
					pd.owner_id,
					pd.reference_alpha_id as receipt_number,
					pd.amount::numeric as amount,
					pd.type,
					LOWER(pd.payment_type) as payment_type,
					pd.patient_id,
					pd.invoice_id,
					TO_CHAR(
						pd.created_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata',
						'DD/MM/YY HH24:MI:SS'
					) AS payment_date,
					pd.created_at,
					CONCAT(ob.first_name, ' ', ob.last_name) as owner_name,
					CASE 
						WHEN pd.patient_id IS NOT NULL THEN 
							(SELECT p.patient_name 
							FROM public.patients p 
							WHERE p.id = pd.patient_id)
						ELSE NULL
					END as patient_name,
					CASE 
						WHEN pd.invoice_id IS NOT NULL THEN 
							(SELECT i.reference_alpha_id 
							FROM public.invoices i 
							WHERE i.id = pd.invoice_id)
						ELSE NULL
					END as invoice_reference,
					CASE 
						WHEN pd.type IN ('Invoice', 'Reconcile Invoice', 'Bulk Reconcile Invoice') THEN 'Clear Invoice Balance'
						WHEN pd.type IN ('Credit Note') THEN 'Clear Credit Note Balance'
						WHEN pd.type = 'Collect' THEN 'Add Credits'
						WHEN pd.type = 'Return' THEN 'Return Credits'
					END as action
				FROM public.payment_details pd
				JOIN public.owner_brands ob ON pd.owner_id = ob.id
				WHERE pd.clinic_id = $1
				AND pd.created_at BETWEEN $2 AND $3
				AND pd.amount > 0
				AND pd.deleted_at IS NULL
				AND (
					CASE 
						WHEN $4 = 'collect' THEN pd.type IN ('Invoice', 'Reconcile Invoice', 'Bulk Reconcile Invoice', 'Collect')
						ELSE pd.type IN ('Credit Note', 'Return')
					END
				)
			),
			GroupedPayments AS (
				SELECT 
					owner_name,
					payment_type,
					receipt_number,
					MIN(payment_date) as payment_date,
					MIN(created_at) as created_at,
					SUM(amount) as total_amount,
					STRING_AGG(DISTINCT action, ', ' ORDER BY action) as actions,
					STRING_AGG(DISTINCT patient_name, ', ' ORDER BY patient_name) FILTER (WHERE patient_name IS NOT NULL) as pets,
					STRING_AGG(DISTINCT invoice_reference, ', ' ORDER BY invoice_reference) FILTER (WHERE invoice_reference IS NOT NULL) as invoice_references
				FROM PaymentData
				GROUP BY 
					owner_name,
					payment_type,
					receipt_number
			)
			SELECT 
				payment_type,
				owner_name as "Owner Name",
				payment_type as "Payment Mode",
				receipt_number as "Receipt Number",
				payment_date as "Payment Date",
				total_amount as "Amount",
				actions as "Action",
				COALESCE(pets, '-') as "Pet",
				COALESCE(invoice_references, '-') as "Invoice Cleared",
				created_at
			FROM GroupedPayments
			ORDER BY created_at DESC;
		`;
        const results = await this.paymentDetailsRepository.query(query, [
            clinicId,
            startDate,
            endDate,
            type
        ]);
        // Organize results by payment mode
        const paymentsByMode = new Map();
        // Initialize standard payment modes
        const standardModes = [
            'wallet',
            'card',
            'cash',
            'cheque',
            'bank transfer'
        ];
        standardModes.forEach(mode => paymentsByMode.set(mode, []));
        // Group results by payment mode
        results.forEach((row) => {
            var _a;
            const mode = row.payment_type.toLowerCase();
            if (!paymentsByMode.has(mode)) {
                paymentsByMode.set(mode, []);
            }
            // Format the row object to match the expected output format
            const formattedRow = {
                'Owner Name': row['Owner Name'],
                'Payment Mode': row['Payment Mode'],
                'Receipt Number': row['Receipt Number'],
                'Payment Date': row['Payment Date'],
                Amount: Number(row['Amount']),
                Action: row['Action'],
                Pet: row['Pet'],
                'Invoice Cleared': row['Invoice Cleared']
            };
            (_a = paymentsByMode.get(mode)) === null || _a === void 0 ? void 0 : _a.push(formattedRow);
        });
        return paymentsByMode;
    }
    async createPaymentModeSheet(data, mode, type) {
        // Calculate total amount and count
        const totalAmount = data.reduce((sum, row) => sum + Number(row['Amount'] || 0), 0);
        const count = data.length;
        // Create header rows
        const headerRows = [
            [
                {
                    v: `Total amount ${type} through ${mode.toLowerCase()} payments = ${totalAmount.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} (${count} transactions)`,
                    s: {
                        font: { bold: true },
                        alignment: { horizontal: 'left' }
                    }
                }
            ],
            [''] // Empty row for spacing
        ];
        // Define column headers - conditional naming for the last column
        const lastColumnHeader = type === 'returned' ? 'Credit Note' : 'Invoice Cleared';
        const columnHeaders = [
            'Owner Name',
            'Payment Mode',
            'Receipt Number',
            'Payment Date',
            type === 'collected' ? 'Amount Collected' : 'Amount Returned',
            'Action',
            'Pet',
            lastColumnHeader
        ];
        // Add column headers with styling
        headerRows.push(columnHeaders.map(header => ({
            v: header,
            s: {
                font: { bold: true },
                border: {
                    top: { style: 'thin' },
                    bottom: { style: 'thin' },
                    left: { style: 'thin' },
                    right: { style: 'thin' }
                },
                alignment: { horizontal: 'center' }
            }
        })));
        // Create worksheet
        const ws = XLSX.utils.aoa_to_sheet(headerRows);
        // Add data rows
        const dataRows = data.map(row => [
            row['Owner Name'],
            row['Payment Mode'],
            row['Receipt Number'],
            row['Payment Date'],
            Number(row['Amount']),
            row['Action'],
            row['Pet'],
            row['Invoice Cleared'] // The data key remains the same, only the header changes
        ]);
        // Append data rows to worksheet
        XLSX.utils.sheet_add_aoa(ws, dataRows, { origin: headerRows.length });
        // Set column widths
        ws['!cols'] = [
            { wch: 25 }, // Owner Name
            { wch: 15 }, // Payment Mode
            { wch: 15 }, // Receipt Number
            { wch: 20 }, // Payment Date
            { wch: 15 }, // Amount
            { wch: 30 }, // Action
            { wch: 25 }, // Pet
            { wch: 15 } // Invoice Cleared/Credit Note
        ];
        // Apply number format to amount column
        const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
        for (let R = headerRows.length; R <= range.e.r; ++R) {
            const amountCell = XLSX.utils.encode_cell({ r: R, c: 4 }); // Column E (Amount)
            if (ws[amountCell]) {
                ws[amountCell].z = '#,##0.00';
                ws[amountCell].s = {
                    alignment: { horizontal: 'right' }
                };
            }
        }
        return ws;
    }
};
exports.AnalyticsService = AnalyticsService;
exports.AnalyticsService = AnalyticsService = AnalyticsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(invoice_entity_1.InvoiceEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(payment_details_entity_1.PaymentDetailsEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(patient_entity_1.Patient)),
    __param(3, (0, typeorm_1.InjectRepository)(owner_brand_entity_1.OwnerBrand)),
    __param(4, (0, typeorm_1.InjectRepository)(appointment_entity_1.AppointmentEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], AnalyticsService);
//# sourceMappingURL=analytics.service.js.map