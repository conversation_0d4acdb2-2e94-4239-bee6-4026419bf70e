"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddPriceToCartItems1731655182866 = void 0;
const typeorm_1 = require("typeorm");
class AddPriceToCartItems1731655182866 {
    async up(queryRunner) {
        await queryRunner.addColumn('cart_items', new typeorm_1.TableColumn({
            name: 'price',
            type: 'decimal',
            scale: 2,
            precision: 10,
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('cart_items', 'price');
    }
}
exports.AddPriceToCartItems1731655182866 = AddPriceToCartItems1731655182866;
//# sourceMappingURL=1731655182866-add-price-to-cart-items.js.map