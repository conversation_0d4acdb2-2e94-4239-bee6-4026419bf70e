import { PetTransferService } from './pet-transfer.service';
import { PetTransferDto } from './dto/pet-transfer.dto';
export declare class PetTransferController {
    private readonly petTransferService;
    constructor(petTransferService: PetTransferService);
    transferPet(petTransferDto: PetTransferDto): Promise<{
        success: boolean;
        message: string;
        transferType: string;
        patientId: string;
        destPatientId: string | null;
        ownerMapping: Record<string, string> | null;
        userMapping: Record<string, string> | null;
        clinicUserMapping: Record<string, string> | null;
    }>;
}
