"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PetTransferService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const query_manager_service_1 = require("./services/query-manager.service");
const inventory_mapping_service_1 = require("./services/inventory-mapping.service");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
const inventory_mapping_entity_1 = require("./entities/inventory-mapping.entity");
const appointment_details_entity_1 = require("../appointments/entities/appointment-details.entity");
const cart_entity_1 = require("../carts/entites/cart.entity");
const cart_item_entity_1 = require("../cart-items/entities/cart-item.entity");
let PetTransferService = class PetTransferService {
    constructor(entityManager, petTransferQueryManager, inventoryMappingService) {
        this.entityManager = entityManager;
        this.petTransferQueryManager = petTransferQueryManager;
        this.inventoryMappingService = inventoryMappingService;
    }
    async transferPet(petTransferDto) {
        // Use only explicitly provided mappings - no auto-detection
        const conflictResolution = {
            patientMapping: petTransferDto.destPatientId || null,
            ownerMapping: new Map(Object.entries(petTransferDto.ownerMapping || {}))
        };
        // Use provided user mappings instead of auto-migration
        const userMappings = new Map(Object.entries(petTransferDto.userMapping || {}));
        // Execute all operations within a single transaction
        await this.entityManager.transaction(async (transactionalEntityManager) => {
            // Handle specialized mappings first
            await this.handleInvoiceMapping(petTransferDto.patientId, petTransferDto.sourceClinicId, petTransferDto.destClinicId, transactionalEntityManager, conflictResolution.patientMapping);
            await this.handleAppointmentAssessmentMapping(petTransferDto.patientId, petTransferDto.sourceClinicId, transactionalEntityManager, conflictResolution.patientMapping);
            await this.handleCartItemMapping(petTransferDto.patientId, petTransferDto.sourceClinicId, petTransferDto.destClinicId, transactionalEntityManager, conflictResolution.patientMapping);
            await this.handleOwnerMigration(petTransferDto.patientId, petTransferDto.destClinicId, petTransferDto.destBrandId, conflictResolution.ownerMapping, conflictResolution.patientMapping, transactionalEntityManager);
            await this.handleDiagnosticTemplateMapping(petTransferDto.patientId, petTransferDto.destClinicId, transactionalEntityManager, conflictResolution.patientMapping);
            // Execute remaining table updates
            const queries = this.petTransferQueryManager.getPetTransferQueries(petTransferDto.patientId, petTransferDto.sourceClinicId, petTransferDto.destClinicId, petTransferDto.destBrandId, userMappings, conflictResolution.patientMapping);
            for (const query of queries) {
                await transactionalEntityManager.query(query);
            }
        });
    }
    async handleInvoiceMapping(patientId, sourceClinicId, destinationClinicId, entityManager, destPatientId) {
        const invoices = await entityManager.find(invoice_entity_1.InvoiceEntity, {
            where: { patientId, clinicId: sourceClinicId }
        });
        for (const invoice of invoices) {
            const updatedDetails = [];
            for (const item of invoice.details) {
                const mappedInventoryId = await this.inventoryMappingService.findAndMapInventoryItem(item.inventoryId, item.itemType, destinationClinicId);
                if (mappedInventoryId) {
                    updatedDetails.push({
                        ...item,
                        inventoryId: mappedInventoryId
                    });
                }
                else {
                    updatedDetails.push(item);
                }
            }
            invoice.details = updatedDetails;
            // Update patient_id if patient mapping exists (patient merging scenario)
            if (destPatientId) {
                invoice.patientId = destPatientId;
            }
            await entityManager.save(invoice_entity_1.InvoiceEntity, invoice);
        }
    }
    async handleAppointmentAssessmentMapping(patientId, sourceClinicId, entityManager, destPatientId) {
        const appointmentDetails = await entityManager.find(appointment_details_entity_1.AppointmentDetailsEntity, {
            where: { appointment: { patientId, clinicId: sourceClinicId } },
            relations: ['appointment']
        });
        for (const detail of appointmentDetails) {
            // Check if details exists and has the expected structure
            if (!detail.details || typeof detail.details !== 'object') {
                continue;
            }
            const detailsObj = detail.details;
            // Handle assessment mapping if assessment.list exists
            if (detailsObj.assessment &&
                Array.isArray(detailsObj.assessment.list)) {
                const updatedAssessmentList = [];
                for (const assessmentItem of detailsObj.assessment.list) {
                    // For assessment items, we might need to map assessment IDs if they reference clinic-specific assessments
                    // Since the current data structure shows assessment items as {label, value},
                    // we'll keep them as-is for now unless there's a specific mapping requirement
                    updatedAssessmentList.push(assessmentItem);
                }
                detailsObj.assessment.list = updatedAssessmentList;
            }
            // Update patient_id if patient mapping exists (patient merging scenario)
            if (destPatientId && detail.appointment) {
                detail.appointment.patientId = destPatientId;
            }
            // Save the updated details
            detail.details = detailsObj;
            await entityManager.save(appointment_details_entity_1.AppointmentDetailsEntity, detail);
        }
    }
    async handleCartItemMapping(patientId, sourceClinicId, destinationClinicId, entityManager, destPatientId) {
        const carts = await entityManager.find(cart_entity_1.CartEntity, {
            where: { appointment: { patientId, clinicId: sourceClinicId } },
            relations: ['appointment']
        });
        for (const cart of carts) {
            const cartItems = await entityManager.find(cart_item_entity_1.CartItemEntity, {
                where: { cartId: cart.id }
            });
            // Update patient_id if patient mapping exists (patient merging scenario)
            if (destPatientId && cart.appointment) {
                cart.appointment.patientId = destPatientId;
                await entityManager.save(cart_entity_1.CartEntity, cart);
            }
            for (const item of cartItems) {
                let mappedId = null;
                let idToUpdate = null;
                switch (item.type) {
                    case 'product':
                        if (item.productId) {
                            mappedId =
                                await this.inventoryMappingService.findAndMapInventoryItem(item.productId, inventory_mapping_entity_1.InventoryItemType.PRODUCT, destinationClinicId);
                            idToUpdate = 'productId';
                        }
                        break;
                    case 'service':
                        if (item.serviceId) {
                            mappedId =
                                await this.inventoryMappingService.findAndMapInventoryItem(item.serviceId, inventory_mapping_entity_1.InventoryItemType.SERVICE, destinationClinicId);
                            idToUpdate = 'serviceId';
                        }
                        break;
                    case 'vaccination':
                        if (item.vaccinationId) {
                            mappedId =
                                await this.inventoryMappingService.findAndMapInventoryItem(item.vaccinationId, inventory_mapping_entity_1.InventoryItemType.VACCINATION, destinationClinicId);
                            idToUpdate = 'vaccinationId';
                        }
                        break;
                    case 'medication':
                        if (item.prescriptionId) {
                            mappedId =
                                await this.inventoryMappingService.findAndMapInventoryItem(item.prescriptionId, inventory_mapping_entity_1.InventoryItemType.MEDICATION, destinationClinicId);
                            idToUpdate = 'prescriptionId';
                        }
                        break;
                    case 'lab_report':
                        if (item.labReportId) {
                            mappedId =
                                await this.inventoryMappingService.findAndMapInventoryItem(item.labReportId, inventory_mapping_entity_1.InventoryItemType.LAB_REPORT, destinationClinicId);
                            idToUpdate = 'labReportId';
                        }
                        break;
                }
                if (mappedId && idToUpdate) {
                    item[idToUpdate] = mappedId;
                    await entityManager.save(cart_item_entity_1.CartItemEntity, item);
                }
            }
        }
    }
    async handleOwnerMigration(patientId, destClinicId, destBrandId, ownerMapping, destPatientId, entityManager) {
        // Get all patient owners for this patient
        const patientOwners = await entityManager.query(`
			SELECT po.*, ob.global_owner_id, ob.first_name, ob.last_name, ob.email,
			       ob.address, ob.owner_balance, ob.opening_balance, ob.owner_credits
			FROM patient_owners po
			JOIN owner_brands ob ON po.owner_id = ob.id
			WHERE po.patient_id = $1
		`, [patientId]);
        for (const patientOwner of patientOwners) {
            let destOwnerBrandId;
            // Check if we have a mapping for this owner (conflict resolution)
            if (ownerMapping.has(patientOwner.owner_id)) {
                // Use existing owner from mapping
                destOwnerBrandId = ownerMapping.get(patientOwner.owner_id);
            }
            else {
                // Check if owner already exists in destination brand
                const existingOwnerInDestBrand = await entityManager.query(`
					SELECT id FROM owner_brands
					WHERE global_owner_id = $1 AND brand_id = $2
				`, [patientOwner.global_owner_id, destBrandId]);
                if (existingOwnerInDestBrand.length === 0) {
                    // Create new owner_brands record for destination brand
                    const newOwnerBrand = await entityManager.query(`
					INSERT INTO owner_brands (
						global_owner_id, brand_id, first_name, last_name, email,
						address, owner_balance, opening_balance, owner_credits,
						created_at, updated_at, version
					) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW(), 1)
					RETURNING id
				`, [
                        patientOwner.global_owner_id,
                        destBrandId,
                        patientOwner.first_name,
                        patientOwner.last_name,
                        patientOwner.email,
                        patientOwner.address,
                        patientOwner.owner_balance,
                        patientOwner.opening_balance,
                        patientOwner.owner_credits
                    ]);
                    destOwnerBrandId = newOwnerBrand[0].id;
                }
                else {
                    destOwnerBrandId = existingOwnerInDestBrand[0].id;
                }
            }
            // Handle patient_owners relationship
            if (destPatientId) {
                // Patient merging: Check if relationship already exists for destination patient
                const existingRelationship = await entityManager.query(`
					SELECT id FROM patient_owners
					WHERE patient_id = $1 AND owner_id = $2
				`, [destPatientId, destOwnerBrandId]);
                if (existingRelationship.length > 0) {
                    // Relationship already exists - just delete the source patient's relationship
                    await entityManager.query(`
						DELETE FROM patient_owners
						WHERE id = $1
					`, [patientOwner.id]);
                }
                else {
                    // No existing relationship - update to point to destination patient
                    await entityManager.query(`
						UPDATE patient_owners
						SET owner_id = $1, patient_id = $2
						WHERE id = $3
					`, [destOwnerBrandId, destPatientId, patientOwner.id]);
                }
            }
            else {
                // Normal transfer: Update patient_owners to reference the new owner_brands record
                await entityManager.query(`
					UPDATE patient_owners
					SET owner_id = $1
					WHERE id = $2
				`, [destOwnerBrandId, patientOwner.id]);
            }
            // Update financial tables to reference the new owner_id
            const oldOwnerId = patientOwner.owner_id;
            // Update invoices
            await entityManager.query(`
				UPDATE invoices
				SET owner_id = $1, clinic_id = $2, brand_id = $3
				WHERE owner_id = $4 AND patient_id = $5
			`, [
                destOwnerBrandId,
                destClinicId,
                destBrandId,
                oldOwnerId,
                patientId
            ]);
            // Update payment_details (both patient-specific and owner-only payments)
            await entityManager.query(`
				UPDATE payment_details
				SET owner_id = $1, clinic_id = $2, brand_id = $3
				WHERE owner_id = $4
			`, [destOwnerBrandId, destClinicId, destBrandId, oldOwnerId]);
            // Update credit_transactions with new owner_id, clinic_id, and brand_id
            await entityManager.query(`
				UPDATE credit_transactions
				SET owner_id = $1, clinic_id = $2, brand_id = $3
				WHERE owner_id = $4
			`, [destOwnerBrandId, destClinicId, destBrandId, oldOwnerId]);
            // Check if the old owner has any remaining pets in the source brand
            const remainingPets = await entityManager.query(`
				SELECT COUNT(*) as count
				FROM patient_owners po
				JOIN patients p ON po.patient_id = p.id
				WHERE po.owner_id = $1 AND p.brand_id = (
					SELECT brand_id FROM owner_brands WHERE id = $1
				)
			`, [oldOwnerId]);
            // If no pets remain for this owner in the source brand, delete the owner_brands record
            if (remainingPets[0].count === '0') {
                await entityManager.query(`
					DELETE FROM owner_brands
					WHERE id = $1
				`, [oldOwnerId]);
            }
        }
    }
    async handleDiagnosticTemplateMapping(patientId, destClinicId, entityManager, destPatientId) {
        // Get all diagnostic notes for this patient
        const diagnosticNotes = await entityManager.query(`
			SELECT id, template_id, template_name
			FROM diagnostic_notes
			WHERE patient_id = $1
		`, [patientId]);
        for (const note of diagnosticNotes) {
            if (!note.template_id)
                continue;
            // Check if template exists in destination clinic
            const existingTemplate = await entityManager.query(`
				SELECT id FROM diagnostic_templates
				WHERE template_name = $1 AND clinic_id = $2
			`, [note.template_name, destClinicId]);
            let destTemplateId;
            if (existingTemplate.length === 0) {
                // Get the original template from source clinic
                const sourceTemplate = await entityManager.query(`
					SELECT * FROM diagnostic_templates
					WHERE id = $1
				`, [note.template_id]);
                if (sourceTemplate.length > 0) {
                    const template = sourceTemplate[0];
                    // Create new template in destination clinic
                    const newTemplate = await entityManager.query(`
						INSERT INTO diagnostic_templates (
							template_name, clinic_id, assigned_diagnostics, template_type,
							table_structure, notes, is_active, created_by, updated_by,
							created_at, updated_at
						) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW())
						RETURNING id
					`, [
                        template.template_name,
                        destClinicId,
                        JSON.stringify(template.assigned_diagnostics),
                        template.template_type,
                        JSON.stringify(template.table_structure),
                        template.notes,
                        template.is_active,
                        template.created_by,
                        template.updated_by
                    ]);
                    destTemplateId = newTemplate[0].id;
                }
            }
            else {
                destTemplateId = existingTemplate[0].id;
            }
            // Update diagnostic note to reference the new template
            if (destTemplateId) {
                // Update patient_id if patient mapping exists (patient merging scenario)
                const updateQuery = destPatientId
                    ? `UPDATE diagnostic_notes SET template_id = $1, patient_id = $3 WHERE id = $2`
                    : `UPDATE diagnostic_notes SET template_id = $1 WHERE id = $2`;
                const params = destPatientId
                    ? [destTemplateId, note.id, destPatientId]
                    : [destTemplateId, note.id];
                await entityManager.query(updateQuery, params);
            }
        }
    }
};
exports.PetTransferService = PetTransferService;
exports.PetTransferService = PetTransferService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.EntityManager,
        query_manager_service_1.PetTransferQueryManagerService,
        inventory_mapping_service_1.InventoryMappingService])
], PetTransferService);
//# sourceMappingURL=pet-transfer.service.js.map