"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TasksController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const tasks_service_1 = require("./tasks.service");
const api_documentation_base_1 = require("../base/api-documentation-base");
const create_task_dto_1 = require("./dto/create-task.dto");
const tasks_entity_1 = require("./entities/tasks.entity");
const update_task_dto_1 = require("./dto/update-task.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../roles/roles.decorator");
const role_enum_1 = require("../roles/role.enum");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
let TasksController = class TasksController extends api_documentation_base_1.ApiDocumentationBase {
    constructor(logger, tasksService) {
        super();
        this.logger = logger;
        this.tasksService = tasksService;
    }
    async createTask(createTaskDto) {
        try {
            this.logger.log('creating new task', {
                dto: createTaskDto
            });
            return await this.tasksService.createTask(createTaskDto);
        }
        catch (error) {
            this.logger.error('error while creating new task', {
                error,
                createTaskDto
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getTasks(userId) {
        try {
            this.logger.log('getting task by userId', { userId });
            return await this.tasksService.getTask(userId);
        }
        catch (error) {
            this.logger.error('error while getting task', {
                error,
                userId
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async deleteTasks(id) {
        try {
            this.logger.log('deleting the task by id', { id });
            return await this.tasksService.deleteTask(id);
        }
        catch (error) {
            this.logger.error('error while deleting task', {
                error,
                id
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async updateTasks(id, updateTaskDto) {
        try {
            this.logger.log(' update the task', {
                id,
                dto: updateTaskDto
            });
            const updatedTask = await this.tasksService.updateTask(id, updateTaskDto);
            this.logger.log('task updated successfully', {
                id
            });
            return updatedTask;
        }
        catch (error) {
            this.logger.error('Error updating task by ID', { error });
            throw new common_1.HttpException('Error updating task', common_1.HttpStatus.BAD_REQUEST);
        }
    }
};
exports.TasksController = TasksController;
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'create new task',
        type: tasks_service_1.TasksService
    }),
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, common_1.UsePipes)(new common_1.ValidationPipe()),
    (0, track_method_decorator_1.TrackMethod)('createTask-tasks'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_task_dto_1.CreateTaskDto]),
    __metadata("design:returntype", Promise)
], TasksController.prototype, "createTask", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'get all task with userId',
        type: tasks_entity_1.Task
    }),
    (0, common_1.Get)(':userId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('getTasks-tasks'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TasksController.prototype, "getTasks", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'delete the task with there id',
        type: tasks_entity_1.Task
    }),
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('deleteTasks-tasks'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TasksController.prototype, "deleteTasks", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'update the task with there id',
        type: tasks_entity_1.Task
    }),
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('updateTasks-tasks'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_task_dto_1.UpdateTaskDto]),
    __metadata("design:returntype", Promise)
], TasksController.prototype, "updateTasks", null);
exports.TasksController = TasksController = __decorate([
    (0, swagger_1.ApiTags)('Tasks'),
    (0, common_1.Controller)('tasks'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        tasks_service_1.TasksService])
], TasksController);
//# sourceMappingURL=tasks.controller.js.map