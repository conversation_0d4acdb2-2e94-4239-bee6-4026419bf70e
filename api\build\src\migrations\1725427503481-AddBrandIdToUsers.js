"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddBrandIdToUsers1725427503481 = void 0;
const typeorm_1 = require("typeorm");
class AddBrandIdToUsers1725427503481 {
    async up(queryRunner) {
        await queryRunner.addColumn('users', new typeorm_1.TableColumn({
            name: 'brand_id',
            type: 'uuid',
            isNullable: true
        }));
        await queryRunner.createForeignKey('users', new typeorm_1.TableForeignKey({
            columnNames: ['brand_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'brands',
            onDelete: 'SET NULL'
        }));
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable('users');
        if (table) {
            const foreignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('brand_id') !== -1);
            if (foreignKey) {
                await queryRunner.dropForeignKey('users', foreignKey);
                await queryRunner.dropColumn('users', 'brand_id');
            }
        }
    }
}
exports.AddBrandIdToUsers1725427503481 = AddBrandIdToUsers1725427503481;
//# sourceMappingURL=1725427503481-AddBrandIdToUsers.js.map