"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateDiagnosticReportTable = void 0;
const generateDiagnosticReportTable = ({ diagnosticName, diagnosticNumber, diagnosticDate, clinicName, clinicAddress, clinicPhone, clinicEmail, clinicWebsite, petName, petBreed, ownerName, ownerEmail, ownerPhone, templateName, tableData, clinicCity, }) => {
    const generateTableHeaders = (columns) => {
        if (!columns)
            return "";
        return columns
            .map((column) => `
            <th class="table-header">${column.name.charAt(0).toUpperCase() +
            column.name.slice(1).replace(/([A-Z])/g, " $1")}</th>
          `)
            .join("");
    };
    const generateTableRows = (data, columns) => {
        if (!data || !data.length)
            return "";
        return data
            .map((row) => `
            <tr class="table-row">
              ${columns
            .map((column) => `<td class="table-cell">${row[column.name] || ""}</td>`)
            .join("")}
            </tr>
          `)
            .join("");
    };
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Diagnostic Report</title>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600&display=swap" rel="stylesheet">
        <style>
          /* Page Setup */
          @page {
            size: A4;
            margin: 40px;
           
          }
  
          /* Base Styles */
          html, body {
            width: 210mm;
            height: 297mm;
            margin: 0;
            padding: 0;
          }
  
          body {
            font-family: 'Inter', sans-serif;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
          }
  
          /* Layout Utilities */
          .container {
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
            background: #fff;
            box-sizing: border-box;
          }
  
          /* Typography Utilities */
          .text-primary {
            color: #59645D;
          }
  
          .text-secondary {
            color: #666;
          }
  
          .text-base {
            color: #333;
          }
  
          .text-xs {
            font-size: 10px;
          }
  
          .text-sm {
            font-size: 12px;
          }
  
          .text-md {
            font-size: 14px;
          }
  
          .text-lg {
            font-size: 18px;
          }
  
          .text-xl {
            font-size: 36px;
          }
  
          .font-thin {
            font-weight: 200;
          }
  
          .font-light {
            font-weight: 300;
          }
  
          .font-regular {
            font-weight: 400;
          }
  
          .font-medium {
            font-weight: 500;
          }
  
          .text-uppercase {
            text-transform: uppercase;
          }
  
          /* Spacing Utilities */
          .margin-top-xs {
            margin-top: 4px;
          }
  
          .margin-top-sm {
            margin-top: 8px;
          }
  
          .margin-top-md {
            margin-top: 15px;
          }
  
          .margin-top-lg {
            margin-top: 20px;
          }
  
          .margin-top-xl {
            margin-top: 30px;
          }
  
          .margin-bottom-xs {
            margin-bottom: 4px;
          }
  
          .padding-md {
            padding: 24px 16px;
          }
  
          /* Border Utilities */
          .border-bottom {
            border-bottom: 0.5px solid #E5E5E5;
          }
  
          .padding-bottom-sm {
            padding-bottom: 20px;
          }
  
          .padding-bottom-md {
            padding-bottom: 30px;
          }
  
          /* Grid Utilities */
          .grid-2-cols {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
          }
  
          .text-right {
            text-align: right;
          }
  
          /* Line Height Utilities */
          .line-height-normal {
            line-height: 1.5;
          }
  
          .line-height-loose {
            line-height: 1.6;
          }
  
          .line-height-relaxed {
            line-height: 1.8;
          }
  
          /* Table Styles */
          .table-container {
            width: 100%;
          }
  
          .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
          }
  
          .table-header,
          .table-cell {
            padding: 8px;
            text-align: left;
            border: 1px solid #E5E5E5;
            word-wrap: break-word;
            white-space: pre-wrap;
            vertical-align: top;
            min-height: 80px;
            height: auto;
            overflow: visible;
            text-overflow: clip;
            font-size: 14px;
            line-height: 1.8;
          }
  
          .table-header {
            background-color: #FFFFFF;
            font-weight: 400;
            color: #333;
            text-transform: uppercase;
            font-size: 12px;
          }
  
          .table-cell {
            color: #666;
          }

          .table-row {
            page-break-inside: avoid;
          }
          
          thead {
            display: table-header-group;
          }
          /* Print Styles */
          @media print {
            body {
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
            }

            .table-row {
              page-break-inside: avoid;
            }
            
            thead {
              display: table-header-group;
            }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="border-bottom padding-bottom-sm">
            <h1 class="text-xl font-thin text-primary" style="margin: 0">${diagnosticName}</h1>
            <div class="text-lg text-primary margin-top-sm">
              Diagnostic Note #${diagnosticNumber} | ${diagnosticDate}
            </div>
          </div>
  
          <div class="grid-2-cols margin-top-xl border-bottom padding-bottom-sm">
            <div>
              <div class="text-md font-regular text-primary margin-bottom-xs">${clinicName}</div>
              <div class="text-xs text-secondary line-height-loose">
                ${clinicAddress}<br>
                ${clinicCity}
              </div>
            </div>
            <div class="text-right">
              <div class="text-md font-regular text-primary margin-bottom-xs">${" "}</div>
              <div class="text-xs text-secondary line-height-loose">
                ${clinicPhone}<br>
                ${clinicEmail}<br>
                ${clinicWebsite}
              </div>
            </div>
          </div>
  
          <div class="margin-top-xl">
            <div class="text-md">
              <span class="font-medium text-base">${petName}</span> | 
              <span class="font-light text-base">${petBreed}</span>
            </div>
            <div class="text-sm text-secondary line-height-loose">
              ${ownerName}<br>
              ${ownerEmail}<br>
              ${ownerPhone}
            </div>
          </div>
  
          <div class="text-md font-medium text-primary margin-top-xl margin-top-md border-bottom">
            ${templateName}
          </div>
  
          <div class="table-container">
            <table class="table">
              <thead>
                <tr class="table-row">
                  ${generateTableHeaders((tableData === null || tableData === void 0 ? void 0 : tableData.columns) || [])}
                </tr>
              </thead>
              <tbody>
                ${generateTableRows((tableData === null || tableData === void 0 ? void 0 : tableData.data) || [], (tableData === null || tableData === void 0 ? void 0 : tableData.columns) || [])}
              </tbody>
            </table>
          </div>
        </div>
      </body>
      </html>
    `;
};
exports.generateDiagnosticReportTable = generateDiagnosticReportTable;
module.exports.generateDiagnosticReportTable = exports.generateDiagnosticReportTable;
//# sourceMappingURL=diagnosticTable.js.map