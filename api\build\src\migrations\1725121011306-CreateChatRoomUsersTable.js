"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateChatRoomUserTable1725121011306 = void 0;
const typeorm_1 = require("typeorm");
class CreateChatRoomUserTable1725121011306 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'chat_room_users',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'chat_room_id',
                    type: 'uuid'
                },
                {
                    name: 'user_id',
                    type: 'uuid'
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                }
            ],
            foreignKeys: [
                {
                    columnNames: ['chat_room_id'],
                    referencedTableName: 'chat_rooms',
                    referencedColumnNames: ['id']
                },
                {
                    columnNames: ['user_id'],
                    referencedTableName: 'clinic_users',
                    referencedColumnNames: ['id']
                }
            ]
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('chat_room_users');
    }
}
exports.CreateChatRoomUserTable1725121011306 = CreateChatRoomUserTable1725121011306;
//# sourceMappingURL=1725121011306-CreateChatRoomUsersTable.js.map