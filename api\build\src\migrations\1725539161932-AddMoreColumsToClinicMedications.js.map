{"version": 3, "file": "1725539161932-AddMoreColumsToClinicMedications.js", "sourceRoot": "", "sources": ["../../../src/migrations/1725539161932-AddMoreColumsToClinicMedications.ts"], "names": [], "mappings": ";;;AAAA,qCAAuE;AAEvE,MAAa,6CAA6C;IAE/C,KAAK,CAAC,EAAE,CAAC,WAAwB;QACpC,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE;YAC/C,IAAI,qBAAW,CAAC;gBACxB,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,MAAM;gBACF,UAAU,EAAE,KAAK;gBACf,OAAO,EAAE,wCAAwC;aAC7D,CAAC;YACO,IAAI,qBAAW,CAAC;gBACxB,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,KAAK;gBACL,OAAO,EAAE,IAAI;aACzB,CAAC;YACO,IAAI,qBAAW,CAAC;gBACxB,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,KAAK;gBACL,OAAO,EAAC,CAAC;aACrB,CAAC;YACO,IAAI,qBAAW,CAAC;gBACxB,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,IAAI;aAChB,CAAC;YACO,IAAI,qBAAW,CAAC;gBACxB,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,SAAS;gBACH,KAAK,EAAE,CAAC,EAAE,8CAA8C;gBACxD,SAAS,EAAE,EAAE,EAAE,uGAAuG;gBAClI,UAAU,EAAE,IAAI;aAChB,CAAC;YACF,IAAI,qBAAW,CAAC;gBACf,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE,SAAS;gBACH,KAAK,EAAE,CAAC,EAAE,8CAA8C;gBACxD,SAAS,EAAE,EAAE,EAAE,uGAAuG;gBACtH,UAAU,EAAE,IAAI;aAC5B,CAAC;YACF,IAAI,qBAAW,CAAC;gBACf,IAAI,EAAE,KAAK;gBACX,IAAI,EAAE,SAAS;gBACH,KAAK,EAAE,CAAC,EAAE,8CAA8C;gBACxD,SAAS,EAAE,EAAE,EAAE,uGAAuG;gBACtH,UAAU,EAAE,IAAI;aAC5B,CAAC;YACO,IAAI,qBAAW,CAAC;gBACxB,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,IAAI;aAChB,CAAC;YACO,IAAI,qBAAW,CAAC;gBACxB,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE,SAAS;gBACH,UAAU,EAAE,IAAI;aAC5B,CAAC;YACO,IAAI,qBAAW,CAAC;gBACxB,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,SAAS;gBACH,UAAU,EAAE,IAAI;aAC5B,CAAC;YACF,IAAI,qBAAW,CAAC;gBACf,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,SAAS;gBACH,UAAU,EAAE,KAAK;gBACjB,OAAO,EAAC,IAAI;aACxB,CAAC;SACI,CAAC,CAAC;IACP,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACtC,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC;QAErE,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC;QAEhE,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,gBAAgB,CAAC,CAAC;QAErE,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,gBAAgB,CAAC,CAAC;QAE/D,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,gBAAgB,CAAC,CAAC;QAE3E,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;QAEvE,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;QAEpD,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,eAAe,CAAC,CAAC;QAE1E,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;QAEvE,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,eAAe,CAAC,CAAC;QAE9D,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,aAAa,CAAC,CAAC;IACtE,CAAC;CACJ;AA/FD,sGA+FC"}