"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateDiagnosticTemplateDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const diagnostic_template_entity_1 = require("../entities/diagnostic-template.entity");
class AssignedDiagnosticDto {
}
__decorate([
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], AssignedDiagnosticDto.prototype, "id", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], AssignedDiagnosticDto.prototype, "name", void 0);
class TableColumnDto {
}
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TableColumnDto.prototype, "name", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(['text', 'number', 'range']),
    __metadata("design:type", String)
], TableColumnDto.prototype, "type", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], TableColumnDto.prototype, "options", void 0);
class TableStructureDto {
}
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => TableColumnDto),
    __metadata("design:type", Array)
], TableStructureDto.prototype, "columns", void 0);
class CreateDiagnosticTemplateDto {
}
exports.CreateDiagnosticTemplateDto = CreateDiagnosticTemplateDto;
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDiagnosticTemplateDto.prototype, "templateName", void 0);
__decorate([
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateDiagnosticTemplateDto.prototype, "clinicId", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => AssignedDiagnosticDto),
    __metadata("design:type", Array)
], CreateDiagnosticTemplateDto.prototype, "assignedDiagnostics", void 0);
__decorate([
    (0, class_validator_1.IsEnum)(diagnostic_template_entity_1.TemplateType),
    __metadata("design:type", String)
], CreateDiagnosticTemplateDto.prototype, "templateType", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => TableStructureDto),
    __metadata("design:type", TableStructureDto)
], CreateDiagnosticTemplateDto.prototype, "tableStructure", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateDiagnosticTemplateDto.prototype, "notes", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], CreateDiagnosticTemplateDto.prototype, "isActive", void 0);
//# sourceMappingURL=create-template.dto.js.map