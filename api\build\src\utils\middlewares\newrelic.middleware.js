"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewRelicMiddleware = void 0;
const common_1 = require("@nestjs/common");
const newrelic = require("newrelic");
let NewRelicMiddleware = class NewRelicMiddleware {
    use(req, res, next) {
        // Add custom attributes to the New Relic transaction
        newrelic.addCustomAttribute('requestPath', req.path);
        newrelic.addCustomAttribute('requestMethod', req.method);
        // Create a transaction
        const transaction = newrelic.getTransaction();
        // Add response listener to capture response data
        res.on('finish', () => {
            newrelic.addCustomAttribute('responseStatus', res.statusCode);
            newrelic.addCustomAttribute('responseTime', Date.now() - req[Symbol.for('start')]);
        });
        req[Symbol.for('start')] = Date.now();
        next();
    }
};
exports.NewRelicMiddleware = NewRelicMiddleware;
exports.NewRelicMiddleware = NewRelicMiddleware = __decorate([
    (0, common_1.Injectable)()
], NewRelicMiddleware);
//# sourceMappingURL=newrelic.middleware.js.map