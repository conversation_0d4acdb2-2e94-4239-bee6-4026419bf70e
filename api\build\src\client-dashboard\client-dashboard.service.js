"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientDashboardService = void 0;
const common_1 = require("@nestjs/common");
const owners_service_1 = require("../owners/owners.service");
const appointments_service_1 = require("../appointments/appointments.service");
const patients_service_1 = require("../patients/patients.service");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const moment = require("moment-timezone");
const enum_appointment_status_1 = require("../appointments/enums/enum-appointment-status");
const typeorm_1 = require("typeorm");
const jwt_1 = require("@nestjs/jwt");
const brands_service_1 = require("../brands/brands.service");
const role_enum_1 = require("../roles/role.enum");
const typeorm_2 = require("@nestjs/typeorm");
const clinic_entity_1 = require("../clinics/entities/clinic.entity");
const typeorm_3 = require("typeorm");
const clinic_service_1 = require("../clinics/clinic.service");
const uuid_1 = require("uuid");
const session_service_1 = require("../session/session.service");
let ClientDashboardService = class ClientDashboardService {
    constructor(ownersService, appointmentsService, patientsService, logger, dataSource, jwtService, sessionService, brandService, clinicRepository, clinicService) {
        this.ownersService = ownersService;
        this.appointmentsService = appointmentsService;
        this.patientsService = patientsService;
        this.logger = logger;
        this.dataSource = dataSource;
        this.jwtService = jwtService;
        this.sessionService = sessionService;
        this.brandService = brandService;
        this.clinicRepository = clinicRepository;
        this.clinicService = clinicService;
    }
    /**
     * Direct login for pet owners in the booking portal
     * @param directLoginDto The login data (phone number, country code, brand ID)
     * @returns Object with JWT token and owner information
     */
    async directLogin(directLoginDto) {
        const { phoneNumber, countryCode = '91', brandId } = directLoginDto;
        // Verify brand exists
        const brand = await this.brandService.getBrandById(brandId);
        if (!brand) {
            throw new common_1.NotFoundException(`Brand with ID ${brandId} not found`);
        }
        // Find owner by phone number
        const globalOwner = await this.ownersService.findGlobalOwnerByPhoneNumber(phoneNumber);
        // If owner doesn't exist globally, throw NotFoundException
        if (!globalOwner) {
            this.logger.warn('Direct login attempt failed: Phone number not found globally', {
                phoneNumber: `+${countryCode}${phoneNumber}`,
                brandId
            });
            throw new common_1.NotFoundException('Mobile number not recognized. Please check the number or contact the clinic.');
        }
        // Owner exists globally, find or create the brand-specific owner record
        let ownerBrand = await this.ownersService.findOwnerBrandByGlobalOwnerAndBrand(globalOwner.id, brand.id);
        // If owner exists globally but not for this specific brand, throw an error
        if (!ownerBrand) {
            this.logger.warn('Direct login attempt failed: OwnerBrand record not found for this brand', {
                globalOwnerId: globalOwner.id,
                brandId
            });
            throw new common_1.NotFoundException('Mobile number not recognized. Please check the number or contact the clinic.');
        }
        // Generate JWT token
        const payload = {
            sub: ownerBrand.id, // Use OwnerBrand ID as subject
            phoneNumber: globalOwner.phoneNumber, // Use global phone number
            role: role_enum_1.Role.OWNER,
            brandId: brand.id,
            globalOwnerId: globalOwner.id
        };
        const sessionId = (0, uuid_1.v4)();
        const token = this.jwtService.sign({ ...payload, sid: sessionId });
        // Persist session for ownerBrand user
        await this.sessionService.setUserSession(ownerBrand.id, sessionId, 60 * 60 * 24);
        // Log successful login
        this.logger.log(`Direct login successful for phone: +${globalOwner.countryCode || countryCode}${globalOwner.phoneNumber}, brand: ${brand.name}`);
        // Return token and owner information
        return {
            token,
            owner: {
                id: ownerBrand.id,
                globalOwnerId: globalOwner.id,
                firstName: ownerBrand.firstName,
                lastName: ownerBrand.lastName,
                phoneNumber: globalOwner.phoneNumber,
                countryCode: globalOwner.countryCode,
                email: ownerBrand.email,
                brandId: brand.id,
                brandName: brand.name
            }
        };
    }
    /**
     * Get client dashboard information including owner details and pets
     * @param ownerId The owner brand ID
     * @param brandId The brand ID
     * @returns ClientDashboardResponseDto
     */
    async getClientDashboard(ownerId, brandId) {
        try {
            this.logger.log('Getting client dashboard information', {
                ownerId,
                brandId
            });
            // Get owner with patients from owners service
            const ownerWithPets = await this.ownersService.getAllOwnerPatients(ownerId, brandId);
            if (!ownerWithPets) {
                throw new common_1.NotFoundException(`Owner with ID ${ownerId} not found`);
            }
            // Compute the owner balance dynamically
            const calculatedOwnerBalance = await this.patientsService.computeOwnerBalance(ownerId);
            // Format the response according to the DTO
            const ownerInfo = {
                id: ownerWithPets.owner.id,
                firstName: ownerWithPets.owner.firstName,
                lastName: ownerWithPets.owner.lastName,
                fullName: `${ownerWithPets.owner.firstName} ${ownerWithPets.owner.lastName}`.trim(),
                phoneNumber: ownerWithPets.owner.phoneNumber,
                email: ownerWithPets.owner.email || '',
                address: ownerWithPets.owner.address || '',
                ownerBalance: calculatedOwnerBalance,
                ownerCredits: ownerWithPets.owner.ownerCredits
            };
            return {
                owner: ownerInfo,
                pets: ownerWithPets.patients.map(patient => ({
                    id: patient.id,
                    name: patient.patientName,
                    breed: patient.breed,
                    species: patient.species || ''
                }))
            };
        }
        catch (error) {
            this.logger.error('Error getting client dashboard', {
                error,
                ownerId
            });
            throw error;
        }
    }
    /**
     * Get all appointments for a client's pets
     * @param ownerId The owner brand ID
     * @param brandId The brand ID
     * @param filters Optional filters for date, status, etc.
     * @returns ClientAppointmentsResponseDto
     */
    async getClientAppointments(ownerId, brandId, filters) {
        var _a, _b, _c, _d, _e, _f;
        try {
            this.logger.log('Getting client appointments', {
                ownerId,
                brandId,
                filters
            });
            // 1. Get owner with patients from owners service
            const ownerWithPets = await this.ownersService.getAllOwnerPatients(ownerId, brandId);
            if (!ownerWithPets || !ownerWithPets.patients.length) {
                return { upcoming: [], previous: [] };
            }
            // 2. Get all pet IDs
            const petIds = ownerWithPets.patients.map(pet => pet.id);
            this.logger.log('Found pets for owner', {
                petCount: petIds.length,
                petIds
            });
            // 3. Get appointments for each pet and combine them
            const allAppointments = [];
            for (const petId of petIds) {
                const petAppointments = await this.appointmentsService.getAppointmentsForPatient(petId, true);
                allAppointments.push(...petAppointments);
            }
            this.logger.log('Retrieved appointments', {
                count: allAppointments.length
            });
            // 4. Format and separate into upcoming and previous
            const now = moment();
            const upcoming = [];
            const previous = [];
            // Create a cache for clinic settings to avoid repeated database calls
            const clinicSettingsCache = new Map();
            // Helper to fetch and cache settings
            const getCachedSettings = async (clinicId) => {
                if (!clinicSettingsCache.has(clinicId)) {
                    try {
                        // Fetch the raw settings which include the TimeDuration object
                        const settings = await this.clinicService.getClientBookingSettings(clinicId);
                        clinicSettingsCache.set(clinicId, settings); // Store the raw settings
                    }
                    catch (error) {
                        this.logger.error('Error fetching clinic settings for modification check', {
                            error,
                            clinicId
                        });
                        // Use default (null deadline) if fetch fails
                        clinicSettingsCache.set(clinicId, {
                            modificationDeadlineTime: null
                        });
                    }
                }
                return clinicSettingsCache.get(clinicId);
            };
            for (const appointment of allAppointments) {
                const appointmentDate = moment(appointment.date).startOf('day');
                const appointmentStartTime = moment(appointment.startTime); // Use the Date object directly
                const appointmentEndTime = moment(appointment.endTime); // Use the Date object directly
                // Get doctor name
                const doctor = ((_a = appointment.appointmentDoctors) === null || _a === void 0 ? void 0 : _a.length) > 0
                    ? (_c = (_b = appointment.appointmentDoctors[0]) === null || _b === void 0 ? void 0 : _b.clinicUser) === null || _c === void 0 ? void 0 : _c.user
                    : null;
                const doctorName = doctor
                    ? `Dr. ${doctor.firstName || ''} ${doctor.lastName || ''}`.trim()
                    : 'Unknown Doctor';
                // Determine if the appointment can be modified or cancelled
                let canModifyOrCancel = false;
                let modificationDeadlineTime = null; // Store the full object or null
                // Only scheduled appointments can be modified/cancelled by client
                if (appointment.status === enum_appointment_status_1.EnumAppointmentStatus.Scheduled) {
                    const clinicSettings = await getCachedSettings(appointment.clinicId);
                    modificationDeadlineTime =
                        (clinicSettings === null || clinicSettings === void 0 ? void 0 : clinicSettings.modificationDeadlineTime) || null;
                    const modificationDeadlineMinutes = timeDurationToMinutes(modificationDeadlineTime); // Calculate minutes for check
                    if (modificationDeadlineMinutes !== null &&
                        modificationDeadlineMinutes >= 0) {
                        const cancellationDeadline = appointmentStartTime
                            .clone()
                            .subtract(modificationDeadlineMinutes, 'minutes');
                        // Can modify/cancel if current time is before the cancellation deadline
                        canModifyOrCancel = now.isBefore(cancellationDeadline);
                    }
                    else {
                        // If no deadline set (null or negative), default to allowing modification
                        canModifyOrCancel = true;
                    }
                }
                // Common appointment properties
                const baseAppointment = {
                    id: appointment.id,
                    date: appointmentDate.format('YYYY-MM-DD'),
                    startTime: appointmentStartTime.utc().toISOString(), // Return as UTC timestamp string
                    endTime: appointmentEndTime.utc().toISOString(), // Return as UTC timestamp string
                    patientName: ((_d = appointment.patient) === null || _d === void 0 ? void 0 : _d.patientName) || 'Unknown Patient',
                    clinicId: appointment.clinicId,
                    doctorId: (_e = appointment.appointmentDoctors[0]) === null || _e === void 0 ? void 0 : _e.doctorId,
                    petId: (_f = appointment.patient) === null || _f === void 0 ? void 0 : _f.id,
                    doctorName,
                    mode: (appointment.mode === 'Online'
                        ? 'Online'
                        : 'Clinic'),
                    status: this.mapAppointmentStatus(appointment.status || enum_appointment_status_1.EnumAppointmentStatus.Completed),
                    canModifyOrCancel,
                    modificationDeadline: modificationDeadlineTime // Return the full TimeDuration object or null
                };
                // Categorize as upcoming or previous
                const appointmentStartMoment = moment(baseAppointment.startTime); // Use the startTime ISO string directly
                if (appointmentStartMoment.isAfter(now) &&
                    appointment.status !== enum_appointment_status_1.EnumAppointmentStatus.Cancelled) {
                    upcoming.push({ ...baseAppointment });
                }
                else {
                    previous.push({
                        ...baseAppointment,
                        visitType: appointment.type || ''
                    });
                }
            }
            // Sort appointments
            upcoming.sort((a, b) => moment(a.startTime).diff(moment(b.startTime)));
            previous.sort((a, b) => moment(b.startTime).diff(moment(a.startTime)));
            return {
                upcoming,
                previous
            };
        }
        catch (error) {
            this.logger.error('Error getting client appointments', {
                error,
                ownerId
            });
            throw error;
        }
    }
    /**
     * Map appointment status from database enum to frontend enum
     */
    mapAppointmentStatus(status) {
        switch (status) {
            case enum_appointment_status_1.EnumAppointmentStatus.Scheduled:
                return 'Scheduled';
            case enum_appointment_status_1.EnumAppointmentStatus.Completed:
                return 'Completed';
            case enum_appointment_status_1.EnumAppointmentStatus.Missed:
                return 'Missed';
            case enum_appointment_status_1.EnumAppointmentStatus.Cancelled:
                return 'Cancelled';
            default:
                return 'Completed';
        }
    }
    /**
     * Get the list of clinics for the client dashboard
     * @param brandId Brand ID to filter clinics
     * @returns Array of clinic objects with id and name that have client booking enabled
     */
    async getClientClinics(brandId) {
        try {
            this.logger.log('Getting client clinics with booking enabled', {
                brandId
            });
            // Execute a query to get clinics from the database that have client booking enabled
            const clinics = await this.dataSource
                .createQueryBuilder()
                .select(['c.id as id', 'c.name as name'])
                .from('clinics', 'c')
                .where('c.brand_id = :brandId', { brandId })
                // Filter for only clinics with client booking enabled
                .andWhere(`c.custom_rule->'clientBookingSettings' IS NOT NULL`)
                .andWhere(`(c.custom_rule->'clientBookingSettings'->>'isEnabled')::boolean = true`)
                .orderBy('c.name', 'ASC')
                .getRawMany();
            this.logger.log('Found clinics with booking enabled', {
                brandId,
                clinicCount: clinics.length
            });
            return clinics;
        }
        catch (error) {
            this.logger.error('Error getting client clinics with booking enabled', {
                error,
                brandId
            });
            throw error;
        }
    }
    /**
     * Get the list of doctors/providers for the client dashboard, potentially filtered by clinic's allowed list.
     * @param brandId Brand ID to filter doctors
     * @param clinicId Optional clinic ID to filter doctors by clinic and its booking settings
     * @returns Array of doctor objects with id and name
     */
    async getClientDoctors(brandId, clinicId) {
        var _a, _b;
        try {
            this.logger.log('Getting client doctors', { brandId, clinicId });
            let allowedDoctorIds = undefined;
            let allowAllDoctorsFlag = false; // Initialize flag
            // Fetch clinic settings if clinicId is provided
            if (clinicId) {
                // Fetch settings using ClinicService to ensure defaults are applied
                const settings = await this.clinicService.getClientBookingSettings(clinicId);
                if (settings) {
                    allowAllDoctorsFlag = (_a = settings.allowAllDoctors) !== null && _a !== void 0 ? _a : false; // Use retrieved flag or default
                    allowedDoctorIds = (_b = settings.allowedDoctorIds) !== null && _b !== void 0 ? _b : null; // Use retrieved IDs or null
                    if (allowAllDoctorsFlag) {
                        this.logger.log('allowAllDoctors is true, skipping allowedDoctorIds filter', { clinicId });
                    }
                    else if (Array.isArray(allowedDoctorIds) &&
                        allowedDoctorIds.length === 0) {
                        // Explicitly no doctors allowed if allowAll=false and list is empty
                        this.logger.log('Settings indicate no specific doctors allowed for booking.', { clinicId });
                        return []; // Return empty immediately
                    }
                }
                else {
                    // This case should ideally not happen if clinicId is valid, but handle defensively
                    this.logger.warn('Could not retrieve client booking settings', { clinicId });
                    // Default behavior: Assume allowAllDoctors is false, allowedDoctorIds is null (no filtering)
                }
            }
            // Create query builder to get doctors
            const queryBuilder = this.dataSource
                .createQueryBuilder()
                .select([
                'cu.id as id', // Select the ClinicUser ID as 'id'. This ID represents the provider in the context of a specific clinic.
                "CONCAT(u.first_name, ' ', u.last_name) as name" // Concatenate first and last names for the provider's full name.
            ])
                .from('clinic_users', 'cu') // Start querying from the clinic_users table (aliased as 'cu').
                .innerJoin('users', 'u', 'cu.user_id = u.id') // Join with the users table to get user details like name.
                .innerJoin('roles', 'r', 'u.role_id = r.id') // Join with the roles table to filter by user role.
                .innerJoin('clinics', 'c', 'cu.clinic_id = c.id') // Join with the clinics table to filter by brand.
                .where('c.brand_id = :brandId', { brandId }) // Filter results to include only users associated with the specified brand.
                // Filter results to include only users with the 'DOCTOR' or 'ADMIN' role.
                // This ensures only relevant personnel are listed as potential providers for booking.
                .andWhere('r.name IN (:...allowedRoles)', {
                allowedRoles: [role_enum_1.Role.DOCTOR, role_enum_1.Role.ADMIN]
            })
                .andWhere('cu.id IS NOT NULL') // Ensure the ClinicUser ID exists (sanity check).
                .orderBy('name', 'ASC'); // Order the results alphabetically by name.
            // Add clinic filter if provided
            if (clinicId) {
                queryBuilder.andWhere('cu.clinic_id = :clinicId', { clinicId });
                // Apply allowedDoctorIds filter only if allowAllDoctors is false and the list is non-empty
                if (allowAllDoctorsFlag === false &&
                    Array.isArray(allowedDoctorIds) &&
                    allowedDoctorIds.length > 0) {
                    this.logger.log('Applying allowed doctor filter based on settings', {
                        clinicId,
                        allowedDoctorIds
                    });
                    // Filter by ClinicUser IDs
                    queryBuilder.andWhere('cu.id IN (:...allowedDoctorIds)', {
                        allowedDoctorIds
                    });
                }
            }
            const doctors = await queryBuilder.getRawMany();
            this.logger.log(`Returning ${doctors.length} doctors`, {
                brandId,
                clinicId,
                filterApplied: !allowAllDoctorsFlag &&
                    Array.isArray(allowedDoctorIds) &&
                    allowedDoctorIds.length > 0
            });
            return doctors;
        }
        catch (error) {
            this.logger.error('Error getting client doctors', {
                error,
                brandId,
                clinicId
            });
            throw error;
        }
    }
};
exports.ClientDashboardService = ClientDashboardService;
exports.ClientDashboardService = ClientDashboardService = __decorate([
    (0, common_1.Injectable)(),
    __param(8, (0, typeorm_2.InjectRepository)(clinic_entity_1.ClinicEntity)),
    __metadata("design:paramtypes", [owners_service_1.OwnersService,
        appointments_service_1.AppointmentsService,
        patients_service_1.PatientsService,
        winston_logger_service_1.WinstonLogger,
        typeorm_1.DataSource,
        jwt_1.JwtService,
        session_service_1.SessionService,
        brands_service_1.BrandService,
        typeorm_3.Repository,
        clinic_service_1.ClinicService])
], ClientDashboardService);
// Helper function (can be moved to a utils file if shared)
function timeDurationToMinutes(duration) {
    if (!duration)
        return null;
    const days = duration.days || 0;
    const hours = duration.hours || 0;
    const minutes = duration.minutes || 0;
    const totalMinutes = days * 24 * 60 + hours * 60 + minutes;
    return totalMinutes > 0 ? totalMinutes : null;
}
//# sourceMappingURL=client-dashboard.service.js.map