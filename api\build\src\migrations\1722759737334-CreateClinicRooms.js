"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateClinicRooms1722759737334 = void 0;
const typeorm_1 = require("typeorm");
class CreateClinicRooms1722759737334 {
    constructor() {
        this.name = 'CreateClinicRooms1722759737334';
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'clinic_rooms',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'name',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'description',
                    type: 'varchar',
                    isNullable: false
                }
            ]
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('clinic_rooms');
    }
}
exports.CreateClinicRooms1722759737334 = CreateClinicRooms1722759737334;
//# sourceMappingURL=1722759737334-CreateClinicRooms.js.map