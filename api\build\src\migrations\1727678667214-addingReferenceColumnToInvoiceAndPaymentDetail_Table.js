"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddingReferenceColumnToInvoiceAndPaymentDetailTable1727678667214 = void 0;
const typeorm_1 = require("typeorm");
class AddingReferenceColumnToInvoiceAndPaymentDetailTable1727678667214 {
    async up(queryRunner) {
        await queryRunner.addColumns('invoices', [
            new typeorm_1.TableColumn({
                name: 'reference_id',
                type: 'serial',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'prescription_reference_id',
                type: 'serial',
                isNullable: true
            })
        ]);
        await queryRunner.addColumn('payment_details', new typeorm_1.TableColumn({
            name: 'reference_id',
            type: 'serial',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('payment_details', 'reference_id');
        await queryRunner.dropColumns('invoices', [
            'reference_id',
            'prescription_reference_id'
        ]);
    }
}
exports.AddingReferenceColumnToInvoiceAndPaymentDetailTable1727678667214 = AddingReferenceColumnToInvoiceAndPaymentDetailTable1727678667214;
//# sourceMappingURL=1727678667214-addingReferenceColumnToInvoiceAndPaymentDetail_Table.js.map