"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddPrescisioInPaymentDetails1727526095017 = void 0;
const typeorm_1 = require("typeorm");
class AddPrescisioInPaymentDetails1727526095017 {
    async up(queryRunner) {
        await queryRunner.changeColumn('payment_details', 'amount', new typeorm_1.TableColumn({
            name: 'amount',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: false,
            default: 0
        }));
        await queryRunner.changeColumn('payment_details', 'amount_payable', new typeorm_1.TableColumn({
            name: 'amount_payable',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: false,
            default: 0
        }));
        await queryRunner.changeColumn('payment_details', 'main_balance', new typeorm_1.TableColumn({
            name: 'main_balance',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: false,
            default: 0
        }));
    }
    async down(queryRunner) {
        await queryRunner.changeColumn('payment_details', 'amount', new typeorm_1.TableColumn({
            name: 'amount',
            type: 'decimal',
            isNullable: true
        }));
        await queryRunner.changeColumn('payment_details', 'amount_payable', new typeorm_1.TableColumn({
            name: 'amount_payable',
            type: 'decimal',
            isNullable: true
        }));
        await queryRunner.changeColumn('payment_details', 'main_balance', new typeorm_1.TableColumn({
            name: 'main_balance',
            type: 'decimal',
            isNullable: true
        }));
    }
}
exports.AddPrescisioInPaymentDetails1727526095017 = AddPrescisioInPaymentDetails1727526095017;
//# sourceMappingURL=1727526095017-AddPrescisioInPaymentDetails.js.map