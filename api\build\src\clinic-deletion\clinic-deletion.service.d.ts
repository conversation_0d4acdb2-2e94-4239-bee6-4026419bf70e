import { Repository, DataSource } from 'typeorm';
import { <PERSON><PERSON>og<PERSON> } from '../utils/logger/winston-logger.service';
import { S3Service } from '../utils/aws/s3/s3.service';
import { DatabaseBackupService } from './services/database-backup.service';
import { FileBackupService } from './services/file-backup.service';
import { DatabaseRestoreService } from './services/database-restore.service';
import { FileRestoreService } from './services/file-restore.service';
import { QueryManagerService } from './services/query-manager.service';
import { DeletionImpactRequestDto, DeletionRequestDto, DeletionImpactResponse, DeletionExecutionResponse, BackupMetadata, BackupListRequestDto, BackupListResponse, RestoreRequestDto, RestoreImpactResponse, RestoreExecutionResponse, ConflictResolution } from './dto/clinic-deletion.dto';
import { Brand } from '../brands/entities/brand.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { ClinicDeletionAuditTrail } from './entities/clinic-deletion-audit-trail.entity';
export declare class ClinicDeletionService {
    private readonly brandRepository;
    private readonly clinicRepository;
    private readonly auditTrailRepository;
    private readonly dataSource;
    private readonly logger;
    private readonly s3Service;
    private readonly queryManagerService;
    private readonly databaseBackupService;
    private readonly fileBackupService;
    private readonly databaseRestoreService;
    private readonly fileRestoreService;
    constructor(brandRepository: Repository<Brand>, clinicRepository: Repository<ClinicEntity>, auditTrailRepository: Repository<ClinicDeletionAuditTrail>, dataSource: DataSource, logger: WinstonLogger, s3Service: S3Service, queryManagerService: QueryManagerService, databaseBackupService: DatabaseBackupService, fileBackupService: FileBackupService, databaseRestoreService: DatabaseRestoreService, fileRestoreService: FileRestoreService);
    /**
     * Analyzes the impact of deleting a clinic or brand
     */
    analyzeImpact(dto: DeletionImpactRequestDto): Promise<DeletionImpactResponse>;
    /**
     * Executes the backup and deletion process
     */
    executeDeletion(dto: DeletionRequestDto, userId: string): Promise<DeletionExecutionResponse>;
    /**
     * Validates that user has access to the clinic
     */
    validateClinicAccess(clinicId: string, userBrandId: string): Promise<boolean>;
    /**
     * Execute dry run - simulate backup and deletion without actually doing it
     */
    private executeDryRun;
    /**
     * Execute actual backup and deletion
     */
    private executeActualBackupAndDeletion;
    /**
     * Execute original S3 file deletion (only called after successful backup)
     */
    private executeOriginalS3FileDeletion;
    /**
     * Execute actual database deletion (only called after successful backup)
     */
    private executeActualDatabaseDeletion;
    /**
     * Gets target entity information
     */
    private getTargetInfo;
    /**
     * Analyzes database impact
     */
    private analyzeDatabaseImpact;
    /**
     * Analyzes S3 file impact
     */
    private analyzeS3Impact;
    /**
     * Calculates estimated time for deletion
     */
    private calculateEstimatedTime;
    /**
     * Generates warnings for the deletion process
     */
    private generateWarnings;
    /**
     * List available backups
     */
    listBackups(query: BackupListRequestDto): Promise<BackupListResponse>;
    /**
     * Get backup details
     */
    getBackupDetails(backupId: string): Promise<BackupMetadata | null>;
    /**
     * Delete backup permanently
     */
    deleteBackup(backupId: string): Promise<{
        success: boolean;
        message: string;
    }>;
    /**
     * Restore from backup
     */
    restoreFromBackup(backupId: string, conflictResolution: ConflictResolution): Promise<RestoreExecutionResponse>;
    /**
     * Analyze restore impact
     */
    analyzeRestoreImpact(dto: RestoreRequestDto): Promise<RestoreImpactResponse>;
    /**
     * Execute restore from backup
     */
    executeRestore(dto: RestoreRequestDto, userId: string): Promise<RestoreExecutionResponse>;
    /**
     * Estimate restore duration based on conflicts and data volume
     */
    private estimateRestoreDuration;
    /**
     * Generate warnings for restore impact analysis
     */
    private generateRestoreWarnings;
    /**
     * Create comprehensive audit trail for operations
     */
    private createAuditTrail;
    /**
     * Validate operation prerequisites
     */
    private validateOperationPrerequisites;
    /**
     * Validate that target exists
     */
    private validateTargetExists;
    /**
     * Check if a clinic is the only clinic for its brand
     * If so, upgrade the deletion to brand deletion
     */
    private upgradeClinicToBrandDeletionIfNeeded;
}
