"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionEndedFilter = void 0;
const common_1 = require("@nestjs/common");
let SessionEndedFilter = class SessionEndedFilter {
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        // Only transform when message equals 'SESSION_ENDED'
        if (exception.message === 'SESSION_ENDED') {
            return response.status(401).json({ error: 'SESSION_ENDED' });
        }
        // Otherwise fall back to default structure
        const status = exception.getStatus();
        const resBody = exception.getResponse();
        return response.status(status).json(resBody);
    }
};
exports.SessionEndedFilter = SessionEndedFilter;
exports.SessionEndedFilter = SessionEndedFilter = __decorate([
    (0, common_1.Catch)(common_1.UnauthorizedException)
], SessionEndedFilter);
//# sourceMappingURL=session-ended.filter.js.map