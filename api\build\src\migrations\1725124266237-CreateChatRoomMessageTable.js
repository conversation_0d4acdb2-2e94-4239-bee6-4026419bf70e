"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateChatRoomMessageTable1725124266237 = void 0;
const typeorm_1 = require("typeorm");
class CreateChatRoomMessageTable1725124266237 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'chat_room_messages',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'chat_room_id',
                    type: 'uuid'
                },
                {
                    name: 'sender_id',
                    type: 'uuid'
                },
                {
                    name: 'message',
                    type: 'varchar'
                },
                {
                    name: 'file',
                    type: 'jsonb',
                    isNullable: true
                },
                {
                    name: 'meta',
                    type: 'jsonb',
                    isNullable: true
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                }
            ],
            foreignKeys: [
                {
                    columnNames: ['chat_room_id'],
                    referencedTableName: 'chat_rooms',
                    referencedColumnNames: ['id']
                },
                {
                    columnNames: ['sender_id'],
                    referencedTableName: 'clinic_users',
                    referencedColumnNames: ['id']
                }
            ]
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('chat_room_messages');
    }
}
exports.CreateChatRoomMessageTable1725124266237 = CreateChatRoomMessageTable1725124266237;
//# sourceMappingURL=1725124266237-CreateChatRoomMessageTable.js.map