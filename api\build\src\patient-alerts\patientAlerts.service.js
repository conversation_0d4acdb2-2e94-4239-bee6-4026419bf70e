"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientAlertsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const patientAlerts_entity_1 = require("./entities/patientAlerts.entity");
const common_2 = require("@nestjs/common");
const typeorm_2 = require("typeorm");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
let PatientAlertsService = class PatientAlertsService {
    constructor(patientAlertsRepository, logger) {
        this.patientAlertsRepository = patientAlertsRepository;
        this.logger = logger;
    }
    async createPatientAlert(createPatientAlertDto) {
        return this.patientAlertsRepository.save(createPatientAlertDto);
    }
    async getPatientAlert(patientId) {
        const patientAlerts = await this.patientAlertsRepository.find({
            where: { patientId }
        });
        return patientAlerts;
    }
    async deletePatientAlertById(id, all) {
        let result;
        if (all) {
            result = await this.patientAlertsRepository.clear();
        }
        else {
            result = await this.patientAlertsRepository.delete(id);
        }
        if ((result === null || result === void 0 ? void 0 : result.affected) === 0) {
            throw new common_2.NotFoundException(`This patientAlert with id: ${id} doesn't exist`);
        }
    }
    async updatePatientAlertById(id, updatePatientAlertDto) {
        const patientAlert = await this.patientAlertsRepository.findOne({
            where: { id }
        });
        if (!patientAlert) {
            this.logger.error('Patient-alert is not found', { patientId: id });
            throw new common_2.NotFoundException(`Patient-alert with ID "${id}" not found`);
        }
        Object.assign(patientAlert, updatePatientAlertDto);
        try {
            const updatedPatientAlert = await this.patientAlertsRepository.save(patientAlert);
            this.logger.log('PatientAlert updated successfully', { id });
            return updatedPatientAlert;
        }
        catch (error) {
            this.logger.error('Error saving updated patient', {
                error,
                patientId: id
            });
            throw error;
        }
    }
};
exports.PatientAlertsService = PatientAlertsService;
exports.PatientAlertsService = PatientAlertsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(patientAlerts_entity_1.PatientAlertsEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        winston_logger_service_1.WinstonLogger])
], PatientAlertsService);
//# sourceMappingURL=patientAlerts.service.js.map