"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TabActivitiesController = void 0;
const common_1 = require("@nestjs/common");
const tab_activity_service_1 = require("./tab-activity.service");
const create_tab_actvity_dto_1 = require("./dto/create-tab-actvity.dto");
const tab_activity_enums_1 = require("./enums/tab-activity.enums");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../roles/roles.decorator");
const role_enum_1 = require("../roles/role.enum");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
const swagger_1 = require("@nestjs/swagger");
let TabActivitiesController = class TabActivitiesController {
    constructor(tabActivitiesService) {
        this.tabActivitiesService = tabActivitiesService;
    }
    create(createTabActivityDto, req) {
        return this.tabActivitiesService.create(createTabActivityDto, req.user.userId);
    }
    getLastActivity(tabName, referenceId) {
        return this.tabActivitiesService.getLastActivity(tabName, referenceId);
    }
    getLastActivitiesByReferenceIds(body) {
        return this.tabActivitiesService.getLastActivitiesByReferenceIds(body.referenceIds);
    }
    getActivitiesByReferenceId(referenceId) {
        return this.tabActivitiesService.getActivitiesByReferenceId(referenceId);
    }
    deleteByReferenceId(referenceId) {
        return this.tabActivitiesService.deleteByReferenceId(referenceId);
    }
    updateByReferenceId(referenceId, updateData, req) {
        return this.tabActivitiesService.updateByReferenceId(referenceId, updateData, req.user.userId);
    }
};
exports.TabActivitiesController = TabActivitiesController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('createTabActivity-tabActivities'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new tab activity' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Tab activity created successfully.' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_tab_actvity_dto_1.CreateTabActivityDto, Object]),
    __metadata("design:returntype", void 0)
], TabActivitiesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('last/:tabName/:referenceId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('getLastActivity-tabActivities'),
    (0, swagger_1.ApiOperation)({ summary: 'Get last activity for a specific tab and reference' }),
    (0, swagger_1.ApiParam)({ name: 'tabName', type: 'string', enum: tab_activity_enums_1.TabName, description: 'Name of the tab' }),
    (0, swagger_1.ApiParam)({ name: 'referenceId', type: 'string', description: 'ID of the reference' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns the last activity for the specified tab and reference.' }),
    __param(0, (0, common_1.Param)('tabName')),
    __param(1, (0, common_1.Param)('referenceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], TabActivitiesController.prototype, "getLastActivity", null);
__decorate([
    (0, common_1.Post)('last-by-references'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('getLastActivitiesByReferenceIds-tabActivities'),
    (0, swagger_1.ApiOperation)({ summary: 'Get last activities for multiple reference IDs' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                referenceIds: {
                    type: 'array',
                    items: { type: 'string' },
                    description: 'Array of reference IDs'
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns the last activities for the provided reference IDs.' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TabActivitiesController.prototype, "getLastActivitiesByReferenceIds", null);
__decorate([
    (0, common_1.Get)('reference/:referenceId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('getActivitiesByReferenceId-tabActivities'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all activities for a specific reference ID' }),
    (0, swagger_1.ApiParam)({ name: 'referenceId', type: 'string', description: 'ID of the reference' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns all activities for the specified reference ID.' }),
    __param(0, (0, common_1.Param)('referenceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TabActivitiesController.prototype, "getActivitiesByReferenceId", null);
__decorate([
    (0, common_1.Delete)('reference/:referenceId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('deleteByReferenceId-tabActivities'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete all activities for a specific reference ID' }),
    (0, swagger_1.ApiParam)({ name: 'referenceId', type: 'string', description: 'ID of the reference' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Successfully deleted activities for the reference ID.' }),
    __param(0, (0, common_1.Param)('referenceId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TabActivitiesController.prototype, "deleteByReferenceId", null);
__decorate([
    (0, common_1.Patch)('reference/:referenceId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('updateByReferenceId-tabActivities'),
    (0, swagger_1.ApiOperation)({ summary: 'Update activities for a specific reference ID' }),
    (0, swagger_1.ApiParam)({ name: 'referenceId', type: 'string', description: 'ID of the reference' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Successfully updated activities for the reference ID.' }),
    __param(0, (0, common_1.Param)('referenceId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", void 0)
], TabActivitiesController.prototype, "updateByReferenceId", null);
exports.TabActivitiesController = TabActivitiesController = __decorate([
    (0, swagger_1.ApiTags)('Tab Activities'),
    (0, common_1.Controller)('tab-activities'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [tab_activity_service_1.TabActivitiesService])
], TabActivitiesController);
//# sourceMappingURL=tab-activity.controller.js.map