"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddStatusColumnToLabReport1726227143006 = void 0;
const typeorm_1 = require("typeorm");
class AddStatusColumnToLabReport1726227143006 {
    async up(queryRunner) {
        await queryRunner.addColumn('lab_reports', new typeorm_1.TableColumn({
            name: 'status',
            type: 'enum',
            enum: ['PENDING', 'COMPLETED'],
            default: `'PENDING'`, // default value can be 'pending'
            isNullable: false
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('lab_reports', 'status');
    }
}
exports.AddStatusColumnToLabReport1726227143006 = AddStatusColumnToLabReport1726227143006;
//# sourceMappingURL=1726227143006-addStatusColumnToLabReport.js.map