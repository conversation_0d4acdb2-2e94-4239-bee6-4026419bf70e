interface DiagnosticReportNoteData {
    clinicName: string;
    clinicAddress: string;
    clinicPhone: string;
    clinicEmail: string;
    clinicWebsite: string;
    clinicStreet: string;
    clinicCity: string;
    title: string;
    assessmentText: string;
    petName?: string;
    petBreed?: string;
    species?: string;
    diagnosticName?: string;
    diagnosticNumber?: string;
    diagnosticDate?: string;
    reproductiveStatus?: string;
    ownerName?: string;
    ownerEmail?: string;
    ownerPhone?: string;
    templateName?: string;
}
export declare const generateDiagnosticReportNote: ({ diagnosticName, diagnosticNumber, diagnosticDate, clinicName, clinicAddress, clinicStreet, clinicCity, clinicPhone, clinicEmail, clinicWebsite, petName, petBreed, ownerName, ownerEmail, ownerPhone, templateName, assessmentText, }: DiagnosticReportNoteData) => string;
export {};
