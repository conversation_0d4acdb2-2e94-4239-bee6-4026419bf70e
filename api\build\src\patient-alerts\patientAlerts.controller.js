"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientAlertController = void 0;
const common_1 = require("@nestjs/common");
const patientAlerts_service_1 = require("./patientAlerts.service");
const create_patientAlert_dto_1 = require("./dto/create-patientAlert.dto");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const swagger_1 = require("@nestjs/swagger");
const api_documentation_base_1 = require("../base/api-documentation-base");
const patientAlerts_entity_1 = require("./entities/patientAlerts.entity");
const update_patientAlert_dto_1 = require("./dto/update-patientAlert.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const role_enum_1 = require("../roles/role.enum");
const roles_decorator_1 = require("../roles/roles.decorator");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
let PatientAlertController = class PatientAlertController extends api_documentation_base_1.ApiDocumentationBase {
    constructor(logger, patientAlertsService) {
        super();
        this.logger = logger;
        this.patientAlertsService = patientAlertsService;
    }
    async createPatientAlert(createPatientAlertDto) {
        try {
            this.logger.log('Creating new patient-alert', {
                dto: createPatientAlertDto
            });
            return await this.patientAlertsService.createPatientAlert(createPatientAlertDto);
        }
        catch (error) {
            this.logger.error('Error creating new patient-alert', {
                error,
                createPatientAlertDto
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getPatientAlert(patientId) {
        try {
            this.logger.log('Fetching patient_alert by patient_id', {
                patientId
            });
            return await this.patientAlertsService.getPatientAlert(patientId);
        }
        catch (error) {
            this.logger.error('Error fetching patient_alert by patient_id', {
                error
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.NOT_FOUND);
        }
    }
    async deletePatientAlert(id, all) {
        try {
            this.logger.log('Delete the patient-alert', { id });
            return await this.patientAlertsService.deletePatientAlertById(id, all);
        }
        catch (error) {
            this.logger.error('Error removing patient-alert by ID', { error });
            throw new common_1.HttpException('Error removing patient-alert', common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async updatePatientAlert(id, updatePatientAlertDto) {
        try {
            this.logger.log(' update the patient-alert', {
                patientId: id,
                dto: updatePatientAlertDto
            });
            const updatedPatientAlert = this.patientAlertsService.updatePatientAlertById(id, updatePatientAlertDto);
            this.logger.log('Patient-alert updated successfully', {
                patientId: id
            });
            return updatedPatientAlert;
        }
        catch (error) {
            this.logger.error('Error updating patient-alert by ID', { error });
            throw new common_1.HttpException('Error removing patient-alert', common_1.HttpStatus.BAD_REQUEST);
        }
    }
};
exports.PatientAlertController = PatientAlertController;
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Creates a new patient-alert',
        type: patientAlerts_service_1.PatientAlertsService
    }),
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, common_1.UsePipes)(new common_1.ValidationPipe()),
    (0, track_method_decorator_1.TrackMethod)('createPatientAlert-patient-alerts'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_patientAlert_dto_1.CreatePatientAlertDto]),
    __metadata("design:returntype", Promise)
], PatientAlertController.prototype, "createPatientAlert", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns the patient-alert with the patient_id',
        type: patientAlerts_entity_1.PatientAlertsEntity
    }),
    (0, common_1.Get)(':patientId'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('getPatientAlert-patient-alerts'),
    __param(0, (0, common_1.Param)('patientId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PatientAlertController.prototype, "getPatientAlert", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'delete the patient-alert ',
        type: patientAlerts_entity_1.PatientAlertsEntity
    }),
    (0, swagger_1.ApiQuery)({ name: 'all', type: 'string', required: false }),
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('deletePatientAlert-patient-alerts'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('all')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], PatientAlertController.prototype, "deletePatientAlert", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'update the patient-alert',
        type: patientAlerts_entity_1.PatientAlertsEntity
    }),
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('updatePatientAlert-patient-alerts'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_patientAlert_dto_1.UpdatePatientAlertDto]),
    __metadata("design:returntype", Promise)
], PatientAlertController.prototype, "updatePatientAlert", null);
exports.PatientAlertController = PatientAlertController = __decorate([
    (0, swagger_1.ApiTags)('PatientAlert'),
    (0, common_1.Controller)('patient-alert'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        patientAlerts_service_1.PatientAlertsService])
], PatientAlertController);
//# sourceMappingURL=patientAlerts.controller.js.map