export interface TreatmentEstimate {
    documentId: string;
    date: string;
    clinicName: string;
    clinicAddress1: string;
    clinicAddress2: string;
    clinicAddress3: string;
    clinicContactNo: string;
    clinicEmail: string;
    clinicWebsite: string;
    petName: string;
    petBreed: string;
    ownerName: string;
    ownerEmail: string;
    ownerMobile: string;
    lineItems: Array<any>;
    estimateTotal: Number;
    digitalSignature?: string;
    docDate?: string;
    vetName?: string;
}
export declare const generateTreatmentEstimateHml: ({ documentId, date, clinicName, clinicAddress1, clinicAddress2, clinicAddress3, clinicContactNo, clinicEmail, clinicWebsite, petName, petBreed, ownerName, ownerEmail, ownerMobile, lineItems, estimateTotal, digitalSignature, docDate, vetName }: TreatmentEstimate) => string;
