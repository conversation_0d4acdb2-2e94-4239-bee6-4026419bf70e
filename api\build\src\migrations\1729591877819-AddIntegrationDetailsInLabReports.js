"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddIntegrationDetailsInLabReports1729591877819 = void 0;
const typeorm_1 = require("typeorm");
class AddIntegrationDetailsInLabReports1729591877819 {
    async up(queryRunner) {
        await queryRunner.addColumns('lab_reports', [
            new typeorm_1.TableColumn({
                name: 'integration_details',
                type: 'varchar',
                isNullable: true,
            }),
            new typeorm_1.TableColumn({
                name: 'integration_order_id',
                type: 'varchar',
                isNullable: true
            }),
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('lab_reports', 'integration_details');
        await queryRunner.dropColumn('lab_reports', 'integration_order_id');
    }
}
exports.AddIntegrationDetailsInLabReports1729591877819 = AddIntegrationDetailsInLabReports1729591877819;
//# sourceMappingURL=1729591877819-AddIntegrationDetailsInLabReports.js.map