import { Repository } from 'typeorm';
import { PatientEstimate } from './entities/patient-estimate.entity';
import { CreatePatientEstimateDto } from './dto/create-patient-estimate.dto';
import { UpdatePatientEstimateDto } from './dto/update-patient-estimate.dto';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { WhatsappService } from '../utils/whatsapp-integration/whatsapp.service';
import { S3Service } from '../utils/aws/s3/s3.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { TreatmentEstimate } from '../utils/pdfs/treatmentEstimate';
import { UpdateSignedDocumentDto } from '../patient-document-libraries/dto/update-patient-document-library.dto';
export declare class PatientEstimateService {
    private patientEstimateRepository;
    private readonly logger;
    private readonly mailService;
    private readonly whatsappService;
    private s3Service;
    constructor(patientEstimateRepository: Repository<PatientEstimate>, logger: WinstonLogger, mailService: SESMailService, whatsappService: WhatsappService, s3Service: S3Service);
    sendMail(body: string, buffers: Buffer[], fileName: string[], email: string, subject?: string): Promise<void>;
    create(createDto: CreatePatientEstimateDto): Promise<PatientEstimate>;
    findAll(): Promise<PatientEstimate[]>;
    findOne(id: string): Promise<PatientEstimate>;
    findByPatient(patientId: string, page?: number, limit?: number, search?: string): Promise<{
        data: PatientEstimate[];
        total: number;
        page: number;
        pageCount: number;
    }>;
    update(id: string, updateDto: UpdatePatientEstimateDto): Promise<PatientEstimate>;
    remove(id: string): Promise<void>;
    sendSignableDocumentUrl(estimateId: string, url: string): Promise<void>;
    sendEstimateDocument(estimateId: string): Promise<any>;
    sendSignedDocument(estimateId: string, updateSignedDocumentDto: UpdateSignedDocumentDto): Promise<import("typeorm").UpdateResult | null>;
    generateHtmlObject(estimateResponse: PatientEstimate, documentId: string): TreatmentEstimate;
}
