"use strict";
// src/cron/cron.module.ts
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CronModule = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
// import { SocketModule } from './socket/socket.module';
const config_1 = require("@nestjs/config");
const app_config_1 = require("./config/app.config");
const aws_config_1 = require("./config/aws.config");
const config_2 = require("./config/config");
const database_config_1 = require("./config/database.config");
const google_config_1 = require("./config/google.config");
const chat_room_module_1 = require("./chat-room/chat-room.module");
const users_module_1 = require("./users/users.module");
const database_module_1 = require("./database/database.module");
const logger_module_1 = require("./utils/logger/logger-module");
const middlewares_module_1 = require("./utils/middlewares/middlewares.module");
const global_reminders_module_1 = require("./patient-global-reminders/global-reminders.module");
const cronHelper_module_1 = require("./utils/cron/cronHelper.module");
const sqs_module_1 = require("./utils/aws/sqs/sqs.module");
const availability_module_1 = require("./availability/availability.module");
let CronModule = class CronModule {
};
exports.CronModule = CronModule;
exports.CronModule = CronModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                load: [
                    app_config_1.default,
                    aws_config_1.default,
                    google_config_1.default,
                    database_config_1.default,
                    config_2.default
                ],
                envFilePath: ['.env']
            }),
            schedule_1.ScheduleModule.forRoot(),
            database_module_1.DatabaseModule,
            logger_module_1.LoggerModule,
            middlewares_module_1.MiddlewareModule,
            // SocketModule,
            chat_room_module_1.ChatRoomModule,
            users_module_1.UsersModule,
            global_reminders_module_1.GlobalReminderModule,
            cronHelper_module_1.CronHelperModule.register({ enableCronJobs: true }),
            sqs_module_1.SqsModule.forRoot(true),
            availability_module_1.AvailabilityModule
        ]
    })
], CronModule);
//# sourceMappingURL=cron.module.js.map