"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddRemovedFromInvoiceColumns1749200108507 = void 0;
class AddRemovedFromInvoiceColumns1749200108507 {
    constructor() {
        this.name = 'AddRemovedFromInvoiceColumns1749200108507';
    }
    async up(queryRunner) {
        // Add removedFromInvoice column to patient_vaccinations table
        await queryRunner.query(`
			ALTER TABLE "patient_vaccinations" 
			ADD COLUMN "removed_from_invoice" boolean NOT NULL DEFAULT false
		`);
        // Add removedFromInvoice column to lab_reports table
        await queryRunner.query(`
			ALTER TABLE "lab_reports" 
			ADD COLUMN "removed_from_invoice" boolean NOT NULL DEFAULT false
		`);
        // Create indexes for better query performance
        await queryRunner.query(`
			CREATE INDEX "IDX_patient_vaccinations_removed_from_invoice" 
			ON "patient_vaccinations" ("removed_from_invoice")
		`);
        await queryRunner.query(`
			CREATE INDEX "IDX_lab_reports_removed_from_invoice" 
			ON "lab_reports" ("removed_from_invoice")
		`);
    }
    async down(queryRunner) {
        // Remove indexes
        await queryRunner.query(`
			DROP INDEX "IDX_patient_vaccinations_removed_from_invoice"
		`);
        await queryRunner.query(`
			DROP INDEX "IDX_lab_reports_removed_from_invoice"
		`);
        // Remove columns
        await queryRunner.query(`
			ALTER TABLE "patient_vaccinations" 
			DROP COLUMN "removed_from_invoice"
		`);
        await queryRunner.query(`
			ALTER TABLE "lab_reports" 
			DROP COLUMN "removed_from_invoice"
		`);
    }
}
exports.AddRemovedFromInvoiceColumns1749200108507 = AddRemovedFromInvoiceColumns1749200108507;
//# sourceMappingURL=1749200108507-AddRemovedFromInvoiceColumns.js.map