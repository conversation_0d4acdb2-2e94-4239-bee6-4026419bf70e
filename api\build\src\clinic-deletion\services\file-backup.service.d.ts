import { DataSource } from 'typeorm';
import { <PERSON><PERSON>og<PERSON> } from '../../utils/logger/winston-logger.service';
import { S3Service } from '../../utils/aws/s3/s3.service';
import { DeletionType, FileManifest } from '../dto/clinic-deletion.dto';
import { QueryManagerService } from './query-manager.service';
export interface FileBackupResult {
    manifest: FileManifest;
    totalFiles: number;
    totalSizeBytes: number;
    filesBackedUp: number;
    filesSkipped: number;
    duration: number;
}
export declare class FileBackupService {
    private readonly dataSource;
    private readonly logger;
    private readonly s3Service;
    private readonly queryManagerService;
    constructor(dataSource: DataSource, logger: WinstonLogger, s3Service: S3Service, queryManagerService: QueryManagerService);
    /**
     * Backup all S3 files for a clinic or brand
     */
    backupS3Files(targetType: DeletionType, targetId: string, backupId: string, backupBasePath: string): Promise<FileBackupResult>;
    /**
     * Rollback file backup by cleaning up uploaded files
     */
    private rollbackFileBackup;
    /**
     * Backup a single file
     */
    private backupSingleFile;
    /**
     * Get all file references for a clinic or brand
     */
    private getFileReferences;
    /**
     * @deprecated Use QueryManagerService.getS3BackupQueries() instead
     * This method has been moved to centralized QueryManagerService for consistency
     */
    private getS3FileQueries;
}
