"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTablePatientDocumentLibrary1735021228217 = void 0;
const typeorm_1 = require("typeorm");
class CreateTablePatientDocumentLibrary1735021228217 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: "patient_document_libraries",
            columns: [
                {
                    name: "id",
                    type: "uuid",
                    isPrimary: true,
                    default: 'uuid_generate_v4()'
                },
                {
                    name: "patient_id",
                    type: "uuid",
                    isNullable: false
                },
                {
                    name: "document_id",
                    type: "uuid",
                    isNullable: false
                },
                {
                    name: "doctor_id",
                    type: "uuid",
                    isNullable: true
                },
                {
                    name: "doctor_name",
                    type: "varchar",
                    isNullable: true
                },
                {
                    name: "document_send_status",
                    type: "enum",
                    enum: ["pending", "completed"],
                    isNullable: false
                },
                {
                    name: "signed_recieved",
                    type: "enum",
                    enum: ["pending", "completed"],
                    isNullable: false
                },
                {
                    name: "document_type",
                    type: "enum",
                    enum: ["signable", "notSignable"],
                    isNullable: false
                },
                {
                    name: "file_key",
                    type: "varchar",
                    isNullable: true
                },
                {
                    name: "signed_by",
                    type: "json",
                    isNullable: true
                },
                {
                    name: "created_at",
                    type: "timestamp",
                    default: "CURRENT_TIMESTAMP",
                    isNullable: false,
                },
                {
                    name: "updated_at",
                    type: "timestamp",
                    default: "CURRENT_TIMESTAMP",
                    onUpdate: "CURRENT_TIMESTAMP",
                    isNullable: false,
                },
                {
                    name: "created_by",
                    type: "uuid",
                    isNullable: true,
                },
                {
                    name: "updated_by",
                    type: "uuid",
                    isNullable: true,
                },
            ],
            foreignKeys: [
                {
                    columnNames: ["patient_id"],
                    referencedTableName: "patients",
                    referencedColumnNames: ["id"],
                    onDelete: "CASCADE"
                },
                {
                    columnNames: ["document_id"],
                    referencedTableName: "document_library",
                    referencedColumnNames: ["id"],
                    onDelete: "CASCADE"
                },
                {
                    columnNames: ['doctor_id'],
                    referencedTableName: 'users',
                    referencedColumnNames: ['id'],
                    onDelete: "CASCADE"
                }
            ]
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable("patient_document_libraries");
    }
}
exports.CreateTablePatientDocumentLibrary1735021228217 = CreateTablePatientDocumentLibrary1735021228217;
//# sourceMappingURL=1735021228217-createTablePatientDocumentLibrary.js.map