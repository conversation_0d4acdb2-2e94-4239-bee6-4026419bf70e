"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateDiagnosticTemplates1738223338542 = void 0;
const typeorm_1 = require("typeorm");
class CreateDiagnosticTemplates1738223338542 {
    async up(queryRunner) {
        await queryRunner.query(`
        CREATE TYPE diagnostic_templates_template_type_enum AS ENUM ('notes', 'table')
        `);
        await queryRunner.createTable(new typeorm_1.Table({
            name: "diagnostic_templates",
            columns: [
                {
                    name: "id",
                    type: "uuid",
                    isPrimary: true,
                    generationStrategy: "uuid",
                    default: "uuid_generate_v4()"
                },
                {
                    name: "template_name",
                    type: "varchar",
                    length: "100",
                    isNullable: false
                },
                {
                    name: "clinic_id",
                    type: "uuid",
                    isNullable: false
                },
                {
                    name: "assigned_diagnostics",
                    type: "jsonb",
                    isNullable: false,
                    comment: "Array of objects containing diagnostic id and name"
                },
                {
                    name: "template_type",
                    type: "enum",
                    enumName: "diagnostic_templates_template_type_enum",
                    default: "'notes'"
                },
                {
                    name: "table_structure",
                    type: "jsonb",
                    isNullable: true
                },
                {
                    name: "notes",
                    type: "text",
                    isNullable: true
                },
                {
                    name: "is_active",
                    type: "boolean",
                    default: true
                },
                {
                    name: "created_by",
                    type: "uuid",
                    isNullable: true
                },
                {
                    name: "updated_by",
                    type: "uuid",
                    isNullable: true
                },
                {
                    name: "created_at",
                    type: "timestamp",
                    default: "CURRENT_TIMESTAMP"
                },
                {
                    name: "updated_at",
                    type: "timestamp",
                    default: "CURRENT_TIMESTAMP",
                    onUpdate: "CURRENT_TIMESTAMP"
                }
            ]
        }), true);
        // Add foreign key constraints
        await queryRunner.createForeignKey("diagnostic_templates", new typeorm_1.TableForeignKey({
            columnNames: ["clinic_id"],
            referencedColumnNames: ["id"],
            referencedTableName: "clinics",
            onDelete: "CASCADE"
        }));
        // Add unique index on clinic_id and template_name
        await queryRunner.createIndex("diagnostic_templates", new typeorm_1.TableIndex({
            name: "IDX_clinic_template_name",
            columnNames: ["clinic_id", "template_name"],
            isUnique: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable("diagnostic_templates");
    }
}
exports.CreateDiagnosticTemplates1738223338542 = CreateDiagnosticTemplates1738223338542;
//# sourceMappingURL=1738223338542-CreateDiagnosticTemplates.js.map