"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddSlugToBrands1727405377203 = void 0;
const typeorm_1 = require("typeorm");
class AddSlugToBrands1727405377203 {
    constructor() {
        this.name = 'AddSlugToBrands1727405377203';
    }
    async up(queryRunner) {
        await queryRunner.addColumns('brands', [
            new typeorm_1.TableColumn({
                name: 'slug',
                type: 'varchar',
                isNullable: true
            })
        ]);
        await queryRunner.query(`
                UPDATE brands
                SET slug = LOWER(REPLACE(name, ' ', ''));
              `);
        await queryRunner.query(`
                ALTER TABLE brands
                ALTER COLUMN slug SET NOT NULL;
              `);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('brands', 'slug');
    }
}
exports.AddSlugToBrands1727405377203 = AddSlugToBrands1727405377203;
//# sourceMappingURL=1727405377203-add-slug-to-brands.js.map