import { EnumAmountType } from '../../payment-details/enums/enum-credit-types';
export declare class CreateCreditTransactionDto {
    ownerId: string;
    amount: number;
    transactionType: EnumAmountType;
    description: string;
    relatedInvoiceId?: string;
    metadata?: Record<string, any>;
}
export declare class OwnerCreditBalanceResponseDto {
    ownerId: string;
    creditBalance: number;
    ownerBalance: number;
}
