"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecreatePatientsTables1722242391046 = void 0;
const typeorm_1 = require("typeorm");
class RecreatePatientsTables1722242391046 {
    async up(queryRunner) {
        await queryRunner.dropTable('patients', true, true, true);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'patients',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'uuid'
                },
                {
                    name: 'patient_name',
                    type: 'varchar',
                    length: '100',
                    isNullable: false
                },
                {
                    name: 'species',
                    type: 'varchar',
                    length: '50',
                    isNullable: true
                },
                {
                    name: 'breed',
                    type: 'varchar',
                    length: '100',
                    isNullable: true
                },
                {
                    name: 'age',
                    type: 'varchar',
                    length: '20',
                    isNullable: true
                },
                {
                    name: 'gender',
                    type: 'enum',
                    enum: ['Male', 'Female', 'Unknown'],
                    default: "'Unknown'"
                },
                {
                    name: 'reproductive_status',
                    type: 'enum',
                    enum: ['Neutered', 'Spayed', 'Intact'],
                    isNullable: true
                },
                {
                    name: 'microchip_id',
                    type: 'varchar',
                    length: '50',
                    isNullable: true
                },
                {
                    name: 'identification',
                    type: 'text',
                    isNullable: true
                },
                {
                    name: 'allergies',
                    type: 'text',
                    isNullable: true
                },
                {
                    name: 'clinic_id',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                }
            ]
        }), true);
        await queryRunner.createForeignKeys('patients', [
            new typeorm_1.TableForeignKey({
                columnNames: ['clinic_id'],
                referencedColumnNames: ['id'],
                referencedTableName: 'clinics',
                onDelete: 'SET NULL'
            }),
            new typeorm_1.TableForeignKey({
                columnNames: ['created_by'],
                referencedColumnNames: ['id'],
                referencedTableName: 'users',
                onDelete: 'SET NULL'
            }),
            new typeorm_1.TableForeignKey({
                columnNames: ['updated_by'],
                referencedColumnNames: ['id'],
                referencedTableName: 'users',
                onDelete: 'SET NULL'
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('patients', true, true, true);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'patients',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'uuid'
                },
                {
                    name: 'patient_name',
                    type: 'varchar',
                    length: '100',
                    isNullable: false
                },
                {
                    name: 'species',
                    type: 'varchar',
                    length: '50',
                    isNullable: true
                },
                {
                    name: 'breed',
                    type: 'varchar',
                    length: '100',
                    isNullable: true
                },
                {
                    name: 'age',
                    type: 'varchar',
                    length: '20',
                    isNullable: true
                },
                {
                    name: 'gender',
                    type: 'enum',
                    enum: ['Male', 'Female', 'Unknown'],
                    default: "'Unknown'"
                },
                {
                    name: 'reproductive_status',
                    type: 'enum',
                    enum: ['Neutered', 'Spayed', 'Intact'],
                    isNullable: true
                },
                {
                    name: 'microchip_id',
                    type: 'varchar',
                    length: '50',
                    isNullable: true
                },
                {
                    name: 'identification',
                    type: 'text',
                    isNullable: true
                },
                {
                    name: 'allergies',
                    type: 'text',
                    isNullable: true
                },
                {
                    name: 'clinic_id',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                }
            ]
        }), true);
    }
}
exports.RecreatePatientsTables1722242391046 = RecreatePatientsTables1722242391046;
//# sourceMappingURL=1722242391046-DropAndCreatePatientsTable.js.map