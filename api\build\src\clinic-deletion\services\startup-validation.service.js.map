{"version": 3, "file": "startup-validation.service.js", "sourceRoot": "", "sources": ["../../../../src/clinic-deletion/services/startup-validation.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA0D;AAC1D,mEAA8D;AAC9D,oEAA0D;AAC1D,sFAA0E;AAE1E;;;GAGG;AAEI,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IACpC,YACkB,mBAAwC,EACxC,MAAqB;QADrB,wBAAmB,GAAnB,mBAAmB,CAAqB;QACxC,WAAM,GAAN,MAAM,CAAe;IACpC,CAAC;IAEJ,KAAK,CAAC,YAAY;QACjB,iDAAiD;QACjD,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC5C,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACjC,CAAC;IACF,CAAC;IAEO,wBAAwB;QAC/B,IAAI,CAAC,MAAM,CAAC,GAAG,CACd,kEAAkE,CAClE,CAAC;QAEF,IAAI,CAAC;YACJ,0CAA0C;YAC1C,MAAM,SAAS,GAAG;gBACjB,EAAE,IAAI,EAAE,kCAAY,CAAC,MAAM,EAAE,EAAE,EAAE,wBAAwB,EAAE;gBAC3D,EAAE,IAAI,EAAE,kCAAY,CAAC,KAAK,EAAE,EAAE,EAAE,uBAAuB,EAAE;aACzD,CAAC;YAEF,IAAI,SAAS,GAAG,KAAK,CAAC;YACtB,IAAI,aAAa,GAAG,CAAC,CAAC;YAEtB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBAClC,MAAM,MAAM,GACX,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CAChD,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,EAAE,CACX,CAAC;gBAEH,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;oBACnB,SAAS,GAAG,IAAI,CAAC;oBACjB,IAAI,CAAC,MAAM,CAAC,KAAK,CAChB,2CAA2C,QAAQ,CAAC,IAAI,YAAY,EACpE,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,CACzB,CAAC;gBACH,CAAC;gBAED,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAChC,aAAa,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;oBACxC,IAAI,CAAC,MAAM,CAAC,IAAI,CACf,kCAAkC,QAAQ,CAAC,IAAI,YAAY,EAC3D,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,CAC7B,CAAC;gBACH,CAAC;YACF,CAAC;YAED,IAAI,SAAS,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAChB,uEAAuE,CACvE,CAAC;YACH,CAAC;iBAAM,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;gBAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CACf,kDAAkD,aAAa,mDAAmD,CAClH,CAAC;YACH,CAAC;iBAAM,CAAC;gBACP,IAAI,CAAC,MAAM,CAAC,GAAG,CACd,uDAAuD,CACvD,CAAC;YACH,CAAC;QACF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAChB,8CAA8C,EAC9C,KAAK,CACL,CAAC;QACH,CAAC;IACF,CAAC;CACD,CAAA;AAxEY,4DAAwB;mCAAxB,wBAAwB;IADpC,IAAA,mBAAU,GAAE;qCAG2B,2CAAmB;QAChC,sCAAa;GAH3B,wBAAwB,CAwEpC"}