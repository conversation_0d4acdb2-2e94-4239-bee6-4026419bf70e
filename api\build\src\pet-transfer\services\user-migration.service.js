"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserMigrationService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../../users/entities/user.entity");
const clinic_user_entity_1 = require("../../clinics/entities/clinic-user.entity");
const patient_owner_entity_1 = require("../../patients/entities/patient-owner.entity");
const owner_brand_entity_1 = require("../../owners/entities/owner-brand.entity");
let UserMigrationService = class UserMigrationService {
    constructor(userRepository, clinicUserRepository, patientOwnerRepository, ownerBrandRepository) {
        this.userRepository = userRepository;
        this.clinicUserRepository = clinicUserRepository;
        this.patientOwnerRepository = patientOwnerRepository;
        this.ownerBrandRepository = ownerBrandRepository;
    }
    async migrateUsers(patientId, sourceClinicId, destinationClinicId, destinationBrandId) {
        const userMappings = new Map();
        const patientOwners = await this.patientOwnerRepository.find({
            where: { patientId, clinicId: sourceClinicId }
        });
        for (const owner of patientOwners) {
            const sourceUser = await this.userRepository.findOne({
                where: { id: owner.ownerId }
            });
            if (sourceUser) {
                let destinationUser = await this.userRepository.findOne({
                    where: {
                        firstName: sourceUser.firstName,
                        lastName: sourceUser.lastName,
                        brandId: destinationBrandId
                    }
                });
                if (!destinationUser) {
                    // Create a "shadow" user
                    destinationUser = this.userRepository.create({
                        ...sourceUser,
                        id: undefined, // Let the database generate a new ID
                        brandId: destinationBrandId,
                        isActive: false // Mark as inactive
                    });
                    await this.userRepository.save(destinationUser);
                }
                userMappings.set(sourceUser.id, destinationUser.id);
                // Ensure a ClinicUser record exists
                let clinicUser = await this.clinicUserRepository.findOne({
                    where: {
                        userId: destinationUser.id,
                        clinicId: destinationClinicId
                    }
                });
                if (!clinicUser) {
                    clinicUser = this.clinicUserRepository.create({
                        userId: destinationUser.id,
                        clinicId: destinationClinicId,
                        brandId: destinationBrandId
                    });
                    await this.clinicUserRepository.save(clinicUser);
                }
                // Ensure an OwnerBrand record exists
                let ownerBrand = await this.ownerBrandRepository.findOne({
                    where: {
                        globalOwnerId: destinationUser.id,
                        brandId: destinationBrandId
                    }
                });
                if (!ownerBrand) {
                    ownerBrand = this.ownerBrandRepository.create({
                        globalOwnerId: destinationUser.id,
                        brandId: destinationBrandId,
                        firstName: destinationUser.firstName,
                        lastName: destinationUser.lastName
                    });
                    await this.ownerBrandRepository.save(ownerBrand);
                }
            }
        }
        return userMappings;
    }
};
exports.UserMigrationService = UserMigrationService;
exports.UserMigrationService = UserMigrationService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(clinic_user_entity_1.ClinicUser)),
    __param(2, (0, typeorm_1.InjectRepository)(patient_owner_entity_1.PatientOwner)),
    __param(3, (0, typeorm_1.InjectRepository)(owner_brand_entity_1.OwnerBrand)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], UserMigrationService);
//# sourceMappingURL=user-migration.service.js.map