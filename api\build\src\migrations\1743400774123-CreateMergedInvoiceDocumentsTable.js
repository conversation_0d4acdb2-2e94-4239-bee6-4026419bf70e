"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateMergedInvoiceDocumentsTable1743400774123 = void 0;
const typeorm_1 = require("typeorm");
class CreateMergedInvoiceDocumentsTable1743400774123 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'merged_invoice_documents',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'owner_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'request_id',
                    type: 'uuid',
                    isNullable: false,
                    comment: 'Unique identifier for each document request'
                },
                {
                    name: 'invoice_ids_hash',
                    type: 'varchar',
                    length: '32',
                    isNullable: false,
                    comment: 'MD5 hash of the sorted invoice reference alpha IDs'
                },
                {
                    name: 'invoice_ids',
                    type: 'jsonb',
                    isNullable: false,
                    comment: 'Array of invoice reference alpha IDs'
                },
                {
                    name: 'file_key',
                    type: 'varchar',
                    length: '255',
                    isNullable: true,
                    comment: 'S3 file key for the merged document'
                },
                {
                    name: 'is_generating',
                    type: 'boolean',
                    default: true,
                    isNullable: false,
                    comment: 'Flag indicating if document is currently being generated'
                },
                {
                    name: 'expires_at',
                    type: 'timestamp',
                    isNullable: true,
                    comment: 'When this document should be deleted'
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                }
            ]
        }), true);
        // Create unique index on request_id
        await queryRunner.createIndex('merged_invoice_documents', new typeorm_1.TableIndex({
            name: 'IDX_MERGED_INVOICE_REQUEST_ID',
            columnNames: ['request_id'],
            isUnique: true
        }));
        // Create index on is_generating for faster status checks
        await queryRunner.createIndex('merged_invoice_documents', new typeorm_1.TableIndex({
            name: 'IDX_MERGED_INVOICE_GENERATING',
            columnNames: ['is_generating']
        }));
        // Create index on expires_at for easier cleanup
        await queryRunner.createIndex('merged_invoice_documents', new typeorm_1.TableIndex({
            name: 'IDX_MERGED_INVOICE_EXPIRES',
            columnNames: ['expires_at']
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropIndex('merged_invoice_documents', 'IDX_MERGED_INVOICE_EXPIRES');
        await queryRunner.dropIndex('merged_invoice_documents', 'IDX_MERGED_INVOICE_GENERATING');
        await queryRunner.dropIndex('merged_invoice_documents', 'IDX_MERGED_INVOICE_REQUEST_ID');
        await queryRunner.dropTable('merged_invoice_documents');
    }
}
exports.CreateMergedInvoiceDocumentsTable1743400774123 = CreateMergedInvoiceDocumentsTable1743400774123;
//# sourceMappingURL=1743400774123-CreateMergedInvoiceDocumentsTable.js.map