"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateDiagnosticReportNote = void 0;
const generateDiagnosticReportNote = ({ diagnosticName, diagnosticNumber, diagnosticDate, clinicName, clinicAddress, clinicStreet, clinic<PERSON>ity, clinicPhone, clinicEmail, clinicWebsite, petName, petBreed, ownerName, ownerEmail, ownerPhone, templateName, assessmentText, }) => {
    const formattedAssessmentText = assessmentText.replace(/\n/g, '<br>');
    return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Diagnostic Report</title>
            <link rel="preconnect" href="https://fonts.googleapis.com">
            <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
            <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600&display=swap" rel="stylesheet">
            <style>
                @page {
                    size: A4;
                    margin: 0;
                }
                
                html, body {
                    width: 210mm;
                    height: 297mm;
                    margin: 0;
                    padding: 0;
                }
                
                body {
                    font-family: 'Inter', sans-serif;
                    color: #59645D;
                    background: #FAFAFA;
                    font-size: 14px;
                    line-height: 1.5;
                }
    
                .diagnostic-container {
                    width: 210mm;
                    height: 297mm;
                    margin: 0 auto;
                    padding: 40px;
                    background: #fff;
                    box-sizing: border-box;
                }
    
                .diagnostic-header {
                    position: relative;
                    padding-bottom: 20px;
                    border-bottom: 0.5px solid #E5E5E5;
                }
    
                .diagnostic-header h1 {
                    font-size: 36px;
                    font-weight: 200;
                    color: #59645D;
                    margin: 0;
                }
    
                .diagnostic-subheader {
                    color: #59645D;
                    font-size: 18px;
                    margin-top: 8px;
                }
    
                .header-image {
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 80px;
                    height: 80px;
                    background: #E5E5E5;
                }
    
                .clinic-details {
                    margin-top: 30px;
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                    padding-bottom: 20px;
                    border-bottom: 0.5px solid #E5E5E5;
                }
    
                .clinic-name {
                    font-size: 14px;
                    font-weight: 400;
                    color: #59645D;
                    margin-bottom: 4px;
                }
    
                .clinic-info {
                    color: #59645D;
                    font-size: 10px;
                    line-height: 1.6;
                }
    
                .clinic-contact {
                    text-align: right;
                }
    
                .pet-details {
                    margin-top: 30px;
                }
    
                .pet-name {
                    font-size: 14px;
                    font-weight: 500;
                    color: #59645D;
                    margin-bottom: 4px;
                }

                .pet-breed{
                    font-size: 14px;
                    font-weight: 300;
                    color: #59645D;
                }

                .pet-info {
                    color: #666;
                    font-size: 12px;
                    line-height: 1.6;
                }
    
                .template-name {
                    font-size: 14px;
                    font-weight: 500;
                    color: #59645D;
                    margin: 30px 0 15px;
                    border-bottom: 0.5px solid #E5E5E5;
                }
    
                .assessment-text {
                    color: #59645D;
                    font-weight: 400;
                    font-size: 10px;
                    line-height: 1.6;
                    margin-top: 10px;
                    white-space: pre-line;
                }
    
                @media print {
                    body {
                        background: #fff;
                    }
                    .diagnostic-container {
                        padding: 20px;
                    }
                }
            </style>
        </head>
        <body>
            <div class="diagnostic-container">
                <div class="diagnostic-header">
                    <h1>${diagnosticName}</h1>
                    <div class="diagnostic-subheader">
                        Diagnostic Note #${diagnosticNumber} | ${diagnosticDate}
                    </div>
                </div>
    
                <div class="clinic-details">
                    <div>
                        <div class="clinic-name">${clinicName}</div>
                        <div class="clinic-info">
                            ${clinicAddress}<br>
                            ${clinicCity}
                        </div>
                    </div>
                    <div class="clinic-contact">
                        <div class="clinic-name">${" "}</div>
                        <div class="clinic-info">
                            ${clinicPhone}<br>
                            ${clinicEmail}<br>
                            ${clinicWebsite}
                        </div>
                    </div>
                </div>
    
                <div class="pet-details">
                    <div class="pet-name">${petName} | <span class="pet-breed"> ${petBreed} </span></div>
                    <div class="pet-info">
                        ${ownerName}<br>
                        ${ownerEmail}<br>
                        ${ownerPhone}
                    </div>
                </div>
    
                <div class="template-name">${templateName}</div>
                <div class="assessment-text">
                    ${formattedAssessmentText}
                </div>
            </div>
        </body>
        </html>
        `;
};
exports.generateDiagnosticReportNote = generateDiagnosticReportNote;
module.exports.generateDiagnosticReportNote = exports.generateDiagnosticReportNote;
//# sourceMappingURL=diagnosticNote.js.map