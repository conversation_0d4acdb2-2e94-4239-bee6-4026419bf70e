"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddClinicUserReferenceToTasks1727062374221 = void 0;
const typeorm_1 = require("typeorm");
class AddClinicUserReferenceToTasks1727062374221 {
    async up(queryRunner) {
        const table = await queryRunner.getTable('tasks');
        if (table) {
            const foreignKeys = table.foreignKeys;
            for (const foreignKey of foreignKeys) {
                await queryRunner.dropForeignKey('tasks', foreignKey);
            }
        }
        await queryRunner.createForeignKey('tasks', new typeorm_1.TableForeignKey({
            columnNames: ['user_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinic_users',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable('tasks');
        if (table) {
            const foreignKeys = table.foreignKeys;
            for (const foreignKey of foreignKeys) {
                await queryRunner.dropForeignKey('tasks', foreignKey);
            }
        }
        await queryRunner.createForeignKey('tasks', new typeorm_1.TableForeignKey({
            columnNames: ['user_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
    }
}
exports.AddClinicUserReferenceToTasks1727062374221 = AddClinicUserReferenceToTasks1727062374221;
//# sourceMappingURL=1727062374221-add-clinic-user-reference-to-tasks.js.map