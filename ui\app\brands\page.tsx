'use client';

import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Heading } from '../atoms';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import { BasicInfo, Modal, Searchbar, Table } from '../molecules';
import { PaginationType } from '../molecules/Table';
import { useCreateBrand, useGetAllBrands } from '../services/brand.queries';
import ConfirmModal from '../organisms/patient/ConfirmModal';
import { AlertT } from '../atoms/Alert';
import classNames from 'classnames';
import CreateBrand from '../organisms/brand/CreateBrand';
import moment, { MomentInput } from 'moment';
import CreateClinic from '../organisms/brand/CreateClinic';
import {
    useCreateClinic,
    useDeactivateClinicMutation,
    useGetAllClinics,
    useReactivateClinicMutation,
    useSoftDeleteClinicMutation,
    UserFormData,
} from '../services/clinic.queries';
import Tabs from '../molecules/Tabs';
import IconAdd from '../atoms/customIcons/IconAdd.svg';
import ViewClinicDetails from '../organisms/brand/ViewClinicDetail';
import ViewBrandDetails from '../organisms/brand/ViewBrandDetail';
import EditClinicForm from '../organisms/brand/EditClinicDetail';
import EditBrandForm from '../organisms/brand/EditBrandDetail';

export interface ClinicFormData {
    name: string;
    brandId: string;
    adminFirstName: string;
    adminLastName: string;
    adminEmail: string;
    adminMobile: string;
}

export default function BrandsPage() {
    const [isBrandsModalOpen, setIsBrandsModalOpen] = useState(false);
    const [isClinicModalOpen, setIsClinicModalOpen] = useState(false);
    const [viewClinicModal, setViewClinicModal] = useState(false);
    const [editClinicModal, setEditClinicModal] = useState(false);
    const [viewBrandModal, setViewBrandModal] = useState(false);
    const [editBrandModal, setEditBrandModal] = useState(false);
    const [clinicId, setClinicId] = useState('');
    const [brandId, setBrandId] = useState('');
    const [selectedClinic, setSelectedClinic] = useState(null);
    const [selectedBrand, setSelectedBrand] = useState(null);

    const [confirmModal, setConfirmModal] = useState<{
        isOpen: boolean;
        clinicId: string | null;
        type?: 'deactivate' | 'reactivate' | 'delete';
    }>({
        isOpen: false,
        clinicId: null,
    });
    const [showAlert, setShowAlert] = useState<AlertT>({
        isOpen: false,
        label: '',
        variant: 'success',
    });

    // Pagination state management
    const limit = 10;
    const [brandsPagination, setBrandsPagination] = useState<PaginationType>({
        pageIndex: 0,
        pageSize: limit,
    });
    const [clinicsPagination, setClinicsPagination] = useState<PaginationType>({
        pageIndex: 0,
        pageSize: limit,
    });
    const [brandsSearchTerm, setBrandsSearchTerm] = useState('');
    const [clinicsSearchTerm, setClinicsSearchTerm] = useState('');

    const createBrandValidationSchema = yup.object().shape({
        brandName: yup
            .string()
            .required('Brand Name is required')
            .max(50, 'Brand Name cannot exceed 50 characters'),
    });

    const createClinicValidationSchema = yup.object().shape({
        name: yup
            .string()
            .required('Clinic Name is required')
            .max(50, 'Clinic Name cannot exceed 50 characters'),
        brandId: yup.string().required('Brand Name is required'),
        adminFirstName: yup
            .string()
            .required('Admin First Name is required')
            .max(50, 'Admin First Name cannot exceed 50 characters'),
        adminLastName: yup
            .string()
            .required('Admin Last Name is required')
            .max(50, 'Admin Last Name cannot exceed 50 characters'),
        adminEmail: yup
            .string()
            .required('Admin Email is required')
            .email('Invalid Email'),
        adminMobile: yup
            .string()
            .required('Admin Mobile is required')
            .matches(
                /^[0-9]+$/,
                'Invalid Mobile Number. Mobile number should contain only numbers'
            )
            .min(10, 'Mobile number should be of 10 digits')
            .max(10, 'Mobile number should be of 10 digits'),
    });

    const {
        control,
        handleSubmit,
        setValue,
        getValues,
        watch,
        formState: { errors },
        reset,
    } = useForm({
        defaultValues: {
            brandName: '',
        },
        resolver: yupResolver(createBrandValidationSchema),
        mode: 'onChange',
    });

    const {
        control: controlClinic,
        handleSubmit: handleSubmitClinic,
        setValue: setValueClinic,
        getValues: getValuesClinic,
        watch: watchClinic,
        formState: { errors: clinicErrors },
        reset: resetClinic,
    } = useForm<ClinicFormData>({
        defaultValues: {
            name: '',
            brandId: '',
            adminFirstName: '',
            adminLastName: '',
            adminEmail: '',
            adminMobile: '',
        },
        resolver: yupResolver(createClinicValidationSchema),
        mode: 'onChange',
    });

    const { mutate: createClinic } = useCreateClinic();

    const {
        mutate: createBrand,
        isSuccess,
        isError,
        error,
        data,
    } = useCreateBrand();

    const { data: brandsData, status: brandsStatus } = useGetAllBrands(
        brandsPagination.pageIndex + 1,
        brandsPagination.pageSize,
        brandsSearchTerm,
        'DESC'
    );
    const { data: clinicsData, status: clinicsStatus } = useGetAllClinics(
        clinicsPagination.pageIndex + 1,
        clinicsPagination.pageSize,
        'DESC'
    );

    const deactivateClinicMutation = useDeactivateClinicMutation();
    const reactivateClinicMutation = useReactivateClinicMutation();
    const softDeleteClinicMutation = useSoftDeleteClinicMutation();

    const handleDeactivateClinic = (id: string) => {
        setConfirmModal({
            isOpen: true,
            clinicId: id,
            type: 'deactivate',
        });
    };

    const handleReactivateClinic = (id: string) => {
        setConfirmModal({
            isOpen: true,
            clinicId: id,
            type: 'reactivate',
        });
    };

    const handleSoftDeleteClinic = (id: string) => {
        setConfirmModal({
            isOpen: true,
            clinicId: id,
            type: 'delete',
        });
    };

    const handleConfirmAction = () => {
        if (!confirmModal.clinicId || !confirmModal.type) return;

        const { clinicId, type } = confirmModal;

        switch (type) {
            case 'deactivate':
                deactivateClinicMutation.mutate(clinicId, {
                    onSuccess: () => {
                        setConfirmModal({ isOpen: false, clinicId: null });
                        setShowAlert({
                            isOpen: true,
                            label: 'Clinic deactivated successfully!',
                            variant: 'success',
                        });
                    },
                    onError: () => {
                        setShowAlert({
                            isOpen: true,
                            label: 'Failed to deactivate clinic.',
                            variant: 'error',
                        });
                    },
                });
                break;

            case 'reactivate':
                reactivateClinicMutation.mutate(clinicId, {
                    onSuccess: () => {
                        setConfirmModal({ isOpen: false, clinicId: null });
                        setShowAlert({
                            isOpen: true,
                            label: 'Clinic reactivated successfully!',
                            variant: 'success',
                        });
                    },
                    onError: () => {
                        setShowAlert({
                            isOpen: true,
                            label: 'Failed to reactivate clinic.',
                            variant: 'error',
                        });
                    },
                });
                break;

            case 'delete':
                softDeleteClinicMutation.mutate(clinicId, {
                    onSuccess: () => {
                        setConfirmModal({ isOpen: false, clinicId: null });
                        setShowAlert({
                            isOpen: true,
                            label: 'Clinic deleted successfully!',
                            variant: 'success',
                        });
                    },
                    onError: () => {
                        setShowAlert({
                            isOpen: true,
                            label: 'Failed to delete clinic.',
                            variant: 'error',
                        });
                    },
                });
                break;
        }
    };

    React.useEffect(() => {
        if (isSuccess) {
            if (data.status === false && data.statusCode === 409) {
                setShowAlert({
                    isOpen: true,
                    label: 'Brand with this name already exists!',
                    variant: 'error',
                });
            } else if (data.status === true) {
                setShowAlert({
                    isOpen: true,
                    label: 'Brand created successfully!',
                    variant: 'success',
                });
            } else {
                setShowAlert({
                    isOpen: true,
                    label: 'Failed to create brand.',
                    variant: 'error',
                });
            }
            reset();
            setIsBrandsModalOpen(false);
        } else if (isError) {
            setShowAlert({
                isOpen: true,
                label: 'Failed to create brand.',
                variant: 'error',
            });
        }
    }, [isSuccess, isError, data, error]);

    const handleCreateBrand = (data: { brandName: string }) => {
        createBrand(data.brandName);
    };

    const handleCreateClinic = (data: ClinicFormData) => {
        createClinic(data, {
            onSuccess: (data) => {
                if (data.status === false && data.statusCode === 409) {
                    setShowAlert({
                        isOpen: true,
                        label: 'Clinic with this name already exists!',
                        variant: 'error',
                    });
                } else if (data.status === false && data.statusCode === 400) {
                    setShowAlert({
                        isOpen: true,
                        label: 'An Admin with this email already exists',
                        variant: 'error',
                    });
                } else if (data.status === true) {
                    setShowAlert({
                        isOpen: true,
                        label: 'Clinic created successfully!',
                        variant: 'success',
                    });
                } else {
                    setShowAlert({
                        isOpen: true,
                        label: 'Failed to create clinic.',
                        variant: 'error',
                    });
                }
                resetClinic();
                setIsClinicModalOpen(false);
            },
        });
    };

    const handleViewClinic = (id: string) => {
        const clinic = clinicsData?.data?.clinics?.find(
            (clinic: { id: string }) => clinic.id === id
        );

        if (clinic) {
            const brand = brandsData?.data?.find(
                (brand: { id: string }) => brand.id === clinic.brandId
            );
            setSelectedClinic({
                ...clinic,
                brandName: brand?.name || 'Unknown Brand',
            });
            setClinicId(id);
            setViewClinicModal(true);
        } else {
            console.error('Clinic not found');
        }
    };

    const handleEditClinic = (id: string) => {
        const clinic = clinicsData?.data?.clinics?.find(
            (clinic: { id: string }) => clinic.id === id
        );

        if (clinic) {
            const brand = brandsData?.data?.find(
                (brand: { id: string }) => brand.id === clinic.brandId
            );
            setSelectedClinic({
                ...clinic,
                brandName: brand?.name || 'Unknown Brand',
            });
            setClinicId(id);
            setEditClinicModal(true);
        } else {
            console.error('Clinic not found');
        }
    };

    const handleViewBrand = (id: string) => {
        const brand = brandsData?.data?.find(
            (brand: { id: string }) => brand.id === id
        );
        if (brand) {
            setSelectedBrand(brand);
            setBrandId(id);
            setViewBrandModal(true);
        }
    };

    const handleEditBrand = (id: string) => {
        const brand = brandsData?.data?.find(
            (brand: { id: string }) => brand.id === id
        );
        if (brand) {
            setSelectedBrand(brand);
            setBrandId(id);
            setEditBrandModal(true);
        }
    };

    const columnHelper = createColumnHelper<any>();

    const clinicColumns: ColumnDef<any, any>[] = [
        columnHelper.accessor('name', {
            header: 'Clinic Name',
            cell: (info) => (
                <div>{String(info.getValue()) || 'Unknown Clinic'}</div>
            ),
        }),
        columnHelper.accessor(
            (row) => `${row.adminFirstName ?? ''} ${row.adminLastName ?? ''}`,
            {
                id: 'adminFullName',
                header: 'Admin Name',
                cell: (info) => <div>{info.getValue() || '--'}</div>,
            }
        ),
        columnHelper.accessor('adminEmail', {
            header: 'Admin Email',
            size: 20,
            meta: {
                tdClassName: 'max-w-[223px]',
                thClassName: 'max-w-[223px]',
            },
            cell: (info) => <div>{info.getValue() || '--'}</div>,
        }),
        columnHelper.accessor('adminMobile', {
            header: 'Admin Mobile',
            cell: (info) => <div>{info.getValue() || '--'}</div>,
        }),
        columnHelper.accessor('createdAt', {
            header: 'Created At',
            cell: (info) => (
                <div>
                    {moment(info.getValue() as MomentInput)?.format(
                        'DD MMM YYYY'
                    )}
                </div>
            ),
        }),
        columnHelper.accessor('brandId', {
            header: 'Brand',
            cell: (info) => {
                const brandId = info.getValue();
                const brand = brandsData?.data?.find(
                    (b: any) => b.id === brandId
                );

                return <div>{brand?.name || 'Unknown Brand'}</div>;
            },
        }),
        columnHelper.accessor('isActive', {
            header: 'Status',
            cell: (info) => {
                const status = info.getValue();
                let displayStatus = '🔴️ Inactive';
                if (status == true) displayStatus = '🟢 Active';
                return <div>{displayStatus}</div>;
            },
        }),
        {
            id: 'action',
            header: 'Action',
            meta: {
                onActionClick: ({ row, action }) => {
                    if (action.id === 'edit') {
                        handleEditClinic(row.original.id);
                    }
                    if (action.id === 'view') {
                        handleViewClinic(row.original.id);
                    }
                    if (action.id === 'deactivate') {
                        handleDeactivateClinic(row.original.id);
                    }
                    if (action.id === 'reactivate') {
                        handleReactivateClinic(row.original.id);
                    }
                    if (action.id === 'delete') {
                        handleSoftDeleteClinic(row.original.id);
                    }
                },
                actionOptions: (row) => {
                    const isActive = row.original.isActive;
                    const baseOptions = [
                        {
                            id: 'edit',
                            label: 'Edit',
                        },
                        {
                            id: 'view',
                            label: 'View',
                        },
                    ];

                    // Show deactivate only for active clinics
                    if (isActive) {
                        baseOptions.push({
                            id: 'deactivate',
                            label: 'Deactivate',
                        });
                    } else {
                        // Show reactivate only for inactive clinics
                        baseOptions.push({
                            id: 'reactivate',
                            label: 'Reactivate',
                        });
                    }

                    // Always show delete option
                    baseOptions.push({
                        id: 'delete',
                        label: 'Delete',
                    });

                    return baseOptions;
                },
            },
        },
    ];
    const brandColumns: ColumnDef<any, any>[] = [
        columnHelper.accessor('name', {
            header: 'Brand Name',
            cell: (info) => (
                <div>{String(info.getValue()) || 'Unknown Clinic'}</div>
            ),
        }),
        columnHelper.accessor('createdAt', {
            header: 'Created At',
            cell: (info) => (
                <div>
                    {moment(info.getValue() as MomentInput)?.format(
                        'DD MMM YYYY'
                    )}
                </div>
            ),
        }),
        {
            id: 'action',
            header: 'Action',
            meta: {
                onActionClick: ({ row, action }) => {
                    if (action.id === 'edit') {
                        handleEditBrand(row.original.id);
                    }
                    if (action.id === 'view') {
                        handleViewBrand(row.original.id);
                    }
                },
                actionOptions: [
                    {
                        id: 'edit',
                        label: 'Edit',
                    },
                    {
                        id: 'view',
                        label: 'View',
                    },
                ],
            },
        },
    ];

    const brandOptions = async (search: string, loadedOptions: unknown[]) => {
        const brands = brandsData?.data ?? [];
        const filteredBrands = brands.filter((item: any) =>
            item.name.toLowerCase().includes(search.toLowerCase())
        );

        const options = filteredBrands.map((item: any) => ({
            value: item.id,
            label: item.name,
        }));

        return {
            options,
            hasMore: false,
        };
    };

    // Helper functions for pagination data
    const getBrandsData = () => {
        return {
            pagination: brandsPagination,
            listLoadStatus: brandsStatus,
            setPagination: setBrandsPagination,
            tableData: brandsData?.data?.brands ?? [],
            totalPages: brandsData?.data?.total
                ? Math.ceil(brandsData.data.total / limit)
                : 1,
        };
    };

    const getClinicsData = () => {
        return {
            pagination: clinicsPagination,
            listLoadStatus: clinicsStatus,
            setPagination: setClinicsPagination,
            tableData: clinicsData?.data?.clinics ?? [],
            totalPages: clinicsData?.data?.total
                ? Math.ceil(clinicsData.data.total / limit)
                : 1,
        };
    };

    return (
        <div className="h-full w-full flex flex-col gap-4">
            <div className="w-full flex items-center justify-end">
                <div className="flex items-center justify-center gap-2">
                    <Button
                        icon
                        id="create-brand"
                        type="button"
                        variant="primary"
                        onClick={() => setIsBrandsModalOpen(true)}
                    >
                        <IconAdd size={16} />
                        <span>Create Brand</span>
                    </Button>
                    <Button
                        icon
                        id="create-brand"
                        type="button"
                        variant="primary"
                        onClick={() => setIsClinicModalOpen(true)}
                    >
                        <IconAdd size={16} />
                        <span>Create Clinic</span>
                    </Button>
                </div>
            </div>
            <div className="w-full flex items-center justify-between">
                <Heading
                    type="h4"
                    fontWeight="font-medium"
                    dataAutomation="verify-page-heading"
                >
                    Brands
                </Heading>

                <Searchbar
                    id="patients-search-bar"
                    name="SearchBar"
                    placeholder="Search..."
                    // register={register}
                />
            </div>
            <div className="w-full">
                {brandsStatus === 'pending' ? (
                    <div>Loading...</div>
                ) : (
                    <Tabs
                        tabContentClass=" py-6 min-h-[400px] "
                        tabs={[
                            {
                                className: '',
                                id: 'clinics',
                                isDisabled: false,
                                label: 'Clinics',
                                tabContent: (
                                    <Table
                                        columns={clinicColumns}
                                        tableData={
                                            clinicsData?.data?.clinics ?? []
                                        }
                                        listLoadStatus={clinicsStatus}
                                    />
                                ),
                            },
                            {
                                className: '',
                                id: 'brands',
                                isDisabled: false,
                                label: 'Brands',
                                tabContent: (
                                    <Table
                                        columns={brandColumns}
                                        tableData={brandsData?.data ?? []}
                                        listLoadStatus={brandsStatus}
                                    />
                                ),
                            },
                        ]}
                    />
                )}
            </div>
            {isBrandsModalOpen && (
                <CreateBrand
                    isOpen={isBrandsModalOpen}
                    onClose={() => setIsBrandsModalOpen(false)}
                    control={control}
                    errors={errors as { [key: string]: { message: string } }}
                    setValue={setValue}
                    getValues={getValues}
                    watch={watch}
                    handleCancel={() => setIsBrandsModalOpen(false)}
                    handleCreateBrand={handleCreateBrand}
                    handleSubmit={handleSubmit}
                />
            )}
            {isClinicModalOpen && (
                <CreateClinic
                    isOpen={isClinicModalOpen}
                    onClose={() => setIsClinicModalOpen(false)}
                    control={controlClinic}
                    errors={
                        clinicErrors as { [key: string]: { message: string } }
                    }
                    setValue={setValueClinic}
                    getValues={getValuesClinic}
                    watch={watchClinic}
                    handleCancel={() => setIsClinicModalOpen(false)}
                    handleSubmit={handleSubmitClinic}
                    handleCreateClinic={handleCreateClinic}
                    brandOptions={brandOptions}
                    brandsData={brandsData}
                />
            )}

            {confirmModal.isOpen && (
                <ConfirmModal
                    isOpen={confirmModal.isOpen}
                    alertType="warning"
                    modalDescription={
                        confirmModal.type === 'deactivate'
                            ? 'Are you sure you want to deactivate this clinic?'
                            : confirmModal.type === 'reactivate'
                              ? 'Are you sure you want to reactivate this clinic?'
                              : 'Are you sure you want to delete this clinic? This action cannot be undone.'
                    }
                    modalTitle={
                        confirmModal.type === 'deactivate'
                            ? 'Deactivate Clinic'
                            : confirmModal.type === 'reactivate'
                              ? 'Reactivate Clinic'
                              : 'Delete Clinic'
                    }
                    primaryBtnProps={{
                        label: 'Yes',
                        onClick: handleConfirmAction,
                        dataAutomation: `${confirmModal.type}-clinic-yes-button`,
                    }}
                    primaryBtnDisabled={false}
                    onClose={() =>
                        setConfirmModal({ isOpen: false, clinicId: null })
                    }
                    secondaryBtnProps={{
                        label: 'No',
                        onClick: () =>
                            setConfirmModal({ isOpen: false, clinicId: null }),
                        dataAutomation: `${confirmModal.type}-clinic-no-button`,
                    }}
                    dataAutomation={''}
                />
            )}

            {viewClinicModal && (
                <ViewClinicDetails
                    clinic={selectedClinic}
                    isOpen={viewClinicModal}
                    onClose={() => setViewClinicModal(false)}
                />
            )}

            {editClinicModal && (
                <Modal
                    isOpen={editClinicModal}
                    modalTitle="Edit Clinic Details"
                    onClose={() => setEditClinicModal(false)}
                    children={
                        <EditClinicForm
                            clinic={selectedClinic}
                            setModalOpen={setEditClinicModal}
                        />
                    }
                />
            )}

            {viewBrandModal && (
                <ViewBrandDetails
                    brand={selectedBrand}
                    isOpen={viewBrandModal}
                    onClose={() => setViewBrandModal(false)}
                />
            )}

            {editBrandModal && (
                <Modal
                    isOpen={true}
                    modalTitle="Edit Brand Details"
                    onClose={() => setEditBrandModal(false)}
                    children={
                        <EditBrandForm
                            brandId={brandId}
                            control={selectedBrand} // Pass the selected brand data
                            handleSubmit={(data: any) => {
                                // Call your update brand mutation here
                                console.log('Updated Brand:', data);
                            }}
                            errors={
                                errors as { [key: string]: { message: string } }
                            }
                            setValue={setValue}
                        />
                    }
                />
            )}

            <Alert
                className={classNames(
                    'font-semibold fixed top-4 left-1/2 -translate-x-1/2 z-50 w-[560px] transition-opacity',
                    showAlert.isOpen
                        ? 'opacity-100 scale-100'
                        : 'opacity-0 scale-0'
                )}
                {...showAlert}
                onClose={() =>
                    setShowAlert({
                        isOpen: false,
                        label: '',
                        variant: 'success',
                    })
                }
            />
        </div>
    );
}
