"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddColumnToClinicLabReports1740568962464 = void 0;
const typeorm_1 = require("typeorm");
class AddColumnToClinicLabReports1740568962464 {
    async up(queryRunner) {
        await queryRunner.addColumn('clinic_lab_reports', new typeorm_1.TableColumn({
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('clinic_lab_reports', 'deleted_at');
    }
}
exports.AddColumnToClinicLabReports1740568962464 = AddColumnToClinicLabReports1740568962464;
//# sourceMappingURL=1740568962464-addColumnToClinicLabReports.js.map