{"version": 3, "file": "1738577974587-CreateGlobalOwnerAndOwnerBrand.js", "sourceRoot": "", "sources": ["../../../src/migrations/1738577974587-CreateGlobalOwnerAndOwnerBrand.ts"], "names": [], "mappings": ";;;AAAA,qCAKiB;AAEjB,MAAa,2CAA2C;IAGhD,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,kDAAkD;QAClD,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;GASvB,CAAC,CAAC;QAEH,6BAA6B;QAC7B,MAAM,WAAW,CAAC,WAAW,CAC5B,IAAI,eAAK,CAAC;YACT,IAAI,EAAE,eAAe;YACrB,OAAO,EAAE;gBACR;oBACC,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,IAAI;oBACf,kBAAkB,EAAE,MAAM;oBAC1B,OAAO,EAAE,oBAAoB;iBAC7B;gBACD;oBACC,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;oBACZ,QAAQ,EAAE,IAAI;iBACd;gBACD;oBACC,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,GAAG;oBACX,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,OAAO;iBAChB;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,OAAO;iBAChB;aACD;SACD,CAAC,EACF,IAAI,CACJ,CAAC;QAEF,4BAA4B;QAC5B,MAAM,WAAW,CAAC,WAAW,CAC5B,IAAI,eAAK,CAAC;YACT,IAAI,EAAE,cAAc;YACpB,OAAO,EAAE;gBACR;oBACC,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,IAAI;oBACf,kBAAkB,EAAE,MAAM;oBAC1B,OAAO,EAAE,oBAAoB;iBAC7B;gBACD;oBACC,IAAI,EAAE,iBAAiB;oBACvB,IAAI,EAAE,MAAM;iBACZ;gBACD;oBACC,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,KAAK;iBACjB;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;iBACZ;gBACD;oBACC,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,IAAI;iBACZ;gBACD;oBACC,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBAChB;gBACD;oBACC,IAAI,EAAE,eAAe;oBACrB,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,CAAC;iBACV;gBACD;oBACC,IAAI,EAAE,iBAAiB;oBACvB,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,EAAE;oBACb,KAAK,EAAE,CAAC;oBACR,OAAO,EAAE,CAAC;iBACV;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,OAAO;oBACb,UAAU,EAAE,IAAI;oBAChB,OAAO,EAAE,IAAI;iBACb;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,OAAO;iBAChB;gBACD;oBACC,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,OAAO;iBAChB;aACD;SACD,CAAC,EACF,IAAI,CACJ,CAAC;QAEF,oCAAoC;QACpC,MAAM,WAAW,CAAC,gBAAgB,CACjC,cAAc,EACd,IAAI,yBAAe,CAAC;YACnB,WAAW,EAAE,CAAC,iBAAiB,CAAC;YAChC,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,eAAe;YACpC,QAAQ,EAAE,SAAS;SACnB,CAAC,CACF,CAAC;QAEF,MAAM,WAAW,CAAC,gBAAgB,CACjC,cAAc,EACd,IAAI,yBAAe,CAAC;YACnB,WAAW,EAAE,CAAC,UAAU,CAAC;YACzB,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,QAAQ;YAC7B,QAAQ,EAAE,SAAS;SACnB,CAAC,CACF,CAAC;QAEF,+CAA+C;QAC/C,MAAM,WAAW,CAAC,KAAK,CAAC;;;GAGvB,CAAC,CAAC;QAEH,gCAAgC;QAChC,uDAAuD;QACvD,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;GAqBvB,CAAC,CAAC;QAEH,+CAA+C;QAC/C,MAAM,WAAW,CAAC,KAAK,CAAC;;;GAGvB,CAAC,CAAC;QAEH,8CAA8C;QAC9C,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;GASvB,CAAC,CAAC;QAEH,iDAAiD;QACjD,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiCvB,CAAC,CAAC;QAEH,mDAAmD;QACnD,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;GAOvB,CAAC,CAAC;QAEH,iDAAiD;QACjD,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;GAkBvB,CAAC,CAAC;QAEH,8DAA8D;QAC9D,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;GAevB,CAAC,CAAC;QAEH,yCAAyC;QACzC,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;GAMvB,CAAC,CAAC;QAEH,2CAA2C;QAC3C,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;GAevB,CAAC,CAAC;QAEH,kDAAkD;QAClD,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;GAevB,CAAC,CAAC;QAEH,+BAA+B;QAC/B,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;;GAWvB,CAAC,CAAC;QAEH,2BAA2B;QAC3B,MAAM,WAAW,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;IAC5D,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,eAAe;QACf,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;;GAWvB,CAAC,CAAC;QAEH,iCAAiC;QACjC,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;GAMvB,CAAC,CAAC;QAEH,0CAA0C;QAC1C,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;GAMvB,CAAC,CAAC;QAEH,0CAA0C;QAC1C,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;GAsBvB,CAAC,CAAC;QAEH,kDAAkD;QAClD,MAAM,WAAW,CAAC,KAAK,CAAC;;;GAGvB,CAAC,CAAC;QAEH,kBAAkB;QAClB,MAAM,WAAW,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;QAC5C,MAAM,WAAW,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;IAC9C,CAAC;CACD;AAhaD,kGAgaC"}