import { User } from '../../users/entities/user.entity';
export declare enum ClinicDeletionAuditOperation {
    BACKUP = "backup",
    DELETION = "deletion",
    RESTORE = "restore"
}
export declare enum ClinicDeletionAuditTargetType {
    CLINIC = "CLINIC",
    BRAND = "BRAND"
}
export interface ClinicDeletionOperationData {
    startTime?: string;
    endTime?: string;
    durationMs?: number;
    databaseImpact?: {
        totalTables: number;
        totalRecords: number;
        tablesProcessed: string[];
        sizeBytes: number;
    };
    fileImpact?: {
        totalFiles: number;
        filesProcessed: number;
        totalSizeBytes: number;
        skippedFiles?: number;
    };
    conflictsDetected?: {
        databaseConflicts: number;
        fileConflicts: number;
        details: any[];
    };
    restoredData?: {
        tablesRestored: string[];
        filesRestored: number;
    };
    estimatedTime?: string;
    warnings?: string[];
    mode?: 'DRY_RUN' | 'EXECUTE';
}
export interface ClinicDeletionErrorDetails {
    message: string;
    stack?: string;
    name: string;
    code?: string;
    context?: Record<string, any>;
}
export declare class ClinicDeletionAuditTrail {
    id: string;
    operation: ClinicDeletionAuditOperation;
    targetType: ClinicDeletionAuditTargetType;
    targetId: string;
    targetName?: string;
    userId: string;
    user?: User;
    success: boolean;
    backupId?: string;
    backupLocation?: string;
    operationData?: ClinicDeletionOperationData;
    errorDetails?: ClinicDeletionErrorDetails;
    durationMs?: number;
    ipAddress?: string;
    userAgent?: string;
    createdAt: Date;
}
