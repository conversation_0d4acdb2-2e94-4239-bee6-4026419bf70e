{"version": 3, "file": "rate-limiting.middleware.js", "sourceRoot": "", "sources": ["../../../../src/utils/middlewares/rate-limiting.middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAIwB;AAExB,iEAA0E;AAC1E,oCAAoC;AACpC,2CAA+C;AAGxC,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IAc/B,YAAoB,aAA4B;;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAV/B,qBAAgB,GAAG;YAChC,gBAAgB;YAChB,0BAA0B;YAC1B,mBAAmB;YACnB,iBAAiB;YACjB,aAAa;YACb,aAAa;YACb,YAAY;SACf,CAAC;QAGE,MAAM,WAAW,GACb,CAAA,MAAA,IAAI,CAAC,aAAa,0CAAE,GAAG,CAAS,kBAAkB,CAAC,KAAI,EAAE,CAAC;QAC9D,MAAM,cAAc,GAChB,CAAA,MAAA,IAAI,CAAC,aAAa,0CAAE,GAAG,CAAS,qBAAqB,CAAC,KAAI,CAAC,CAAC;QAEhE,IAAI,CAAC,iBAAiB,GAAG,IAAI,yCAAiB,CAAC;YAC3C,MAAM,EAAE,WAAW;YACnB,QAAQ,EAAE,cAAc;SAC3B,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CACzB,CAAA,MAAA,IAAI,CAAC,aAAa,0CAAE,GAAG,CAAW,oBAAoB,CAAC,KAAI,EAAE,CAChE,CAAC;QACF,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,CACzB,CAAA,MAAA,IAAI,CAAC,aAAa,0CAAE,GAAG,CAAW,oBAAoB,CAAC,KAAI,EAAE,CAChE,CAAC;IACN,CAAC;IAED,KAAK,CAAC,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QACrD,IAAI,CAAC;YACD,8BAA8B;YAC9B,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3C,OAAO,IAAI,EAAE,CAAC;YAClB,CAAC;YAED,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,EAAE,CAAC;YAExB,wBAAwB;YACxB,IAAI,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;gBACpC,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;YAC/E,CAAC;YAED,yBAAyB;YACzB,MAAM,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACzC,IAAI,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC/E,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,gCAAgC,EAAE,CAAC,CAAC;YAChG,CAAC;YAED,sBAAsB;YACtB,IAAI,EAAE,EAAE,CAAC;gBACL,IAAI,CAAC;oBACD,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAC7C,CAAC;gBAAC,OAAO,cAAc,EAAE,CAAC;oBACtB,IAAI,cAAc,YAAY,sCAAc,EAAE,CAAC;wBAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;wBACtE,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;wBAC3C,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAC;oBAC3F,CAAC;gBACL,CAAC;YACL,CAAC;YAED,OAAO,IAAI,EAAE,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO,GAAG,CAAC,MAAM,CAAC,mBAAU,CAAC,qBAAqB,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;QACnG,CAAC;IACL,CAAC;CACJ,CAAA;AAxEY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;qCAe0B,sBAAa;GAdvC,sBAAsB,CAwElC"}