"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientAlertsTable1722426847551 = void 0;
const typeorm_1 = require("typeorm");
class PatientAlertsTable1722426847551 {
    constructor() {
        this.name = 'PatientAlertsTable1722426847551';
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'patient_alerts',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'patient_id',
                    type: 'uuid'
                },
                {
                    name: 'name',
                    type: 'varchar'
                },
                {
                    name: 'severity',
                    type: 'varchar'
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    default: 'uuid_generate_v4()',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    default: 'uuid_generate_v4()',
                    isNullable: true
                }
            ]
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('patient_alerts');
    }
}
exports.PatientAlertsTable1722426847551 = PatientAlertsTable1722426847551;
//# sourceMappingURL=1722426847551-PatientAlertsTable.js.map