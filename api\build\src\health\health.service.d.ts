import { HealthCheckService, TypeOrmHealthIndicator, HealthIndicatorResult, HealthCheckResult } from '@nestjs/terminus';
import { RedisHealthIndicator } from './redis.health';
import { RedisService } from '../utils/redis/redis.service';
export declare class HealthService {
    private health;
    private db;
    private redis;
    private readonly redisService;
    constructor(health: HealthCheckService, db: TypeOrmHealthIndicator, redis: RedisHealthIndicator, redisService: RedisService);
    checkDatabase(): Promise<HealthCheckResult>;
    checkMigrations(): Promise<HealthCheckResult>;
    checkRedis(): Promise<HealthCheckResult>;
    checkRedisLocks(): Promise<{
        status: string;
        info: HealthIndicatorResult;
        error?: undefined;
    } | {
        status: string;
        error: {
            message: string;
            details: any;
        };
        info?: undefined;
    }>;
    checkAll(): Promise<HealthCheckResult>;
    /**
     * Comprehensive health check including Redis cluster details
     * Use this for detailed system monitoring in production
     */
    checkAllWithCluster(): Promise<HealthCheckResult>;
    checkGeneralHealth(): Promise<{
        isHealthy: boolean;
        message: string;
    }>;
    /**
     * Enhanced Redis health check with cluster information
     * Provides comprehensive Redis cluster monitoring including:
     * - Cluster mode detection (single vs cluster)
     * - Individual node health status
     * - Performance metrics and statistics
     * - Connection and failover information
     */
    checkRedisCluster(): Promise<HealthCheckResult>;
    /**
     * Get Redis connection and performance metrics
     * Useful for monitoring Redis performance in production
     */
    getRedisMetrics(): Promise<{
        status: string;
        metrics: {
            ping_time_ms: number;
            active_locks: number;
            total_monitored_locks: number;
            connection_type: string;
            timestamp: string;
        };
        locks: Record<string, {
            exists: boolean;
            ttl: number;
            value: string | null;
        }>;
        error?: undefined;
    } | {
        status: string;
        error: {
            message: string;
            details: any;
        };
        metrics?: undefined;
        locks?: undefined;
    }>;
}
