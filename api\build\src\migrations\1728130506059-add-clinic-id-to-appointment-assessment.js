"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddClinicIdToAppointmentAssessment1728130506059 = void 0;
const typeorm_1 = require("typeorm");
class AddClinicIdToAppointmentAssessment1728130506059 {
    async up(queryRunner) {
        await queryRunner.addColumn('appointment_assessments', new typeorm_1.TableColumn({
            name: 'clinic_id',
            type: 'uuid'
        }));
        await queryRunner.createForeignKey('appointment_assessments', new typeorm_1.TableForeignKey({
            columnNames: ['clinic_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinics',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('appointment_assessments', 'clinic_id');
        await queryRunner.dropForeignKey('appointment_assessments', 'clinic_id');
    }
}
exports.AddClinicIdToAppointmentAssessment1728130506059 = AddClinicIdToAppointmentAssessment1728130506059;
//# sourceMappingURL=1728130506059-add-clinic-id-to-appointment-assessment.js.map