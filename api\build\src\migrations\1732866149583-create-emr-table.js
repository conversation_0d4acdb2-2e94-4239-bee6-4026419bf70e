"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateEmrTable1732866149583 = void 0;
const typeorm_1 = require("typeorm");
class CreateEmrTable1732866149583 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'emr',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'emr_file_key',
                    type: 'varchar'
                },
                {
                    name: 'patient_id',
                    type: 'uuid'
                },
                {
                    name: 'appointment_id',
                    type: 'uuid'
                },
                {
                    name: 'clinic_id',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                }
            ]
        }));
        await queryRunner.createForeignKey('emr', new typeorm_1.TableForeignKey({
            columnNames: ['patient_id'],
            referencedTableName: 'patients',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE'
        }));
        await queryRunner.createForeignKey('emr', new typeorm_1.TableForeignKey({
            columnNames: ['appointment_id'],
            referencedTableName: 'appointments',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE'
        }));
        await queryRunner.createForeignKey('emr', new typeorm_1.TableForeignKey({
            columnNames: ['clinic_id'],
            referencedTableName: 'clinics',
            referencedColumnNames: ['id'],
            onDelete: 'SET NULL'
        }));
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable('emr');
        const foreignKeys = table === null || table === void 0 ? void 0 : table.foreignKeys.filter(fk => ['patient_id', 'appointment_id', 'clinic_id'].includes(fk.columnNames[0]));
        if (foreignKeys) {
            for (const fk of foreignKeys) {
                await queryRunner.dropForeignKey('emr', fk);
            }
        }
        await queryRunner.dropTable('emr');
    }
}
exports.CreateEmrTable1732866149583 = CreateEmrTable1732866149583;
//# sourceMappingURL=1732866149583-create-emr-table.js.map