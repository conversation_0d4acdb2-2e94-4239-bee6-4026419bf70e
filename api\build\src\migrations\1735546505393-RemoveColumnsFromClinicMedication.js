"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RemoveColumnsFromClinicMedication1735546505393 = void 0;
const typeorm_1 = require("typeorm");
class RemoveColumnsFromClinicMedication1735546505393 {
    constructor() {
        this.name = 'RemoveColumnsFromClinicMedication1735546505393';
    }
    async up(queryRunner) {
        await queryRunner.dropColumn('clinic_medications', 'drug');
        await queryRunner.dropColumn('clinic_medications', 'form');
        await queryRunner.dropColumn('clinic_medications', 'strength');
        await queryRunner.dropColumn('clinic_medications', 'unit');
        await queryRunner.dropColumn('clinic_medications', 'minimum_dosage');
        await queryRunner.dropColumn('clinic_medications', 'maximum_dosage');
        await queryRunner.dropColumn('clinic_medications', 'purchase_price');
        await queryRunner.dropColumn('clinic_medications', 'reorder_value');
        await queryRunner.dropColumn('clinic_medications', 'description');
    }
    async down(queryRunner) {
        await queryRunner.addColumn('clinic_medications', new typeorm_1.TableColumn({
            name: 'drug',
            type: 'varchar',
            isNullable: false
        }));
        await queryRunner.addColumn('clinic_medications', new typeorm_1.TableColumn({
            name: 'form',
            type: 'varchar',
            isNullable: false
        }));
        await queryRunner.addColumn('clinic_medications', new typeorm_1.TableColumn({
            name: 'strength',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: true
        }));
        await queryRunner.addColumn('clinic_medications', new typeorm_1.TableColumn({
            name: 'unit',
            type: 'varchar',
            isNullable: false
        }));
        await queryRunner.addColumn('clinic_medications', new typeorm_1.TableColumn({
            name: 'minimum_dosage',
            type: 'int'
        }));
        await queryRunner.addColumn('clinic_medications', new typeorm_1.TableColumn({
            name: 'maximum_dosage',
            type: 'int'
        }));
        await queryRunner.addColumn('clinic_medications', new typeorm_1.TableColumn({
            name: 'purchase_price',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: true
        }));
        await queryRunner.addColumn('clinic_medications', new typeorm_1.TableColumn({
            name: 'reorder_value',
            type: 'int'
        }));
        await queryRunner.addColumn('clinic_medications', new typeorm_1.TableColumn({
            name: 'description',
            type: 'varchar'
        }));
    }
}
exports.RemoveColumnsFromClinicMedication1735546505393 = RemoveColumnsFromClinicMedication1735546505393;
//# sourceMappingURL=1735546505393-RemoveColumnsFromClinicMedication.js.map