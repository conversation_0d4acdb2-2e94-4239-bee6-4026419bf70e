"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddLineItemIdToLabReports1741846920611 = void 0;
const typeorm_1 = require("typeorm");
class AddLineItemIdToLabReports1741846920611 {
    async up(queryRunner) {
        await queryRunner.addColumn('lab_reports', new typeorm_1.TableColumn({
            name: 'line_item_id',
            type: 'uuid',
            isNullable: false,
            default: 'uuid_generate_v4()'
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('lab_reports', 'line_item_id');
    }
}
exports.AddLineItemIdToLabReports1741846920611 = AddLineItemIdToLabReports1741846920611;
//# sourceMappingURL=1741846920611-AddLineItemIdToLabReports.js.map