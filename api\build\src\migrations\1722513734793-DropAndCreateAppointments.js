"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DropAndCreateAppointments1722497369596 = void 0;
const typeorm_1 = require("typeorm");
class DropAndCreateAppointments1722497369596 {
    async up(queryRunner) {
        await queryRunner.dropTable('appointments', true, true, true);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'appointments',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'clinic_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'patient_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'room_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'reason',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'status',
                    type: 'varchar',
                    isNullable: true
                },
                {
                    name: 'type',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'triage',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'date',
                    type: 'timestamp',
                    isNullable: false
                },
                {
                    name: 'start_time',
                    type: 'timestamp with time zone',
                    isNullable: false
                },
                {
                    name: 'end_time',
                    type: 'timestamp with time zone',
                    isNullable: true
                },
                {
                    name: 'deleted_at',
                    type: 'timestamp with time zone',
                    isNullable: true
                },
                {
                    name: 'weight',
                    type: 'integer',
                    isNullable: true
                },
                {
                    name: 'notes',
                    type: 'jsonb',
                    isNullable: true
                },
                {
                    name: 'pre_visit_questions',
                    type: 'jsonb',
                    isNullable: true
                },
                {
                    name: 'is_blocked',
                    type: 'boolean',
                    isNullable: true
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'now()',
                    onUpdate: 'now()'
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'initial_notes',
                    type: 'varchar',
                    isNullable: true
                }
            ]
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('appointments');
    }
}
exports.DropAndCreateAppointments1722497369596 = DropAndCreateAppointments1722497369596;
//# sourceMappingURL=1722513734793-DropAndCreateAppointments.js.map