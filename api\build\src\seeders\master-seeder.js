"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const clinic_rooms_seeder_1 = require("./clinic_rooms.seeder");
const role_seeder_1 = require("./role.seeder");
async function runSeeders() {
    try {
        console.log('========================');
        console.log('Running role seeder...');
        await (0, role_seeder_1.roleSeeder)();
        console.log('Role seeding completed.');
        console.log('========================');
        console.log('Running clinic rooms seeder...');
        await (0, clinic_rooms_seeder_1.clinicRoomsSeeder)();
        console.log('Clinic rooms seeding completed.');
        console.log('========================');
        console.log('All seeders executed successfully.');
    }
    catch (error) {
        console.error('Error executing seeders:', error);
        process.exit(1); // Exit with a failure code
    }
}
runSeeders()
    .then(() => process.exit(0))
    .catch(error => {
    console.error('Error running seeders:', error);
    process.exit(1);
});
//# sourceMappingURL=master-seeder.js.map