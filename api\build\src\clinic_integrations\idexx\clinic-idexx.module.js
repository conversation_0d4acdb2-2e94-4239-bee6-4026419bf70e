"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicIdexxModule = void 0;
const common_1 = require("@nestjs/common");
const clinic_idexx_service_1 = require("./clinic-idexx.service");
const clinic_idexx_controller_1 = require("./clinic-idexx.controller");
const typeorm_1 = require("@nestjs/typeorm");
const create_clinic_idexx_entity_1 = require("./entities/create-clinic-idexx.entity");
const axios_1 = require("@nestjs/axios");
const clinic_lab_report_entity_1 = require("../../clinic-lab-report/entities/clinic-lab-report.entity");
const lab_report_entity_1 = require("../../clinic-lab-report/entities/lab-report.entity");
const patients_service_1 = require("../../patients/patients.service");
const patient_entity_1 = require("../../patients/entities/patient.entity");
const patient_owner_entity_1 = require("../../patients/entities/patient-owner.entity");
const owner_brand_entity_1 = require("../../owners/entities/owner-brand.entity");
const global_owner_entity_1 = require("../../owners/entities/global-owner.entity");
const clinic_entity_1 = require("../../clinics/entities/clinic.entity");
const owners_service_1 = require("../../owners/owners.service");
const clinic_lab_report_module_1 = require("../../clinic-lab-report/clinic-lab-report.module");
const appointment_entity_1 = require("../../appointments/entities/appointment.entity");
const s3_service_1 = require("../../utils/aws/s3/s3.service");
const clinic_idexx_utils_service_1 = require("../../utils/idexx/clinic-idexx-utils.service");
const appointments_service_1 = require("../../appointments/appointments.service");
const appointment_doctor_entity_1 = require("../../appointments/entities/appointment-doctor.entity");
const appointment_details_entity_1 = require("../../appointments/entities/appointment-details.entity");
const send_mail_service_1 = require("../../utils/aws/ses/send-mail-service");
const tasks_service_1 = require("../../tasks/tasks.service");
const tasks_entity_1 = require("../../tasks/entities/tasks.entity");
const whatsapp_service_1 = require("../../utils/whatsapp-integration/whatsapp.service");
const emr_module_1 = require("../../emr/emr.module");
const patient_reminder_service_1 = require("../../patient-reminders/patient-reminder.service");
const patient_reminder_entity_1 = require("../../patient-reminders/entities/patient-reminder.entity");
const patient_reminder_history_entity_1 = require("../../patient-reminders/entities/patient-reminder-history.entity");
const sqs_service_1 = require("../../utils/aws/sqs/sqs.service");
const global_reminders_module_1 = require("../../patient-global-reminders/global-reminders.module");
const role_module_1 = require("../../roles/role.module");
const brand_entity_1 = require("../../brands/entities/brand.entity");
const pet_transfer_history_entity_1 = require("../../owners/entities/pet-transfer-history.entity");
const payment_details_entity_1 = require("../../payment-details/entities/payment-details.entity");
const ses_module_1 = require("../../utils/aws/ses/ses.module");
const whatsapp_module_1 = require("../../utils/whatsapp-integration/whatsapp.module");
const availability_module_1 = require("../../availability/availability.module");
const long_term_medications_module_1 = require("../../long-term-medications/long-term-medications.module");
const socket_module_1 = require("../../socket/socket.module");
let ClinicIdexxModule = class ClinicIdexxModule {
};
exports.ClinicIdexxModule = ClinicIdexxModule;
exports.ClinicIdexxModule = ClinicIdexxModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                create_clinic_idexx_entity_1.CreateClinicIdexxEntity,
                clinic_lab_report_entity_1.ClinicLabReport,
                lab_report_entity_1.LabReport,
                patient_entity_1.Patient,
                patient_owner_entity_1.PatientOwner,
                owner_brand_entity_1.OwnerBrand,
                global_owner_entity_1.GlobalOwner,
                clinic_entity_1.ClinicEntity,
                appointment_entity_1.AppointmentEntity,
                appointment_doctor_entity_1.AppointmentDoctorsEntity,
                appointment_details_entity_1.AppointmentDetailsEntity,
                tasks_entity_1.Task,
                patient_reminder_entity_1.PatientReminder,
                patient_reminder_history_entity_1.PatientReminderHistory,
                brand_entity_1.Brand,
                pet_transfer_history_entity_1.PetTransferHistory,
                payment_details_entity_1.PaymentDetailsEntity
            ]),
            axios_1.HttpModule,
            clinic_lab_report_module_1.ClinicLabReportModule,
            emr_module_1.EmrModule,
            global_reminders_module_1.GlobalReminderModule,
            role_module_1.RoleModule,
            ses_module_1.SESModule,
            whatsapp_module_1.WhatsappModule,
            long_term_medications_module_1.LongTermMedicationsModule,
            (0, common_1.forwardRef)(() => availability_module_1.AvailabilityModule),
            (0, common_1.forwardRef)(() => socket_module_1.SocketModule)
        ],
        controllers: [clinic_idexx_controller_1.ClinicIdexxController],
        providers: [
            clinic_idexx_service_1.ClinicIdexxService,
            patients_service_1.PatientsService,
            owners_service_1.OwnersService,
            s3_service_1.S3Service,
            clinic_idexx_utils_service_1.ClinicIdexxUtilsService,
            appointments_service_1.AppointmentsService,
            send_mail_service_1.SESMailService,
            tasks_service_1.TasksService,
            whatsapp_service_1.WhatsappService,
            patient_reminder_service_1.PatientRemindersService,
            sqs_service_1.SqsService
        ]
    })
], ClinicIdexxModule);
//# sourceMappingURL=clinic-idexx.module.js.map