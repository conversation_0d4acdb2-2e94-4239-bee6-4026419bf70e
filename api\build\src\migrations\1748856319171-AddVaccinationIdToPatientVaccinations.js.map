{"version": 3, "file": "1748856319171-AddVaccinationIdToPatientVaccinations.js", "sourceRoot": "", "sources": ["../../../src/migrations/1748856319171-AddVaccinationIdToPatientVaccinations.ts"], "names": [], "mappings": ";;;AAEA,MAAa,kDAAkD;IAA/D;QAGC,SAAI,GAAG,oDAAoD,CAAC;IAwL7D,CAAC;IAtLO,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,6DAA6D;QAC7D,MAAM,WAAW,CAAC,KAAK,CAAC;;;GAGvB,CAAC,CAAC;QAEH,2DAA2D;QAC3D,MAAM,WAAW,CAAC,KAAK,CAAC;;;GAGvB,CAAC,CAAC;QAEH,kDAAkD;QAClD,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;QAE/C,wDAAwD;QACxD,MAAM,WAAW,CAAC,KAAK,CAAC;;;GAGvB,CAAC,CAAC;QAEH,uDAAuD;QACvD,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;GAMvB,CAAC,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,gCAAgC;QAChC,MAAM,WAAW,CAAC,KAAK,CAAC;;;GAGvB,CAAC,CAAC;QAEH,eAAe;QACf,MAAM,WAAW,CAAC,KAAK,CAAC;;GAEvB,CAAC,CAAC;QAEH,gBAAgB;QAChB,MAAM,WAAW,CAAC,KAAK,CAAC;;;GAGvB,CAAC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,sBAAsB,CACnC,WAAwB;QAExB,OAAO,CAAC,GAAG,CACV,0EAA0E,CAC1E,CAAC;QAEF,kEAAkE;QAClE,MAAM,mBAAmB,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;GAUnD,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CACV,SAAS,mBAAmB,CAAC,MAAM,iCAAiC,CACpE,CAAC;QAEF,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,KAAK,MAAM,kBAAkB,IAAI,mBAAmB,EAAE,CAAC;YACtD,MAAM,EAAE,EAAE,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,GAChD,kBAAkB,CAAC;YAEpB,kDAAkD;YAClD,MAAM,mBAAmB,GAAG,MAAM,WAAW,CAAC,KAAK,CAClD;;;;;;IAMA,EACA,CAAC,SAAS,EAAE,YAAY,CAAC,CACzB,CAAC;YAEF,IAAI,aAAqB,CAAC;YAE1B,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpC,oBAAoB;gBACpB,aAAa,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC1C,YAAY,EAAE,CAAC;gBACf,OAAO,CAAC,GAAG,CACV,cAAc,YAAY,iBAAiB,UAAU,EAAE,CACvD,CAAC;YACH,CAAC;iBAAM,CAAC;gBACP,+BAA+B;gBAC/B,MAAM,YAAY,GAAG,MAAM,WAAW,CAAC,KAAK,CAC3C;;;;;;;;;KASA,EACA,CAAC,SAAS,EAAE,IAAI,YAAY,GAAG,CAAC,CAChC,CAAC;gBAEF,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC7B,aAAa,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBACnC,YAAY,EAAE,CAAC;oBACf,OAAO,CAAC,GAAG,CACV,oBAAoB,YAAY,iBAAiB,UAAU,EAAE,CAC7D,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACP,2DAA2D;oBAC3D,MAAM,mBAAmB,GAAG,MAAM,WAAW,CAAC,KAAK,CAClD;;;;;MAKA,EACA,CAAC,SAAS,CAAC,CACX,CAAC;oBAEF,IAAI,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACpC,aAAa,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBAC1C,aAAa,EAAE,CAAC;wBAChB,OAAO,CAAC,GAAG,CACV,yBAAyB,YAAY,cAAc,UAAU,GAAG,CAChE,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACP,wEAAwE;wBACxE,MAAM,cAAc,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC;;;;OAI9C,CAAC,CAAC;wBAEH,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BAC/B,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;4BACrC,aAAa,EAAE,CAAC;4BAChB,OAAO,CAAC,GAAG,CACV,iCAAiC,YAAY,cAAc,UAAU,GAAG,CACxE,CAAC;wBACH,CAAC;6BAAM,CAAC;4BACP,MAAM,IAAI,KAAK,CACd,oGAAoG,EAAE,EAAE,CACxG,CAAC;wBACH,CAAC;oBACF,CAAC;gBACF,CAAC;YACF,CAAC;YAED,yDAAyD;YACzD,MAAM,WAAW,CAAC,KAAK,CACtB;;;;IAIA,EACA,CAAC,aAAa,EAAE,EAAE,CAAC,CACnB,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,+BAA+B,YAAY,EAAE,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,8BAA8B,aAAa,EAAE,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,uBAAuB,YAAY,GAAG,aAAa,EAAE,CAAC,CAAC;IACpE,CAAC;CACD;AA3LD,gHA2LC"}