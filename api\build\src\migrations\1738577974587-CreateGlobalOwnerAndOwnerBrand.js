"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateGlobalOwnerAndOwnerBrand1738577974587 = void 0;
const typeorm_1 = require("typeorm");
class CreateGlobalOwnerAndOwnerBrand1738577974587 {
    async up(queryRunner) {
        // First drop the existing foreign key constraints
        await queryRunner.query(`
			ALTER TABLE patient_owners
			DROP CONSTRAINT IF EXISTS "FK_fa182967dcc73b2b0bcecb2ef95";

			ALTER TABLE invoices
			DROP CONSTRAINT IF EXISTS "FK_ae9abff3a2cc602d8bf7489fc74";

			ALTER TABLE payment_details
			DROP CONSTRAINT IF EXISTS "FK_payment_details_owner";
		`);
        // Create global_owners table
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'global_owners',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'phone_number',
                    type: 'varchar',
                    length: '20',
                    isUnique: true
                },
                {
                    name: 'country_code',
                    type: 'varchar',
                    length: '5',
                    isNullable: true
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'now()'
                }
            ]
        }), true);
        // Create owner_brands table
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'owner_brands',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'global_owner_id',
                    type: 'uuid'
                },
                {
                    name: 'brand_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'first_name',
                    type: 'varchar',
                    length: '50'
                },
                {
                    name: 'last_name',
                    type: 'varchar',
                    length: '50'
                },
                {
                    name: 'email',
                    type: 'varchar',
                    length: '100',
                    isNullable: true
                },
                {
                    name: 'address',
                    type: 'text',
                    isNullable: true
                },
                {
                    name: 'owner_balance',
                    type: 'numeric',
                    default: 0
                },
                {
                    name: 'opening_balance',
                    type: 'decimal',
                    precision: 10,
                    scale: 2,
                    default: 0
                },
                {
                    name: 'dummy_data',
                    type: 'jsonb',
                    isNullable: true,
                    default: null
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'now()'
                }
            ]
        }), true);
        // Add foreign keys for owner_brands
        await queryRunner.createForeignKey('owner_brands', new typeorm_1.TableForeignKey({
            columnNames: ['global_owner_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'global_owners',
            onDelete: 'CASCADE'
        }));
        await queryRunner.createForeignKey('owner_brands', new typeorm_1.TableForeignKey({
            columnNames: ['brand_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'brands',
            onDelete: 'CASCADE'
        }));
        // Add global_owner_id column to patient_owners
        await queryRunner.query(`
			ALTER TABLE patient_owners 
			ADD COLUMN global_owner_id uuid;
		`);
        // Migrate data to global_owners
        // First, create a temporary table to store the mapping
        await queryRunner.query(`
			CREATE TEMP TABLE owner_global_mapping AS
			WITH numbered_owners AS (
				SELECT 
					id as owner_id,
					phone_number as original_phone,
					ROW_NUMBER() OVER (
						PARTITION BY phone_number 
						ORDER BY created_at
					) as duplicate_number
				FROM owners
				WHERE phone_number IS NOT NULL
			)
			SELECT 
				owner_id,
				original_phone,
				CASE 
					WHEN duplicate_number = 1 THEN original_phone 
					ELSE original_phone || '_' || (duplicate_number - 1)::text 
				END as global_phone
			FROM numbered_owners
		`);
        // Drop existing foreign key before any updates
        await queryRunner.query(`
			ALTER TABLE patient_owners
			DROP CONSTRAINT IF EXISTS "FK_fa182967dcc73b2b0bcecb2ef95";
		`);
        // Insert into global_owners using the mapping
        await queryRunner.query(`
			INSERT INTO global_owners (phone_number, country_code, created_at, updated_at)
			SELECT 
				m.global_phone as phone_number,
				country_code,
				created_at,
				updated_at
			FROM owners o
			JOIN owner_global_mapping m ON o.id = m.owner_id
		`);
        // Migrate data to owner_brands using the mapping
        await queryRunner.query(`
			INSERT INTO owner_brands (
				global_owner_id,
				brand_id,
				first_name,
				last_name,
				email,
				address,
				owner_balance,
				opening_balance,
				dummy_data,
				created_at,
				updated_at
			)
			SELECT DISTINCT
				go.id as global_owner_id,
				c.brand_id,
				o.first_name,
				o.last_name,
				o.email,
				o.address,
				COALESCE(o.owner_balance, 0),
				COALESCE(o.opening_balance, 0),
				o.dummy_data,
				o.created_at,
				o.updated_at
			FROM owners o
			JOIN patient_owners po ON o.id = po.owner_id
			JOIN patients p ON po.patient_id = p.id
			JOIN clinics c ON p.clinic_id = c.id
			JOIN owner_global_mapping m ON o.id = m.owner_id
			JOIN global_owners go ON go.phone_number = m.global_phone
			WHERE c.brand_id IS NOT NULL
		`);
        // Update patient_owners table with global_owner_id
        await queryRunner.query(`
			UPDATE patient_owners po
			SET global_owner_id = go.id
			FROM owners o
			JOIN owner_global_mapping m ON o.id = m.owner_id
			JOIN global_owners go ON go.phone_number = m.global_phone
			WHERE po.owner_id = o.id
		`);
        // Update patient_owners with new owner_brand ids
        await queryRunner.query(`
			WITH owner_mappings AS (
				SELECT 
					po.id as po_id,
					ob.id as new_owner_id
				FROM patient_owners po
				JOIN owners o ON po.owner_id = o.id
				JOIN owner_global_mapping m ON o.id = m.owner_id
				JOIN global_owners go ON go.phone_number = m.global_phone
				JOIN owner_brands ob ON go.id = ob.global_owner_id
				JOIN patients p ON po.patient_id = p.id
				JOIN clinics c ON p.clinic_id = c.id
				WHERE ob.brand_id = c.brand_id
			)
			UPDATE patient_owners
			SET owner_id = owner_mappings.new_owner_id
			FROM owner_mappings
			WHERE patient_owners.id = owner_mappings.po_id
		`);
        // Verify all patient_owners have valid owner_brand references
        await queryRunner.query(`
			DO $$
			DECLARE
				invalid_count integer;
			BEGIN
				SELECT COUNT(*)
				INTO invalid_count
				FROM patient_owners po
				LEFT JOIN owner_brands ob ON po.owner_id = ob.id
				WHERE ob.id IS NULL;

				IF invalid_count > 0 THEN
					RAISE EXCEPTION 'Found % patient_owners records with invalid owner_brand references', invalid_count;
				END IF;
			END $$;
		`);
        // Now add the new foreign key constraint
        await queryRunner.query(`
			ALTER TABLE patient_owners
			ADD CONSTRAINT FK_patient_owners_owner_brands
			FOREIGN KEY (owner_id)
			REFERENCES owner_brands(id)
			ON DELETE CASCADE;
		`);
        // Update invoices with new owner_brand ids
        await queryRunner.query(`
			WITH invoice_mappings AS (
				SELECT 
					i.id as invoice_id,
					ob.id as new_owner_id
				FROM invoices i
				JOIN owners o ON i.owner_id = o.id
				JOIN global_owners go ON o.phone_number = go.phone_number
				JOIN owner_brands ob ON go.id = ob.global_owner_id
				WHERE i.brand_id = ob.brand_id
			)
			UPDATE invoices
			SET owner_id = invoice_mappings.new_owner_id
			FROM invoice_mappings
			WHERE invoices.id = invoice_mappings.invoice_id
		`);
        // Update payment_details with new owner_brand ids
        await queryRunner.query(`
			WITH payment_mappings AS (
				SELECT 
					pd.id as payment_id,
					ob.id as new_owner_id
				FROM payment_details pd
				JOIN owners o ON pd.owner_id = o.id
				JOIN global_owners go ON o.phone_number = go.phone_number
				JOIN owner_brands ob ON go.id = ob.global_owner_id
				WHERE pd.brand_id = ob.brand_id
			)
			UPDATE payment_details
			SET owner_id = payment_mappings.new_owner_id
			FROM payment_mappings
			WHERE payment_details.id = payment_mappings.payment_id
		`);
        // Create all necessary indexes
        await queryRunner.query(`
			CREATE INDEX idx_global_owners_phone_number ON global_owners(phone_number);
			CREATE INDEX idx_owner_brands_global_owner_id ON owner_brands(global_owner_id);
			CREATE INDEX idx_owner_brands_brand_id ON owner_brands(brand_id);
			CREATE INDEX idx_patient_owners_owner_id ON patient_owners(owner_id);
			CREATE INDEX idx_patient_owners_global_owner_id ON patient_owners(global_owner_id);
			CREATE INDEX idx_patient_owners_brand_id ON patient_owners(brand_id);
			CREATE INDEX idx_invoices_owner_id ON invoices(owner_id);
			CREATE INDEX idx_invoices_brand_id ON invoices(brand_id);
			CREATE INDEX idx_payment_details_owner_id ON payment_details(owner_id);
			CREATE INDEX idx_payment_details_brand_id ON payment_details(brand_id);
		`);
        // Clean up temporary table
        await queryRunner.query(`DROP TABLE owner_global_mapping`);
    }
    async down(queryRunner) {
        // Drop indexes
        await queryRunner.query(`
			DROP INDEX IF EXISTS idx_global_owners_phone_number;
			DROP INDEX IF EXISTS idx_owner_brands_global_owner_id;
			DROP INDEX IF EXISTS idx_owner_brands_brand_id;
			DROP INDEX IF EXISTS idx_patient_owners_owner_id;
			DROP INDEX IF EXISTS idx_patient_owners_global_owner_id;
			DROP INDEX IF EXISTS idx_patient_owners_brand_id;
			DROP INDEX IF EXISTS idx_invoices_owner_id;
			DROP INDEX IF EXISTS idx_invoices_brand_id;
			DROP INDEX IF EXISTS idx_payment_details_owner_id;
			DROP INDEX IF EXISTS idx_payment_details_brand_id;
		`);
        // Remove foreign key constraints
        await queryRunner.query(`
			ALTER TABLE patient_owners
			DROP CONSTRAINT IF EXISTS fk_patient_owners_global_owner;

			ALTER TABLE patient_owners
			DROP CONSTRAINT IF EXISTS FK_patient_owners_owner_brands;
		`);
        // Restore original foreign key constraint
        await queryRunner.query(`
			ALTER TABLE patient_owners
			ADD CONSTRAINT "FK_fa182967dcc73b2b0bcecb2ef95"
			FOREIGN KEY (owner_id)
			REFERENCES owners(id)
			ON DELETE CASCADE;
		`);
        // Restore original data in related tables
        await queryRunner.query(`
			UPDATE patient_owners po
			SET owner_id = o.id,
				global_owner_id = NULL
			FROM owner_brands ob
			JOIN global_owners go ON ob.global_owner_id = go.id
			JOIN owners o ON go.phone_number = o.phone_number
			WHERE po.owner_id = ob.id;

			UPDATE invoices i
			SET owner_id = o.id
			FROM owner_brands ob
			JOIN global_owners go ON ob.global_owner_id = go.id
			JOIN owners o ON go.phone_number = o.phone_number
			WHERE i.owner_id = ob.id;

			UPDATE payment_details pd
			SET owner_id = o.id
			FROM owner_brands ob
			JOIN global_owners go ON ob.global_owner_id = go.id
			JOIN owners o ON go.phone_number = o.phone_number
			WHERE pd.owner_id = ob.id;
		`);
        // Drop global_owner_id column from patient_owners
        await queryRunner.query(`
			ALTER TABLE patient_owners
			DROP COLUMN IF EXISTS global_owner_id;
		`);
        // Drop new tables
        await queryRunner.dropTable('owner_brands');
        await queryRunner.dropTable('global_owners');
    }
}
exports.CreateGlobalOwnerAndOwnerBrand1738577974587 = CreateGlobalOwnerAndOwnerBrand1738577974587;
//# sourceMappingURL=1738577974587-CreateGlobalOwnerAndOwnerBrand.js.map