"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddClinicTimezone1746011776863 = void 0;
const typeorm_1 = require("typeorm");
class AddClinicTimezone1746011776863 {
    async up(queryRunner) {
        // Add the timezone column to the clinics table
        await queryRunner.addColumn('clinics', // Table name
        new typeorm_1.TableColumn({
            name: 'timezone', // Column name
            type: 'varchar', // Data type
            isNullable: false, // Column cannot be null
            default: "'Asia/Kolkata'", // Default value (note the single quotes for SQL string literal)
            comment: "IANA timezone name (e.g., 'Asia/Kolkata', 'America/New_York')" // Column comment
        }));
    }
    async down(queryRunner) {
        // Remove the timezone column from the clinics table
        await queryRunner.dropColumn('clinics', 'timezone');
    }
}
exports.AddClinicTimezone1746011776863 = AddClinicTimezone1746011776863;
//# sourceMappingURL=1746011776863-AddClinicTimezone.js.map