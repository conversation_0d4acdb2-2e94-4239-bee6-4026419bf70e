"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppointmentAuditLog = exports.AppointmentAuditLogAction = void 0;
const typeorm_1 = require("typeorm");
// Re-define the enum here for use within the entity and application code
var AppointmentAuditLogAction;
(function (AppointmentAuditLogAction) {
    AppointmentAuditLogAction["CREATE"] = "CREATE";
    AppointmentAuditLogAction["UPDATE"] = "UPDATE";
    AppointmentAuditLogAction["CANCEL"] = "CANCEL";
})(AppointmentAuditLogAction || (exports.AppointmentAuditLogAction = AppointmentAuditLogAction = {}));
let AppointmentAuditLog = class AppointmentAuditLog {
};
exports.AppointmentAuditLog = AppointmentAuditLog;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AppointmentAuditLog.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', comment: 'ID of the appointment being audited' }),
    __metadata("design:type", String)
], AppointmentAuditLog.prototype, "appointmentId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'uuid',
        comment: 'ID of the user (owner) performing the action'
    }),
    __metadata("design:type", String)
], AppointmentAuditLog.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        enum: AppointmentAuditLogAction,
        comment: 'The action performed (CREATE, UPDATE, CANCEL)'
    }),
    __metadata("design:type", String)
], AppointmentAuditLog.prototype, "action", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb',
        nullable: true,
        comment: 'JSON object detailing the changes made'
    }),
    __metadata("design:type", Object)
], AppointmentAuditLog.prototype, "changedFields", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'text',
        nullable: true,
        comment: 'Optional additional context'
    }),
    __metadata("design:type", Object)
], AppointmentAuditLog.prototype, "context", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        type: 'timestamp with time zone',
        default: () => 'CURRENT_TIMESTAMP',
        comment: 'Timestamp when the action occurred'
    }),
    __metadata("design:type", Date)
], AppointmentAuditLog.prototype, "timestamp", void 0);
exports.AppointmentAuditLog = AppointmentAuditLog = __decorate([
    (0, typeorm_1.Entity)('appointment_audit_log') // Ensure this matches the table name in the migration
    ,
    (0, typeorm_1.Index)(['appointmentId']),
    (0, typeorm_1.Index)(['userId']),
    (0, typeorm_1.Index)(['timestamp'])
], AppointmentAuditLog);
//# sourceMappingURL=appointment-audit-log.entity.js.map