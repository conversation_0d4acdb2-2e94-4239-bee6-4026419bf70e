"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicDeletionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const s3_service_1 = require("../utils/aws/s3/s3.service");
const database_backup_service_1 = require("./services/database-backup.service");
const file_backup_service_1 = require("./services/file-backup.service");
const database_restore_service_1 = require("./services/database-restore.service");
const file_restore_service_1 = require("./services/file-restore.service");
const query_manager_service_1 = require("./services/query-manager.service");
const clinic_deletion_dto_1 = require("./dto/clinic-deletion.dto");
const uuid_1 = require("uuid");
// Import entities
const brand_entity_1 = require("../brands/entities/brand.entity");
const clinic_entity_1 = require("../clinics/entities/clinic.entity");
const clinic_deletion_audit_trail_entity_1 = require("./entities/clinic-deletion-audit-trail.entity");
let ClinicDeletionService = class ClinicDeletionService {
    constructor(brandRepository, clinicRepository, auditTrailRepository, dataSource, logger, s3Service, queryManagerService, databaseBackupService, fileBackupService, databaseRestoreService, fileRestoreService) {
        this.brandRepository = brandRepository;
        this.clinicRepository = clinicRepository;
        this.auditTrailRepository = auditTrailRepository;
        this.dataSource = dataSource;
        this.logger = logger;
        this.s3Service = s3Service;
        this.queryManagerService = queryManagerService;
        this.databaseBackupService = databaseBackupService;
        this.fileBackupService = fileBackupService;
        this.databaseRestoreService = databaseRestoreService;
        this.fileRestoreService = fileRestoreService;
    }
    /**
     * Analyzes the impact of deleting a clinic or brand
     */
    async analyzeImpact(dto) {
        const startTime = Date.now();
        try {
            // Check if clinic deletion should be upgraded to brand deletion
            const upgradedDto = await this.upgradeClinicToBrandDeletionIfNeeded(dto);
            // Get target entity information
            const targetInfo = await this.getTargetInfo(upgradedDto);
            // Analyze database impact
            const databaseImpact = await this.analyzeDatabaseImpact(upgradedDto);
            // Analyze S3 file impact
            const s3Impact = await this.analyzeS3Impact(upgradedDto);
            // Calculate estimated time
            const estimatedTime = this.calculateEstimatedTime(databaseImpact.totalRecords, s3Impact.totalFiles);
            // Generate warnings
            const warnings = this.generateWarnings(databaseImpact, s3Impact);
            const result = {
                targetInfo,
                databaseImpact,
                s3Impact,
                estimatedTime,
                warnings
            };
            this.logger.log('Deletion impact analysis completed', {
                duration: Date.now() - startTime,
                originalType: dto.type,
                finalType: upgradedDto.type,
                targetType: upgradedDto.type,
                totalRecords: databaseImpact.totalRecords,
                totalFiles: s3Impact.totalFiles
            });
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Error in impact analysis', {
                error: errorMessage,
                dto
            });
            throw error;
        }
    }
    /**
     * Executes the backup and deletion process
     */
    async executeDeletion(dto, userId) {
        const startTime = Date.now();
        try {
            // Check if clinic deletion should be upgraded to brand deletion
            const upgradedDto = await this.upgradeClinicToBrandDeletionIfNeeded(dto);
            // Validate prerequisites
            const targetId = upgradedDto.clinicId || upgradedDto.brandId;
            if (!targetId) {
                throw new Error('Either clinicId or brandId must be provided');
            }
            const validation = await this.validateOperationPrerequisites('deletion', upgradedDto.type, targetId);
            if (!validation.valid) {
                throw new Error(`Operation validation failed: ${validation.errors.join(', ')}`);
            }
            const targetInfo = await this.getTargetInfo(upgradedDto);
            let result;
            if (dto.mode === clinic_deletion_dto_1.DeletionMode.DRY_RUN) {
                result = await this.executeDryRun(upgradedDto, targetInfo, userId);
            }
            else {
                result = await this.executeActualBackupAndDeletion(upgradedDto, targetInfo, userId);
            }
            // Create audit trail for successful operation
            await this.createAuditTrail('deletion', upgradedDto.type, targetId, userId, result, undefined, // no error
            startTime);
            this.logger.log('Backup and deletion execution completed', {
                mode: dto.mode,
                originalType: dto.type,
                finalType: upgradedDto.type,
                duration: Date.now() - startTime,
                success: result.success,
                backupId: result.backupId
            });
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const targetId = dto.clinicId || dto.brandId;
            // Create audit trail for failed operation (only if we have a valid targetId)
            if (targetId) {
                // Try to get the upgraded DTO type, but fall back to original if upgrade failed
                let auditType = dto.type;
                try {
                    const upgradedDto = await this.upgradeClinicToBrandDeletionIfNeeded(dto);
                    auditType = upgradedDto.type;
                }
                catch (_a) {
                    // If upgrade fails, use original type
                }
                await this.createAuditTrail('deletion', auditType, targetId, userId, null, error instanceof Error ? error : new Error(errorMessage), startTime);
            }
            this.logger.error('Error in backup and deletion execution', {
                error: errorMessage,
                dto,
                duration: Date.now() - startTime,
                severity: 'CRITICAL'
            });
            throw error;
        }
    }
    /**
     * Validates that user has access to the clinic
     */
    async validateClinicAccess(clinicId, userBrandId) {
        var _a;
        const clinic = await this.clinicRepository.findOne({
            where: { id: clinicId },
            relations: ['brand']
        });
        return ((_a = clinic === null || clinic === void 0 ? void 0 : clinic.brand) === null || _a === void 0 ? void 0 : _a.id) === userBrandId;
    }
    /**
     * Execute dry run - simulate backup and deletion without actually doing it
     */
    async executeDryRun(dto, targetInfo, userId) {
        // Get impact analysis
        const databaseImpact = await this.analyzeDatabaseImpact(dto);
        const s3Impact = await this.analyzeS3Impact(dto);
        // Simulate backup timing
        const estimatedBackupTime = this.calculateEstimatedTime(databaseImpact.totalRecords, s3Impact.totalFiles);
        return {
            success: true,
            mode: dto.mode,
            backupId: `dry-run-${(0, uuid_1.v4)()}`,
            targetInfo: {
                type: targetInfo.type,
                id: targetInfo.id,
                name: targetInfo.name
            },
            results: {
                databaseBackup: {
                    tablesProcessed: databaseImpact.tables.length,
                    recordsBackedUp: databaseImpact.totalRecords,
                    duration: estimatedBackupTime
                },
                s3Backup: dto.skipS3Backup
                    ? undefined
                    : {
                        filesProcessed: s3Impact.totalFiles,
                        filesBackedUp: s3Impact.totalFiles,
                        filesSkipped: 0,
                        totalSizeBytes: 0, // Estimated
                        duration: estimatedBackupTime
                    },
                s3OriginalFileDeletion: dto.skipS3Backup
                    ? undefined
                    : {
                        filesProcessed: s3Impact.totalFiles,
                        filesDeleted: s3Impact.totalFiles,
                        filesSkipped: 0,
                        duration: '1 second'
                    },
                databaseDeletion: {
                    tablesProcessed: databaseImpact.tables.length,
                    recordsDeleted: databaseImpact.totalRecords,
                    duration: '1 second'
                }
            },
            backup: {
                backupLocation: `backups/clinic-deletion/${targetInfo.id}/${new Date().toISOString()}`,
                backupSize: 0, // Estimated
                estimatedRestoreTime: estimatedBackupTime
            },
            audit: {
                executedBy: userId,
                executedAt: new Date()
            }
        };
    }
    /**
     * Execute actual backup and deletion
     */
    async executeActualBackupAndDeletion(dto, targetInfo, userId) {
        const backupId = (0, uuid_1.v4)();
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupBasePath = `backups/clinic-deletion/${targetInfo.id}/${timestamp}`;
        let databaseBackupResult = null;
        let fileBackupResult = null;
        let s3Impact = null;
        try {
            // Step 1: Get S3 impact analysis once (if S3 backup is not skipped)
            if (!dto.skipS3Backup) {
                this.logger.log('Analyzing S3 impact for backup and deletion', {
                    backupId
                });
                s3Impact = await this.analyzeS3Impact(dto);
            }
            // Step 2: Create backup metadata
            const backupMetadata = {
                backupId,
                targetType: dto.type,
                targetId: targetInfo.id,
                targetName: targetInfo.name,
                createdAt: new Date(),
                createdBy: userId,
                status: clinic_deletion_dto_1.BackupStatus.IN_PROGRESS,
                backupLocation: backupBasePath,
                databaseBackup: {
                    totalTables: 0,
                    totalRecords: 0,
                    tablesBackedUp: [],
                    sizeBytes: 0
                },
                fileBackup: {
                    totalFiles: 0,
                    filesBackedUp: 0,
                    totalSizeBytes: 0
                },
                estimatedRestoreTime: '0 seconds'
            };
            // Step 3: Backup database records
            this.logger.log('Starting database backup', { backupId });
            databaseBackupResult =
                await this.databaseBackupService.backupDatabaseRecords(dto.type, targetInfo.id, backupId, backupBasePath);
            // Update metadata with database backup results
            backupMetadata.databaseBackup = {
                totalTables: databaseBackupResult.manifest.tables.length,
                totalRecords: databaseBackupResult.totalRecords,
                tablesBackedUp: databaseBackupResult.manifest.tables.map((t) => t.tableName),
                sizeBytes: databaseBackupResult.totalSizeBytes
            };
            // Step 4: Backup S3 files (if not skipped)
            if (!dto.skipS3Backup && s3Impact) {
                this.logger.log('Starting S3 file backup', { backupId });
                fileBackupResult = await this.fileBackupService.backupS3Files(dto.type, targetInfo.id, backupId, backupBasePath);
                // Update metadata with file backup results
                backupMetadata.fileBackup = {
                    totalFiles: fileBackupResult.totalFiles,
                    filesBackedUp: fileBackupResult.filesBackedUp,
                    totalSizeBytes: fileBackupResult.totalSizeBytes
                };
            }
            // Step 4: Calculate estimated restore time
            backupMetadata.estimatedRestoreTime = this.calculateEstimatedTime(backupMetadata.databaseBackup.totalRecords, backupMetadata.fileBackup.totalFiles);
            // Step 5: Update backup status to completed
            backupMetadata.status = clinic_deletion_dto_1.BackupStatus.COMPLETED;
            // Step 6: Save backup metadata
            const metadataPath = `${backupBasePath}/backup-metadata.json`;
            await this.s3Service.uploadBuffer(Buffer.from(JSON.stringify(backupMetadata, null, 2)), metadataPath, 'application/json');
            // Step 7: Delete original S3 files (if not skipped and after successful backup)
            let s3OriginalFileDeletionResult = null;
            if (!dto.skipS3Backup && s3Impact) {
                this.logger.log('Starting original S3 file deletion after successful backup', { backupId });
                // Use the already computed S3 impact analysis
                s3OriginalFileDeletionResult =
                    await this.executeOriginalS3FileDeletion(s3Impact);
            }
            // Step 8: Delete database records (after successful backup and S3 cleanup)
            this.logger.log('Starting database deletion after successful backup', { backupId });
            const deletionResult = await this.executeActualDatabaseDeletion(dto);
            this.logger.log('Backup and deletion completed successfully', {
                backupId,
                backupLocation: backupBasePath
            });
            return {
                success: true,
                mode: dto.mode,
                backupId,
                targetInfo: {
                    type: targetInfo.type,
                    id: targetInfo.id,
                    name: targetInfo.name
                },
                results: {
                    databaseBackup: {
                        tablesProcessed: databaseBackupResult.manifest.tables.length,
                        recordsBackedUp: databaseBackupResult.totalRecords,
                        duration: `${databaseBackupResult.duration}ms`
                    },
                    s3Backup: fileBackupResult
                        ? {
                            filesProcessed: fileBackupResult.totalFiles,
                            filesBackedUp: fileBackupResult.filesBackedUp,
                            filesSkipped: fileBackupResult.filesSkipped,
                            totalSizeBytes: fileBackupResult.totalSizeBytes,
                            duration: `${fileBackupResult.duration}ms`
                        }
                        : undefined,
                    s3OriginalFileDeletion: s3OriginalFileDeletionResult
                        ? {
                            filesProcessed: s3OriginalFileDeletionResult.filesProcessed,
                            filesDeleted: s3OriginalFileDeletionResult.filesDeleted,
                            filesSkipped: s3OriginalFileDeletionResult.filesSkipped,
                            duration: s3OriginalFileDeletionResult.duration
                        }
                        : undefined,
                    databaseDeletion: {
                        tablesProcessed: deletionResult.tablesProcessed,
                        recordsDeleted: deletionResult.recordsDeleted,
                        duration: deletionResult.duration
                    }
                },
                backup: {
                    backupLocation: backupBasePath,
                    backupSize: backupMetadata.databaseBackup.sizeBytes +
                        backupMetadata.fileBackup.totalSizeBytes,
                    estimatedRestoreTime: backupMetadata.estimatedRestoreTime
                },
                audit: {
                    executedBy: userId,
                    executedAt: new Date()
                }
            };
        }
        catch (error) {
            // If backup fails, don't delete anything
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Backup failed - aborting deletion', {
                error: errorMessage,
                backupId
            });
            // Update backup status to failed
            try {
                const failedMetadata = {
                    backupId,
                    targetType: dto.type,
                    targetId: targetInfo.id,
                    targetName: targetInfo.name,
                    createdAt: new Date(),
                    createdBy: userId,
                    status: clinic_deletion_dto_1.BackupStatus.FAILED,
                    backupLocation: backupBasePath,
                    databaseBackup: {
                        totalTables: 0,
                        totalRecords: 0,
                        tablesBackedUp: [],
                        sizeBytes: 0
                    },
                    fileBackup: {
                        totalFiles: 0,
                        filesBackedUp: 0,
                        totalSizeBytes: 0
                    },
                    estimatedRestoreTime: '0 seconds'
                };
                const metadataPath = `${backupBasePath}/backup-metadata.json`;
                await this.s3Service.uploadBuffer(Buffer.from(JSON.stringify(failedMetadata, null, 2)), metadataPath, 'application/json');
            }
            catch (metadataError) {
                this.logger.error('Failed to save failed backup metadata', {
                    error: metadataError
                });
            }
            throw new common_1.InternalServerErrorException(`Backup failed: ${errorMessage}. No data was deleted.`);
        }
    }
    /**
     * Execute original S3 file deletion (only called after successful backup)
     */
    async executeOriginalS3FileDeletion(s3Impact) {
        const startTime = Date.now();
        try {
            const fileReferences = s3Impact.fileReferences;
            let filesProcessed = 0;
            let filesDeleted = 0;
            let filesSkipped = 0;
            this.logger.log('Starting original S3 file deletion', {
                totalFiles: fileReferences.length
            });
            // Delete each original file
            for (const fileRef of fileReferences) {
                try {
                    filesProcessed++;
                    // Clean the file key (remove leading slash if present)
                    const cleanFileKey = fileRef.fileKey.startsWith('/')
                        ? fileRef.fileKey.substring(1)
                        : fileRef.fileKey;
                    // Check if file exists before attempting deletion
                    const fileExists = await this.s3Service.objectExists(cleanFileKey);
                    if (!fileExists) {
                        filesSkipped++;
                        this.logger.warn(`File does not exist, skipping: ${cleanFileKey}`);
                        continue;
                    }
                    // Delete the file
                    await this.s3Service.deleteFile(cleanFileKey);
                    filesDeleted++;
                    this.logger.log(`Deleted original S3 file: ${cleanFileKey}`, {
                        sourceTable: fileRef.sourceTable
                    });
                }
                catch (error) {
                    filesSkipped++;
                    const errorMessage = error instanceof Error
                        ? error.message
                        : 'Unknown error';
                    this.logger.error(`Failed to delete original S3 file: ${fileRef.fileKey}`, {
                        error: errorMessage,
                        sourceTable: fileRef.sourceTable
                    });
                    // Continue with other files rather than failing the entire operation
                }
            }
            const duration = `${Date.now() - startTime}ms`;
            this.logger.log('Original S3 file deletion completed', {
                filesProcessed,
                filesDeleted,
                filesSkipped,
                duration
            });
            return {
                filesProcessed,
                filesDeleted,
                filesSkipped,
                duration
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Original S3 file deletion failed', {
                error: errorMessage
            });
            throw error;
        }
    }
    /**
     * Execute actual database deletion (only called after successful backup)
     */
    async executeActualDatabaseDeletion(dto) {
        const startTime = Date.now();
        try {
            // Get deletion queries in correct order from QueryManager (centralized)
            const targetId = dto.clinicId || dto.brandId;
            const deletionQueries = this.queryManagerService.getDatabaseDeletionQueries(dto.type, targetId);
            let tablesProcessed = 0;
            let recordsDeleted = 0;
            // Execute deletions in transaction
            await this.dataSource.transaction(async (manager) => {
                for (const [tableName, query] of Object.entries(deletionQueries)) {
                    try {
                        const result = await manager.query(query.sql, query.params);
                        // For PostgreSQL DELETE queries, the result is an array with affected rows info
                        // result[1] contains the rowCount for DELETE operations
                        const deletedCount = result[1] || 0;
                        if (deletedCount > 0) {
                            tablesProcessed++;
                            recordsDeleted += deletedCount;
                            this.logger.log(`Deleted ${deletedCount} records from ${tableName}`);
                        }
                        else {
                            this.logger.log(`No records to delete from ${tableName}`);
                        }
                    }
                    catch (error) {
                        const errorMessage = error instanceof Error
                            ? error.message
                            : 'Unknown error';
                        this.logger.error(`Failed to delete from ${tableName}`, {
                            error: errorMessage
                        });
                        throw error;
                    }
                }
            });
            const duration = `${Date.now() - startTime}ms`;
            return {
                tablesProcessed,
                recordsDeleted,
                duration
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Database deletion failed', {
                error: errorMessage
            });
            throw error;
        }
    }
    /**
     * Gets target entity information
     */
    async getTargetInfo(dto) {
        if (dto.type === clinic_deletion_dto_1.DeletionType.CLINIC && dto.clinicId) {
            const clinic = await this.clinicRepository.findOne({
                where: { id: dto.clinicId },
                relations: ['brand']
            });
            if (!clinic) {
                throw new common_1.NotFoundException(`Clinic with ID ${dto.clinicId} not found`);
            }
            return {
                type: clinic_deletion_dto_1.DeletionType.CLINIC,
                id: clinic.id,
                name: clinic.name,
                brandId: clinic.brand.id,
                brandName: clinic.brand.name
            };
        }
        else if (dto.type === clinic_deletion_dto_1.DeletionType.BRAND && dto.brandId) {
            const brand = await this.brandRepository.findOne({
                where: { id: dto.brandId }
            });
            if (!brand) {
                throw new common_1.NotFoundException(`Brand with ID ${dto.brandId} not found`);
            }
            return {
                type: clinic_deletion_dto_1.DeletionType.BRAND,
                id: brand.id,
                name: brand.name
            };
        }
        throw new Error('Invalid deletion target');
    }
    /**
     * Analyzes database impact
     */
    async analyzeDatabaseImpact(dto) {
        var _a;
        const tables = [];
        let totalRecords = 0;
        // Get table analysis queries from QueryManager (centralized)
        const targetId = dto.clinicId || dto.brandId;
        if (!targetId) {
            throw new Error('Either clinicId or brandId must be provided');
        }
        const analysisQueries = this.queryManagerService.getDatabaseAnalysisQueries(dto.type, targetId);
        for (const [tableName, query] of Object.entries(analysisQueries)) {
            try {
                const result = await this.dataSource.query(query.sql, query.params);
                const count = parseInt(((_a = result[0]) === null || _a === void 0 ? void 0 : _a.count) || '0');
                if (count > 0) {
                    tables.push({
                        tableName,
                        recordCount: count,
                        description: query.description || `${tableName} records`
                    });
                    totalRecords += count;
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                this.logger.error(`Error analyzing table ${tableName}`, {
                    error: errorMessage
                });
            }
        }
        return { tables, totalRecords };
    }
    /**
     * Analyzes S3 file impact
     */
    async analyzeS3Impact(dto) {
        const files = [];
        let totalFiles = 0;
        const allFileReferences = [];
        // Get S3 file analysis queries from QueryManager (centralized)
        const targetId = dto.clinicId || dto.brandId;
        if (!targetId) {
            throw new Error('Either clinicId or brandId must be provided');
        }
        const s3Queries = this.queryManagerService.getS3AnalysisQueries(dto.type, targetId);
        for (const [sourceTable, query] of Object.entries(s3Queries)) {
            try {
                const result = await this.dataSource.query(query.sql, query.params);
                const fileRefs = result
                    .filter((row) => row.file_key &&
                    typeof row.file_key === 'string' &&
                    row.file_key.trim() !== '')
                    .map((row) => ({
                    sourceTable,
                    fileKey: row.file_key,
                    createdAt: row.created_at
                }));
                if (fileRefs.length > 0) {
                    files.push({
                        sourceTable,
                        fileCount: fileRefs.length,
                        description: query.description || `${sourceTable} files`
                    });
                    totalFiles += fileRefs.length;
                    allFileReferences.push(...fileRefs);
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                this.logger.error(`Error analyzing S3 files for ${sourceTable}`, {
                    error: errorMessage
                });
            }
        }
        return {
            files,
            totalFiles,
            fileReferences: allFileReferences
        };
    }
    /**
     * Calculates estimated time for deletion
     */
    calculateEstimatedTime(totalRecords, totalFiles) {
        // Rough estimates: 1000 DB records per second, 10 S3 files per second
        const dbTime = Math.ceil(totalRecords / 1000);
        const s3Time = Math.ceil(totalFiles / 10);
        const totalSeconds = dbTime + s3Time;
        if (totalSeconds < 60) {
            return `${totalSeconds} seconds`;
        }
        else if (totalSeconds < 3600) {
            return `${Math.ceil(totalSeconds / 60)} minutes`;
        }
        else {
            return `${Math.ceil(totalSeconds / 3600)} hours`;
        }
    }
    /**
     * Generates warnings for the deletion process
     */
    generateWarnings(databaseImpact, s3Impact) {
        const warnings = [];
        if (databaseImpact.totalRecords > 10000) {
            warnings.push('Large number of database records detected - deletion may take significant time');
        }
        if (s3Impact.totalFiles > 1000) {
            warnings.push('Large number of S3 files detected - cleanup may take significant time');
        }
        if (databaseImpact.tables.some((t) => t.tableName === 'patients' && t.recordCount > 100)) {
            warnings.push('Large number of patient records will be deleted - ensure proper backup');
        }
        warnings.push('This action is irreversible - ensure you have proper backups');
        warnings.push('All associated data including medical records will be permanently deleted');
        return warnings;
    }
    /**
     * List available backups
     */
    async listBackups(query) {
        try {
            this.logger.log('Listing backups', { query });
            // Scan S3 for backup-metadata.json files
            const backupPrefix = 'backups/clinic-deletion/';
            // List all objects under the backup prefix
            const s3Objects = await this.s3Service.listObjects(backupPrefix);
            if (!s3Objects.Contents) {
                this.logger.log('No backup objects found');
                return {
                    backups: [],
                    total: 0,
                    limit: query.limit || 50,
                    offset: query.offset || 0
                };
            }
            // Filter for backup-metadata.json files
            const metadataFiles = s3Objects.Contents.filter(obj => obj.Key && obj.Key.endsWith('/backup-metadata.json')).map(obj => obj.Key);
            this.logger.log('Found metadata files', {
                metadataFiles: metadataFiles.length
            });
            // Load and parse all metadata files
            const allBackups = [];
            for (const metadataPath of metadataFiles) {
                try {
                    const metadataObject = await this.s3Service.getObject(metadataPath);
                    if (metadataObject && metadataObject.Body) {
                        const metadataContent = metadataObject.Body.toString();
                        const metadata = JSON.parse(metadataContent);
                        allBackups.push(metadata);
                    }
                }
                catch (parseError) {
                    // Skip invalid metadata files
                    this.logger.warn('Failed to parse metadata file', {
                        metadataPath,
                        error: parseError instanceof Error
                            ? parseError.message
                            : 'Unknown error'
                    });
                    continue;
                }
            }
            // Apply filters
            let filteredBackups = allBackups;
            if (query.targetType) {
                filteredBackups = filteredBackups.filter(backup => backup.targetType === query.targetType);
            }
            if (query.targetId) {
                filteredBackups = filteredBackups.filter(backup => backup.targetId === query.targetId);
            }
            if (query.status) {
                filteredBackups = filteredBackups.filter(backup => backup.status === query.status);
            }
            // Sort by creation date (newest first)
            filteredBackups.sort((a, b) => new Date(b.createdAt).getTime() -
                new Date(a.createdAt).getTime());
            // Apply pagination
            const limit = query.limit || 50;
            const offset = query.offset || 0;
            const paginatedBackups = filteredBackups.slice(offset, offset + limit);
            this.logger.log('Backup listing completed', {
                totalFound: allBackups.length,
                afterFilters: filteredBackups.length,
                returned: paginatedBackups.length
            });
            return {
                backups: paginatedBackups,
                total: filteredBackups.length,
                limit,
                offset
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Failed to list backups', {
                error: errorMessage,
                query
            });
            throw error;
        }
    }
    /**
     * Get backup details
     */
    async getBackupDetails(backupId) {
        try {
            this.logger.log('Getting backup details', { backupId });
            // Scan S3 for backup-metadata.json files to find the one with matching backupId
            const backupPrefix = 'backups/clinic-deletion/';
            // List all objects under the backup prefix
            const s3Objects = await this.s3Service.listObjects(backupPrefix);
            if (!s3Objects.Contents) {
                this.logger.log('No backup objects found', { backupId });
                return null;
            }
            // Filter for backup-metadata.json files
            const metadataFiles = s3Objects.Contents.filter(obj => obj.Key && obj.Key.endsWith('/backup-metadata.json')).map(obj => obj.Key);
            this.logger.log('Found metadata files', {
                backupId,
                metadataFiles: metadataFiles.length
            });
            // Check each metadata file for matching backupId
            for (const metadataPath of metadataFiles) {
                try {
                    const metadataObject = await this.s3Service.getObject(metadataPath);
                    if (metadataObject && metadataObject.Body) {
                        const metadataContent = metadataObject.Body.toString();
                        const metadata = JSON.parse(metadataContent);
                        if (metadata.backupId === backupId) {
                            this.logger.log('Found matching backup metadata', {
                                backupId,
                                metadataPath
                            });
                            return metadata;
                        }
                    }
                }
                catch (parseError) {
                    // Skip invalid metadata files
                    this.logger.warn('Failed to parse metadata file', {
                        metadataPath,
                        error: parseError instanceof Error
                            ? parseError.message
                            : 'Unknown error'
                    });
                    continue;
                }
            }
            this.logger.log('No matching backup found', { backupId });
            return null;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Failed to get backup details', {
                error: errorMessage,
                backupId
            });
            throw error;
        }
    }
    /**
     * Delete backup permanently
     */
    async deleteBackup(backupId) {
        try {
            this.logger.warn('Deleting backup', { backupId });
            // Step 1: Get backup metadata to identify all files
            const backupMetadata = await this.getBackupDetails(backupId);
            if (!backupMetadata) {
                throw new common_1.NotFoundException(`Backup with ID ${backupId} not found`);
            }
            const filesToDelete = [];
            const backupBasePath = backupMetadata.backupLocation;
            // Step 2: Collect all files to delete
            // Add backup metadata file
            filesToDelete.push(`${backupBasePath}/backup-metadata.json`);
            // Add database manifest and data files
            try {
                const databaseManifestPath = `${backupBasePath}/database/manifest.json`;
                const databaseManifest = await this.s3Service.getObject(databaseManifestPath);
                if (databaseManifest && databaseManifest.Body) {
                    const manifest = JSON.parse(databaseManifest.Body.toString());
                    // Add manifest file
                    filesToDelete.push(databaseManifestPath);
                    // Add all table data files
                    if (manifest.tables) {
                        for (const table of manifest.tables) {
                            filesToDelete.push(`${backupBasePath}/database/${table.fileName}`);
                        }
                    }
                }
            }
            catch (error) {
                this.logger.warn('Could not load database manifest for deletion', {
                    backupId,
                    error: error instanceof Error
                        ? error.message
                        : 'Unknown error'
                });
            }
            // Add file manifest and backed up files
            try {
                const fileManifestPath = `${backupBasePath}/files/file-manifest.json`;
                const fileManifest = await this.s3Service.getObject(fileManifestPath);
                if (fileManifest && fileManifest.Body) {
                    const manifest = JSON.parse(fileManifest.Body.toString());
                    // Add manifest file
                    filesToDelete.push(fileManifestPath);
                    // Add all backed up files
                    if (manifest.files) {
                        for (const file of manifest.files) {
                            filesToDelete.push(file.backupPath);
                        }
                    }
                }
            }
            catch (error) {
                this.logger.warn('Could not load file manifest for deletion', {
                    backupId,
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
            // Step 3: Delete all files
            let deletedCount = 0;
            let errorCount = 0;
            this.logger.log('Starting backup file deletion', {
                backupId,
                totalFiles: filesToDelete.length
            });
            for (const filePath of filesToDelete) {
                try {
                    await this.s3Service.deleteFile(filePath);
                    deletedCount++;
                    this.logger.log(`Deleted backup file: ${filePath}`);
                }
                catch (deleteError) {
                    errorCount++;
                    this.logger.error(`Failed to delete backup file: ${filePath}`, {
                        error: deleteError instanceof Error
                            ? deleteError.message
                            : 'Unknown error'
                    });
                }
            }
            const success = errorCount === 0;
            const message = success
                ? `Backup ${backupId} deleted successfully (${deletedCount} files)`
                : `Backup ${backupId} partially deleted (${deletedCount}/${filesToDelete.length} files, ${errorCount} errors)`;
            this.logger.log('Backup deletion completed', {
                backupId,
                success,
                deletedCount,
                errorCount,
                totalFiles: filesToDelete.length
            });
            return { success, message };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Failed to delete backup', {
                error: errorMessage,
                backupId
            });
            throw error;
        }
    }
    /**
     * Restore from backup
     */
    async restoreFromBackup(backupId, conflictResolution) {
        try {
            this.logger.log('Starting restore from backup', {
                backupId,
                conflictResolution
            });
            // Get backup metadata
            const backupMetadata = await this.getBackupDetails(backupId);
            if (!backupMetadata) {
                throw new common_1.NotFoundException(`Backup with ID ${backupId} not found`);
            }
            // Restore database records
            const databaseRestoreResult = await this.databaseRestoreService.restoreDatabaseRecords(backupMetadata.backupLocation, conflictResolution);
            // Restore S3 files
            const fileRestoreResult = await this.fileRestoreService.restoreS3Files(backupMetadata.backupLocation, conflictResolution);
            const response = {
                success: true,
                mode: clinic_deletion_dto_1.RestoreMode.EXECUTE,
                backupId,
                results: {
                    databaseRestore: {
                        tablesProcessed: databaseRestoreResult.tablesProcessed,
                        recordsRestored: databaseRestoreResult.recordsRestored,
                        recordsSkipped: databaseRestoreResult.recordsSkipped,
                        duration: `${databaseRestoreResult.duration}ms`
                    },
                    fileRestore: {
                        filesProcessed: fileRestoreResult.filesProcessed,
                        filesRestored: fileRestoreResult.filesRestored,
                        filesSkipped: fileRestoreResult.filesSkipped,
                        duration: `${fileRestoreResult.duration}ms`
                    }
                },
                conflicts: {
                    resolved: databaseRestoreResult.conflicts.resolved +
                        fileRestoreResult.conflicts.resolved,
                    skipped: databaseRestoreResult.conflicts.skipped +
                        fileRestoreResult.conflicts.skipped,
                    failed: databaseRestoreResult.conflicts.failed +
                        fileRestoreResult.conflicts.failed
                },
                audit: {
                    executedBy: 'system', // This will be updated by the controller
                    executedAt: new Date(),
                    restoredFrom: backupMetadata.backupLocation
                }
            };
            this.logger.log('Restore from backup completed', {
                backupId,
                success: response.success,
                recordsRestored: databaseRestoreResult.recordsRestored,
                filesRestored: fileRestoreResult.filesRestored
            });
            return response;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Failed to restore from backup', {
                error: errorMessage,
                backupId,
                conflictResolution
            });
            throw error;
        }
    }
    /**
     * Analyze restore impact
     */
    async analyzeRestoreImpact(dto) {
        try {
            // Load backup metadata
            const backupMetadata = await this.getBackupDetails(dto.backupId);
            if (!backupMetadata) {
                throw new common_1.NotFoundException(`Backup with ID ${dto.backupId} not found`);
            }
            // Analyze database conflicts
            const databaseConflicts = await this.databaseRestoreService.analyzeRestoreConflicts(backupMetadata.backupLocation);
            // Analyze file conflicts
            const fileConflicts = await this.fileRestoreService.analyzeFileRestoreConflicts(backupMetadata.backupLocation);
            // Generate warnings based on analysis results
            const warnings = this.generateRestoreWarnings(databaseConflicts, fileConflicts);
            // Calculate conflict-aware restore duration estimate
            const estimatedRestoreTime = this.estimateRestoreDuration(databaseConflicts, fileConflicts);
            return {
                backupInfo: backupMetadata,
                conflicts: {
                    databaseConflicts: databaseConflicts.map(conflict => ({
                        tableName: conflict.tableName,
                        conflictingRecords: conflict.conflictingRecords,
                        conflictType: conflict.conflictType
                    })),
                    fileConflicts: fileConflicts.map(conflict => ({
                        originalPath: conflict.originalPath,
                        conflictType: conflict.conflictType,
                        existingFileSize: conflict.existingFileSize,
                        backupFileSize: conflict.backupFileSize
                    }))
                },
                estimatedRestoreTime,
                warnings
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Failed to analyze restore impact', {
                error: errorMessage,
                backupId: dto.backupId
            });
            throw error;
        }
    }
    /**
     * Execute restore from backup
     */
    async executeRestore(dto, userId) {
        const startTime = Date.now();
        try {
            // Load backup metadata
            const backupMetadata = await this.getBackupDetails(dto.backupId);
            if (!backupMetadata) {
                throw new common_1.NotFoundException(`Backup with ID ${dto.backupId} not found`);
            }
            // For dry run, just analyze conflicts
            if (dto.mode === 'dry_run') {
                const impactAnalysis = await this.analyzeRestoreImpact(dto);
                const result = {
                    success: true,
                    mode: dto.mode,
                    backupId: dto.backupId,
                    results: {
                        databaseRestore: {
                            tablesProcessed: backupMetadata.databaseBackup.totalTables,
                            recordsRestored: backupMetadata.databaseBackup.totalRecords,
                            recordsSkipped: 0,
                            duration: 'simulated'
                        },
                        fileRestore: dto.skipFileRestore
                            ? undefined
                            : {
                                filesProcessed: backupMetadata.fileBackup.totalFiles,
                                filesRestored: backupMetadata.fileBackup.filesBackedUp,
                                filesSkipped: 0,
                                duration: 'simulated'
                            }
                    },
                    conflicts: {
                        resolved: 0,
                        skipped: impactAnalysis.conflicts.databaseConflicts.length +
                            impactAnalysis.conflicts.fileConflicts.length,
                        failed: 0
                    },
                    audit: {
                        executedBy: userId,
                        executedAt: new Date(),
                        restoredFrom: backupMetadata.backupLocation
                    }
                };
                // Create audit trail for dry run restore
                await this.createAuditTrail('restore', backupMetadata.targetType, backupMetadata.targetId, userId, result, undefined, startTime);
                return result;
            }
            // Execute actual restore
            let databaseRestoreResult = null;
            let fileRestoreResult = null;
            // Restore database
            if (!dto.skipDatabaseRestore) {
                databaseRestoreResult =
                    await this.databaseRestoreService.restoreDatabaseRecords(backupMetadata.backupLocation, dto.conflictResolution);
            }
            // Restore files
            if (!dto.skipFileRestore) {
                fileRestoreResult =
                    await this.fileRestoreService.restoreS3Files(backupMetadata.backupLocation, dto.conflictResolution);
            }
            const result = {
                success: true,
                mode: dto.mode,
                backupId: dto.backupId,
                results: {
                    databaseRestore: databaseRestoreResult
                        ? {
                            tablesProcessed: databaseRestoreResult.tablesProcessed,
                            recordsRestored: databaseRestoreResult.recordsRestored,
                            recordsSkipped: databaseRestoreResult.recordsSkipped,
                            duration: `${databaseRestoreResult.duration}ms`
                        }
                        : {
                            tablesProcessed: 0,
                            recordsRestored: 0,
                            recordsSkipped: 0,
                            duration: 'skipped'
                        },
                    fileRestore: fileRestoreResult
                        ? {
                            filesProcessed: fileRestoreResult.filesProcessed,
                            filesRestored: fileRestoreResult.filesRestored,
                            filesSkipped: fileRestoreResult.filesSkipped,
                            duration: `${fileRestoreResult.duration}ms`
                        }
                        : undefined
                },
                conflicts: {
                    resolved: ((databaseRestoreResult === null || databaseRestoreResult === void 0 ? void 0 : databaseRestoreResult.conflicts.resolved) || 0) +
                        ((fileRestoreResult === null || fileRestoreResult === void 0 ? void 0 : fileRestoreResult.conflicts.resolved) || 0),
                    skipped: ((databaseRestoreResult === null || databaseRestoreResult === void 0 ? void 0 : databaseRestoreResult.conflicts.skipped) || 0) +
                        ((fileRestoreResult === null || fileRestoreResult === void 0 ? void 0 : fileRestoreResult.conflicts.skipped) || 0),
                    failed: ((databaseRestoreResult === null || databaseRestoreResult === void 0 ? void 0 : databaseRestoreResult.conflicts.failed) || 0) +
                        ((fileRestoreResult === null || fileRestoreResult === void 0 ? void 0 : fileRestoreResult.conflicts.failed) || 0)
                },
                audit: {
                    executedBy: userId,
                    executedAt: new Date(),
                    restoredFrom: backupMetadata.backupLocation
                }
            };
            // Create audit trail for successful restore execution
            await this.createAuditTrail('restore', backupMetadata.targetType, backupMetadata.targetId, userId, result, undefined, startTime);
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Failed to execute restore', {
                error: errorMessage,
                backupId: dto.backupId
            });
            // Create audit trail for failed restore
            try {
                const backupMetadata = await this.getBackupDetails(dto.backupId);
                if (backupMetadata) {
                    await this.createAuditTrail('restore', backupMetadata.targetType, backupMetadata.targetId, userId, null, error instanceof Error
                        ? error
                        : new Error(errorMessage), startTime);
                }
            }
            catch (auditError) {
                this.logger.error('Failed to create audit trail for failed restore', {
                    auditError: auditError instanceof Error
                        ? auditError.message
                        : 'Unknown audit error'
                });
            }
            throw error;
        }
    }
    /**
     * Estimate restore duration based on conflicts and data volume
     */
    estimateRestoreDuration(databaseConflicts, fileConflicts) {
        // Base time for restore operations
        const baseTime = 30; // 30 seconds base time
        // Time per database table with conflicts (more complex than simple record count)
        const dbTime = databaseConflicts.length * 10; // 10 seconds per table with conflicts
        // Time per file conflict (file operations are slower)
        const fileTime = fileConflicts.length * 5; // 5 seconds per file conflict
        const totalSeconds = baseTime + dbTime + fileTime;
        if (totalSeconds < 60) {
            return `${totalSeconds} seconds`;
        }
        else if (totalSeconds < 3600) {
            const minutes = Math.ceil(totalSeconds / 60);
            return `${minutes} minute${minutes > 1 ? 's' : ''}`;
        }
        else {
            const hours = Math.ceil(totalSeconds / 3600);
            return `${hours} hour${hours > 1 ? 's' : ''}`;
        }
    }
    /**
     * Generate warnings for restore impact analysis
     */
    generateRestoreWarnings(databaseConflicts, fileConflicts) {
        const warnings = [];
        // Count analysis failures (conflicts with -1 conflicting records)
        const databaseAnalysisFailures = databaseConflicts.filter(c => c.conflictingRecords === -1).length;
        // Count actual conflicts
        const actualDatabaseConflicts = databaseConflicts.filter(c => c.conflictingRecords > 0).length;
        const totalFileConflicts = fileConflicts.length;
        // Warn about analysis failures
        if (databaseAnalysisFailures > 0) {
            warnings.push(`Database conflict analysis failed for ${databaseAnalysisFailures} table(s). Restore may encounter unexpected conflicts.`);
        }
        // Warn about actual conflicts
        if (actualDatabaseConflicts > 0) {
            warnings.push(`${actualDatabaseConflicts} table(s) have database conflicts that need resolution.`);
        }
        if (totalFileConflicts > 0) {
            warnings.push(`${totalFileConflicts} file(s) have conflicts that need resolution.`);
        }
        // Warn about large numbers of conflicts
        if (actualDatabaseConflicts > 10) {
            warnings.push('Large number of database conflicts detected. Consider reviewing data integrity before restore.');
        }
        if (totalFileConflicts > 50) {
            warnings.push('Large number of file conflicts detected. Restore may take significant time.');
        }
        // Success message only if no conflicts AND no analysis failures
        if (warnings.length === 0) {
            warnings.push('No conflicts detected. Restore should proceed smoothly.');
        }
        return warnings;
    }
    /**
     * Create comprehensive audit trail for operations
     */
    async createAuditTrail(operation, targetType, targetId, userId, result, error, startTime, ipAddress, userAgent) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3;
        const endTime = Date.now();
        const durationMs = startTime
            ? endTime - startTime
            : (result === null || result === void 0 ? void 0 : result.duration) || 0;
        // Get target name for better audit trail readability
        let targetName;
        try {
            if (targetType === clinic_deletion_dto_1.DeletionType.CLINIC) {
                const clinic = await this.clinicRepository.findOne({
                    where: { id: targetId },
                    select: ['name']
                });
                targetName = clinic === null || clinic === void 0 ? void 0 : clinic.name;
            }
            else if (targetType === clinic_deletion_dto_1.DeletionType.BRAND) {
                const brand = await this.brandRepository.findOne({
                    where: { id: targetId },
                    select: ['name']
                });
                targetName = brand === null || brand === void 0 ? void 0 : brand.name;
            }
        }
        catch (nameError) {
            // Don't fail audit if we can't get the name
            this.logger.warn('Could not retrieve target name for audit trail', {
                targetType,
                targetId,
                error: nameError instanceof Error
                    ? nameError.message
                    : 'Unknown error'
            });
        }
        // Prepare operation data
        const operationData = {
            startTime: startTime
                ? new Date(startTime).toISOString()
                : undefined,
            endTime: new Date(endTime).toISOString(),
            durationMs,
            mode: (result === null || result === void 0 ? void 0 : result.mode) || 'EXECUTE'
        };
        // Add operation-specific data
        if (result) {
            if (((_a = result.results) === null || _a === void 0 ? void 0 : _a.databaseBackup) ||
                ((_b = result.results) === null || _b === void 0 ? void 0 : _b.databaseDeletion) ||
                ((_c = result.results) === null || _c === void 0 ? void 0 : _c.databaseRestore)) {
                operationData.databaseImpact = {
                    totalTables: ((_d = result.results.databaseBackup) === null || _d === void 0 ? void 0 : _d.totalTables) ||
                        ((_e = result.results.databaseDeletion) === null || _e === void 0 ? void 0 : _e.totalTables) ||
                        ((_f = result.results.databaseRestore) === null || _f === void 0 ? void 0 : _f.totalTables) ||
                        0,
                    totalRecords: ((_g = result.results.databaseBackup) === null || _g === void 0 ? void 0 : _g.recordsBackedUp) ||
                        ((_h = result.results.databaseDeletion) === null || _h === void 0 ? void 0 : _h.recordsDeleted) ||
                        ((_j = result.results.databaseRestore) === null || _j === void 0 ? void 0 : _j.recordsRestored) ||
                        0,
                    tablesProcessed: ((_k = result.results.databaseBackup) === null || _k === void 0 ? void 0 : _k.tablesBackedUp) ||
                        ((_l = result.results.databaseDeletion) === null || _l === void 0 ? void 0 : _l.tablesDeleted) ||
                        ((_m = result.results.databaseRestore) === null || _m === void 0 ? void 0 : _m.tablesRestored) ||
                        [],
                    sizeBytes: ((_o = result.results.databaseBackup) === null || _o === void 0 ? void 0 : _o.totalSizeBytes) ||
                        ((_p = result.results.databaseRestore) === null || _p === void 0 ? void 0 : _p.totalSizeBytes) ||
                        0
                };
            }
            if (((_q = result.results) === null || _q === void 0 ? void 0 : _q.s3Backup) ||
                ((_r = result.results) === null || _r === void 0 ? void 0 : _r.s3Cleanup) ||
                ((_s = result.results) === null || _s === void 0 ? void 0 : _s.fileRestore)) {
                operationData.fileImpact = {
                    totalFiles: ((_t = result.results.s3Backup) === null || _t === void 0 ? void 0 : _t.totalFiles) ||
                        ((_u = result.results.s3Cleanup) === null || _u === void 0 ? void 0 : _u.totalFiles) ||
                        ((_v = result.results.fileRestore) === null || _v === void 0 ? void 0 : _v.filesProcessed) ||
                        0,
                    filesProcessed: ((_w = result.results.s3Backup) === null || _w === void 0 ? void 0 : _w.filesBackedUp) ||
                        ((_x = result.results.s3Cleanup) === null || _x === void 0 ? void 0 : _x.filesDeleted) ||
                        ((_y = result.results.fileRestore) === null || _y === void 0 ? void 0 : _y.filesRestored) ||
                        0,
                    totalSizeBytes: ((_z = result.results.s3Backup) === null || _z === void 0 ? void 0 : _z.totalSizeBytes) ||
                        ((_0 = result.results.fileRestore) === null || _0 === void 0 ? void 0 : _0.totalSizeBytes) ||
                        0,
                    skippedFiles: ((_1 = result.results.s3Backup) === null || _1 === void 0 ? void 0 : _1.filesSkipped) ||
                        ((_2 = result.results.fileRestore) === null || _2 === void 0 ? void 0 : _2.filesSkipped) ||
                        0
                };
            }
            if (result.estimatedTime) {
                operationData.estimatedTime = result.estimatedTime;
            }
            if (result.warnings && result.warnings.length > 0) {
                operationData.warnings = result.warnings;
            }
        }
        // Prepare error details
        let errorDetails;
        if (error) {
            errorDetails = {
                message: error.message,
                stack: error.stack,
                name: error.name,
                context: {
                    operation,
                    targetType,
                    targetId,
                    userId
                }
            };
        }
        try {
            // Map DeletionType to ClinicDeletionAuditTargetType
            const auditTargetType = targetType === clinic_deletion_dto_1.DeletionType.CLINIC
                ? clinic_deletion_audit_trail_entity_1.ClinicDeletionAuditTargetType.CLINIC
                : clinic_deletion_audit_trail_entity_1.ClinicDeletionAuditTargetType.BRAND;
            // Validate backup ID is a proper UUID (dry-run IDs are not valid UUIDs)
            const isValidUUID = (str) => {
                const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
                return uuidRegex.test(str);
            };
            const validBackupId = (result === null || result === void 0 ? void 0 : result.backupId) && isValidUUID(result.backupId)
                ? result.backupId
                : null;
            // Create audit trail entry
            const auditEntry = this.auditTrailRepository.create({
                operation: operation,
                targetType: auditTargetType,
                targetId,
                targetName,
                userId,
                success: !error,
                backupId: validBackupId,
                backupLocation: (_3 = result === null || result === void 0 ? void 0 : result.backup) === null || _3 === void 0 ? void 0 : _3.backupLocation,
                operationData,
                errorDetails,
                durationMs,
                ipAddress,
                userAgent
            });
            // Save to database
            await this.auditTrailRepository.save(auditEntry);
            // Also log to application logs for immediate visibility
            this.logger.log('Audit trail created and stored', {
                auditId: auditEntry.id,
                operation,
                targetType,
                targetId,
                targetName,
                userId,
                success: !error,
                durationMs
            });
        }
        catch (auditError) {
            // Don't fail the main operation if audit fails, but log the error
            this.logger.error('Failed to create audit trail', {
                error: auditError instanceof Error
                    ? auditError.message
                    : 'Unknown audit error',
                originalOperation: operation,
                targetType,
                targetId,
                userId,
                auditErrorStack: auditError instanceof Error ? auditError.stack : undefined
            });
        }
    }
    /**
     * Validate operation prerequisites
     */
    async validateOperationPrerequisites(operation, targetType, targetId) {
        const errors = [];
        try {
            // Check if target exists
            const targetExists = await this.validateTargetExists(targetType, targetId);
            if (!targetExists) {
                errors.push(`${targetType} with ID ${targetId} does not exist`);
            }
            // Check for active operations
            // TODO: Implement active operation checking
            // const activeOperations = await this.checkActiveOperations(targetType, targetId);
            // if (activeOperations.length > 0) {
            //   errors.push(`Active operations in progress for ${targetType} ${targetId}`);
            // }
            // Operation-specific validations
            if (operation === 'deletion') {
                // Check for dependent data that might prevent deletion
                // TODO: Implement dependency checking
            }
            if (operation === 'restore') {
                // Check for conflicts that might prevent restore
                // TODO: Implement conflict checking
            }
            return {
                valid: errors.length === 0,
                errors
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error
                ? error.message
                : 'Unknown validation error';
            errors.push(`Validation failed: ${errorMessage}`);
            return {
                valid: false,
                errors
            };
        }
    }
    /**
     * Validate that target exists
     */
    async validateTargetExists(targetType, targetId) {
        try {
            const query = targetType === clinic_deletion_dto_1.DeletionType.CLINIC
                ? 'SELECT id FROM clinics WHERE id = $1'
                : 'SELECT id FROM brands WHERE id = $1';
            const result = await this.dataSource.query(query, [targetId]);
            return result.length > 0;
        }
        catch (error) {
            this.logger.error('Failed to validate target existence', {
                error: error instanceof Error ? error.message : 'Unknown error',
                targetType,
                targetId
            });
            return false;
        }
    }
    /**
     * Check if a clinic is the only clinic for its brand
     * If so, upgrade the deletion to brand deletion
     */
    async upgradeClinicToBrandDeletionIfNeeded(dto) {
        // Only check for clinic deletions
        if (dto.type !== clinic_deletion_dto_1.DeletionType.CLINIC || !dto.clinicId) {
            return dto;
        }
        try {
            // Get the clinic and its brand
            const clinic = await this.clinicRepository.findOne({
                where: { id: dto.clinicId },
                relations: ['brand']
            });
            if (!clinic) {
                // If clinic doesn't exist, let the normal validation handle it
                return dto;
            }
            // Count how many clinics exist for this brand
            const clinicCount = await this.clinicRepository.count({
                where: { brand: { id: clinic.brand.id } }
            });
            // If this is the only clinic for the brand, upgrade to brand deletion
            if (clinicCount === 1) {
                this.logger.log(`Upgrading clinic deletion to brand deletion - clinic ${dto.clinicId} is the only clinic for brand ${clinic.brand.id}`, {
                    clinicId: dto.clinicId,
                    brandId: clinic.brand.id,
                    brandName: clinic.brand.name
                });
                // Create new DTO with brand deletion type
                const upgradedDto = {
                    ...dto,
                    type: clinic_deletion_dto_1.DeletionType.BRAND,
                    brandId: clinic.brand.id,
                    clinicId: undefined // Remove clinicId since we're now doing brand deletion
                };
                return upgradedDto;
            }
            return dto;
        }
        catch (error) {
            this.logger.error('Failed to check if clinic deletion should be upgraded to brand deletion', {
                error: error instanceof Error
                    ? error.message
                    : 'Unknown error',
                clinicId: dto.clinicId
            });
            // If there's an error, return original DTO and let normal validation handle it
            return dto;
        }
    }
};
exports.ClinicDeletionService = ClinicDeletionService;
exports.ClinicDeletionService = ClinicDeletionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(brand_entity_1.Brand)),
    __param(1, (0, typeorm_1.InjectRepository)(clinic_entity_1.ClinicEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(clinic_deletion_audit_trail_entity_1.ClinicDeletionAuditTrail)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource,
        winston_logger_service_1.WinstonLogger,
        s3_service_1.S3Service,
        query_manager_service_1.QueryManagerService,
        database_backup_service_1.DatabaseBackupService,
        file_backup_service_1.FileBackupService,
        database_restore_service_1.DatabaseRestoreService,
        file_restore_service_1.FileRestoreService])
], ClinicDeletionService);
//# sourceMappingURL=clinic-deletion.service.js.map