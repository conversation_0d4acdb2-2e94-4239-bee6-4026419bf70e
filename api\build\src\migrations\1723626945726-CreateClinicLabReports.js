"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateClinicLabReports1723626945726 = void 0;
const typeorm_1 = require("typeorm");
class CreateClinicLabReports1723626945726 {
    constructor() {
        this.name = 'CreateClinicLabReports1723626945726';
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'clinic_lab_reports',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'clinic_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'name',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP',
                    isNullable: false
                }
            ]
        }), true);
        await queryRunner.createForeignKey('clinic_lab_reports', new typeorm_1.TableForeignKey({
            columnNames: ['clinic_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinics',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('clinic_lab_reports');
    }
}
exports.CreateClinicLabReports1723626945726 = CreateClinicLabReports1723626945726;
//# sourceMappingURL=1723626945726-CreateClinicLabReports.js.map