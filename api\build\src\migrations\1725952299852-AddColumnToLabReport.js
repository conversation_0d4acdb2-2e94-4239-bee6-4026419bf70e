"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddMoreColumsToClinicLabReports1725952299852 = void 0;
const typeorm_1 = require("typeorm");
class AddMoreColumsToClinicLabReports1725952299852 {
    async up(queryRunner) {
        await queryRunner.addColumns('clinic_lab_reports', [
            new typeorm_1.TableColumn({
                name: 'unique_id',
                type: 'varchar',
                isNullable: true,
                isUnique: true
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('clinic_lab_reports', 'unique_id');
    }
}
exports.AddMoreColumsToClinicLabReports1725952299852 = AddMoreColumsToClinicLabReports1725952299852;
//# sourceMappingURL=1725952299852-AddColumnToLabReport.js.map