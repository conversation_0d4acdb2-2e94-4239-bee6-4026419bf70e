"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var CronHelperModule_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CronHelperModule = void 0;
const common_1 = require("@nestjs/common");
const cronHelper_service_1 = require("./cronHelper.service");
const typeorm_1 = require("@nestjs/typeorm");
const patient_reminder_entity_1 = require("../../patient-reminders/entities/patient-reminder.entity");
const appointment_entity_1 = require("../../appointments/entities/appointment.entity");
const appointment_session_changes_entity_1 = require("../../socket/appointment-session-changes.entity");
const appointment_sessions_entity_1 = require("../../socket/appointment-sessions.entity");
const send_mail_service_1 = require("../aws/ses/send-mail-service");
const whatsapp_module_1 = require("../whatsapp-integration/whatsapp.module");
const config_1 = require("@nestjs/config");
const sqs_module_1 = require("../aws/sqs/sqs.module");
let CronHelperModule = CronHelperModule_1 = class CronHelperModule {
    static register(options) {
        var _a;
        const isCronEnabled = (_a = options === null || options === void 0 ? void 0 : options.enableCronJobs) !== null && _a !== void 0 ? _a : this.shouldEnableCron();
        const providers = isCronEnabled
            ? [cronHelper_service_1.CronHelperService, send_mail_service_1.SESMailService]
            : [send_mail_service_1.SESMailService];
        if (!isCronEnabled) {
            console.log('Cron jobs are disabled for this service instance');
        }
        else {
            console.log('Cron jobs are enabled for this service instance');
        }
        return {
            module: CronHelperModule_1,
            imports: [
                typeorm_1.TypeOrmModule.forFeature([
                    patient_reminder_entity_1.PatientReminder,
                    appointment_entity_1.AppointmentEntity,
                    appointment_session_changes_entity_1.AppointmentSessionChange,
                    appointment_sessions_entity_1.AppointmentSessions
                ]),
                whatsapp_module_1.WhatsappModule,
                config_1.ConfigModule,
                sqs_module_1.SqsModule.forRoot(true)
            ],
            controllers: [],
            providers,
            exports: isCronEnabled ? [cronHelper_service_1.CronHelperService] : []
        };
    }
    static shouldEnableCron() {
        // Check if this is a dedicated cron service based on SERVICE_TYPE environment variable
        const serviceType = process.env.SERVICE_TYPE || '';
        return serviceType === 'cron';
    }
};
exports.CronHelperModule = CronHelperModule;
exports.CronHelperModule = CronHelperModule = CronHelperModule_1 = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                patient_reminder_entity_1.PatientReminder,
                appointment_entity_1.AppointmentEntity,
                appointment_session_changes_entity_1.AppointmentSessionChange,
                appointment_sessions_entity_1.AppointmentSessions
            ]),
            whatsapp_module_1.WhatsappModule,
            config_1.ConfigModule,
            sqs_module_1.SqsModule.forRoot(true)
        ],
        controllers: [],
        providers: [send_mail_service_1.SESMailService],
        exports: [] // Don't export anything in the base module
    })
], CronHelperModule);
//# sourceMappingURL=cronHelper.module.js.map