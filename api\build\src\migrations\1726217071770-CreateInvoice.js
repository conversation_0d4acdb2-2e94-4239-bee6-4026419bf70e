"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateInvoice1726217071770 = void 0;
const typeorm_1 = require("typeorm");
class CreateInvoice1726217071770 {
    constructor() {
        this.name = 'CreateInvoice1726217071770';
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'invoices',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'cart_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'patient_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'discount',
                    type: 'decimal',
                    isNullable: false
                },
                {
                    name: 'price_after_discount',
                    type: 'decimal',
                    isNullable: false
                },
                {
                    name: 'total_price',
                    type: 'decimal',
                    isNullable: false
                },
                {
                    name: 'total_tax',
                    type: 'decimal',
                    isNullable: true
                },
                {
                    name: 'total_credit',
                    type: 'decimal',
                    isNullable: true
                },
                {
                    name: 'total_discount',
                    type: 'decimal',
                    isNullable: true
                },
                {
                    name: 'amount_payable',
                    type: 'decimal',
                    isNullable: true
                },
                {
                    name: 'amount_paid',
                    type: 'decimal',
                    isNullable: true
                },
                {
                    name: 'payment_mode',
                    type: 'varchar',
                    isNullable: true
                },
                {
                    name: 'details',
                    type: 'jsonb',
                    isNullable: false,
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                },
            ]
        }), true);
        await queryRunner.createForeignKey('invoices', new typeorm_1.TableForeignKey({
            columnNames: ['cart_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'carts',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('invoices');
    }
}
exports.CreateInvoice1726217071770 = CreateInvoice1726217071770;
//# sourceMappingURL=1726217071770-CreateInvoice.js.map