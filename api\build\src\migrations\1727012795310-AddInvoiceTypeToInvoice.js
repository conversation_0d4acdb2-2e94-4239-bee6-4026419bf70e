"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddInvoiceTypeToInvoice1727012795310 = void 0;
const typeorm_1 = require("typeorm");
class AddInvoiceTypeToInvoice1727012795310 {
    async up(queryRunner) {
        await queryRunner.addColumns('invoices', [
            new typeorm_1.TableColumn({
                name: 'invoice_type',
                type: 'varchar',
                isNullable: true,
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('invoices', 'invoice_type');
    }
}
exports.AddInvoiceTypeToInvoice1727012795310 = AddInvoiceTypeToInvoice1727012795310;
//# sourceMappingURL=1727012795310-AddInvoiceTypeToInvoice.js.map