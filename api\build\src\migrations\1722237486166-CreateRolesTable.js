"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateRolesTable1722237486166 = void 0;
const typeorm_1 = require("typeorm");
class CreateRolesTable1722237486166 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'roles',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true
                },
                {
                    name: 'name',
                    type: 'varchar',
                    isUnique: true
                },
                {
                    name: 'description',
                    type: 'varchar',
                    isNullable: true
                }
            ]
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('roles');
    }
}
exports.CreateRolesTable1722237486166 = CreateRolesTable1722237486166;
//# sourceMappingURL=1722237486166-CreateRolesTable.js.map