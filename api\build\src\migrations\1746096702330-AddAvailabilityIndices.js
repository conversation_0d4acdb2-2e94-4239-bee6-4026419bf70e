"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddAvailabilityIndices1746096702330 = void 0;
const typeorm_1 = require("typeorm");
class AddAvailabilityIndices1746096702330 {
    constructor() {
        this.tableName = 'clinic_availability_slots';
        this.index1Name = 'idx_availability_user_date_available_times';
        this.index2Name = 'idx_availability_date_available_user_times';
        this.index3Name = 'idx_availability_user_available_date';
    }
    async up(queryRunner) {
        // Index 1: For user/date specific lookups
        await queryRunner.createIndex(this.tableName, new typeorm_1.TableIndex({
            name: this.index1Name,
            columnNames: [
                'clinic_user_id',
                'date',
                'is_available',
                'start_time',
                'end_time'
            ]
        }));
        // Index 2: For finding available users at a specific time
        await queryRunner.createIndex(this.tableName, new typeorm_1.TableIndex({
            name: this.index2Name,
            columnNames: [
                'date',
                'is_available',
                'clinic_user_id',
                'start_time',
                'end_time'
            ]
        }));
        // Index 3: For finding available dates for a user
        await queryRunner.createIndex(this.tableName, new typeorm_1.TableIndex({
            name: this.index3Name,
            columnNames: ['clinic_user_id', 'is_available', 'date']
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropIndex(this.tableName, this.index3Name);
        await queryRunner.dropIndex(this.tableName, this.index2Name);
        await queryRunner.dropIndex(this.tableName, this.index1Name);
    }
}
exports.AddAvailabilityIndices1746096702330 = AddAvailabilityIndices1746096702330;
//# sourceMappingURL=1746096702330-AddAvailabilityIndices.js.map