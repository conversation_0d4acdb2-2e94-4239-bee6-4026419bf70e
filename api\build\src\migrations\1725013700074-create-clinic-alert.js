"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateClinicAlert1725013700074 = void 0;
const typeorm_1 = require("typeorm");
class CreateClinicAlert1725013700074 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'clinic_alerts',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'clinic_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'alert_name',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'severity',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'deleted_at',
                    type: 'timestamp',
                    isNullable: true
                }
            ]
        }));
        await queryRunner.createForeignKey('clinic_alerts', new typeorm_1.TableForeignKey({
            columnNames: ['clinic_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinics',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('clinic_alerts');
    }
}
exports.CreateClinicAlert1725013700074 = CreateClinicAlert1725013700074;
//# sourceMappingURL=1725013700074-create-clinic-alert.js.map