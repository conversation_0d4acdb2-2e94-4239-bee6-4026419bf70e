"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSignedDocumentDto = exports.UpdatePatientDocumentLibraryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const create_patient_document_library_dto_1 = require("./create-patient-document-library.dto");
const class_validator_1 = require("class-validator");
class UpdatePatientDocumentLibraryDto extends (0, swagger_1.PartialType)(create_patient_document_library_dto_1.CreatePatientDocumentLibraryDto) {
}
exports.UpdatePatientDocumentLibraryDto = UpdatePatientDocumentLibraryDto;
class UpdateSignedDocumentDto {
}
exports.UpdateSignedDocumentDto = UpdateSignedDocumentDto;
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UpdateSignedDocumentDto.prototype, "firstName", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UpdateSignedDocumentDto.prototype, "lastName", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UpdateSignedDocumentDto.prototype, "signatureSvg", void 0);
//# sourceMappingURL=update-patient-document-library.dto.js.map