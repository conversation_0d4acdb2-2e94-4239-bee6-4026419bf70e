import { ClinicFormData } from '@/app/brands/page';
import RenderFields, { fieldsType } from '@/app/molecules/RenderFields';
import { Control, UseFormReturn } from 'react-hook-form';

interface ClinicDetailProps {
    control: Control<ClinicFormData>;
    errors: { [key: string]: { message: string } };
    setValue: UseFormReturn<ClinicFormData>['setValue'];
    getValues: UseFormReturn<ClinicFormData>['getValues'];
    watch: (name: keyof ClinicFormData) => any;
    brandOptions: (
        search: string,
        loadedOptions: unknown[]
    ) => Promise<{
        options: { value: any; label: string }[];
        hasMore: boolean;
    }>;
    brandsData: any;
}

const ClinicDetails: React.FC<ClinicDetailProps> = ({
    control,
    errors,
    setValue,
    watch,
    brandOptions,
    getValues,
    brandsData,
}) => {
    const fields: fieldsType[] = [
        {
            id: 'name',
            name: 'name',
            label: 'Clinic Name',
            placeholder: 'Enter clinic name',
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            disabled: false,
            value: getValues().name,
            onChange: (event: React.ChangeEvent<HTMLInputElement>) => {
                setValue('name', event.target.value, {
                    shouldValidate: true,
                });
            },
        },
        {
            id: 'brandId',
            name: 'brandId',
            label: 'Brand Name',
            placeholder: 'Select brand',
            type: 'async-select',
            fieldSize: 'medium',
            required: true,
            disabled: false,
            value: brandsData?.data?.brands?.find(
                (brand: any) => brand.id === getValues('brandId')
            )
                ? {
                      value: getValues('brandId'),
                      label: brandsData?.data?.brands?.find(
                          (brand: any) => brand.id === getValues('brandId')
                      )?.name,
                  }
                : null,
            loadOptions: brandOptions, // Function to load options
            onChange: (selectedOption: { value: string; label: string }) => {
                if (selectedOption) {
                    // Set only the value (ID) in the form state
                    setValue('brandId', selectedOption.value, {
                        shouldValidate: true,
                    });
                }
            },
        },
        {
            id: 'adminFirstName',
            name: 'adminFirstName',
            label: 'Admin First Name',
            placeholder: 'Enter Admin First name',
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            disabled: false,
            value: getValues().adminFirstName,
            onChange: (event: React.ChangeEvent<HTMLInputElement>) => {
                setValue('adminFirstName', event.target.value, {
                    shouldValidate: true,
                });
            },
        },
        {
            id: 'adminLastName',
            name: 'adminLastName',
            label: 'Admin Last Name',
            placeholder: 'Enter admin last name',
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            disabled: false,
            value: getValues().adminLastName,
            onChange: (event: React.ChangeEvent<HTMLInputElement>) => {
                setValue('adminLastName', event.target.value, {
                    shouldValidate: true,
                });
            },
        },
        {
            id: 'adminEmail',
            name: 'adminEmail',
            label: 'Admin Email',
            placeholder: 'Enter admin email',
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            disabled: false,
            value: getValues().adminEmail,
            onChange: (event: React.ChangeEvent<HTMLInputElement>) => {
                setValue('adminEmail', event.target.value, {
                    shouldValidate: true,
                });
            },
        },
        {
            id: 'adminMobile',
            name: 'adminMobile',
            label: 'Admin Mobile Number',
            placeholder: 'Enter admin mobile',
            type: 'text-input',
            fieldSize: 'medium',
            required: true,
            disabled: false,
            value: getValues().adminMobile,
            onChange: (event: React.ChangeEvent<HTMLInputElement>) => {
                setValue('adminMobile', event.target.value, {
                    shouldValidate: true,
                });
            },
        },
    ];

    return (
        <div className="w-full">
            <RenderFields
                control={control}
                errors={errors}
                fields={fields}
                setValue={setValue}
                watch={watch}
            />
        </div>
    );
};

export default ClinicDetails;
