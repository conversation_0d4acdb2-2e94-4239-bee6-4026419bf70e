"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePaymentDetails1727067176076 = void 0;
const typeorm_1 = require("typeorm");
class CreatePaymentDetails1727067176076 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'payment_details',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'patient_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'amount',
                    type: 'decimal',
                    isNullable: false
                },
                {
                    name: 'type',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'payment_type',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'amount_payable',
                    type: 'decimal',
                    isNullable: false,
                    default: 0
                },
                {
                    name: 'main_balance',
                    type: 'decimal',
                    isNullable: false,
                    default: 0
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                },
            ]
        }), true);
        await queryRunner.createForeignKey('payment_details', new typeorm_1.TableForeignKey({
            columnNames: ['patient_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'patients',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('payment_details');
    }
}
exports.CreatePaymentDetails1727067176076 = CreatePaymentDetails1727067176076;
//# sourceMappingURL=1727067176076-CreatePaymentDetails.js.map