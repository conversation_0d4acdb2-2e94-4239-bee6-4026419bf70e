"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientAppointmentsResponseDto = exports.PreviousAppointmentDto = exports.UpcomingAppointmentDto = exports.AppointmentBaseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class AppointmentBaseDto {
}
exports.AppointmentBaseDto = AppointmentBaseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The ID of the appointment',
        example: '123e4567-e89b-12d3-a456-426614174000'
    }),
    __metadata("design:type", String)
], AppointmentBaseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The date of the appointment',
        example: '2025-02-02'
    }),
    __metadata("design:type", String)
], AppointmentBaseDto.prototype, "date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The start time of the appointment',
        example: '15:00:00'
    }),
    __metadata("design:type", String)
], AppointmentBaseDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The end time of the appointment',
        example: '16:00:00'
    }),
    __metadata("design:type", String)
], AppointmentBaseDto.prototype, "endTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The name of the patient', example: 'Leo' }),
    __metadata("design:type", String)
], AppointmentBaseDto.prototype, "patientName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The name of the doctor',
        example: 'Dr. Mark Sloan'
    }),
    __metadata("design:type", String)
], AppointmentBaseDto.prototype, "doctorName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'How the appointment was booked',
        example: 'Online',
        enum: ['Online', 'Clinic']
    }),
    __metadata("design:type", String)
], AppointmentBaseDto.prototype, "mode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The status of the appointment',
        example: 'Completed',
        enum: ['Completed', 'Missed', 'Cancelled', 'Scheduled']
    }),
    __metadata("design:type", String)
], AppointmentBaseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether the appointment can be modified or cancelled based on deadline settings',
        example: true,
        type: Boolean
    }),
    __metadata("design:type", Boolean)
], AppointmentBaseDto.prototype, "canModifyOrCancel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The time until the appointment can be modified or cancelled',
        example: '1 hour',
        required: false
    }),
    __metadata("design:type", Object)
], AppointmentBaseDto.prototype, "modificationDeadline", void 0);
class UpcomingAppointmentDto extends AppointmentBaseDto {
}
exports.UpcomingAppointmentDto = UpcomingAppointmentDto;
class PreviousAppointmentDto extends AppointmentBaseDto {
}
exports.PreviousAppointmentDto = PreviousAppointmentDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The type of visit',
        example: 'Vaccination',
        required: false
    }),
    __metadata("design:type", String)
], PreviousAppointmentDto.prototype, "visitType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The status of the appointment',
        example: 'Completed',
        enum: ['Completed', 'Missed', 'Cancelled', 'Scheduled']
    }),
    __metadata("design:type", String)
], PreviousAppointmentDto.prototype, "status", void 0);
class ClientAppointmentsResponseDto {
}
exports.ClientAppointmentsResponseDto = ClientAppointmentsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of upcoming appointments',
        type: [UpcomingAppointmentDto]
    }),
    __metadata("design:type", Array)
], ClientAppointmentsResponseDto.prototype, "upcoming", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of previous appointments',
        type: [PreviousAppointmentDto]
    }),
    __metadata("design:type", Array)
], ClientAppointmentsResponseDto.prototype, "previous", void 0);
//# sourceMappingURL=client-appointments-response.dto.js.map