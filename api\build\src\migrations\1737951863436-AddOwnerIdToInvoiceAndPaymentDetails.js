"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddOwnerIdToInvoiceAndPaymentDetails1737951863436 = void 0;
class AddOwnerIdToInvoiceAndPaymentDetails1737951863436 {
    constructor() {
        this.name = 'AddOwnerIdToInvoiceAndPaymentDetails1737951863436';
    }
    async up(queryRunner) {
        // Add owner_balance column to owners table
        await queryRunner.query(`ALTER TABLE "owners" ADD "owner_balance" numeric DEFAULT 0`);
        // Add owner_id to payment_details table
        await queryRunner.query(`ALTER TABLE "payment_details" ADD "owner_id" uuid`);
        // Add owner_id to invoice table
        await queryRunner.query(`ALTER TABLE "invoices" ADD "owner_id" uuid`);
        // First, let's identify any invoices that don't have a matching owner
        const orphanedInvoices = await queryRunner.query(`
            SELECT i.id, i.patient_id 
            FROM invoices i 
            LEFT JOIN patient_owners po ON po.patient_id = i.patient_id 
            WHERE po.owner_id IS NULL
        `);
        if (orphanedInvoices.length > 0) {
            console.log(`Found ${orphanedInvoices.length} invoices without owners`);
            // Create a default owner for orphaned records if needed
            // You may want to adjust this based on your business logic
            await queryRunner.query(`
                INSERT INTO owners (
                    id, 
                    first_name,
                    last_name, 
                    email, 
                    phone_number,
                    country_code,
                    created_at, 
                    updated_at
                )
                VALUES (
                    uuid_generate_v4(),
                    'System',
                    'Generated',
                    '<EMAIL>',
                    '**********',
                    '91',
                    NOW(),
                    NOW()
                )
                RETURNING id
            `);
            const defaultOwner = await queryRunner.query(`
                SELECT id FROM owners WHERE email = '<EMAIL>' LIMIT 1
            `);
            const defaultOwnerId = defaultOwner[0].id;
            // Create patient_owners entries for orphaned patients
            for (const invoice of orphanedInvoices) {
                // First check if a relationship already exists
                const existingRelation = await queryRunner.query(`SELECT id FROM patient_owners WHERE patient_id = $1 AND owner_id = $2`, [invoice.patient_id, defaultOwnerId]);
                if (existingRelation.length === 0) {
                    await queryRunner.query(`
                        INSERT INTO patient_owners (patient_id, owner_id, created_at, updated_at)
                        VALUES ($1, $2, NOW(), NOW())
                    `, [invoice.patient_id, defaultOwnerId]);
                }
            }
        }
        // Update owner_id in invoices from patients table
        await queryRunner.query(`
            UPDATE invoices i
            SET owner_id = (
                SELECT po.owner_id 
                FROM patient_owners po 
                WHERE po.patient_id = i.patient_id 
                LIMIT 1
            )
        `);
        // Update owner_id in payment_details from patients table
        await queryRunner.query(`
            UPDATE payment_details pd
            SET owner_id = (
                SELECT po.owner_id 
                FROM patient_owners po 
                WHERE po.patient_id = pd.patient_id 
                LIMIT 1
            )
        `);
        // Verify no null values remain before setting NOT NULL constraint
        const nullOwnerInvoices = await queryRunner.query(`
            SELECT COUNT(*) FROM invoices WHERE owner_id IS NULL
        `);
        if (parseInt(nullOwnerInvoices[0].count) > 0) {
            throw new Error(`Found ${nullOwnerInvoices[0].count} invoices with null owner_id after migration. Please check the data.`);
        }
        // Make owner_id not nullable after data migration
        await queryRunner.query(`ALTER TABLE "invoices" ALTER COLUMN "owner_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "payment_details" ALTER COLUMN "owner_id" SET NOT NULL`);
    }
    async down(queryRunner) {
        // Remove owner_id from invoice table
        await queryRunner.query(`ALTER TABLE "invoices" DROP COLUMN "owner_id"`);
        await queryRunner.query(`ALTER TABLE "payment_details" DROP COLUMN "owner_id"`);
        // Remove owner_balance from owners table
        await queryRunner.query(`ALTER TABLE "owners" DROP COLUMN "owner_balance"`);
    }
}
exports.AddOwnerIdToInvoiceAndPaymentDetails1737951863436 = AddOwnerIdToInvoiceAndPaymentDetails1737951863436;
//# sourceMappingURL=1737951863436-AddOwnerIdToInvoiceAndPaymentDetails.js.map