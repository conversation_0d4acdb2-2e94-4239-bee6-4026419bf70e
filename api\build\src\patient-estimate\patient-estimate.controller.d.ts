import { PatientEstimateService } from './patient-estimate.service';
import { CreatePatientEstimateDto } from './dto/create-patient-estimate.dto';
import { PatientEstimate } from './entities/patient-estimate.entity';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { ApiDocumentationBase } from '../base/api-documentation-base';
import { UpdateSignedDocumentDto } from '../patient-document-libraries/dto/update-patient-document-library.dto';
export declare class PatientEstimateController extends ApiDocumentationBase {
    private readonly patientEstimateService;
    private readonly logger;
    constructor(patientEstimateService: PatientEstimateService, logger: WinstonLogger);
    create(createPatientEstimateDto: CreatePatientEstimateDto): Promise<PatientEstimate>;
    findAll(): Promise<PatientEstimate[]>;
    findOne(id: string): Promise<PatientEstimate>;
    findByPatient(patientId: string, page?: number, limit?: number, search?: string): Promise<{
        data: PatientEstimate[];
        total: number;
        page: number;
        pageCount: number;
    }>;
    update(id: string, body: UpdateSignedDocumentDto): Promise<import("typeorm").UpdateResult | null>;
    remove(id: string): Promise<void>;
}
