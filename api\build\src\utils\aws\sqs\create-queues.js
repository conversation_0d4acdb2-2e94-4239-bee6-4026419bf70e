"use strict";
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const client_sqs_1 = require("@aws-sdk/client-sqs");
const sqs_queue_config_1 = require("./sqs-queue.config");
require('dotenv').config({ path: '.env' });
const env = process.env.NODE_ENV || 'development';
// dotenv.config({
// 	path: `${__dirname}/../../../../envs/.env.${env}`
// });
console.log("processEnvName", (_a = process === null || process === void 0 ? void 0 : process.env) === null || _a === void 0 ? void 0 : _a.NODE_ENV);
async function createQueues() {
    const region = process.env.AWS_SQS_REGION;
    const accessKeyId = process.env.AWS_SQS_ACCESS_KEY_ID;
    const secretAccessKey = process.env.AWS_SQS_SECRET_KEY;
    if (!region || !accessKeyId || !secretAccessKey) {
        throw new Error('Missing AWS configuration: Ensure aws.sqs.region, aws.sqs.accessKeyId, and aws.sqs.secretKey are defined in your environment variables.');
    }
    const sqsClient = new client_sqs_1.SQSClient({
        region,
        credentials: {
            accessKeyId,
            secretAccessKey
        }
    });
    const queues = (0, sqs_queue_config_1.getEnvSpecificQueues)(env);
    for (const queue of Object.values(queues)) {
        try {
            await checkOrCreateQueue(sqsClient, queue);
            console.log(`Queue checked/created: ${queue.name}`);
        }
        catch (error) {
            console.error(`Failed to process queue: ${queue.name}`, error);
        }
    }
}
async function checkOrCreateQueue(sqsClient, queue) {
    const existingQueueUrl = await checkIfQueueExists(sqsClient, queue.name);
    if (existingQueueUrl) {
        console.log(`Queue already exists: ${queue.name}`);
        return;
    }
    const createdQueueUrl = await createQueue(sqsClient, queue);
    await associateDLQ(sqsClient, queue, createdQueueUrl);
}
async function checkIfQueueExists(sqsClient, queueName) {
    const listQueuesCommand = new client_sqs_1.ListQueuesCommand({
        QueueNamePrefix: queueName
    });
    const listResponse = await sqsClient.send(listQueuesCommand);
    if (listResponse.QueueUrls && listResponse.QueueUrls.length > 0) {
        return listResponse.QueueUrls[0];
    }
    return null;
}
async function createQueue(sqsClient, queue) {
    const command = new client_sqs_1.CreateQueueCommand({
        QueueName: queue.name,
        Attributes: {
            DelaySeconds: queue.delaySeconds.toString(),
            MessageRetentionPeriod: queue.messageRetentionPeriod.toString() // 1 day
        }
    });
    const response = await sqsClient.send(command);
    return response.QueueUrl;
}
async function associateDLQ(sqsClient, queue, queueUrl) {
    const region = process.env.AWS_SQS_REGION;
    const accountId = process.env.AWS_SQS_ACCOUNT_ID;
    if (!queue.dlqName) {
        console.log(`No DLQ configured for queue: ${queue.name}`);
        return;
    }
    const setAttributesCommand = new client_sqs_1.SetQueueAttributesCommand({
        QueueUrl: queueUrl,
        Attributes: {
            RedrivePolicy: JSON.stringify({
                maxReceiveCount: 5,
                deadLetterTargetArn: `arn:aws:sqs:${region}:${accountId}:${queue.dlqName}`
            })
        }
    });
    await sqsClient.send(setAttributesCommand);
    console.log(`DLQ ${queue.dlqName} associated with queue: ${queue.name}`);
}
createQueues()
    .then(() => {
    console.log('All queues checked/created successfully');
})
    .catch(error => {
    console.error('Error creating queues:', error);
});
//# sourceMappingURL=create-queues.js.map