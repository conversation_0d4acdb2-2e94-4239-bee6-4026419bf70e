import { Winston<PERSON>og<PERSON> } from '../../utils/logger/winston-logger.service';
import { S3Service } from '../../utils/aws/s3/s3.service';
import { ConflictResolution } from '../dto/clinic-deletion.dto';
export interface FileRestoreResult {
    filesProcessed: number;
    filesRestored: number;
    filesSkipped: number;
    conflicts: {
        resolved: number;
        skipped: number;
        failed: number;
    };
    duration: number;
}
export interface FileRestoreConflict {
    originalPath: string;
    conflictType: 'file_exists' | 'path_conflict';
    existingFileSize?: number;
    backupFileSize: number;
    details: string;
}
export declare class FileRestoreService {
    private readonly logger;
    private readonly s3Service;
    constructor(logger: WinstonLogger, s3Service: S3Service);
    /**
     * Restore S3 files from backup
     */
    restoreS3Files(backupBasePath: string, conflictResolution?: ConflictResolution): Promise<FileRestoreResult>;
    /**
     * Analyze file restore conflicts before actual restore
     */
    analyzeFileRestoreConflicts(backupBasePath: string): Promise<FileRestoreConflict[]>;
    /**
     * Load file manifest from S3
     */
    private loadFileManifest;
    /**
     * Restore a single file
     */
    private restoreSingleFile;
    /**
     * Analyze conflict for a single file
     */
    private analyzeFileConflict;
}
