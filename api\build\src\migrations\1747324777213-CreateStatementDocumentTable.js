"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateStatementDocumentTable1747324777213 = void 0;
const typeorm_1 = require("typeorm");
class CreateStatementDocumentTable1747324777213 {
    constructor() {
        this.name = 'CreateStatementDocumentTable1747324777213';
    }
    async up(queryRunner) {
        // Create enum types for status and action
        await queryRunner.query(`CREATE TYPE "public"."statement_documents_status_enum" AS ENUM('PROCESSING', 'COMPLETED', 'PARTIAL_COMPLETION', 'ERROR')`);
        await queryRunner.query(`CREATE TYPE "public"."statement_documents_action_enum" AS ENUM('DOWNLOAD', 'SHARE')`);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'statement_documents',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    isGenerated: true,
                    generationStrategy: 'uuid'
                },
                {
                    name: 'requestId',
                    type: 'varchar',
                    isUnique: true,
                    isNullable: false
                },
                {
                    name: 'owner_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'clinic_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'brand_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'requested_by_user_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'statement_types',
                    type: 'jsonb', // For storing array of strings
                    isNullable: false
                },
                {
                    name: 'status',
                    type: 'enum',
                    enumName: 'statement_documents_status_enum',
                    default: "'PROCESSING'"
                },
                {
                    name: 'file_keys',
                    type: 'jsonb', // For storing an object
                    isNullable: true
                },
                {
                    name: 'action',
                    type: 'enum',
                    enumName: 'statement_documents_action_enum',
                    isNullable: false
                },
                {
                    name: 'share_details',
                    type: 'jsonb',
                    isNullable: true
                },
                {
                    name: 'error_message',
                    type: 'text',
                    isNullable: true
                },
                {
                    name: 'retry_count',
                    type: 'int',
                    default: 0
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'expires_at',
                    type: 'timestamp',
                    isNullable: true
                }
            ]
        }), true // Create dependencies like unique constraints
        );
        // Create index for requestId as specified by @Index decorator
        await queryRunner.createIndex('statement_documents', new typeorm_1.TableIndex({
            name: 'IDX_statement_doc_requestId', // Explicit index name
            columnNames: ['requestId']
        }));
    }
    async down(queryRunner) {
        // Drop the index first
        await queryRunner.dropIndex('statement_documents', 'IDX_statement_doc_requestId');
        // Drop the table
        await queryRunner.dropTable('statement_documents');
        // Drop the enum types
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."statement_documents_status_enum"`);
        await queryRunner.query(`DROP TYPE IF EXISTS "public"."statement_documents_action_enum"`);
    }
}
exports.CreateStatementDocumentTable1747324777213 = CreateStatementDocumentTable1747324777213;
//# sourceMappingURL=1747324777213-CreateStatementDocumentTable.js.map