"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.clinicRoomsSeeder = void 0;
const data_source_1 = require("../database/data-source");
const clinic_room_entity_1 = require("../clinics/entities/clinic-room.entity");
const uuid_1 = require("uuid");
const clinicRoomsSeeder = async () => {
    const clinicRoomsRepository = data_source_1.dataSource.getRepository(clinic_room_entity_1.ClinicRoomEntity);
    const clinicRooms = [
        {
            id: (0, uuid_1.v4)(),
            name: 'Room 1',
            description: 'This is the discussion room for Room 1'
        },
        {
            id: (0, uuid_1.v4)(),
            name: 'Room 2',
            description: 'This is the discussion room for Room 2'
        },
        {
            id: (0, uuid_1.v4)(),
            name: 'Room 3',
            description: 'This is the discussion room for Room 3'
        },
        {
            id: (0, uuid_1.v4)(),
            name: 'Room 4',
            description: 'This is the discussion room for Room 4'
        },
        {
            id: (0, uuid_1.v4)(),
            name: 'Room 5',
            description: 'This is the discussion room for Room 5'
        }
    ];
    await clinicRoomsRepository.save(clinicRooms);
};
exports.clinicRoomsSeeder = clinicRoomsSeeder;
(0, exports.clinicRoomsSeeder)().catch(error => {
    console.error('Error executing clinic rooms seed script:', error);
});
//# sourceMappingURL=clinic_rooms.seeder.js.map