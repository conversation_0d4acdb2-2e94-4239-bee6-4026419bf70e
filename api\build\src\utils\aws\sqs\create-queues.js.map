{"version": 3, "file": "create-queues.js", "sourceRoot": "", "sources": ["../../../../../src/utils/aws/sqs/create-queues.ts"], "names": [], "mappings": ";;;AACA,oDAK6B;AAC7B,yDAA0D;AAG1D,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;AAC3C,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa,CAAC;AAClD,kBAAkB;AAClB,qDAAqD;AACrD,MAAM;AAEN,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,0CAAE,QAAQ,CAAC,CAAA;AAErD,KAAK,UAAU,YAAY;IAC1B,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;IAC1C,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC;IACtD,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;IAEvD,IAAI,CAAC,MAAM,IAAI,CAAC,WAAW,IAAI,CAAC,eAAe,EAAE,CAAC;QACjD,MAAM,IAAI,KAAK,CACd,yIAAyI,CACzI,CAAC;IACH,CAAC;IAED,MAAM,SAAS,GAAG,IAAI,sBAAS,CAAC;QAC/B,MAAM;QACN,WAAW,EAAE;YACZ,WAAW;YACX,eAAe;SACf;KACD,CAAC,CAAC;IAEH,MAAM,MAAM,GAAG,IAAA,uCAAoB,EAAC,GAAG,CAAC,CAAC;IAEzC,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QAC3C,IAAI,CAAC;YACJ,MAAM,kBAAkB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAC3C,OAAO,CAAC,GAAG,CAAC,0BAA0B,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,4BAA4B,KAAK,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC;QAChE,CAAC;IACF,CAAC;AACF,CAAC;AAED,KAAK,UAAU,kBAAkB,CAChC,SAAoB,EACpB,KAAkB;IAElB,MAAM,gBAAgB,GAAG,MAAM,kBAAkB,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;IAEzE,IAAI,gBAAgB,EAAE,CAAC;QACtB,OAAO,CAAC,GAAG,CAAC,yBAAyB,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QACnD,OAAO;IACR,CAAC;IAED,MAAM,eAAe,GAAG,MAAM,WAAW,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAC5D,MAAM,YAAY,CAAC,SAAS,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;AACvD,CAAC;AAED,KAAK,UAAU,kBAAkB,CAChC,SAAoB,EACpB,SAAiB;IAEjB,MAAM,iBAAiB,GAAG,IAAI,8BAAiB,CAAC;QAC/C,eAAe,EAAE,SAAS;KAC1B,CAAC,CAAC;IACH,MAAM,YAAY,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAE7D,IAAI,YAAY,CAAC,SAAS,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACjE,OAAO,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC;IACD,OAAO,IAAI,CAAC;AACb,CAAC;AAED,KAAK,UAAU,WAAW,CACzB,SAAoB,EACpB,KAAkB;IAElB,MAAM,OAAO,GAAG,IAAI,+BAAkB,CAAC;QACtC,SAAS,EAAE,KAAK,CAAC,IAAI;QACrB,UAAU,EAAE;YACX,YAAY,EAAE,KAAK,CAAC,YAAY,CAAC,QAAQ,EAAE;YAC3C,sBAAsB,EAAE,KAAK,CAAC,sBAAsB,CAAC,QAAQ,EAAE,CAAC,QAAQ;SACxE;KACD,CAAC,CAAC;IAEH,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC/C,OAAO,QAAQ,CAAC,QAAS,CAAC;AAC3B,CAAC;AAED,KAAK,UAAU,YAAY,CAC1B,SAAoB,EACpB,KAAkB,EAClB,QAAgB;IAEhB,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;IAC1C,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC;IACjD,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACpB,OAAO,CAAC,GAAG,CAAC,gCAAgC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;QAC1D,OAAO;IACR,CAAC;IAED,MAAM,oBAAoB,GAAG,IAAI,sCAAyB,CAAC;QAC1D,QAAQ,EAAE,QAAQ;QAClB,UAAU,EAAE;YACX,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC;gBAC7B,eAAe,EAAE,CAAC;gBAClB,mBAAmB,EAAE,eAAe,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,OAAO,EAAE;aAC1E,CAAC;SACF;KACD,CAAC,CAAC;IAEH,MAAM,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,CAAC,OAAO,2BAA2B,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;AAC1E,CAAC;AAED,YAAY,EAAE;KACZ,IAAI,CAAC,GAAG,EAAE;IACV,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;AACxD,CAAC,CAAC;KACD,KAAK,CAAC,KAAK,CAAC,EAAE;IACd,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;AAChD,CAAC,CAAC,CAAC"}