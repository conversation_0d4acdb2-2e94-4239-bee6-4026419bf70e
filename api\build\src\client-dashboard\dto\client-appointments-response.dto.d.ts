import { TimeDuration } from '../../clinics/entities/clinic.entity';
export declare class AppointmentBaseDto {
    id: string;
    date: string;
    startTime: string;
    endTime: string;
    patientName: string;
    doctorName: string;
    mode: 'Online' | 'Clinic';
    status: 'Completed' | 'Missed' | 'Cancelled' | 'Scheduled';
    canModifyOrCancel: boolean;
    modificationDeadline?: TimeDuration | null;
}
export declare class UpcomingAppointmentDto extends AppointmentBaseDto {
}
export declare class PreviousAppointmentDto extends AppointmentBaseDto {
    visitType?: string;
    status: 'Completed' | 'Missed' | 'Cancelled' | 'Scheduled';
}
export declare class ClientAppointmentsResponseDto {
    upcoming: UpcomingAppointmentDto[];
    previous: PreviousAppointmentDto[];
}
