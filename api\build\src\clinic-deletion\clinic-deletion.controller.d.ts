import { <PERSON><PERSON>og<PERSON> } from '../utils/logger/winston-logger.service';
import { ClinicDeletionService } from './clinic-deletion.service';
import { DeletionImpactRequestDto, DeletionRequestDto, DeletionImpactResponse, DeletionExecutionResponse, BackupListRequestDto, BackupListResponse, BackupMetadata, RestoreRequestDto, RestoreImpactResponse, RestoreExecutionResponse } from './dto/clinic-deletion.dto';
export declare class ClinicDeletionController {
    private readonly clinicDeletionService;
    private readonly logger;
    constructor(clinicDeletionService: ClinicDeletionService, logger: WinstonLogger);
    analyzeImpact(dto: DeletionImpactRequestDto, req: {
        user: {
            id: string;
            email: string;
            brandId: string;
            role: string;
        };
    }): Promise<DeletionImpactResponse>;
    executeDeletion(dto: DeletionRequestDto, req: {
        user: {
            id: string;
            email: string;
            brandId: string;
            role: string;
        };
    }): Promise<DeletionExecutionResponse>;
    /**
     * List backups
     */
    listBackups(query: BackupListRequestDto, req: any): Promise<BackupListResponse>;
    /**
     * Get backup details
     */
    getBackupDetails(backupId: string, req: any): Promise<BackupMetadata>;
    /**
     * Delete backup
     */
    deleteBackup(backupId: string, req: any): Promise<{
        success: boolean;
        message: string;
    }>;
    /**
     * Analyze restore impact
     */
    analyzeRestoreImpact(dto: RestoreRequestDto, req: any): Promise<RestoreImpactResponse>;
    /**
     * Execute restore
     */
    executeRestore(dto: RestoreRequestDto, req: any): Promise<RestoreExecutionResponse>;
    /**
     * Validates the deletion request parameters
     */
    private validateDeletionRequest;
    /**
     * Validates that the user has access to the brand they're trying to delete
     */
    private validateBrandAccess;
}
