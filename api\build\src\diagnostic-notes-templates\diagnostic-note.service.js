"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiagnosticTemplatesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const diagnostic_template_entity_1 = require("./entities/diagnostic-template.entity");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const lab_report_entity_1 = require("../clinic-lab-report/entities/lab-report.entity");
const clinic_lab_report_entity_1 = require("../clinic-lab-report/entities/clinic-lab-report.entity");
const diagnostic_note_entity_1 = require("./entities/diagnostic-note.entity");
const typeorm_3 = require("typeorm");
const patients_service_1 = require("../patients/patients.service");
const moment = require("moment");
const generatePdf_1 = require("../utils/generatePdf");
const uuidv7_1 = require("uuidv7");
const diagnosticNote_1 = require("../utils/pdfs/diagnosticNote");
const diagnosticTable_1 = require("../utils/pdfs/diagnosticTable");
const s3_service_1 = require("../utils/aws/s3/s3.service");
const generate_alpha_numeric_code_1 = require("../utils/common/generate_alpha-numeric_code");
const appointment_entity_1 = require("../appointments/entities/appointment.entity");
const appointment_details_entity_1 = require("../appointments/entities/appointment-details.entity");
const socket_appointment_gateway_1 = require("../socket/socket.appointment.gateway");
let DiagnosticTemplatesService = class DiagnosticTemplatesService {
    constructor(templateRepository, diagnosticNoteRepository, labReportRepository, clinicLabReportRepository, appointmentRepository, appointmentDetailsRepository, logger, patientService, s3Service, dataSource, appointmentGateway) {
        this.templateRepository = templateRepository;
        this.diagnosticNoteRepository = diagnosticNoteRepository;
        this.labReportRepository = labReportRepository;
        this.clinicLabReportRepository = clinicLabReportRepository;
        this.appointmentRepository = appointmentRepository;
        this.appointmentDetailsRepository = appointmentDetailsRepository;
        this.logger = logger;
        this.patientService = patientService;
        this.s3Service = s3Service;
        this.dataSource = dataSource;
        this.appointmentGateway = appointmentGateway;
    }
    async create(createDto, userId) {
        try {
            // Check for existing template with same name in clinic
            const existing = await this.templateRepository.findOne({
                where: {
                    clinicId: createDto.clinicId,
                    templateName: createDto.templateName
                }
            });
            if (existing) {
                throw new common_1.ConflictException('Template with this name already exists');
            }
            const template = this.templateRepository.create({
                ...createDto,
                createdBy: userId,
                updatedBy: userId
            });
            const savedTemplate = await this.templateRepository.save(template);
            this.logger.log('Template created successfully', {
                templateId: savedTemplate.id
            });
            return savedTemplate;
        }
        catch (error) {
            this.logger.error('Error creating template', {
                error,
                dto: createDto
            });
            throw error;
        }
    }
    async findAll(clinicId) {
        try {
            return this.templateRepository.find({
                where: { clinicId },
                order: { createdAt: 'DESC' }
            });
        }
        catch (error) {
            this.logger.error('Error fetching templates', { error, clinicId });
            throw error;
        }
    }
    async findOne(id, clinicId) {
        const template = await this.templateRepository.findOne({
            where: { id, clinicId }
        });
        if (!template) {
            throw new common_1.NotFoundException('Template not found');
        }
        return template;
    }
    async update(id, updateDto, userId, clinicId) {
        const template = await this.findOne(id, clinicId);
        // Check name uniqueness if name is being updated
        if (updateDto.templateName &&
            updateDto.templateName !== template.templateName) {
            const existing = await this.templateRepository.findOne({
                where: {
                    clinicId: updateDto.clinicId,
                    templateName: updateDto.templateName,
                    id: (0, typeorm_2.Not)(id)
                }
            });
            if (existing) {
                throw new common_1.ConflictException('Template with this name already exists');
            }
        }
        Object.assign(template, {
            ...updateDto,
            updatedBy: userId
        });
        return this.templateRepository.save(template);
    }
    async remove(id, clinicId) {
        // Start a transaction
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            // Find the template
            const template = await this.templateRepository.findOne({
                where: { id, clinicId }
            });
            if (!template) {
                throw new common_1.NotFoundException('Template not found');
            }
            // Find all associated notes
            const associatedNotes = await this.diagnosticNoteRepository.find({
                where: { templateId: id }
            });
            // Delete all associated notes
            if (associatedNotes.length > 0) {
                await queryRunner.manager.remove(diagnostic_note_entity_1.DiagnosticNote, associatedNotes);
                this.logger.log(`Deleted ${associatedNotes.length} associated notes for template ${id}`);
            }
            // Delete the template
            await queryRunner.manager.remove(diagnostic_template_entity_1.DiagnosticTemplate, template);
            // Commit the transaction
            await queryRunner.commitTransaction();
            this.logger.log('Template and associated notes deleted successfully', { templateId: id });
        }
        catch (error) {
            // Rollback transaction on error
            await queryRunner.rollbackTransaction();
            this.logger.error('Error deleting template and notes', {
                error,
                templateId: id
            });
            throw error;
        }
        finally {
            // Release the query runner
            await queryRunner.release();
        }
    }
    // async createNote(createNoteDto: CreateDiagnosticNoteDto, userId: string) {
    //     try {
    //         const { labReportId, clinicId, templateId, templateName,noteData } = createNoteDto;
    //         console.log("userId",userId)
    //         // Find lab report and verify clinic access
    //         const labReport = await this.labReportRepository.findOne({
    //           where: { id: labReportId, clinicId },
    //           relations: ['appointment']
    //         });
    //         if (!labReport) {
    //         throw new NotFoundException('Lab report not found');
    //         }
    //         let template;
    //         if (templateId) {
    //         template = await this.templateRepository.findOne({
    //             where: { id: templateId, clinicId }
    //         });
    //         if (!template) {
    //             throw new NotFoundException('Template not found');
    //         }
    //         }
    //         const note = this.diagnosticNoteRepository.create({
    //         labReportId,
    //         clinicId,
    //         patientId: labReport.patientId,
    //         appointmentId: labReport.appointmentId,
    //         templateId,
    //         templateName,
    //         noteData,
    //         createdBy: userId,
    //         updatedBy: userId
    //         });
    //         return this.diagnosticNoteRepository.save(note);
    //     } catch (error) {
    //         this.logger.error('Error fetching templates', { error });
    //         console.log(error)
    //         throw error;
    //     }
    //   }
    async createNote(createNoteDto, userId) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const { labReportId, diagnosticId, clinicId, templateId, templateName, noteData } = createNoteDto;
            const labReport = await this.labReportRepository.findOne({
                where: { id: labReportId, clinicId },
                relations: ['clinicLabReport']
            });
            if (!labReport) {
                throw new common_1.NotFoundException('Lab report not found');
            }
            const diagnosticNumber = await (0, generate_alpha_numeric_code_1.generateUniqueCode)('diagnosticNumber', this.diagnosticNoteRepository);
            // Get complete data for PDF generation
            const completeNoteData = await this.getCompleteNoteData(labReportId, clinicId, templateName, noteData, labReport.patientId, diagnosticNumber, labReport.clinicLabReport.name, labReport.createdAt);
            // Generate and upload PDF
            const pdfFileKey = await this.generateAndUploadPDF(completeNoteData, noteData.values ? 'table' : 'notes');
            // Create note entity
            const note = this.diagnosticNoteRepository.create({
                labReportId,
                clinicLabReportId: diagnosticId,
                appointmentId: labReport.appointmentId,
                clinicId,
                patientId: labReport.patientId,
                templateId,
                templateName,
                noteData,
                fileKey: pdfFileKey,
                fileName: `${templateName}_notes.pdf`,
                createdBy: userId,
                updatedBy: userId,
                diagnosticNumber
            });
            const savedNote = await queryRunner.manager.save(diagnostic_note_entity_1.DiagnosticNote, note);
            await queryRunner.commitTransaction();
            // Update appointment details and broadcast the diagnostic note creation
            if (labReport.appointmentId) {
                await this.updateAppointmentDetailsAndBroadcastDiagnosticNotes(labReport.appointmentId, labReportId);
            }
            return savedNote;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            console.log('Error creating diagnostic note', { error });
            this.logger.error('Error creating diagnostic note', { error });
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    //   async updateNote1(id: string, updateNoteDto: UpdateDiagnosticNoteDto, userId: string) {
    //     const note = await this.diagnosticNoteRepository.findOne({
    //         where: { id },
    //         relations: ['template']
    //     });
    //     if (!note) {
    //         throw new NotFoundException('Diagnostic note not found');
    //     }
    //     // Check if template exists and is active if template is being changed
    //     if (updateNoteDto.templateId && updateNoteDto.templateId !== note.templateId) {
    //         const template = await this.templateRepository.findOne({
    //             where: {
    //                 id: updateNoteDto.templateId,
    //                 isActive: true
    //             }
    //         });
    //         if (!template) {
    //             throw new NotFoundException('Template not found or inactive');
    //         }
    //         note.templateId = template.id;
    //         // Fix for templateName undefined error
    //         if (updateNoteDto.templateName) {
    //             note.templateName = updateNoteDto.templateName;
    //         }
    //     }
    //     // Update note data
    //     if (updateNoteDto.noteData) {
    //         if (updateNoteDto.templateType === 'notes') {
    //             note.noteData = {
    //                 notes: updateNoteDto.noteData.notes || '',
    //                 values: {} // Initialize empty values for notes type
    //             };
    //         } else if (updateNoteDto.templateType === 'table') {
    //             note.noteData = {
    //                 notes: '',
    //                 values: updateNoteDto.noteData.values || {} // Initialize empty object if undefined
    //             };
    //         }
    //     }
    //     // Update other fields
    //     note.updatedBy = userId;
    //     note.version = (note.version || 0) + 1;
    //     note.updatedAt = new Date();
    //     try {
    //         const updatedNote = await this.diagnosticNoteRepository.save(note);
    //         this.logger.log('Note updated successfully', { noteId: id });
    //         return updatedNote;
    //     } catch (error) {
    //         this.logger.error('Error updating note', { error, noteId: id });
    //         throw new InternalServerErrorException('Failed to update note');
    //     }
    // }
    async getTemplatesForLabReport(labReportId, clinicId) {
        const labReport = await this.clinicLabReportRepository.findOne({
            where: { id: labReportId, clinicId }
        });
        if (!labReport) {
            throw new common_1.NotFoundException('Lab report not found');
        }
        const templates = await this.templateRepository.find({
            where: {
                clinicId,
                isActive: true,
                assignedDiagnostics: {
                    id: labReport.id
                }
            }
        });
        return templates;
    }
    async updateNote(id, updateNoteDto, userId) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            // Find existing note
            const note = await this.diagnosticNoteRepository.findOne({
                where: { id },
                relations: ['template']
            });
            if (!note) {
                throw new common_1.NotFoundException('Diagnostic note not found');
            }
            // Check template if being changed
            if (updateNoteDto.templateId &&
                updateNoteDto.templateId !== note.templateId) {
                const template = await this.templateRepository.findOne({
                    where: {
                        id: updateNoteDto.templateId,
                        isActive: true
                    }
                });
                if (!template) {
                    throw new common_1.NotFoundException('Template not found or inactive');
                }
                note.templateId = template.id;
                if (updateNoteDto.templateName) {
                    note.templateName = updateNoteDto.templateName;
                }
            }
            // Update note data and generate new PDF if content changed
            if (updateNoteDto.noteData) {
                // Update note data based on template type
                if (updateNoteDto.templateType === 'notes') {
                    note.noteData = {
                        notes: updateNoteDto.noteData.notes || '',
                        values: {}
                    };
                }
                else if (updateNoteDto.templateType === 'table') {
                    note.noteData = {
                        notes: '',
                        values: updateNoteDto.noteData.values || {}
                    };
                }
                // Get complete data for PDF generation
                const completeNoteData = await this.getCompleteNoteData(note.labReportId, note.clinicId, updateNoteDto.templateName || note.templateName, note.noteData, note.patientId, note.diagnosticNumber);
                // Generate and upload new PDF
                const newPdfFileKey = await this.generateAndUploadPDF(completeNoteData, updateNoteDto.templateType === 'table' ? 'table' : 'notes');
                // Delete old PDF if exists
                if (note.fileKey) {
                    try {
                        await this.s3Service.deleteFile(note.fileKey);
                    }
                    catch (error) {
                        this.logger.warn('Failed to delete old PDF file', {
                            fileKey: note.fileKey,
                            error
                        });
                    }
                }
                // Update file information
                note.fileKey = newPdfFileKey;
                note.fileName = `${note.templateName}_notes.pdf`;
            }
            // Update metadata
            note.updatedBy = userId;
            note.version = (note.version || 0) + 1;
            note.updatedAt = new Date();
            // Save updated note
            const updatedNote = await queryRunner.manager.save(diagnostic_note_entity_1.DiagnosticNote, note);
            await queryRunner.commitTransaction();
            // Update appointment details and broadcast the diagnostic note update
            if (note.appointmentId) {
                await this.updateAppointmentDetailsAndBroadcastDiagnosticNotes(note.appointmentId, note.labReportId);
            }
            this.logger.log('Note updated successfully', {
                noteId: id,
                version: note.version
            });
            return updatedNote;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error('Error updating diagnostic note', {
                error,
                noteId: id,
                userId
            });
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException(`Failed to update diagnostic note`);
        }
        finally {
            await queryRunner.release();
        }
    }
    async findTemplatesByDiagnostic(clinicLabReportId, clinicId) {
        try {
            // Using TypeORM's query builder to match against JSONB array
            const templates = await this.templateRepository
                .createQueryBuilder('template')
                .where('template.clinicId = :clinicId', { clinicId })
                .andWhere('template.isActive = :isActive', { isActive: true })
                .andWhere('template.assignedDiagnostics @> :diagnosticId::jsonb', {
                diagnosticId: JSON.stringify([
                    { id: clinicLabReportId }
                ])
            })
                .getMany();
            this.logger.log('Found templates for diagnostic', {
                clinicLabReportId,
                clinicId,
                count: templates.length
            });
            return {
                status: true,
                data: templates
            };
        }
        catch (error) {
            this.logger.error('Error finding templates', {
                error,
                clinicLabReportId,
                clinicId
            });
            throw error;
        }
    }
    async getNotesByLabReport(labReportId, clinicId) {
        const notes = await this.diagnosticNoteRepository.find({
            where: { labReportId, clinicId },
            relations: ['template', 'creator'],
            order: { createdAt: 'DESC' }
        });
        return notes;
    }
    async getPatientNotes(patientId) {
        const note = await this.diagnosticNoteRepository.find({
            where: { patientId }
        });
        if (!note) {
            throw new common_1.NotFoundException('Diagnostic notes were not found');
        }
        return note;
    }
    async getNote(noteId) {
        const note = await this.diagnosticNoteRepository.find({
            where: { id: noteId }
        });
        if (!note) {
            throw new common_1.NotFoundException('Diagnostic note not found');
        }
        return note;
    }
    async deleteNote(id) {
        const note = await this.diagnosticNoteRepository.findOne({
            where: { id }
        });
        if (!note) {
            throw new common_1.NotFoundException('Diagnostic note not found');
        }
        // Store appointment and lab report info before deletion for broadcasting
        const appointmentId = note.appointmentId;
        const labReportId = note.labReportId;
        await this.diagnosticNoteRepository.remove(note);
        // Update appointment details and broadcast the diagnostic note deletion
        if (appointmentId && labReportId) {
            await this.updateAppointmentDetailsAndBroadcastDiagnosticNotes(appointmentId, labReportId);
        }
        return { success: true };
    }
    /**
     * Helper method to update appointment details with diagnostic notes and broadcast changes via socket
     * Uses selective updates to only modify the specific lab report that had diagnostic notes changed.
     */
    async updateAppointmentDetailsAndBroadcastDiagnosticNotes(appointmentId, labReportId) {
        var _a, _b, _c;
        this.logger.log(`Updating appointment details and broadcasting diagnostic notes change for appointment ${appointmentId}`, { appointmentId, labReportId });
        try {
            // Fetch the appointment with appointment details relation
            const appointment = await this.appointmentRepository.findOne({
                where: { id: appointmentId },
                relations: ['appointmentDetails']
            });
            if (!appointment) {
                this.logger.warn(`Appointment ${appointmentId} not found for diagnostic notes update`, { appointmentId });
                return;
            }
            // Get current appointment details or initialize if not exists
            const currentDetails = ((_a = appointment.appointmentDetails) === null || _a === void 0 ? void 0 : _a.details) || {};
            const currentObjective = currentDetails.objective || {};
            const currentLabReports = currentObjective.labReports || [];
            // Find the specific lab report that needs updating
            const labReportIndex = currentLabReports.findIndex((report) => report.labReportId === labReportId);
            if (labReportIndex === -1) {
                this.logger.warn(`Lab report ${labReportId} not found in appointment ${appointmentId} details`, { appointmentId, labReportId });
                return;
            }
            // Fetch updated diagnostic notes for only the specific lab report
            const updatedDiagnosticNotes = await this.diagnosticNoteRepository.find({
                where: {
                    labReportId: labReportId,
                    appointmentId: appointmentId
                },
                order: { createdAt: 'DESC' }
            });
            // Create updated lab reports array with selective update
            const updatedLabReports = [...currentLabReports];
            const existingLabReport = updatedLabReports[labReportIndex];
            // Only update the diagnostic notes field, preserve all other fields
            updatedLabReports[labReportIndex] = {
                ...existingLabReport,
                diagnosticNotes: updatedDiagnosticNotes || []
            };
            // Update appointment details in the database - preserve other objective fields
            const updatedDetails = {
                ...currentDetails,
                objective: {
                    ...currentObjective,
                    labReports: updatedLabReports
                }
            };
            // Update or create the appointment details entity
            if (appointment.appointmentDetails) {
                // Update existing details - save the appointmentDetails entity directly
                appointment.appointmentDetails.details = updatedDetails;
                const savedDetails = await this.appointmentDetailsRepository.save(appointment.appointmentDetails);
                this.logger.log(`Updated existing appointment details with diagnostic notes for appointment ${appointmentId}. Details ID: ${savedDetails.id}`, {
                    appointmentId,
                    detailsId: savedDetails.id,
                    updatedLabReportId: labReportId,
                    diagnosticNotesCount: updatedDiagnosticNotes.length
                });
                // Verify the save by fetching the data back
                const verifyDetails = await this.appointmentDetailsRepository.findOne({
                    where: { id: savedDetails.id }
                });
                const verifyObjective = ((_b = verifyDetails === null || verifyDetails === void 0 ? void 0 : verifyDetails.details) === null || _b === void 0 ? void 0 : _b.objective) || {};
                const verifyLabReports = verifyObjective.labReports || [];
                const verifyTargetReport = verifyLabReports.find((r) => r.labReportId === labReportId);
                this.logger.log(`Verification: Fetched appointment details after diagnostic notes save`, {
                    appointmentId,
                    detailsId: savedDetails.id,
                    verifyLabReportsCount: verifyLabReports.length,
                    targetReportDiagnosticNotesCount: ((_c = verifyTargetReport === null || verifyTargetReport === void 0 ? void 0 : verifyTargetReport.diagnosticNotes) === null || _c === void 0 ? void 0 : _c.length) || 0
                });
            }
            else {
                // Create new details if none exist
                const newAppointmentDetails = this.appointmentDetailsRepository.create({
                    appointmentId,
                    details: updatedDetails
                });
                await this.appointmentDetailsRepository.save(newAppointmentDetails);
                this.logger.log(`Created new appointment details with diagnostic notes for appointment ${appointmentId}`, {
                    appointmentId,
                    labReportId,
                    diagnosticNotesCount: updatedDiagnosticNotes.length
                });
            }
            this.logger.log(`Updated appointment details with diagnostic notes for appointment ${appointmentId}`, {
                appointmentId,
                labReportId,
                totalLabReportsCount: updatedLabReports.length,
                updatedDiagnosticNotesCount: updatedDiagnosticNotes.length,
                updateType: 'selective_diagnostic_notes_update'
            });
            // Broadcast the updated state via socket
            const broadcastPayload = {
                appointmentId: appointmentId,
                key: 'objective.labReports',
                value: updatedLabReports
            };
            this.logger.log(`Broadcasting diagnostic notes change via socket for appointment ${appointmentId}`, { appointmentId, labReportId });
            await this.appointmentGateway.publishAppointmentUpdate(appointmentId, broadcastPayload);
            this.logger.log(`Successfully updated appointment details and broadcasted diagnostic notes change for appointment ${appointmentId}`);
        }
        catch (error) {
            this.logger.error('Error updating appointment details and broadcasting diagnostic notes change', {
                appointmentId,
                labReportId,
                error: error instanceof Error ? error.message : String(error),
                stack: error instanceof Error ? error.stack : undefined
            });
            // Don't throw error to avoid breaking the main operation
        }
    }
    async generateAndUploadPDF(noteData, templateType) {
        try {
            const noteHTML = templateType === 'table'
                ? (0, diagnosticTable_1.generateDiagnosticReportTable)(noteData)
                : (0, diagnosticNote_1.generateDiagnosticReportNote)(noteData);
            const pdfBuffer = await (0, generatePdf_1.generatePDF)(noteHTML);
            const fileKey = `diagnostic-notes/${(0, uuidv7_1.uuidv4)()}.pdf`;
            await this.s3Service.uploadPdfToS3(pdfBuffer, fileKey);
            return fileKey;
        }
        catch (error) {
            this.logger.error('Error generating/uploading PDF', { error });
            throw new common_1.InternalServerErrorException('Failed to generate PDF');
        }
    }
    getClinicAddress(patientDetail) {
        var _a, _b;
        const addressParts = [];
        if ((_a = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _a === void 0 ? void 0 : _a.addressLine1) {
            addressParts.push(patientDetail.clinic.addressLine1);
        }
        // if (patientDetail?.clinic?.city) {
        // 	addressParts.push(patientDetail.clinic.city);
        // }
        if ((_b = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.clinic) === null || _b === void 0 ? void 0 : _b.addressPincode) {
            addressParts.push(`- ${patientDetail.clinic.addressPincode}`);
        }
        // if (patientDetail?.clinic?.state) {
        // 	addressParts.push(patientDetail.clinic.state);
        // }
        this.logger.log('Clinic Address formatted', {
            addressParts
        });
        return addressParts.join(', ').trim();
    }
    async getCompleteNoteData(labReportId, clinicId, templateName, noteData, patientId, diagnosticNumber, diagnosticName, createdAt) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z;
        // Get patient details with all necessary relations
        const patientDetails = await this.patientService.getPatientDetails(patientId);
        const clinic = patientDetails.clinic;
        // Format data for PDF generation
        return {
            diagnosticName: diagnosticName || '',
            diagnosticNumber: diagnosticNumber,
            diagnosticDate: moment(createdAt).format('DD MMM YYYY'),
            // Clinic Information
            clinicName: ((_a = patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.clinic) === null || _a === void 0 ? void 0 : _a.name) || '',
            clinicAddress: this.getClinicAddress(patientDetails),
            clinicCity: (_b = patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.clinic) === null || _b === void 0 ? void 0 : _b.city,
            clinicPhone: ((_c = patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.clinic) === null || _c === void 0 ? void 0 : _c.phoneNumbers[0].number) || '',
            clinicEmail: ((_d = patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.clinic) === null || _d === void 0 ? void 0 : _d.email) || '',
            clinicWebsite: ((_e = patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.clinic) === null || _e === void 0 ? void 0 : _e.website) || '',
            clinicLogoUrl: (clinic === null || clinic === void 0 ? void 0 : clinic.logoUrl) || '',
            // Patient Information
            petName: (patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.patientName) || '',
            petBreed: ((_f = patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.breed) === null || _f === void 0 ? void 0 : _f.split('_').join(' ').toLowerCase().replace(/\b\w/g, char => char.toUpperCase())) || '',
            petSpecies: (patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.species) || '',
            petAge: (patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.age) || '',
            petGender: (patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.gender) || '',
            // Owner Information
            customerName: `${((_j = (_h = (_g = patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.patientOwners) === null || _g === void 0 ? void 0 : _g[0]) === null || _h === void 0 ? void 0 : _h.ownerBrand) === null || _j === void 0 ? void 0 : _j.firstName) || ''} ${((_m = (_l = (_k = patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.patientOwners) === null || _k === void 0 ? void 0 : _k[0]) === null || _l === void 0 ? void 0 : _l.ownerBrand) === null || _m === void 0 ? void 0 : _m.lastName) || ''}`,
            ownerName: ((_q = (_p = (_o = patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.patientOwners) === null || _o === void 0 ? void 0 : _o[0]) === null || _p === void 0 ? void 0 : _p.ownerBrand) === null || _q === void 0 ? void 0 : _q.firstName) || '',
            ownerEmail: ((_t = (_s = (_r = patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.patientOwners) === null || _r === void 0 ? void 0 : _r[0]) === null || _s === void 0 ? void 0 : _s.ownerBrand) === null || _t === void 0 ? void 0 : _t.email) || '',
            ownerPhone: `${(_w = (_v = (_u = patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.patientOwners[0]) === null || _u === void 0 ? void 0 : _u.ownerBrand) === null || _v === void 0 ? void 0 : _v.globalOwner) === null || _w === void 0 ? void 0 : _w.countryCode} ${(_z = (_y = (_x = patientDetails === null || patientDetails === void 0 ? void 0 : patientDetails.patientOwners[0]) === null || _x === void 0 ? void 0 : _x.ownerBrand) === null || _y === void 0 ? void 0 : _y.globalOwner) === null || _z === void 0 ? void 0 : _z.phoneNumber}`,
            // Template and Assessment Information
            templateName: templateName,
            assessmentText: (noteData === null || noteData === void 0 ? void 0 : noteData.notes) || '',
            tableData: (noteData === null || noteData === void 0 ? void 0 : noteData.values) || {}
        };
    }
};
exports.DiagnosticTemplatesService = DiagnosticTemplatesService;
exports.DiagnosticTemplatesService = DiagnosticTemplatesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(diagnostic_template_entity_1.DiagnosticTemplate)),
    __param(1, (0, typeorm_1.InjectRepository)(diagnostic_note_entity_1.DiagnosticNote)),
    __param(2, (0, typeorm_1.InjectRepository)(lab_report_entity_1.LabReport)),
    __param(3, (0, typeorm_1.InjectRepository)(clinic_lab_report_entity_1.ClinicLabReport)),
    __param(4, (0, typeorm_1.InjectRepository)(appointment_entity_1.AppointmentEntity)),
    __param(5, (0, typeorm_1.InjectRepository)(appointment_details_entity_1.AppointmentDetailsEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        winston_logger_service_1.WinstonLogger,
        patients_service_1.PatientsService,
        s3_service_1.S3Service,
        typeorm_3.DataSource,
        socket_appointment_gateway_1.AppointmentGateway])
], DiagnosticTemplatesService);
//# sourceMappingURL=diagnostic-note.service.js.map