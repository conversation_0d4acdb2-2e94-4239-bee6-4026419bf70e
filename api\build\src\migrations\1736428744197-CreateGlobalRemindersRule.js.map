{"version": 3, "file": "1736428744197-CreateGlobalRemindersRule.js", "sourceRoot": "", "sources": ["../../../src/migrations/1736428744197-CreateGlobalRemindersRule.ts"], "names": [], "mappings": ";;;AAAA,qCAA+F;AAC/F,mGAA8F;AAC9F,4EAA0E;AAE1E,MAAa,sCAAsC;IAExC,KAAK,CAAC,EAAE,CAAC,WAAwB;QAEpC,MAAM,WAAW,CAAC,WAAW,CACzB,IAAI,eAAK,CAAC;YACN,IAAI,EAAE,uBAAuB;YAC7B,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,IAAI;oBACf,kBAAkB,EAAE,MAAM;oBAC1B,OAAO,EAAE,oBAAoB;iBAChC;gBACD;oBACI,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,MAAM;iBACf;gBACD;oBACI,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,2CAAmB,CAAC;iBAC3C;gBACD;oBACI,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,OAAO;oBACb,UAAU,EAAE,KAAK;iBACpB;gBACD;oBACI,IAAI,EAAE,kBAAkB;oBACxB,IAAI,EAAE,OAAO;oBACb,UAAU,EAAE,KAAK;iBACpB;gBACD;oBACI,IAAI,EAAE,mBAAmB;oBACzB,IAAI,EAAE,OAAO;oBACb,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,iBAAiB;oBACvB,IAAI,EAAE,OAAO;oBACb,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,sBAAsB;oBAC5B,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,iBAAiB;oBACvB,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,8BAAc,CAAC;oBACnC,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,qBAAqB;oBAC3B,IAAI,EAAE,WAAW;oBACjB,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,4BAA4B;oBAClC,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,qBAAqB;oBAC3B,IAAI,EAAE,SAAS;oBACf,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,IAAI;iBAChB;gBACD;oBACI,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;iBACnB;gBACD;oBACI,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;iBAC/B;gBACD;oBACI,IAAI,EAAE,YAAY;oBAClB,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,mBAAmB;oBAC5B,QAAQ,EAAE,mBAAmB;iBAChC;aACJ;SACJ,CAAC,EACF,IAAI,CACP,CAAC;QAED,6BAA6B;QAC7B,MAAM,WAAW,CAAC,gBAAgB,CAC/B,uBAAuB,EACvB,IAAI,yBAAe,CAAC;YAChB,WAAW,EAAE,CAAC,WAAW,CAAC;YAC1B,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,SAAS;YAC9B,QAAQ,EAAE,SAAS;SACtB,CAAC,CACL,CAAC;QAEF,sCAAsC;QACtC,MAAM,WAAW,CAAC,gBAAgB,CAC9B,uBAAuB,EACvB,IAAI,yBAAe,CAAC;YAChB,WAAW,EAAE,CAAC,YAAY,CAAC;YAC3B,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,OAAO;YAC5B,QAAQ,EAAE,UAAU;SACvB,CAAC,CACL,CAAC;QAEF,6DAA6D;QAC7D,MAAM,WAAW,CAAC,SAAS,CACvB,mBAAmB,EACnB,IAAI,qBAAW,CAAC;YACZ,IAAI,EAAE,sBAAsB;YAC5B,IAAI,EAAE,MAAM;YACZ,UAAU,EAAE,IAAI;SACnB,CAAC,CACL,CAAC;QAEF,2CAA2C;QAC3C,MAAM,WAAW,CAAC,gBAAgB,CAC9B,mBAAmB,EACnB,IAAI,yBAAe,CAAC;YAChB,WAAW,EAAE,CAAC,sBAAsB,CAAC;YACrC,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,uBAAuB;YAC5C,QAAQ,EAAE,UAAU;SACvB,CAAC,CACL,CAAC;IACN,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACtC,MAAM,qBAAqB,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;QAC9E,MAAM,gBAAgB,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;QAE9E,IAAG,qBAAqB,EAAC,CAAC;YACrB,MAAM,gBAAgB,GAAG,qBAAqB,CAAC,WAAW,CAAC,IAAI,CAC3D,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAC9D,CAAC;YACF,IAAI,gBAAgB,EAAE,CAAC;gBACnB,MAAM,WAAW,CAAC,cAAc,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;YAC5E,CAAC;YAED,MAAM,WAAW,CAAC,UAAU,CAAC,mBAAmB,EAAE,sBAAsB,CAAC,CAAC;YAE1E,IAAI,gBAAgB,EAAE,CAAC;gBACnB,MAAM,WAAW,GAAG,gBAAgB,CAAC,WAAW,CAAC;gBACjD,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;oBACnC,MAAM,WAAW,CAAC,cAAc,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC;gBAC1E,CAAC;YACL,CAAC;YAED,MAAM,WAAW,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;QAC1D,CAAC;IACJ,CAAC;CAEJ;AArKD,wFAqKC"}