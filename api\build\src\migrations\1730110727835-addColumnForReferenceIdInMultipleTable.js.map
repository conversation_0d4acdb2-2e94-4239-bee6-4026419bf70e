{"version": 3, "file": "1730110727835-addColumnForReferenceIdInMultipleTable.js", "sourceRoot": "", "sources": ["../../../src/migrations/1730110727835-addColumnForReferenceIdInMultipleTable.ts"], "names": [], "mappings": ";;;AAAA,qCAAuE;AAEvE,MAAa,qCAAqC;IAG1C,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,MAAM,WAAW,CAAC,SAAS,CAC1B,UAAU,EACV,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,oBAAoB;YAC1B,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;QAEF,MAAM,WAAW,CAAC,SAAS,CAC1B,UAAU,EACV,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,iCAAiC;YACvC,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;QAEF,MAAM,WAAW,CAAC,SAAS,CAC1B,iBAAiB,EACjB,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,oBAAoB;YAC1B,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,MAAM,WAAW,CAAC,UAAU,CAAC,UAAU,EAAE,oBAAoB,CAAC,CAAC;QAC/D,MAAM,WAAW,CAAC,UAAU,CAC3B,UAAU,EACV,iCAAiC,CACjC,CAAC;QACF,MAAM,WAAW,CAAC,UAAU,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,CAAC;IACvE,CAAC;CACD;AAxCD,sFAwCC"}