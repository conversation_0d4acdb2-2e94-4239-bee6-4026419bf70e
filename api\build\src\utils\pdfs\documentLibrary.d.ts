interface VaccinationCertificateData {
    clinicName: string;
    clinicAddress: string;
    clinicPhone: string;
    clinicEmail: string;
    clinicWebsite: string;
    vetName: string;
    docDate: string;
    digitalSignature: string;
    title: string;
    bodyText: string;
    petName?: string;
    ownerName?: string;
    species?: string;
    breed?: string;
    color?: string;
    weight?: string;
    reproductiveStatus?: string;
    dob?: string;
    age?: Number;
}
export declare const generateDocumentLibrary: ({ clinicName, clinicAddress, clinicPhone, clinicEmail, clinicWebsite, vetName, docDate, digitalSignature, title, bodyText, petName, ownerName, species, breed, color, weight, reproductiveStatus, dob, age }: VaccinationCertificateData) => string;
export {};
