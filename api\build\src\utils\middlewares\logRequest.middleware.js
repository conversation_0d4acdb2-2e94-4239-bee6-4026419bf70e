"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggingMiddleware = void 0;
const common_1 = require("@nestjs/common");
const uuid_1 = require("uuid");
const winston_logger_service_1 = require("../logger/winston-logger.service");
const trace_context_1 = require("./trace.context");
// import * as newrelic from 'newrelic';
let LoggingMiddleware = class LoggingMiddleware {
    constructor(logger) {
        this.logger = logger;
    }
    use(req, res, next) {
        trace_context_1.TraceContext.run(() => {
            const traceID = req.headers['trace-id'] || (0, uuid_1.v7)();
            const sessionID = req.headers['session-id'] ||
                req.cookies['sessionID'] ||
                (0, uuid_1.v7)();
            trace_context_1.TraceContext.setTraceId(traceID);
            trace_context_1.TraceContext.setSessionId(sessionID);
            req.headers['trace-id'] = traceID;
            req.headers['session-id'] = sessionID;
            res.setHeader('trace-id', traceID);
            res.setHeader('session-id', sessionID);
            const start = Date.now();
            // Add New Relic custom attributes
            // newrelic.addCustomAttribute('traceID', traceID);
            // newrelic.addCustomAttribute('sessionID', sessionID);
            res.on('finish', () => {
                const duration = Date.now() - start;
                const { method, originalUrl, ip } = req;
                const { statusCode } = res;
                const logMessage = `[TraceID: ${traceID}] [SessionID: ${sessionID}] [Method: ${method}] [URL: ${originalUrl}] [Status: ${statusCode}] [Duration: ${duration}ms] [IP: ${ip}]`;
                const logObject = {
                    traceID,
                    sessionID,
                    method,
                    url: originalUrl,
                    statusCode,
                    duration,
                    ip,
                    userAgent: req.headers['user-agent']
                };
                // Record metrics in New Relic
                // newrelic.recordMetric(`Custom/API${originalUrl}`, duration);
                // newrelic.addCustomAttribute('statusCode', statusCode);
                if (statusCode >= 500) {
                    this.logger.error(logMessage, logObject);
                    // newrelic.noticeError(
                    // 	new Error(`Server Error: ${statusCode}`)
                    // );
                }
                else if (statusCode >= 400) {
                    this.logger.error(logMessage, logObject);
                    // newrelic.noticeError(
                    // 	new Error(`Client Error: ${statusCode}`)
                    // );
                }
                else {
                    this.logger.log(logMessage, logObject);
                }
            });
            next();
        });
    }
};
exports.LoggingMiddleware = LoggingMiddleware;
exports.LoggingMiddleware = LoggingMiddleware = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger])
], LoggingMiddleware);
//# sourceMappingURL=logRequest.middleware.js.map