"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddOwnerRole1744161860000 = void 0;
const uuid_1 = require("uuid");
class AddOwnerRole1744161860000 {
    async up(queryRunner) {
        // Check if OWNER role already exists
        const existingRole = await queryRunner.query(`SELECT * FROM roles WHERE name = 'owner'`);
        // If role doesn't exist, create it
        if (!existingRole || existingRole.length === 0) {
            const roleId = (0, uuid_1.v4)();
            await queryRunner.query(`INSERT INTO roles (id, name, description)
                 VALUES ('${roleId}', 'owner', 'Pet owner role for booking portal')`);
            console.log('Added OWNER role to the database');
        }
        else {
            console.log('OWNER role already exists in the database');
        }
    }
    async down(queryRunner) {
        // Remove the OWNER role if needed
        await queryRunner.query(`DELETE FROM roles WHERE name = 'owner'`);
    }
}
exports.AddOwnerRole1744161860000 = AddOwnerRole1744161860000;
//# sourceMappingURL=1744161860000-AddOwnerRole.js.map