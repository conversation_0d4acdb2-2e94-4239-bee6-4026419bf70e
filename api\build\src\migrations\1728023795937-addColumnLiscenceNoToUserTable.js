"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddColumnLiscenceNoToUserTable1728023795937 = void 0;
const typeorm_1 = require("typeorm");
class AddColumnLiscenceNoToUserTable1728023795937 {
    async up(queryRunner) {
        await queryRunner.addColumn('users', new typeorm_1.TableColumn({
            name: 'license_number',
            type: 'varchar',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('users', 'license_number');
    }
}
exports.AddColumnLiscenceNoToUserTable1728023795937 = AddColumnLiscenceNoToUserTable1728023795937;
//# sourceMappingURL=1728023795937-addColumnLiscenceNoToUserTable.js.map