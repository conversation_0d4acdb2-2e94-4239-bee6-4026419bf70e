{"version": 3, "file": "1738749983423-RecalculatePreviousBalances.js", "sourceRoot": "", "sources": ["../../../src/migrations/1738749983423-RecalculatePreviousBalances.ts"], "names": [], "mappings": ";;;AACA,2CAAiC;AAEjC,MAAa,wCAAwC;IAG7C,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,gGAAgG;QAChG,MAAM,WAAW,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;SAejB,CAAC,CAAC;QAET,wEAAwE;QACxE,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC;;;SAGrC,CAAC,CAAC;QAET,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACtC,IAAI,cAAc,GAAG,IAAI,oBAAO,CAAC,UAAU,CAAC,eAAe,IAAI,CAAC,CAAC,CAAC;YAElE,MAAM,YAAY,GAAG,MAAM,WAAW,CAAC,KAAK,CAC3C;;gDAE4C,EAC5C,CAAC,UAAU,CAAC,QAAQ,CAAC,CACrB,CAAC;YAEF,qCAAqC;YACrC,KAAK,MAAM,EAAE,IAAI,YAAY,EAAE,CAAC;gBAC/B,MAAM,MAAM,GAAG,IAAI,oBAAO,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC;gBAC3C,MAAM,QAAQ,GAAG,IAAI,oBAAO,CAAC,EAAE,CAAC,kBAAkB,IAAI,CAAC,CAAC,CAAC;gBACzD,MAAM,eAAe,GAAG,cAAc,CAAC;gBAEvC,mDAAmD;gBACnD,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;oBACjB,KAAK,SAAS;wBACb,cAAc,GAAG,cAAc,CAAC,IAAI,CACnC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CACtB,CAAC;wBACF,MAAM;oBAEP,KAAK,SAAS;wBACb,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC7C,MAAM;oBAEP,KAAK,QAAQ;wBACZ,cAAc,GAAG,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;wBAC9C,MAAM;oBAEP,KAAK,aAAa;wBACjB,cAAc,GAAG,cAAc,CAAC,KAAK,CACpC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CACtB,CAAC;wBACF,MAAM;oBAEP;wBACC,OAAO,CAAC,IAAI,CAAC,6BAA6B,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;gBACvD,CAAC;gBAED,+CAA+C;gBAC/C,MAAM,WAAW,CAAC,KAAK,CACtB;;kCAE6B,EAC7B,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CACtD,CAAC;YACH,CAAC;YAED,6DAA6D;YAC7D,MAAM,WAAW,CAAC,KAAK,CACtB;;8BAE0B,EAC1B;gBACC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;gBAC5C,UAAU,CAAC,QAAQ;aACnB,CACD,CAAC;QACH,CAAC;QAED,kBAAkB;QAClB,MAAM,WAAW,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;QAE3D,qBAAqB;QACrB,MAAM,YAAY,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC;;;;SAItC,CAAC,CAAC;QACT,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACnE,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,YAAyB;QAC1C,qEAAqE;IACtE,CAAC;CACD;AA1GD,4FA0GC"}