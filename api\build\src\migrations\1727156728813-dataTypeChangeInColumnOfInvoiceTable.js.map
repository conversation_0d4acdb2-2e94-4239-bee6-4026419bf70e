{"version": 3, "file": "1727156728813-dataTypeChangeInColumnOfInvoiceTable.js", "sourceRoot": "", "sources": ["../../../src/migrations/1727156728813-dataTypeChangeInColumnOfInvoiceTable.ts"], "names": [], "mappings": ";;;AAAA,qCAAuE;AAEvE,MAAa,iDAAiD;IAGtD,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,MAAM,WAAW,CAAC,YAAY,CAC7B,UAAU,EACV,UAAU,EACV,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,MAAM;YACZ,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,MAAM,WAAW,CAAC,YAAY,CAC7B,UAAU,EACV,UAAU,EACV,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;IACH,CAAC;CACD;AA1BD,8GA0BC"}