{"version": 3, "file": "1724650812726-AddMoreColumnsToMedications.js", "sourceRoot": "", "sources": ["../../../src/migrations/1724650812726-AddMoreColumnsToMedications.ts"], "names": [], "mappings": ";;;AAAA,qCAAuE;AAEvE,MAAa,yCAAyC;IAG9C,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE;YAClD,IAAI,qBAAW,CAAC;gBACf,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,KAAK;gBACjB,OAAO,EAAE,KAAK;aACd,CAAC;YACF,IAAI,qBAAW,CAAC;gBACf,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,IAAI;aAChB,CAAC;YACF,IAAI,qBAAW,CAAC;gBACf,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,IAAI;aAChB,CAAC;YACF,IAAI,qBAAW,CAAC;gBACf,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,IAAI;aAChB,CAAC;YACF,IAAI,qBAAW,CAAC;gBACf,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,IAAI;aAChB,CAAC;YACF,IAAI,qBAAW,CAAC;gBACf,IAAI,EAAE,kBAAkB;gBACxB,IAAI,EAAE,SAAS;gBACf,UAAU,EAAE,KAAK;gBACjB,OAAO,EAAE,KAAK;aACd,CAAC;SACF,CAAC,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,eAAe,CAAC,CAAC;QAEpE,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;QAE3D,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;QAE3D,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC;QAE/D,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;QAE3D,MAAM,WAAW,CAAC,UAAU,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAC;IACxE,CAAC;CACD;AArDD,8FAqDC"}