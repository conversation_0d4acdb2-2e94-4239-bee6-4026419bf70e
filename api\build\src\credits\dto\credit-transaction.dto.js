"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OwnerCreditBalanceResponseDto = exports.CreateCreditTransactionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const enum_credit_types_1 = require("../../payment-details/enums/enum-credit-types");
class CreateCreditTransactionDto {
}
exports.CreateCreditTransactionDto = CreateCreditTransactionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The owner id',
        example: 'uuid'
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCreditTransactionDto.prototype, "ownerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Amount of credit',
        example: 1500
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], CreateCreditTransactionDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of credit transaction',
        example: enum_credit_types_1.EnumAmountType.Collect
    }),
    (0, class_validator_1.IsEnum)(enum_credit_types_1.EnumAmountType),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCreditTransactionDto.prototype, "transactionType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Description of the transaction',
        example: 'Credit added for future use'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateCreditTransactionDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Related invoice ID if applicable',
        example: 'uuid',
        required: false
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateCreditTransactionDto.prototype, "relatedInvoiceId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Additional metadata for the transaction',
        example: { source: 'manual_addition', notes: 'Customer requested' },
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Object)
], CreateCreditTransactionDto.prototype, "metadata", void 0);
class OwnerCreditBalanceResponseDto {
}
exports.OwnerCreditBalanceResponseDto = OwnerCreditBalanceResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Owner ID',
        example: 'uuid'
    }),
    __metadata("design:type", String)
], OwnerCreditBalanceResponseDto.prototype, "ownerId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current credit balance',
        example: 1500
    }),
    __metadata("design:type", Number)
], OwnerCreditBalanceResponseDto.prototype, "creditBalance", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Legacy owner balance (for backward compatibility)',
        example: 2000
    }),
    __metadata("design:type", Number)
], OwnerCreditBalanceResponseDto.prototype, "ownerBalance", void 0);
//# sourceMappingURL=credit-transaction.dto.js.map