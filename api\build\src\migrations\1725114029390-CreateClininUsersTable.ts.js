"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateClinicUsersTable1725114029390 = void 0;
const typeorm_1 = require("typeorm");
class CreateClinicUsersTable1725114029390 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'clinic_users',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'clinic_id',
                    type: 'uuid'
                },
                {
                    name: 'user_id',
                    type: 'uuid'
                },
                {
                    name: 'is_primary',
                    type: 'boolean',
                    default: false
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                }
            ]
        }), true);
        await queryRunner.createForeignKey('clinic_users', new typeorm_1.TableForeignKey({
            columnNames: ['clinic_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinics',
            onDelete: 'CASCADE'
        }));
        await queryRunner.createForeignKey('clinic_users', new typeorm_1.TableForeignKey({
            columnNames: ['user_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'CASCADE'
        }));
        await queryRunner.createForeignKey('clinic_users', new typeorm_1.TableForeignKey({
            columnNames: ['created_by'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'SET NULL'
        }));
        await queryRunner.createForeignKey('clinic_users', new typeorm_1.TableForeignKey({
            columnNames: ['updated_by'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'SET NULL'
        }));
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable('clinic_users');
        if (table) {
            const foreignKeys = table.foreignKeys;
            if (foreignKeys) {
                await Promise.all(foreignKeys.map(foreignKey => queryRunner.dropForeignKey('clinic_users', foreignKey)));
                await queryRunner.dropTable('clinic_users');
            }
        }
    }
}
exports.CreateClinicUsersTable1725114029390 = CreateClinicUsersTable1725114029390;
//# sourceMappingURL=1725114029390-CreateClininUsersTable.ts.js.map