"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetSummaryDto = exports.GetDoctorSummaryDto = exports.GetAppointmentsChartDataDto = exports.DownloadAnalyticsReportDto = exports.GetRevenueChartDataDto = exports.AppointmentAnalyticsType = exports.AnalyticsReportType = exports.AnalyticsType = exports.AnalyticsTimeFrame = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
var AnalyticsTimeFrame;
(function (AnalyticsTimeFrame) {
    AnalyticsTimeFrame["ONE_DAY"] = "1D";
    AnalyticsTimeFrame["ONE_WEEK"] = "1W";
    AnalyticsTimeFrame["ONE_MONTH"] = "1M";
    AnalyticsTimeFrame["ONE_YEAR"] = "1Y";
})(AnalyticsTimeFrame || (exports.AnalyticsTimeFrame = AnalyticsTimeFrame = {}));
var AnalyticsType;
(function (AnalyticsType) {
    AnalyticsType["REVENUE"] = "REVENUE";
    AnalyticsType["APPOINTMENTS"] = "APPOINTMENTS";
    AnalyticsType["DOCTOR_PERFORMANCE"] = "DOCTOR_PERFORMANCE";
    AnalyticsType["OUTSTANDING_BALANCE"] = "OUTSTANDING_BALANCE";
    AnalyticsType["COLLECTED_PAYMENTS"] = "COLLECTED_PAYMENTS";
})(AnalyticsType || (exports.AnalyticsType = AnalyticsType = {}));
var AnalyticsReportType;
(function (AnalyticsReportType) {
    AnalyticsReportType["BY_BILLING"] = "by-billing";
    AnalyticsReportType["BY_PATIENT"] = "by-patient";
})(AnalyticsReportType || (exports.AnalyticsReportType = AnalyticsReportType = {}));
var AppointmentAnalyticsType;
(function (AppointmentAnalyticsType) {
    AppointmentAnalyticsType["ALL"] = "All";
    AppointmentAnalyticsType["BUSIEST_DAYS"] = "BusiestDays";
    AppointmentAnalyticsType["BUSIEST_HOURS"] = "BusiestHours";
    AppointmentAnalyticsType["AVERAGE_DURATION"] = "AverageDuration";
})(AppointmentAnalyticsType || (exports.AppointmentAnalyticsType = AppointmentAnalyticsType = {}));
class GetRevenueChartDataDto {
}
exports.GetRevenueChartDataDto = GetRevenueChartDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Start date for the chart data'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetRevenueChartDataDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'End date for the chart data'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetRevenueChartDataDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Clinic ID'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetRevenueChartDataDto.prototype, "clinicId", void 0);
class DownloadAnalyticsReportDto {
}
exports.DownloadAnalyticsReportDto = DownloadAnalyticsReportDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        enum: AnalyticsType,
        description: 'Type of analytics report'
    }),
    (0, class_validator_1.IsEnum)(AnalyticsType),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DownloadAnalyticsReportDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Start date for the report'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DownloadAnalyticsReportDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'End date for the report'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DownloadAnalyticsReportDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Clinic ID'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], DownloadAnalyticsReportDto.prototype, "clinicId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        enum: AnalyticsReportType,
        description: 'Report type',
        required: false
    }),
    (0, class_validator_1.IsEnum)(AnalyticsReportType),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], DownloadAnalyticsReportDto.prototype, "reportType", void 0);
class GetAppointmentsChartDataDto {
}
exports.GetAppointmentsChartDataDto = GetAppointmentsChartDataDto;
class GetDoctorSummaryDto {
}
exports.GetDoctorSummaryDto = GetDoctorSummaryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Start date for the summary data'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetDoctorSummaryDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'End date for the summary data'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetDoctorSummaryDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Clinic ID'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetDoctorSummaryDto.prototype, "clinicId", void 0);
class GetSummaryDto {
}
exports.GetSummaryDto = GetSummaryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Start date for the summary data'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetSummaryDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'End date for the summary data'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetSummaryDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Clinic ID'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetSummaryDto.prototype, "clinicId", void 0);
//# sourceMappingURL=analytics.dto.js.map