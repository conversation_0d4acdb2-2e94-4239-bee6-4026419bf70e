"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAppointmentDoctors1722421402069 = void 0;
class CreateAppointmentDoctors1722421402069 {
    constructor() {
        this.name = 'CreateAppointmentDoctors1722421402069';
    }
    async up(queryRunner) {
        await queryRunner.query(`CREATE TABLE "appointment_doctors" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "appointment_id" uuid NOT NULL, "doctor_id" uuid NOT NULL, "created_by" uuid, "updated_by" uuid, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_c02843a5c65b18debd7d34e51ac" PRIMARY KEY ("id"))`);
    }
    async down(queryRunner) {
        await queryRunner.query(`DROP TABLE "appointment_doctors"`);
    }
}
exports.CreateAppointmentDoctors1722421402069 = CreateAppointmentDoctors1722421402069;
//# sourceMappingURL=1722421402069-CreateAppointmentDoctors.js.map