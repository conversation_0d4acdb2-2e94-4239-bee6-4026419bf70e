"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAppointmentSessionsChanges1734679704170 = void 0;
const typeorm_1 = require("typeorm");
class CreateAppointmentSessionsChanges1734679704170 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'appointment_session_changes',
            columns: [
                {
                    name: 'roomId',
                    type: 'varchar',
                    isPrimary: true
                },
                {
                    name: 'key',
                    type: 'varchar',
                    isPrimary: true
                },
                {
                    name: 'value',
                    type: 'jsonb'
                },
                {
                    name: 'lastUpdatedBy',
                    type: 'varchar'
                },
                {
                    name: 'createdAt',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'updatedAt',
                    type: 'timestamp',
                    default: 'now()'
                }
            ]
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('appointment_session_changes');
    }
}
exports.CreateAppointmentSessionsChanges1734679704170 = CreateAppointmentSessionsChanges1734679704170;
//# sourceMappingURL=1734679704170-CreateAppointmentSessionsChanges.js.map