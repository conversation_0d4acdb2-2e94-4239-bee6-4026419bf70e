"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddOpeningBalanceToOwners1738132553055 = void 0;
class AddOpeningBalanceToOwners1738132553055 {
    async up(queryRunner) {
        // Add opening_balance column with default value 0
        await queryRunner.query(`
            ALTER TABLE owners 
            ADD COLUMN opening_balance NUMERIC DEFAULT 0;
            
            -- Set existing records' opening_balance to 0
            UPDATE owners 
            SET opening_balance = 0 
            WHERE opening_balance IS NULL;
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE owners 
            DROP COLUMN opening_balance;
        `);
    }
}
exports.AddOpeningBalanceToOwners1738132553055 = AddOpeningBalanceToOwners1738132553055;
//# sourceMappingURL=1738132553055-AddOpeningBalanceToOwners.js.map