"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bootstrap = bootstrap;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const core_1 = require("@nestjs/core");
const cookieParser = require("cookie-parser");
const helmet_1 = require("helmet");
const app_module_1 = require("./app.module");
const api_documentation_base_1 = require("./base/api-documentation-base");
const http_exception_filter_1 = require("./utils/http-exception.filter");
const winston_logger_service_1 = require("./utils/logger/winston-logger.service");
// import { SanitizeHtmlPipe } from './utils/sanitizar/sanitize-html.pipe';
async function bootstrap() {
    var _a;
    // Set the SERVICE_TYPE environment variable to identify this as an SQS worker
    process.env.SERVICE_TYPE = 'sqs';
    const app = await core_1.NestFactory.create(app_module_1.AppModule.register({ isSqsEnabled: true }));
    app.setGlobalPrefix('api');
    const configService = app.get(config_1.ConfigService);
    app.useLogger(app.get(winston_logger_service_1.WinstonLogger));
    app.use(cookieParser());
    app.use((0, helmet_1.default)({
        contentSecurityPolicy: {
            directives: {
                defaultSrc: ["'self'"],
                scriptSrc: ["'self'", 'trusted-scripts.com'],
                styleSrc: ["'self'", 'trusted-styles.com'],
                imgSrc: ["'self'", 'data:', 'trusted-images.com'],
                connectSrc: ["'self'", 'trusted-api.com'],
                fontSrc: ["'self'", 'trusted-fonts.com'],
                reportUri: '/csp-report' // Set the report URI here
            }
        }
    }));
    app.useGlobalFilters(new http_exception_filter_1.HttpExceptionFilter(app.get(winston_logger_service_1.WinstonLogger)));
    // app.useGlobalPipes(new SanitizeHtmlPipe());
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true
    }));
    app.enableCors({
        origin: configService === null || configService === void 0 ? void 0 : configService.get('app.allowedHosts'),
        methods: 'GET,POST,PUT,DELETE,OPTIONS',
        allowedHeaders: 'Content-Type, Authorization'
    });
    // Initializing Swagger
    api_documentation_base_1.ApiDocumentationBase.initApiDocumentation(app);
    const port = (_a = configService === null || configService === void 0 ? void 0 : configService.get('app.sqsPort')) !== null && _a !== void 0 ? _a : 8000;
    console.log('Port is at', port);
    await app.listen(port);
    return app;
}
bootstrap();
//# sourceMappingURL=sqs.js.map