import { DataService } from './data.service';
import { CreateAppointmentDetailsDto, CreateAppointmentDto, CreateOwnerDto, CreatePatientDto, CreatePatientOwnerDto, InvoiceDataDto, BulkUpdateOpeningBalanceDto } from './dto/data.dto';
import { S3Service } from '../utils/aws/s3/s3.service';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { DataSource } from 'typeorm';
import { AppointmentDetailsEntity } from '../appointments/entities/appointment-details.entity';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
export declare class DataController {
    private readonly dataService;
    private readonly s3Service;
    private readonly logger;
    private dataSource;
    constructor(dataService: DataService, s3Service: S3Service, logger: WinstonLogger, dataSource: DataSource);
    getHello(): string;
    createOwner(createOwnerDto: CreateOwnerDto): Promise<import("../owners/entities/owner-brand.entity").OwnerBrand>;
    createPatient(createPatientDto: CreatePatientDto): Promise<import("../patients/entities/patient.entity").Patient>;
    getPatients(clinicId: string, patientId: string): Promise<{
        id: string;
        oldPatientId: string;
        clinicId: string;
        status: string;
    }>;
    createPatientOwner(createPatientOwnerDto: CreatePatientOwnerDto): Promise<import("../patients/entities/patient-owner.entity").PatientOwner>;
    createAppointment(createAppointmentDto: CreateAppointmentDto): Promise<AppointmentEntity>;
    createAppointmentDetails(createAppointmentDetailsDto: CreateAppointmentDetailsDto): Promise<AppointmentDetailsEntity>;
    private processAttachments;
    private downloadFile;
    /**
     * Process invoices with either a PDF URL or an existing S3 key
     *
     * @example With PDF URL:
     * {
     *   "patientId": "...",
     *   "date": "...",
     *   "pdfUrl": "...",
     *   "clinicId": "...",
     *   "brandId": "..."
     * }
     *
     * @example With existing S3 key:
     * {
     *   "patientId": "...",
     *   "date": "...",
     *   "s3Key": "...",
     *   "clinicId": "...",
     *   "brandId": "..."
     * }
     */
    processInvoices(invoiceData: InvoiceDataDto[]): Promise<{
        success: boolean;
        results: any[];
        summary: {
            total: number;
            successful: number;
            failed: number;
        };
    }>;
    uploadEmr(file: Express.Multer.File, patientId: string): Promise<{
        status: string;
        patientId: string;
        s3Key: string;
    }>;
    private downloadPDF;
    updateOwnersOpeningBalance(bulkUpdateDto: BulkUpdateOpeningBalanceDto): Promise<{
        success: boolean;
        results: {
            clientId: string;
            status: "success" | "error";
            message?: string;
            ownerId?: string;
        }[];
        summary: {
            total: number;
            successful: number;
            failed: number;
        };
    }>;
    updateMicrochipId(id: string, updateData: {
        microchipId: string;
    }): Promise<{
        success: boolean;
        data: import("../patients/entities/patient.entity").Patient;
    }>;
}
