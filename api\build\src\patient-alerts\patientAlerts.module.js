"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientAlertsModule = void 0;
const common_1 = require("@nestjs/common");
const patientAlerts_service_1 = require("./patientAlerts.service");
const patientAlerts_controller_1 = require("./patientAlerts.controller");
const patientAlerts_entity_1 = require("./entities/patientAlerts.entity");
const typeorm_1 = require("@nestjs/typeorm");
const role_module_1 = require("../roles/role.module");
let PatientAlertsModule = class PatientAlertsModule {
};
exports.PatientAlertsModule = PatientAlertsModule;
exports.PatientAlertsModule = PatientAlertsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([patientAlerts_entity_1.PatientAlertsEntity]), role_module_1.RoleModule],
        controllers: [patientAlerts_controller_1.PatientAlertController],
        providers: [patientAlerts_service_1.PatientAlertsService]
    })
], PatientAlertsModule);
//# sourceMappingURL=patientAlerts.module.js.map