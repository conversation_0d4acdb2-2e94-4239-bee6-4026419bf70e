"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PetTransferDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class PetTransferDto {
}
exports.PetTransferDto = PetTransferDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The ID of the patient to be transferred',
        example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef'
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], PetTransferDto.prototype, "patientId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The ID of the source clinic',
        example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef'
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], PetTransferDto.prototype, "sourceClinicId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The ID of the destination clinic',
        example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef'
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], PetTransferDto.prototype, "destClinicId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The ID of the destination brand',
        example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef'
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], PetTransferDto.prototype, "destBrandId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional mapping of patient ID if patient already exists in destination clinic (for merging)',
        example: 'b2c3d4e5-f6g7-8901-2345-678901bcdefg',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], PetTransferDto.prototype, "destPatientId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional mapping of owner IDs if owners already exist in destination brand (for conflict resolution)',
        example: { 'source-owner-id': 'dest-owner-id' },
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], PetTransferDto.prototype, "ownerMapping", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional mapping of user IDs for created_by/updated_by field updates',
        example: { 'source-user-id': 'dest-user-id' },
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], PetTransferDto.prototype, "userMapping", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional mapping of clinic user IDs for appointment_doctors and other clinic-specific user references',
        example: { 'source-clinic-user-id': 'dest-clinic-user-id' },
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], PetTransferDto.prototype, "clinicUserMapping", void 0);
//# sourceMappingURL=pet-transfer.dto.js.map