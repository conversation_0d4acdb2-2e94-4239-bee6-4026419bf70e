"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const users_service_1 = require("./users.service");
const users_controller_1 = require("./users.controller");
const user_entity_1 = require("./entities/user.entity");
const role_module_1 = require("../roles/role.module");
const ses_module_1 = require("../utils/aws/ses/ses.module");
const clinic_user_entity_1 = require("../clinics/entities/clinic-user.entity");
const brands_module_1 = require("../brands/brands.module");
const appointment_doctor_entity_1 = require("../appointments/entities/appointment-doctor.entity");
const availability_exception_entity_1 = require("./entities/availability-exception.entity");
const availability_module_1 = require("../availability/availability.module");
let UsersModule = class UsersModule {
};
exports.UsersModule = UsersModule;
exports.UsersModule = UsersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                user_entity_1.User,
                clinic_user_entity_1.ClinicUser,
                appointment_doctor_entity_1.AppointmentDoctorsEntity,
                availability_exception_entity_1.AvailabilityExceptionEntity
            ]),
            role_module_1.RoleModule,
            ses_module_1.SESModule,
            brands_module_1.BrandsModule,
            (0, common_1.forwardRef)(() => availability_module_1.AvailabilityModule)
        ],
        controllers: [users_controller_1.UsersController],
        providers: [users_service_1.UsersService],
        exports: [users_service_1.UsersService]
    })
], UsersModule);
//# sourceMappingURL=users.module.js.map