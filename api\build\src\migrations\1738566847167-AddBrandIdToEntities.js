"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddBrandIdToEntities1738566847167 = void 0;
class AddBrandIdToEntities1738566847167 {
    constructor() {
        this.name = 'AddBrandIdToEntities1738566847167';
    }
    async up(queryRunner) {
        // Add brandId column to appointment_assessments
        await queryRunner.query(`ALTER TABLE "appointment_assessments" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "appointment_assessments" ADD CONSTRAINT "FK_appointment_assessments_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Add brandId column to lab_reports
        await queryRunner.query(`ALTER TABLE "lab_reports" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "lab_reports" ADD CONSTRAINT "FK_lab_reports_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Add brandId column to clinic_alerts
        await queryRunner.query(`ALTER TABLE "clinic_alerts" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "clinic_alerts" ADD CONSTRAINT "FK_clinic_alerts_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Add brandId column to document_library
        await queryRunner.query(`ALTER TABLE "document_library" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "document_library" ADD CONSTRAINT "FK_document_library_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Add brandId column to appointments
        await queryRunner.query(`ALTER TABLE "appointments" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "appointments" ADD CONSTRAINT "FK_appointments_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Add brandId column to clinic_integrations
        await queryRunner.query(`ALTER TABLE "clinic_integrations" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "clinic_integrations" ADD CONSTRAINT "FK_clinic_integrations_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Add brandId column to clinic_plans
        await queryRunner.query(`ALTER TABLE "clinic_plans" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "clinic_plans" ADD CONSTRAINT "FK_clinic_plans_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Add brandId column to clinic_rooms
        await queryRunner.query(`ALTER TABLE "clinic_rooms" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "clinic_rooms" ADD CONSTRAINT "FK_clinic_rooms_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Add brandId column to emr
        await queryRunner.query(`ALTER TABLE "emr" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "emr" ADD CONSTRAINT "FK_emr_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Add brandId column to long-term-medications
        await queryRunner.query(`ALTER TABLE "long-term-medications" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "long-term-medications" ADD CONSTRAINT "FK_long_term_medications_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Add brandId column to global_reminder_rules
        await queryRunner.query(`ALTER TABLE "global_reminder_rules" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "global_reminder_rules" ADD CONSTRAINT "FK_global_reminder_rules_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Add brandId column to patient_reminders
        await queryRunner.query(`ALTER TABLE "patient_reminders" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "patient_reminders" ADD CONSTRAINT "FK_patient_reminders_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Add brandId column to patients
        await queryRunner.query(`ALTER TABLE "patients" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "patients" ADD CONSTRAINT "FK_patients_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Add clinic_id and brand_id to invoices
        await queryRunner.query(`ALTER TABLE "invoices" ADD "clinic_id" uuid`);
        await queryRunner.query(`ALTER TABLE "invoices" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "invoices" ADD CONSTRAINT "FK_invoices_clinic" FOREIGN KEY ("clinic_id") REFERENCES "clinics"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "invoices" ADD CONSTRAINT "FK_invoices_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Add clinic_id and brand_id to patient_owners
        await queryRunner.query(`ALTER TABLE "patient_owners" ADD "clinic_id" uuid`);
        await queryRunner.query(`ALTER TABLE "patient_owners" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "patient_owners" ADD CONSTRAINT "FK_patient_owners_clinic" FOREIGN KEY ("clinic_id") REFERENCES "clinics"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "patient_owners" ADD CONSTRAINT "FK_patient_owners_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Add clinic_id and brand_id to payment_details
        await queryRunner.query(`ALTER TABLE "payment_details" ADD "clinic_id" uuid`);
        await queryRunner.query(`ALTER TABLE "payment_details" ADD "brand_id" uuid`);
        await queryRunner.query(`ALTER TABLE "payment_details" ADD CONSTRAINT "FK_payment_details_clinic" FOREIGN KEY ("clinic_id") REFERENCES "clinics"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "payment_details" ADD CONSTRAINT "FK_payment_details_brand" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        // Update brandId from clinics table - Execute each update separately to handle errors better
        await queryRunner.query(`
            UPDATE "appointment_assessments" aa 
            SET brand_id = c.brand_id 
            FROM "clinics" c 
            WHERE aa.clinic_id = c.id
        `);
        await queryRunner.query(`
            UPDATE "lab_reports" lr 
            SET brand_id = c.brand_id 
            FROM "clinics" c 
            WHERE lr.clinic_id = c.id
        `);
        await queryRunner.query(`
            UPDATE "clinic_alerts" ca 
            SET brand_id = c.brand_id 
            FROM "clinics" c 
            WHERE ca.clinic_id = c.id
        `);
        await queryRunner.query(`
            UPDATE "document_library" dl 
            SET brand_id = c.brand_id 
            FROM "clinics" c 
            WHERE dl.clinic_id = c.id
        `);
        await queryRunner.query(`
            UPDATE "appointments" a 
            SET brand_id = c.brand_id 
            FROM "clinics" c 
            WHERE a.clinic_id = c.id
        `);
        await queryRunner.query(`
            UPDATE "clinic_integrations" cii 
            SET brand_id = c.brand_id 
            FROM "clinics" c 
            WHERE cii.clinic_id = c.id
        `);
        await queryRunner.query(`
            UPDATE "clinic_plans" cp 
            SET brand_id = c.brand_id 
            FROM "clinics" c 
            WHERE cp.clinic_id = c.id
        `);
        await queryRunner.query(`
            UPDATE "clinic_rooms" cr 
            SET brand_id = c.brand_id 
            FROM "clinics" c 
            WHERE cr.clinic_id = c.id
        `);
        await queryRunner.query(`
            UPDATE "emr" e 
            SET brand_id = c.brand_id 
            FROM "clinics" c 
            WHERE e.clinic_id = c.id
        `);
        await queryRunner.query(`
            UPDATE "long-term-medications" ltm 
            SET brand_id = c.brand_id 
            FROM "clinics" c 
            WHERE ltm.clinic_id = c.id
        `);
        await queryRunner.query(`
            UPDATE "global_reminder_rules" grr 
            SET brand_id = c.brand_id 
            FROM "clinics" c 
            WHERE grr.clinic_id = c.id
        `);
        await queryRunner.query(`
            UPDATE "patient_reminders" pr 
            SET brand_id = c.brand_id 
            FROM "clinics" c 
            WHERE pr.clinic_id = c.id
        `);
        await queryRunner.query(`
            UPDATE "patients" p 
            SET brand_id = c.brand_id 
            FROM "clinics" c 
            WHERE p.clinic_id = c.id
        `);
        // Update invoices clinic_id and brand_id through patient relationship
        await queryRunner.query(`
            UPDATE "invoices" i 
            SET 
                clinic_id = p.clinic_id,
                brand_id = c.brand_id 
            FROM "patients" p 
            JOIN "clinics" c ON p.clinic_id = c.id 
            WHERE i.patient_id = p.id
        `);
        // Update patient_owners clinic_id and brand_id through patient relationship
        await queryRunner.query(`
            UPDATE "patient_owners" po 
            SET 
                clinic_id = p.clinic_id,
                brand_id = c.brand_id 
            FROM "patients" p 
            JOIN "clinics" c ON p.clinic_id = c.id 
            WHERE po.patient_id = p.id
        `);
        // Update payment_details clinic_id and brand_id through patient relationship
        await queryRunner.query(`
            UPDATE "payment_details" pd 
            SET 
                clinic_id = p.clinic_id,
                brand_id = c.brand_id 
            FROM "patients" p 
            JOIN "clinics" c ON p.clinic_id = c.id 
            WHERE pd.patient_id = p.id
        `);
    }
    async down(queryRunner) {
        // Remove foreign key constraints first
        await queryRunner.query(`ALTER TABLE "patients" DROP CONSTRAINT "FK_patients_brand"`);
        await queryRunner.query(`ALTER TABLE "patient_reminders" DROP CONSTRAINT "FK_patient_reminders_brand"`);
        await queryRunner.query(`ALTER TABLE "global_reminder_rules" DROP CONSTRAINT "FK_global_reminder_rules_brand"`);
        await queryRunner.query(`ALTER TABLE "long-term-medications" DROP CONSTRAINT "FK_long_term_medications_brand"`);
        await queryRunner.query(`ALTER TABLE "emr" DROP CONSTRAINT "FK_emr_brand"`);
        await queryRunner.query(`ALTER TABLE "clinic_rooms" DROP CONSTRAINT "FK_clinic_rooms_brand"`);
        await queryRunner.query(`ALTER TABLE "clinic_plans" DROP CONSTRAINT "FK_clinic_plans_brand"`);
        await queryRunner.query(`ALTER TABLE "clinic_integrations" DROP CONSTRAINT "FK_clinic_integrations_brand"`);
        await queryRunner.query(`ALTER TABLE "appointments" DROP CONSTRAINT "FK_appointments_brand"`);
        await queryRunner.query(`ALTER TABLE "appointment_assessments" DROP CONSTRAINT "FK_appointment_assessments_brand"`);
        await queryRunner.query(`ALTER TABLE "lab_reports" DROP CONSTRAINT "FK_lab_reports_brand"`);
        await queryRunner.query(`ALTER TABLE "document_library" DROP CONSTRAINT "FK_document_library_brand"`);
        await queryRunner.query(`ALTER TABLE "clinic_alerts" DROP CONSTRAINT "FK_clinic_alerts_brand"`);
        // Then remove the columns
        await queryRunner.query(`ALTER TABLE "patients" DROP COLUMN "brand_id"`);
        await queryRunner.query(`ALTER TABLE "patient_reminders" DROP COLUMN "brand_id"`);
        await queryRunner.query(`ALTER TABLE "global_reminder_rules" DROP COLUMN "brand_id"`);
        await queryRunner.query(`ALTER TABLE "long-term-medications" DROP COLUMN "brand_id"`);
        await queryRunner.query(`ALTER TABLE "emr" DROP COLUMN "brand_id"`);
        await queryRunner.query(`ALTER TABLE "clinic_rooms" DROP COLUMN "brand_id"`);
        await queryRunner.query(`ALTER TABLE "clinic_plans" DROP COLUMN "brand_id"`);
        await queryRunner.query(`ALTER TABLE "clinic_integrations" DROP COLUMN "brand_id"`);
        await queryRunner.query(`ALTER TABLE "appointments" DROP COLUMN "brand_id"`);
        await queryRunner.query(`ALTER TABLE "appointment_assessments" DROP COLUMN "brand_id"`);
        // Add new constraint drops before the existing ones
        await queryRunner.query(`ALTER TABLE "payment_details" DROP CONSTRAINT "FK_payment_details_brand"`);
        await queryRunner.query(`ALTER TABLE "payment_details" DROP CONSTRAINT "FK_payment_details_clinic"`);
        await queryRunner.query(`ALTER TABLE "patient_owners" DROP CONSTRAINT "FK_patient_owners_brand"`);
        await queryRunner.query(`ALTER TABLE "patient_owners" DROP CONSTRAINT "FK_patient_owners_clinic"`);
        await queryRunner.query(`ALTER TABLE "invoices" DROP CONSTRAINT "FK_invoices_brand"`);
        await queryRunner.query(`ALTER TABLE "invoices" DROP CONSTRAINT "FK_invoices_clinic"`);
        // Add new column drops before the existing ones
        await queryRunner.query(`ALTER TABLE "payment_details" DROP COLUMN "brand_id"`);
        await queryRunner.query(`ALTER TABLE "payment_details" DROP COLUMN "clinic_id"`);
        await queryRunner.query(`ALTER TABLE "patient_owners" DROP COLUMN "brand_id"`);
        await queryRunner.query(`ALTER TABLE "patient_owners" DROP COLUMN "clinic_id"`);
        await queryRunner.query(`ALTER TABLE "invoices" DROP COLUMN "brand_id"`);
        await queryRunner.query(`ALTER TABLE "invoices" DROP COLUMN "clinic_id"`);
        await queryRunner.query(`ALTER TABLE "document_library" DROP COLUMN "brand_id"`);
        await queryRunner.query(`ALTER TABLE "lab_reports" DROP COLUMN "brand_id"`);
        await queryRunner.query(`ALTER TABLE "clinic_alerts" DROP COLUMN "brand_id"`);
    }
}
exports.AddBrandIdToEntities1738566847167 = AddBrandIdToEntities1738566847167;
//# sourceMappingURL=1738566847167-AddBrandIdToEntities.js.map