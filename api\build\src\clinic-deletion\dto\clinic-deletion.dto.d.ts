export declare enum DeletionType {
    CLINIC = "clinic",
    BRAND = "brand"
}
export declare enum DeletionMode {
    DRY_RUN = "dry_run",
    EXECUTE = "execute"
}
export declare enum BackupStatus {
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    FAILED = "failed"
}
export declare enum RestoreMode {
    DRY_RUN = "dry_run",
    EXECUTE = "execute"
}
export declare enum ConflictResolution {
    SKIP = "skip",
    OVERWRITE = "overwrite",
    FAIL = "fail"
}
export declare class DeletionImpactRequestDto {
    type: DeletionType;
    clinicId?: string;
    brandId?: string;
}
export declare class DeletionRequestDto extends DeletionImpactRequestDto {
    mode: DeletionMode;
    skipS3Backup?: boolean;
    confirmationPhrase?: string;
}
export declare class RestoreRequestDto {
    backupId: string;
    mode: RestoreMode;
    conflictResolution?: ConflictResolution;
    skipDatabaseRestore?: boolean;
    skipFileRestore?: boolean;
    confirmationPhrase?: string;
}
export declare class BackupListRequestDto {
    targetType?: DeletionType;
    targetId?: string;
    status?: BackupStatus;
    limit?: number;
    offset?: number;
}
export interface FileReference {
    sourceTable: string;
    fileKey: string;
    createdAt?: Date;
}
export interface DeletionImpactResponse {
    targetInfo: {
        type: DeletionType;
        id: string;
        name: string;
        brandId?: string;
        brandName?: string;
    };
    databaseImpact: {
        tables: Array<{
            tableName: string;
            recordCount: number;
            description: string;
        }>;
        totalRecords: number;
    };
    s3Impact: {
        files: Array<{
            sourceTable: string;
            fileCount: number;
            description: string;
        }>;
        totalFiles: number;
        fileReferences: FileReference[];
    };
    estimatedTime: string;
    warnings: string[];
}
export interface DeletionExecutionResponse {
    success: boolean;
    mode: DeletionMode;
    backupId: string;
    targetInfo: {
        type: DeletionType;
        id: string;
        name: string;
    };
    results: {
        databaseBackup: {
            tablesProcessed: number;
            recordsBackedUp: number;
            duration: string;
        };
        s3Backup?: {
            filesProcessed: number;
            filesBackedUp: number;
            filesSkipped: number;
            totalSizeBytes: number;
            duration: string;
        };
        s3OriginalFileDeletion?: {
            filesProcessed: number;
            filesDeleted: number;
            filesSkipped: number;
            duration: string;
        };
        databaseDeletion: {
            tablesProcessed: number;
            recordsDeleted: number;
            duration: string;
        };
    };
    backup: {
        backupLocation: string;
        backupSize: number;
        estimatedRestoreTime: string;
    };
    audit: {
        executedBy: string;
        executedAt: Date;
    };
    errors?: string[];
}
export interface BackupMetadata {
    backupId: string;
    targetType: DeletionType;
    targetId: string;
    targetName: string;
    createdAt: Date;
    createdBy: string;
    status: BackupStatus;
    backupLocation: string;
    databaseBackup: {
        totalTables: number;
        totalRecords: number;
        tablesBackedUp: string[];
        sizeBytes: number;
    };
    fileBackup: {
        totalFiles: number;
        filesBackedUp: number;
        totalSizeBytes: number;
    };
    estimatedRestoreTime: string;
    expiresAt?: Date;
}
export interface DatabaseManifest {
    backupId: string;
    createdAt: Date;
    tables: Array<{
        tableName: string;
        recordCount: number;
        fileName: string;
        dependencies: string[];
        sizeBytes: number;
    }>;
    restoreOrder: string[];
    totalRecords: number;
    totalSizeBytes: number;
}
export interface FileManifest {
    backupId: string;
    createdAt: Date;
    files: Array<{
        originalPath: string;
        backupPath: string;
        sourceTable: string;
        sizeBytes: number;
        lastModified: Date;
        checksum?: string;
    }>;
    totalFiles: number;
    totalSizeBytes: number;
}
export interface BackupListResponse {
    backups: BackupMetadata[];
    total: number;
    limit: number;
    offset: number;
}
export interface RestoreImpactResponse {
    backupInfo: BackupMetadata;
    conflicts: {
        databaseConflicts: Array<{
            tableName: string;
            conflictingRecords: number;
            conflictType: 'primary_key' | 'unique_constraint' | 'foreign_key';
        }>;
        fileConflicts: Array<{
            originalPath: string;
            conflictType: 'file_exists' | 'path_conflict';
            existingFileSize?: number;
            backupFileSize: number;
        }>;
    };
    estimatedRestoreTime: string;
    warnings: string[];
}
export interface RestoreExecutionResponse {
    success: boolean;
    mode: RestoreMode;
    backupId: string;
    results: {
        databaseRestore: {
            tablesProcessed: number;
            recordsRestored: number;
            recordsSkipped: number;
            duration: string;
        };
        fileRestore?: {
            filesProcessed: number;
            filesRestored: number;
            filesSkipped: number;
            duration: string;
        };
    };
    conflicts: {
        resolved: number;
        skipped: number;
        failed: number;
    };
    audit: {
        executedBy: string;
        executedAt: Date;
        restoredFrom: string;
    };
    errors?: string[];
}
