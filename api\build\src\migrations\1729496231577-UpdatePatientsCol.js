"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddDummyDataToPatients1729496231577 = void 0;
const typeorm_1 = require("typeorm");
class AddDummyDataToPatients1729496231577 {
    async up(queryRunner) {
        await queryRunner.addColumn('patients', new typeorm_1.TableColumn({
            name: 'dummy_data',
            type: 'jsonb',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('patients', 'dummy_data');
    }
}
exports.AddDummyDataToPatients1729496231577 = AddDummyDataToPatients1729496231577;
//# sourceMappingURL=1729496231577-UpdatePatientsCol.js.map