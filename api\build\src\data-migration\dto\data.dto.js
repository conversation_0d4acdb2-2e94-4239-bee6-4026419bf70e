"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulkUpdateOpeningBalanceDto = exports.UpdateOwnerOpeningBalanceDto = exports.MigrateAppointmentDto = exports.DiagnosticsDataDto = exports.InvoiceDataDto = exports.CreateAppointmentDetailsDto = exports.CreateAppointmentDto = exports.CreatePatientOwnerDto = exports.PatientLookupDto = exports.CreatePatientDto = exports.CreateOwnerDto = void 0;
class CreateOwnerDto {
}
exports.CreateOwnerDto = CreateOwnerDto;
class CreatePatientDto {
}
exports.CreatePatientDto = CreatePatientDto;
class PatientLookupDto {
}
exports.PatientLookupDto = PatientLookupDto;
class CreatePatientOwnerDto {
}
exports.CreatePatientOwnerDto = CreatePatientOwnerDto;
class CreateAppointmentDto {
}
exports.CreateAppointmentDto = CreateAppointmentDto;
class CreateAppointmentDetailsDto {
}
exports.CreateAppointmentDetailsDto = CreateAppointmentDetailsDto;
class InvoiceDataDto {
}
exports.InvoiceDataDto = InvoiceDataDto;
class DiagnosticsDataDto {
}
exports.DiagnosticsDataDto = DiagnosticsDataDto;
class MigrateAppointmentDto {
}
exports.MigrateAppointmentDto = MigrateAppointmentDto;
class UpdateOwnerOpeningBalanceDto {
}
exports.UpdateOwnerOpeningBalanceDto = UpdateOwnerOpeningBalanceDto;
class BulkUpdateOpeningBalanceDto {
}
exports.BulkUpdateOpeningBalanceDto = BulkUpdateOpeningBalanceDto;
//# sourceMappingURL=data.dto.js.map