"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentLibraryController = void 0;
const common_1 = require("@nestjs/common");
const document_library_service_1 = require("./document-library.service");
const create_document_library_dto_1 = require("./dto/create-document-library.dto");
const update_document_library_dto_1 = require("./dto/update-document-library.dto");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../roles/roles.decorator");
const role_enum_1 = require("../roles/role.enum");
const swagger_1 = require("@nestjs/swagger");
const api_documentation_base_1 = require("../base/api-documentation-base");
const document_library_entity_1 = require("./entities/document-library.entity");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
let DocumentLibraryController = class DocumentLibraryController extends api_documentation_base_1.ApiDocumentationBase {
    constructor(documentLibraryService, logger) {
        super();
        this.documentLibraryService = documentLibraryService;
        this.logger = logger;
    }
    async create(createDocumentLibraryDto, req) {
        this.logger.log('Received request to create document library entry.', {
            dto: createDocumentLibraryDto
        });
        const result = await this.documentLibraryService.create(createDocumentLibraryDto, req.user.brandId);
        this.logger.log('Document library entry created successfully.', {
            id: result.id
        });
        return result;
    }
    async findAll(page = 1, limit = 10, clinicId, search, orderBy = 'DESC') {
        try {
            this.logger.log('Fetching documents', {
                page,
                limit,
                search,
                orderBy
            });
            const result = await this.documentLibraryService.findAll(clinicId, page, limit, search, orderBy);
            this.logger.log('Documents retrieved successfully', {
                count: result.documents.length
            });
            return result;
        }
        catch (error) {
            this.logger.error('Error fetching documents', { error });
            throw new common_1.InternalServerErrorException('Failed to fetch documents');
        }
    }
    async findOne(id) {
        this.logger.log(`Received request to retrieve document library entry with ID: ${id}`);
        const result = await this.documentLibraryService.findOne(id);
        this.logger.log(`Successfully retrieved document library entry with ID: ${id}`);
        return result;
    }
    async update(id, updateDocumentLibraryDto) {
        this.logger.log(`Received request to update document library entry with ID: ${id}`, {
            dto: updateDocumentLibraryDto
        });
        const result = await this.documentLibraryService.update(id, updateDocumentLibraryDto);
        this.logger.log(`Successfully updated document library entry with ID: ${id}`);
        return result;
    }
    async remove(id) {
        this.logger.log(`Received request to delete document library entry with ID: ${id}`);
        await this.documentLibraryService.remove(id);
        this.logger.log(`Successfully deleted document library entry with ID: ${id}`);
    }
};
exports.DocumentLibraryController = DocumentLibraryController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new document library entry' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'The document library entry has been successfully created.',
        type: document_library_entity_1.DocumentLibrary
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data.'
    }),
    (0, track_method_decorator_1.TrackMethod)('create-document-library'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_document_library_dto_1.CreateDocumentLibraryDto, Object]),
    __metadata("design:returntype", Promise)
], DocumentLibraryController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, track_method_decorator_1.TrackMethod)('findAll-document-library'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('clinicId')),
    __param(3, (0, common_1.Query)('search')),
    __param(4, (0, common_1.Query)('orderBy')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String, String]),
    __metadata("design:returntype", Promise)
], DocumentLibraryController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Retrieve a document library entry by ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'The requested document library entry.',
        type: document_library_entity_1.DocumentLibrary
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Document library entry not found.'
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('findOne-document-library'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DocumentLibraryController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a document library entry by ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'The document library entry has been successfully updated.',
        type: document_library_entity_1.DocumentLibrary
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Document library entry not found.'
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('update-document-library'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_document_library_dto_1.UpdateDocumentLibraryDto]),
    __metadata("design:returntype", Promise)
], DocumentLibraryController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a document library entry by ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NO_CONTENT,
        description: 'The document library entry has been successfully deleted.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Document library entry not found.'
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.LAB_TECHNICIAN, role_enum_1.Role.RECEPTIONIST),
    (0, track_method_decorator_1.TrackMethod)('remove-document-library'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], DocumentLibraryController.prototype, "remove", null);
exports.DocumentLibraryController = DocumentLibraryController = __decorate([
    (0, swagger_1.ApiTags)('document-library'),
    (0, common_1.Controller)('document-library'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [document_library_service_1.DocumentLibraryService,
        winston_logger_service_1.WinstonLogger])
], DocumentLibraryController);
//# sourceMappingURL=document-library.controller.js.map