"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddClinicIdToClinicRooms1722938707862 = void 0;
const typeorm_1 = require("typeorm");
class AddClinicIdToClinicRooms1722938707862 {
    async up(queryRunner) {
        await queryRunner.addColumns('clinic_rooms', [
            new typeorm_1.TableColumn({
                name: 'clinic_id',
                type: 'uuid',
                isNullable: true
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('clinic_rooms', 'clinic_id');
    }
}
exports.AddClinicIdToClinicRooms1722938707862 = AddClinicIdToClinicRooms1722938707862;
//# sourceMappingURL=1722938707862-AddClinicIdToClinicRooms.js.map