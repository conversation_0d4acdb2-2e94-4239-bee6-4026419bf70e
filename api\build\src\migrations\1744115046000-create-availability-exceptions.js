"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAvailabilityExceptions1744115046000 = void 0;
class CreateAvailabilityExceptions1744115046000 {
    constructor() {
        this.name = 'CreateAvailabilityExceptions1744115046000';
    }
    async up(queryRunner) {
        // First create the enum type
        await queryRunner.query(`
      CREATE TYPE "public"."exception_type_enum" AS ENUM('out-of-office', 'additional-hours')
    `);
        // Then create the table
        await queryRunner.query(`
      CREATE TABLE "availability_exceptions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "clinic_user_id" uuid NOT NULL,
        "start_date" date NOT NULL,
        "end_date" date,
        "type" "public"."exception_type_enum" NOT NULL DEFAULT 'out-of-office',
        "is_full_day" boolean NOT NULL DEFAULT false,
        "times" jsonb,
        "created_by" uuid,
        "updated_by" uuid,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_availability_exceptions" PRIMARY KEY ("id")
      )
    `);
        // Add the foreign key constraint
        await queryRunner.query(`
      ALTER TABLE "availability_exceptions" 
      ADD CONSTRAINT "FK_availability_exceptions_clinic_user"
      FOREIGN KEY ("clinic_user_id") 
      REFERENCES "clinic_users"("id") 
      ON DELETE CASCADE
    `);
    }
    async down(queryRunner) {
        // Drop the constraints first
        await queryRunner.query(`
      ALTER TABLE "availability_exceptions" 
      DROP CONSTRAINT "FK_availability_exceptions_clinic_user"
    `);
        // Drop the table
        await queryRunner.query(`
      DROP TABLE "availability_exceptions"
    `);
        // Drop the enum type
        await queryRunner.query(`
      DROP TYPE "public"."exception_type_enum"
    `);
    }
}
exports.CreateAvailabilityExceptions1744115046000 = CreateAvailabilityExceptions1744115046000;
//# sourceMappingURL=1744115046000-create-availability-exceptions.js.map