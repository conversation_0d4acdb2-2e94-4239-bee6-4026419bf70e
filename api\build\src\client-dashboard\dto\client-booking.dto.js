"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientBookingResponseDto = exports.UpdateClientBookingDto = exports.CreateClientBookingDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const enum_appointment_status_1 = require("../../appointments/enums/enum-appointment-status");
/**
 * DTO for creating a new client booking
 */
class CreateClientBookingDto {
    constructor() {
        this.petId = '';
        this.doctorId = '';
        this.clinicId = '';
        this.startTime = '';
        this.endTime = '';
    }
}
exports.CreateClientBookingDto = CreateClientBookingDto;
__decorate([
    (0, class_validator_1.IsUUID)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID of the pet',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    __metadata("design:type", String)
], CreateClientBookingDto.prototype, "petId", void 0);
__decorate([
    (0, class_validator_1.IsUUID)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID of the doctor',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    __metadata("design:type", String)
], CreateClientBookingDto.prototype, "doctorId", void 0);
__decorate([
    (0, class_validator_1.IsUUID)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID of the clinic',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    __metadata("design:type", String)
], CreateClientBookingDto.prototype, "clinicId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The date of an appointment'
    }),
    (0, class_validator_1.IsNotEmpty)()
    // @IsDate()
    ,
    __metadata("design:type", String)
], CreateClientBookingDto.prototype, "date", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        description: 'Start time of the appointment (HH:MM)',
        example: '09:00'
    }),
    __metadata("design:type", String)
], CreateClientBookingDto.prototype, "startTime", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        description: 'End time of the appointment (HH:MM)',
        example: '09:30'
    }),
    __metadata("design:type", String)
], CreateClientBookingDto.prototype, "endTime", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, swagger_1.ApiProperty)({
        description: 'Optional notes for the appointment',
        required: false,
        example: 'First-time checkup'
    }),
    __metadata("design:type", String)
], CreateClientBookingDto.prototype, "reason", void 0);
/**
 * DTO for updating a client booking (reschedule or cancel)
 */
class UpdateClientBookingDto {
}
exports.UpdateClientBookingDto = UpdateClientBookingDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The date of an appointment'
    }),
    (0, class_validator_1.IsNotEmpty)()
    // @IsDate()
    ,
    __metadata("design:type", String)
], UpdateClientBookingDto.prototype, "date", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, swagger_1.ApiProperty)({
        description: 'New start time of the appointment (HH:MM)',
        required: false,
        example: '10:00'
    }),
    __metadata("design:type", String)
], UpdateClientBookingDto.prototype, "startTime", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    (0, swagger_1.ApiProperty)({
        description: 'New end time of the appointment (HH:MM)',
        required: false,
        example: '10:30'
    }),
    __metadata("design:type", String)
], UpdateClientBookingDto.prototype, "endTime", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsEnum)(enum_appointment_status_1.EnumAppointmentStatus),
    (0, class_validator_1.IsOptional)(),
    (0, swagger_1.ApiProperty)({
        description: 'New status of the appointment (Note: "Cancelled" will trigger soft-deletion by setting deleted_at)',
        required: false,
        enum: enum_appointment_status_1.EnumAppointmentStatus,
        example: enum_appointment_status_1.EnumAppointmentStatus.Cancelled
    }),
    __metadata("design:type", String)
], UpdateClientBookingDto.prototype, "status", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, swagger_1.ApiProperty)({
        description: 'ID of the doctor',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    __metadata("design:type", String)
], UpdateClientBookingDto.prototype, "doctorId", void 0);
/**
 * DTO for client booking response
 */
class ClientBookingResponseDto {
    constructor() {
        this.id = '';
        this.petId = '';
        this.petName = '';
        this.doctorId = '';
        this.doctorName = '';
        this.clinicId = '';
        this.clinicName = '';
        this.date = '';
        this.startTime = '';
        this.endTime = '';
        this.status = '';
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }
}
exports.ClientBookingResponseDto = ClientBookingResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the booking',
        example: '123e4567-e89b-12d3-a456-426614174003'
    }),
    __metadata("design:type", String)
], ClientBookingResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the pet',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    __metadata("design:type", String)
], ClientBookingResponseDto.prototype, "petId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the pet',
        example: 'Max'
    }),
    __metadata("design:type", String)
], ClientBookingResponseDto.prototype, "petName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the doctor',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    __metadata("design:type", String)
], ClientBookingResponseDto.prototype, "doctorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the doctor',
        example: 'Dr. Smith'
    }),
    __metadata("design:type", String)
], ClientBookingResponseDto.prototype, "doctorName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the clinic',
        example: '123e4567-e89b-12d3-a456-************'
    }),
    __metadata("design:type", String)
], ClientBookingResponseDto.prototype, "clinicId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the clinic',
        example: 'Pet Care Clinic'
    }),
    __metadata("design:type", String)
], ClientBookingResponseDto.prototype, "clinicName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Date of the appointment',
        example: '2023-06-01'
    }),
    __metadata("design:type", String)
], ClientBookingResponseDto.prototype, "date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Start time of the appointment',
        example: '09:00'
    }),
    __metadata("design:type", String)
], ClientBookingResponseDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'End time of the appointment',
        example: '09:30'
    }),
    __metadata("design:type", String)
], ClientBookingResponseDto.prototype, "endTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status of the appointment',
        enum: enum_appointment_status_1.EnumAppointmentStatus,
        example: enum_appointment_status_1.EnumAppointmentStatus.Scheduled
    }),
    __metadata("design:type", String)
], ClientBookingResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional notes for the appointment',
        required: false,
        example: 'First-time checkup'
    }),
    __metadata("design:type", String)
], ClientBookingResponseDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'When the booking was created',
        example: '2023-05-15T12:00:00Z'
    }),
    __metadata("design:type", Date)
], ClientBookingResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'When the booking was last updated',
        example: '2023-05-15T12:00:00Z'
    }),
    __metadata("design:type", Date)
], ClientBookingResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=client-booking.dto.js.map