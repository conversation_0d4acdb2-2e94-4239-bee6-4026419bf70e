"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUsersTable1721818436773 = void 0;
const typeorm_1 = require("typeorm");
class CreateUsersTable1721818436773 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'users',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'email',
                    type: 'varchar',
                    length: '255',
                    isUnique: true,
                    isNullable: false
                },
                {
                    name: 'phone_number',
                    type: 'varchar',
                    length: '20',
                    isNullable: true
                },
                {
                    name: 'pin',
                    type: 'varchar',
                    length: '255',
                    isNullable: true
                },
                {
                    name: 'role',
                    type: 'enum',
                    enum: [
                        'super_admin',
                        'admin',
                        'receptionist',
                        'doctor',
                        'vet_technician',
                        'lab_technician'
                    ],
                    default: "'receptionist'"
                },
                {
                    name: 'is_active',
                    type: 'boolean',
                    default: true
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP'
                }
            ]
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('users');
    }
}
exports.CreateUsersTable1721818436773 = CreateUsersTable1721818436773;
//# sourceMappingURL=1721818436773-CreateUsersTable.js.map