"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeleteDuplicatePatientOwners1739959606738 = void 0;
class DeleteDuplicatePatientOwners1739959606738 {
    async up(queryRunner) {
        // Create temporary table to store duplicates to be deleted
        await queryRunner.query(`
            CREATE TEMPORARY TABLE duplicates_to_delete AS
            SELECT po1.id as id_to_delete
            FROM public.patient_owners po1
            JOIN public.patient_owners po2 
                ON po1.patient_id = po2.patient_id 
                AND po1.owner_id = po2.owner_id
                AND po1.id <> po2.id
            WHERE po1.is_primary = FALSE 
                AND po2.is_primary = TRUE;
        `);
        // Delete the duplicate records
        await queryRunner.query(`
            DELETE FROM public.patient_owners
            WHERE id IN (
                SELECT id_to_delete 
                FROM duplicates_to_delete
            );
        `);
        // Drop temporary table
        await queryRunner.query(`DROP TABLE duplicates_to_delete;`);
    }
    async down(_queryRunner) {
        throw new Error('This migration cannot be reverted as it removes duplicate data');
    }
}
exports.DeleteDuplicatePatientOwners1739959606738 = DeleteDuplicatePatientOwners1739959606738;
//# sourceMappingURL=1739959606738-DeleteDuplicatePatientOwners.js.map