"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePatientsTable1721892832251 = void 0;
const typeorm_1 = require("typeorm");
class UpdatePatientsTable1721892832251 {
    async up(queryRunner) {
        await queryRunner.renameColumn('patients', 'name', 'patient_name');
        await queryRunner.dropColumn('patients', 'owner_ids');
        await queryRunner.addColumn('patients', new typeorm_1.TableColumn({
            name: 'secondary_owner_id',
            type: 'uuid',
            isNullable: true
        }));
        await queryRunner.createForeignKey('patients', new typeorm_1.TableForeignKey({
            columnNames: ['secondary_owner_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'owners',
            onDelete: 'SET NULL'
        }));
    }
    async down(queryRunner) {
        await queryRunner.renameColumn('patients', 'patient_name', 'name');
        await queryRunner.dropForeignKey('patients', 'secondary_owner_id');
        await queryRunner.dropColumn('patients', 'secondary_owner_id');
        await queryRunner.addColumn('patients', new typeorm_1.TableColumn({
            name: 'owner_ids',
            type: 'uuid',
            isArray: true,
            isNullable: false
        }));
    }
}
exports.UpdatePatientsTable1721892832251 = UpdatePatientsTable1721892832251;
//# sourceMappingURL=1721892832251-UpdatePatientsTable.js.map