"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PetTransferModule = void 0;
const common_1 = require("@nestjs/common");
const pet_transfer_controller_1 = require("./pet-transfer.controller");
const pet_transfer_service_1 = require("./pet-transfer.service");
const query_manager_service_1 = require("./services/query-manager.service");
const inventory_mapping_service_1 = require("./services/inventory-mapping.service");
const typeorm_1 = require("@nestjs/typeorm");
const clinic_service_entity_1 = require("../clinic-services/entities/clinic-service.entity");
const clinic_product_entity_1 = require("../clinic-products/entities/clinic-product.entity");
const clinic_medication_entity_1 = require("../clinic-medications/entities/clinic-medication.entity");
const clinic_consumable_entity_1 = require("../clinic-consumables/entities/clinic-consumable.entity");
const clinic_vaccination_entity_1 = require("../clinic-vaccinations/entities/clinic-vaccination.entity");
const clinic_lab_report_entity_1 = require("../clinic-lab-report/entities/clinic-lab-report.entity");
const inventory_mapping_entity_1 = require("./entities/inventory-mapping.entity");
const appointment_assessment_entity_1 = require("../appointment-assessment/entities/appointment-assessment.entity");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
const appointment_details_entity_1 = require("../appointments/entities/appointment-details.entity");
const cart_entity_1 = require("../carts/entites/cart.entity");
const cart_item_entity_1 = require("../cart-items/entities/cart-item.entity");
let PetTransferModule = class PetTransferModule {
};
exports.PetTransferModule = PetTransferModule;
exports.PetTransferModule = PetTransferModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                clinic_service_entity_1.ClinicServiceEntity,
                clinic_product_entity_1.ClinicProductEntity,
                clinic_medication_entity_1.ClinicMedicationEntity,
                clinic_consumable_entity_1.ClinicConsumableEntity,
                clinic_vaccination_entity_1.ClinicVaccinationEntity,
                clinic_lab_report_entity_1.ClinicLabReport,
                inventory_mapping_entity_1.InventoryMapping,
                appointment_assessment_entity_1.AppointmentAssessmentEntity,
                invoice_entity_1.InvoiceEntity,
                appointment_details_entity_1.AppointmentDetailsEntity,
                cart_entity_1.CartEntity,
                cart_item_entity_1.CartItemEntity
            ])
        ],
        controllers: [pet_transfer_controller_1.PetTransferController],
        providers: [
            pet_transfer_service_1.PetTransferService,
            query_manager_service_1.PetTransferQueryManagerService,
            inventory_mapping_service_1.InventoryMappingService
        ]
    })
], PetTransferModule);
//# sourceMappingURL=pet-transfer.module.js.map