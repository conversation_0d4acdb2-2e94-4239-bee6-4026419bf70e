import { DeletionType } from '../dto/clinic-deletion.dto';
export interface QueryDefinition {
    sql: string;
    params: any[];
    description?: string;
    dependencies?: string[];
    requiresConstraintHandling?: boolean;
}
export interface QuerySet {
    [tableName: string]: QueryDefinition;
}
/**
 * Centralized query manager for all clinic deletion operations
 * This ensures consistency across impact analysis, backup, and deletion operations
 */
export declare class QueryManagerService {
    /**
     * Get database queries for impact analysis (COUNT queries)
     */
    getDatabaseAnalysisQueries(targetType: DeletionType, targetId: string): QuerySet;
    /**
     * Get database queries for backup operations (SELECT * queries)
     * Excludes no-op queries to maintain consistency with analysis and deletion
     */
    getDatabaseBackupQueries(targetType: DeletionType, targetId: string): QuerySet;
    /**
     * Get database queries for deletion operations (DELETE queries)
     */
    getDatabaseDeletionQueries(targetType: DeletionType, targetId: string): QuerySet;
    /**
     * Get S3 file queries for analysis (with file metadata)
     */
    getS3AnalysisQueries(targetType: DeletionType, targetId: string): QuerySet;
    /**
     * Get S3 file queries for backup operations (simple file keys)
     */
    getS3BackupQueries(targetType: DeletionType, targetId: string): QuerySet;
    /**
     * Get database queries for restore conflict detection
     * These check if records already exist that would conflict with restore
     */
    getDatabaseRestoreConflictQueries(targetType: DeletionType, targetId: string): QuerySet;
    /**
     * Get database queries for restore operations (INSERT queries)
     * These will be generated dynamically based on the backup data structure
     */
    getDatabaseRestoreInsertQueries(tableName: string, columns: string[], recordCount: number): QueryDefinition;
    /**
     * Get a single INSERT query for restore operations
     * This is for inserting one record at a time with proper error handling
     */
    getDatabaseRestoreSingleInsertQuery(tableName: string, columns: string[]): QueryDefinition;
    /**
     * Get UPSERT query for restore operations (INSERT ... ON CONFLICT)
     * This handles cases where records might already exist
     */
    getDatabaseRestoreUpsertQuery(tableName: string, columns: string[], conflictColumns?: string[], updateColumns?: string[]): QueryDefinition;
    /**
     * Get database queries for restore validation
     * These verify that the restore was successful
     */
    getDatabaseRestoreValidationQueries(targetType: DeletionType, targetId: string): QuerySet;
    /**
     * Get S3 file queries for restore conflict detection
     */
    getS3RestoreConflictQueries(targetType: DeletionType, targetId: string): QuerySet;
    /**
     * Get the proper restore order (parents first, then children)
     * This is the reverse of deletion order to maintain referential integrity
     */
    getRestoreOrder(): string[];
    /**
     * Core method that defines all database table queries
     * This is the single source of truth for all table queries
     */
    private getDatabaseTableQueries;
    /**
     * Core method that defines all S3 file queries
     * This is the single source of truth for all S3 file queries
     */
    private getS3FileQueries;
    /**
     * Convert SELECT queries with JOINs to proper PostgreSQL DELETE syntax
     * PostgreSQL requires DELETE FROM table WHERE id IN (subquery) format
     */
    private convertSelectWithJoinsToDelete;
    /**
     * Get base condition for queries based on deletion type
     */
    private getBaseCondition;
    /**
     * Get proper deletion order (children first, then parents)
     * This order is critical to avoid foreign key constraint violations
     * Uses topological sort to ensure dependencies are respected
     */
    private getDeletionOrder;
    /**
     * Validate that all query sets have consistent table coverage
     */
    validateQueryConsistency(targetType: DeletionType, targetId: string): {
        valid: boolean;
        errors: string[];
        warnings: string[];
    };
    /**
     * Detects if a SQL query is a no-op query that should be skipped during deletion
     * No-op queries are designed to return no results and don't need corresponding DELETE statements
     */
    private isNoOpQuery;
    /**
     * Generate special deletion queries for tables with foreign key constraints
     * This handles cases where direct deletion would violate foreign key constraints
     */
    private generateConstraintHandlingQuery;
}
