{"version": 3, "file": "client-booking.dto.js", "sourceRoot": "", "sources": ["../../../../src/client-dashboard/dto/client-booking.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,qDAMyB;AACzB,8FAAyF;AAEzF;;GAEG;AACH,MAAa,sBAAsB;IAAnC;QAMC,UAAK,GAAW,EAAE,CAAC;QAOnB,aAAQ,GAAW,EAAE,CAAC;QAOtB,aAAQ,GAAW,EAAE,CAAC;QActB,cAAS,GAAW,EAAE,CAAC;QAOvB,YAAO,GAAW,EAAE,CAAC;IAUtB,CAAC;CAAA;AAnDD,wDAmDC;AA7CA;IALC,IAAA,wBAAM,GAAE;IACR,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,eAAe;QAC5B,OAAO,EAAE,sCAAsC;KAC/C,CAAC;;qDACiB;AAOnB;IALC,IAAA,wBAAM,GAAE;IACR,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE,sCAAsC;KAC/C,CAAC;;wDACoB;AAOtB;IALC,IAAA,wBAAM,GAAE;IACR,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE,sCAAsC;KAC/C,CAAC;;wDACoB;AAOtB;IALC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,4BAA4B;KACzC,CAAC;IACD,IAAA,4BAAU,GAAE;IACb,YAAY;;;oDACE;AAOd;IALC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,uCAAuC;QACpD,OAAO,EAAE,OAAO;KAChB,CAAC;;yDACqB;AAOvB;IALC,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,qCAAqC;QAClD,OAAO,EAAE,OAAO;KAChB,CAAC;;uDACmB;AASrB;IAPC,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;IACZ,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,oCAAoC;QACjD,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,oBAAoB;KAC7B,CAAC;;sDACc;AAGjB;;GAEG;AACH,MAAa,sBAAsB;CA6ClC;AA7CD,wDA6CC;AAvCA;IALC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,4BAA4B;KACzC,CAAC;IACD,IAAA,4BAAU,GAAE;IACb,YAAY;;;oDACE;AASd;IAPC,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;IACZ,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,2CAA2C;QACxD,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,OAAO;KAChB,CAAC;;yDACiB;AASnB;IAPC,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;IACZ,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,yCAAyC;QACtD,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,OAAO;KAChB,CAAC;;uDACe;AAYjB;IAVC,IAAA,0BAAQ,GAAE;IACV,IAAA,wBAAM,EAAC,+CAAqB,CAAC;IAC7B,IAAA,4BAAU,GAAE;IACZ,IAAA,qBAAW,EAAC;QACZ,WAAW,EACV,oGAAoG;QACrG,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,+CAAqB;QAC3B,OAAO,EAAE,+CAAqB,CAAC,SAAS;KACxC,CAAC;;sDAC6B;AAQ/B;IANC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE,sCAAsC;KAC/C,CAAC;;wDACgB;AAGnB;;GAEG;AACH,MAAa,wBAAwB;IAArC;QAKC,OAAE,GAAW,EAAE,CAAC;QAMhB,UAAK,GAAW,EAAE,CAAC;QAMnB,YAAO,GAAW,EAAE,CAAC;QAMrB,aAAQ,GAAW,EAAE,CAAC;QAMtB,eAAU,GAAW,EAAE,CAAC;QAMxB,aAAQ,GAAW,EAAE,CAAC;QAMtB,eAAU,GAAW,EAAE,CAAC;QAMxB,SAAI,GAAW,EAAE,CAAC;QAMlB,cAAS,GAAW,EAAE,CAAC;QAMvB,YAAO,GAAW,EAAE,CAAC;QAOrB,WAAM,GAAW,EAAE,CAAC;QAapB,cAAS,GAAS,IAAI,IAAI,EAAE,CAAC;QAM7B,cAAS,GAAS,IAAI,IAAI,EAAE,CAAC;IAC9B,CAAC;CAAA;AAtFD,4DAsFC;AAjFA;IAJC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,mBAAmB;QAChC,OAAO,EAAE,sCAAsC;KAC/C,CAAC;;oDACc;AAMhB;IAJC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,eAAe;QAC5B,OAAO,EAAE,sCAAsC;KAC/C,CAAC;;uDACiB;AAMnB;IAJC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,iBAAiB;QAC9B,OAAO,EAAE,KAAK;KACd,CAAC;;yDACmB;AAMrB;IAJC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE,sCAAsC;KAC/C,CAAC;;0DACoB;AAMtB;IAJC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,oBAAoB;QACjC,OAAO,EAAE,WAAW;KACpB,CAAC;;4DACsB;AAMxB;IAJC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE,sCAAsC;KAC/C,CAAC;;0DACoB;AAMtB;IAJC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,oBAAoB;QACjC,OAAO,EAAE,iBAAiB;KAC1B,CAAC;;4DACsB;AAMxB;IAJC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,yBAAyB;QACtC,OAAO,EAAE,YAAY;KACrB,CAAC;;sDACgB;AAMlB;IAJC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,+BAA+B;QAC5C,OAAO,EAAE,OAAO;KAChB,CAAC;;2DACqB;AAMvB;IAJC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,6BAA6B;QAC1C,OAAO,EAAE,OAAO;KAChB,CAAC;;yDACmB;AAOrB;IALC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,2BAA2B;QACxC,IAAI,EAAE,+CAAqB;QAC3B,OAAO,EAAE,+CAAqB,CAAC,SAAS;KACxC,CAAC;;wDACkB;AAOpB;IALC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,oCAAoC;QACjD,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,oBAAoB;KAC7B,CAAC;;uDACa;AAMf;IAJC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,8BAA8B;QAC3C,OAAO,EAAE,sBAAsB;KAC/B,CAAC;8BACS,IAAI;2DAAc;AAM7B;IAJC,IAAA,qBAAW,EAAC;QACZ,WAAW,EAAE,mCAAmC;QAChD,OAAO,EAAE,sBAAsB;KAC/B,CAAC;8BACS,IAAI;2DAAc"}