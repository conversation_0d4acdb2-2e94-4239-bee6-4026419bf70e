"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUsersTable1723050195290 = void 0;
const typeorm_1 = require("typeorm");
class UpdateUsersTable1723050195290 {
    constructor() {
        this.name = 'UpdateUsersTable1723050195290';
    }
    async up(queryRunner) {
        await queryRunner.changeColumn('users', 'pin', new typeorm_1.TableColumn({
            name: 'pin',
            type: 'varchar',
            isNullable: true,
            length: '60'
        }));
    }
    async down(queryRunner) {
        await queryRunner.changeColumn('users', 'pin', new typeorm_1.TableColumn({
            name: 'pin',
            type: 'varchar',
            isNullable: true,
            length: '4'
        }));
    }
}
exports.UpdateUsersTable1723050195290 = UpdateUsersTable1723050195290;
//# sourceMappingURL=1723050195290-UpdateUsersTable.js.map