"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RemoveColumnsFromClinicProduct1735535812730 = void 0;
const typeorm_1 = require("typeorm");
class RemoveColumnsFromClinicProduct1735535812730 {
    constructor() {
        this.name = 'RemoveColumnsFromClinicProduct1735535812730';
    }
    async up(queryRunner) {
        await queryRunner.dropColumn('clinic_products', 'purchase_price');
        await queryRunner.dropColumn('clinic_products', 'reorder_value');
        await queryRunner.dropColumn('clinic_products', 'description');
    }
    async down(queryRunner) {
        await queryRunner.addColumn('clinic_products', new typeorm_1.TableColumn({
            name: 'purchase_price',
            type: 'decimal'
        }));
        await queryRunner.addColumn('clinic_products', new typeorm_1.TableColumn({
            name: 'reorder_value',
            type: 'int'
        }));
        await queryRunner.addColumn('clinic_products', new typeorm_1.TableColumn({
            name: 'description',
            type: 'varchar'
        }));
    }
}
exports.RemoveColumnsFromClinicProduct1735535812730 = RemoveColumnsFromClinicProduct1735535812730;
//# sourceMappingURL=1735535812730-RemoveColumnsFromClinicProduct.js.map