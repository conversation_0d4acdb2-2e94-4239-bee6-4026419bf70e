"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MakeVaccinationIdNullableInPatientVaccinations1752470526477 = void 0;
class MakeVaccinationIdNullableInPatientVaccinations1752470526477 {
    async up(queryRunner) {
        // Drop the foreign key constraint first
        await queryRunner.query(`
            ALTER TABLE "patient_vaccinations"
            DROP CONSTRAINT IF EXISTS "FK_patient_vaccinations_vaccination_id"
        `);
        // Make vaccination_id column nullable
        await queryRunner.query(`
            ALTER TABLE "patient_vaccinations"
            ALTER COLUMN "vaccination_id" DROP NOT NULL
        `);
        // Re-add the foreign key constraint but allow NULL values
        await queryRunner.query(`
            ALTER TABLE "patient_vaccinations"
            ADD CONSTRAINT "FK_patient_vaccinations_vaccination_id"
            FOREIGN KEY ("vaccination_id")
            REFERENCES "clinic_vaccinations"("id")
            ON DELETE SET NULL ON UPDATE CASCADE
        `);
    }
    async down(queryRunner) {
        // Drop the foreign key constraint
        await queryRunner.query(`
            ALTER TABLE "patient_vaccinations"
            DROP CONSTRAINT IF EXISTS "FK_patient_vaccinations_vaccination_id"
        `);
        // Make vaccination_id column NOT NULL again
        // Note: This will fail if there are NULL values in the column
        await queryRunner.query(`
            ALTER TABLE "patient_vaccinations"
            ALTER COLUMN "vaccination_id" SET NOT NULL
        `);
        // Re-add the original foreign key constraint
        await queryRunner.query(`
            ALTER TABLE "patient_vaccinations"
            ADD CONSTRAINT "FK_patient_vaccinations_vaccination_id"
            FOREIGN KEY ("vaccination_id")
            REFERENCES "clinic_vaccinations"("id")
            ON DELETE RESTRICT ON UPDATE CASCADE
        `);
    }
}
exports.MakeVaccinationIdNullableInPatientVaccinations1752470526477 = MakeVaccinationIdNullableInPatientVaccinations1752470526477;
//# sourceMappingURL=1752470526477-MakeVaccinationIdNullableInPatientVaccinations.js.map