"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddReminderTrackingColumn1744964035790 = void 0;
class AddReminderTrackingColumn1744964035790 {
    async up(queryRunner) {
        // Add reminder_tracking column to appointments table
        await queryRunner.query(`
            ALTER TABLE appointments
            ADD COLUMN IF NOT EXISTS reminder_tracking JSONB DEFAULT '{}'::jsonb;
        `);
        // Add comment for better documentation
        await queryRunner.query(`
            COMMENT ON COLUMN appointments.reminder_tracking IS 'Tracks reminder delivery status including email and WhatsApp notifications';
        `);
    }
    async down(queryRunner) {
        // Remove reminder_tracking column in case of rollback
        await queryRunner.query(`
            ALTER TABLE appointments
            DROP COLUMN IF EXISTS reminder_tracking;
        `);
    }
}
exports.AddReminderTrackingColumn1744964035790 = AddReminderTrackingColumn1744964035790;
//# sourceMappingURL=1744964035790-AddReminderTrackingColumn.js.map