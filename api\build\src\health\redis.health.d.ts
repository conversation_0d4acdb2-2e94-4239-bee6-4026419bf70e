import { HealthIndicator, HealthIndicatorResult } from '@nestjs/terminus';
import { RedisService } from '../utils/redis/redis.service';
/**
 * Enhanced Redis Health Indicator with comprehensive cluster monitoring
 * Provides detailed information about Redis single-node or cluster configurations
 */
export declare class RedisHealthIndicator extends HealthIndicator {
    private readonly redisService;
    private readonly logger;
    constructor(redisService: RedisService);
    /**
     * Basic health check - ping test for backward compatibility
     */
    isHealthy(key: string): Promise<HealthIndicatorResult>;
    /**
     * Comprehensive cluster health check with detailed information
     * Returns cluster mode, node status, performance metrics, and connectivity info
     */
    getClusterHealth(key: string): Promise<HealthIndicatorResult>;
    /**
     * Get cluster information and status
     */
    private getClusterInfo;
    /**
     * Get individual cluster node health status
     */
    private getClusterNodesHealth;
    /**
     * Get cluster performance statistics
     */
    private getClusterStats;
    /**
     * Get single node information
     */
    private getSingleNodeInfo;
    /**
     * Calculate cache hit rate percentage
     */
    private calculateHitRate;
}
