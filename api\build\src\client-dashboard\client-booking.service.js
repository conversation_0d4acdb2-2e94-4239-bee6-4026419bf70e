"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ClientBookingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientBookingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const moment = require("moment");
const appointments_service_1 = require("../appointments/appointments.service");
const appointment_entity_1 = require("../appointments/entities/appointment.entity");
const enum_appointment_mode_1 = require("../appointments/enums/enum-appointment-mode");
const enum_appointment_status_1 = require("../appointments/enums/enum-appointment-status");
const enum_appointment_type_1 = require("../appointments/enums/enum-appointment-type");
const patient_entity_1 = require("../patients/entities/patient.entity");
const user_entity_1 = require("../users/entities/user.entity");
const clinic_entity_1 = require("../clinics/entities/clinic.entity");
const clinic_user_entity_1 = require("../clinics/entities/clinic-user.entity");
const client_availability_service_1 = require("./client-availability.service");
const patients_service_1 = require("../patients/patients.service");
const appointment_audit_log_entity_1 = require("../audit/entities/appointment-audit-log.entity");
const clinic_service_1 = require("../clinics/clinic.service");
// Helper function (can be moved to a utils file)
function timeDurationToMinutes(duration) {
    if (!duration)
        return null;
    const days = duration.days || 0;
    const hours = duration.hours || 0;
    const minutes = duration.minutes || 0;
    const totalMinutes = days * 24 * 60 + hours * 60 + minutes;
    return totalMinutes > 0 ? totalMinutes : null;
}
let ClientBookingService = ClientBookingService_1 = class ClientBookingService {
    constructor(appointmentsRepository, patientsRepository, usersRepository, clinicsRepository, clinicUsersRepository, appointmentsService, clientAvailabilityService, patientsService, clinicService, auditLogRepository) {
        this.appointmentsRepository = appointmentsRepository;
        this.patientsRepository = patientsRepository;
        this.usersRepository = usersRepository;
        this.clinicsRepository = clinicsRepository;
        this.clinicUsersRepository = clinicUsersRepository;
        this.appointmentsService = appointmentsService;
        this.clientAvailabilityService = clientAvailabilityService;
        this.patientsService = patientsService;
        this.clinicService = clinicService;
        this.auditLogRepository = auditLogRepository;
        this.logger = new common_1.Logger(ClientBookingService_1.name);
    }
    /**
     * Private helper to log audit events
     * @param appointmentId ID of the affected appointment
     * @param userId ID of the user performing the action
     * @param action The action performed
     * @param changedFields Details of the changes (optional)
     * @param context Additional context (optional)
     */
    async _logAuditEvent(appointmentId, userId, action, changedFields = null, context = null) {
        try {
            const logEntry = this.auditLogRepository.create({
                appointmentId,
                userId,
                action,
                changedFields,
                context,
                timestamp: new Date()
            });
            await this.auditLogRepository.save(logEntry);
        }
        catch (error) {
            this.logger.error('Failed to save appointment audit log', {
                appointmentId,
                action,
                error
            });
        }
    }
    // Helper to get settings and determine effective settings
    async getEffectiveBookingSettings(clinicId) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        const defaultSettings = {
            isEnabled: true,
            workingHours: null,
            allowedDoctorIds: null,
            minBookingLeadTime: null,
            modificationDeadlineTime: null,
            maxAdvanceBookingTime: null,
            minBookingLeadHours: 0,
            modificationDeadlineHours: 0,
            minBookingLeadMinutes: null,
            modificationDeadlineMinutes: null,
            maxAdvanceBookingMinutes: null
        };
        if (!clinicId)
            return defaultSettings;
        try {
            const settings = await this.clinicService.getClientBookingSettings(clinicId);
            const minLeadMinutes = timeDurationToMinutes(settings === null || settings === void 0 ? void 0 : settings.minBookingLeadTime);
            const modDeadlineMinutes = timeDurationToMinutes(settings === null || settings === void 0 ? void 0 : settings.modificationDeadlineTime);
            const maxAdvanceMinutes = timeDurationToMinutes(settings === null || settings === void 0 ? void 0 : settings.maxAdvanceBookingTime);
            const effectiveSettings = {
                isEnabled: (_a = settings === null || settings === void 0 ? void 0 : settings.isEnabled) !== null && _a !== void 0 ? _a : defaultSettings.isEnabled,
                allowAllDoctors: (_b = settings === null || settings === void 0 ? void 0 : settings.allowAllDoctors) !== null && _b !== void 0 ? _b : defaultSettings.allowAllDoctors,
                workingHours: (_c = settings === null || settings === void 0 ? void 0 : settings.workingHours) !== null && _c !== void 0 ? _c : defaultSettings.workingHours,
                allowedDoctorIds: (_d = settings === null || settings === void 0 ? void 0 : settings.allowedDoctorIds) !== null && _d !== void 0 ? _d : defaultSettings.allowedDoctorIds,
                minBookingLeadTime: (_e = settings === null || settings === void 0 ? void 0 : settings.minBookingLeadTime) !== null && _e !== void 0 ? _e : defaultSettings.minBookingLeadTime,
                modificationDeadlineTime: (_f = settings === null || settings === void 0 ? void 0 : settings.modificationDeadlineTime) !== null && _f !== void 0 ? _f : defaultSettings.modificationDeadlineTime,
                maxAdvanceBookingTime: (_g = settings === null || settings === void 0 ? void 0 : settings.maxAdvanceBookingTime) !== null && _g !== void 0 ? _g : defaultSettings.maxAdvanceBookingTime,
                minBookingLeadMinutes: minLeadMinutes,
                modificationDeadlineMinutes: modDeadlineMinutes,
                maxAdvanceBookingMinutes: maxAdvanceMinutes,
                minBookingLeadHours: (_h = settings === null || settings === void 0 ? void 0 : settings.minBookingLeadHours) !== null && _h !== void 0 ? _h : defaultSettings.minBookingLeadHours,
                modificationDeadlineHours: (_j = settings === null || settings === void 0 ? void 0 : settings.modificationDeadlineHours) !== null && _j !== void 0 ? _j : defaultSettings.modificationDeadlineHours
            };
            return effectiveSettings;
        }
        catch (error) {
            console.error(`Error fetching settings for clinic ${clinicId}, using defaults`, error);
            return defaultSettings;
        }
    }
    /**
     * Create a new client booking
     * @param createBookingDto DTO with booking details
     * @param ownerId Owner ID from auth
     * @returns Booking details
     */
    async createClientBooking(createBookingDto, ownerId) {
        var _a;
        if (!ownerId) {
            throw new common_1.ForbiddenException('User must be a pet owner to book appointments');
        }
        // --- Fetch Entities --- Start ---
        // 1. Fetch Pet and verify ownership
        const pet = await this.patientsRepository.findOne({
            where: { id: createBookingDto.petId },
            relations: ['patientOwners', 'patientOwners.ownerBrand']
        });
        if (!pet) {
            throw new common_1.NotFoundException(`Pet with ID ${createBookingDto.petId} not found`);
        }
        const ownerHasPet = pet.patientOwners.some(patientOwner => patientOwner.ownerId === ownerId);
        if (!ownerHasPet) {
            throw new common_1.ForbiddenException('You can only book appointments for your own pets');
        }
        // 2. Fetch Clinic
        const clinic = await this.clinicsRepository.findOne({
            where: { id: createBookingDto.clinicId }
        });
        if (!clinic) {
            throw new common_1.NotFoundException(`Clinic with ID ${createBookingDto.clinicId} not found`);
        }
        const brandId = clinic.brandId; // Needed for appointment creation
        // 3. Fetch Doctor (ClinicUser) and verify clinic association
        const doctorClinicUser = await this.clinicUsersRepository.findOne({
            where: {
                id: createBookingDto.doctorId,
                clinicId: createBookingDto.clinicId
            },
            relations: ['user']
        });
        if (!doctorClinicUser) {
            throw new common_1.NotFoundException(`Doctor with ID ${createBookingDto.doctorId} not found for this clinic`);
        }
        // --- Fetch Entities --- End ---
        // --- Settings Validation --- Start ---
        const settings = await this.getEffectiveBookingSettings(createBookingDto.clinicId);
        // 1. Check isEnabled
        if (!settings.isEnabled) {
            throw new common_1.BadRequestException('Online booking is currently disabled for this clinic.');
        }
        // 2. Check Doctor Allowed (only if allowAllDoctors is false and a specific list is provided)
        if (settings.allowAllDoctors === false &&
            Array.isArray(settings.allowedDoctorIds) &&
            settings.allowedDoctorIds.length > 0 &&
            !settings.allowedDoctorIds.includes(createBookingDto.doctorId)) {
            throw new common_1.BadRequestException('Selected doctor is not available for online booking.');
        }
        // 3. Check Lead Time (using minutes)
        const nowMoment = moment();
        // Parse date in DD-MMM-YYYY format and extract time from ISO string
        const dateMoment = moment(createBookingDto.date, 'DD-MMM-YYYY');
        if (!dateMoment.isValid()) {
            throw new common_1.BadRequestException('Invalid date format. Use DD-MMM-YYYY (e.g., 30-Apr-2025).');
        }
        // Parse start and end times from ISO strings
        const startMoment = moment(createBookingDto.startTime);
        const endMoment = moment(createBookingDto.endTime);
        if (!startMoment.isValid() || !endMoment.isValid()) {
            throw new common_1.BadRequestException('Invalid start or end time format provided.');
        }
        // Check if start time is before end time
        if (startMoment.isSameOrAfter(endMoment)) {
            throw new common_1.BadRequestException('Start time must be earlier than end time.');
        }
        if (settings.minBookingLeadMinutes) {
            const leadTimeThreshold = nowMoment
                .clone()
                .add(settings.minBookingLeadMinutes, 'minutes');
            if (startMoment.isBefore(leadTimeThreshold)) {
                throw new common_1.BadRequestException(`Booking must be made at least ${settings.minBookingLeadMinutes} minutes in advance.`);
            }
        }
        // 4. Check Maximum Advance Booking Time (using minutes)
        if (settings.maxAdvanceBookingMinutes) {
            const maxAdvanceThreshold = nowMoment
                .clone()
                .add(settings.maxAdvanceBookingMinutes, 'minutes');
            if (startMoment.isAfter(maxAdvanceThreshold)) {
                throw new common_1.BadRequestException(`Booking cannot be made more than ${settings.maxAdvanceBookingMinutes} minutes in advance.`);
            }
        }
        // 5. Check Working Hours
        if (settings.workingHours) {
            const dayOfWeek = startMoment
                .format('dddd')
                .toLowerCase();
            const daySchedule = (_a = settings.workingHours) === null || _a === void 0 ? void 0 : _a[dayOfWeek];
            const allowedIntervals = Array.isArray(daySchedule)
                ? daySchedule.filter(interval => interval.isWorkingDay &&
                    interval.startTime &&
                    interval.endTime)
                : [];
            // Get clinic timezone for proper comparison
            const clinicTimezone = clinic.timezone || 'Asia/Kolkata'; // Default to IST if no timezone set
            const isWithinAllowedInterval = allowedIntervals.some(interval => {
                if (!interval.startTime || !interval.endTime)
                    return false;
                // Format date as YYYY-MM-DD for consistent comparison
                const formattedDate = dateMoment.format('YYYY-MM-DD');
                // Convert start/end times to clinic timezone for comparison
                const startTimeInClinicTz = moment(startMoment).tz(clinicTimezone);
                const endTimeInClinicTz = moment(endMoment).tz(clinicTimezone);
                // Create moments in clinic timezone for working hour intervals
                const intervalStartMoment = moment.tz(`${formattedDate} ${interval.startTime}`, 'YYYY-MM-DD HH:mm', clinicTimezone);
                const intervalEndMoment = moment.tz(`${formattedDate} ${interval.endTime}`, 'YYYY-MM-DD HH:mm', clinicTimezone);
                // Compare the time portions in clinic's timezone
                const startTimeLocal = startTimeInClinicTz.format('HH:mm');
                const endTimeLocal = endTimeInClinicTz.format('HH:mm');
                const intervalStartLocal = intervalStartMoment.format('HH:mm');
                const intervalEndLocal = intervalEndMoment.format('HH:mm');
                this.logger.debug('Comparing times in clinic timezone', {
                    startTimeLocal,
                    endTimeLocal,
                    intervalStartLocal,
                    intervalEndLocal,
                    clinicTimezone
                });
                return (startTimeLocal >= intervalStartLocal &&
                    endTimeLocal <= intervalEndLocal);
            });
            if (!isWithinAllowedInterval) {
                throw new common_1.BadRequestException('Requested time slot is outside of the allowed booking hours for this day.');
            }
        }
        // --- Settings Validation --- End ---
        // --- Appointment Creation --- Start ---
        const appointmentData = {
            patientId: createBookingDto.petId,
            clinicId: createBookingDto.clinicId,
            doctorIds: [createBookingDto.doctorId],
            providerIds: [], // Initialize providerIds as empty array (adjust if needed)
            date: createBookingDto.date, // Use YYYY-MM-DD string as required by service
            startTime: startMoment.toDate(), // Use parsed moment objects directly
            endTime: endMoment.toDate(), // Use parsed moment objects directly
            reason: createBookingDto.reason || '', // Provide default empty string for reason
            mode: enum_appointment_mode_1.EnumAppointmentMode.ONLINE,
            status: enum_appointment_status_1.EnumAppointmentStatus.Scheduled, // Default status for new booking
            type: enum_appointment_type_1.EnumAppointmentType.Consultation, // Default type for client booking
            ownerId: ownerId,
            createdBy: ownerId // Owner is the creator
            // brandId is needed by appointmentsService.createAppointment
        };
        try {
            const createdAppointmentEntity = await this.appointmentsService.createAppointment(appointmentData, brandId);
            // Fetch the newly created appointment with relations for response formatting
            const newAppointment = await this.appointmentsRepository.findOne({
                where: { id: createdAppointmentEntity.id },
                relations: [
                    'patient',
                    'clinic',
                    'appointmentDoctors',
                    'appointmentDoctors.clinicUser',
                    'appointmentDoctors.clinicUser.user'
                ]
            });
            if (!newAppointment) {
                throw new common_1.InternalServerErrorException('Failed to retrieve created appointment details.');
            }
            // Log audit event
            await this._logAuditEvent(newAppointment.id, ownerId, appointment_audit_log_entity_1.AppointmentAuditLogAction.CREATE, createBookingDto, 'Client booking created');
            return this.formatAppointmentResponse(newAppointment);
        }
        catch (error) {
            this.logger.error('Error creating appointment via client booking', {
                error,
                createBookingDto
            });
            if (error instanceof common_1.BadRequestException ||
                error instanceof common_1.NotFoundException ||
                error instanceof common_1.ForbiddenException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to create the appointment.');
        }
        // --- Appointment Creation --- End ---
    }
    /**
     * Get details of a client booking
     * @param appointmentId Appointment ID
     * @param ownerId Owner ID from auth
     * @returns Booking details
     */
    async getClientBooking(appointmentId, ownerId) {
        if (!ownerId) {
            throw new common_1.ForbiddenException('User must be a pet owner to view appointments');
        }
        // Get the appointment with relations
        const appointment = await this.appointmentsRepository.findOne({
            where: { id: appointmentId },
            relations: [
                'patient',
                'patient.patientOwners',
                'patient.patientOwners.ownerBrand',
                'clinic',
                'appointmentDoctors',
                'appointmentDoctors.clinicUser',
                'appointmentDoctors.clinicUser.user'
            ]
        });
        if (!appointment) {
            throw new common_1.NotFoundException(`Appointment with ID ${appointmentId} not found`);
        }
        // Verify that the pet belongs to the owner
        const ownerHasPet = appointment.patient.patientOwners.some(patientOwner => patientOwner.ownerId === ownerId);
        if (!ownerHasPet) {
            throw new common_1.ForbiddenException('You can only view appointments for your own pets');
        }
        // Format the response
        return this.formatAppointmentResponse(appointment);
    }
    /**
     * Update a client booking (reschedule or cancel)
     * @param appointmentId Appointment ID
     * @param updateBookingDto DTO with updated booking details
     * @param ownerId Owner ID from auth
     * @returns Updated booking details
     */
    async updateClientBooking(appointmentId, updateBookingDto, ownerId) {
        var _a, _b, _c;
        if (!ownerId) {
            throw new common_1.ForbiddenException('User must be a pet owner to update appointments');
        }
        const appointment = await this.appointmentsRepository.findOne({
            where: { id: appointmentId },
            relations: [
                'patient',
                'patient.patientOwners',
                'patient.patientOwners.ownerBrand',
                'clinic',
                'appointmentDoctors',
                'appointmentDoctors.clinicUser',
                'appointmentDoctors.clinicUser.user'
            ]
        });
        if (!appointment) {
            throw new common_1.NotFoundException(`Appointment with ID ${appointmentId} not found`);
        }
        const ownerHasPet = appointment.patient.patientOwners.some(patientOwner => patientOwner.ownerId === ownerId);
        if (!ownerHasPet) {
            throw new common_1.ForbiddenException('You can only update appointments for your own pets');
        }
        const allowedStatuses = [
            enum_appointment_status_1.EnumAppointmentStatus.Scheduled,
            enum_appointment_status_1.EnumAppointmentStatus.Checkedin
        ];
        if (!allowedStatuses.includes(appointment.status)) {
            throw new common_1.BadRequestException(`Cannot update appointment with status ${appointment.status}`);
        }
        // --- Settings Validation (Modification Deadline) --- Start ---
        const settings = await this.getEffectiveBookingSettings(appointment.clinicId);
        if (settings.modificationDeadlineMinutes !== null) {
            if (!appointment.startTime) {
                throw new common_1.InternalServerErrorException('Appointment start time is missing, cannot check deadline.');
            }
            const originalAppointmentStartTime = moment(appointment.startTime);
            const deadline = originalAppointmentStartTime
                .clone()
                .subtract(settings.modificationDeadlineMinutes, 'minutes');
            if (moment().isAfter(deadline)) {
                throw new common_1.BadRequestException(`Edits are not allowed within ${settings.modificationDeadlineMinutes} minutes of the appointment time.`);
            }
        }
        if (!settings.isEnabled &&
            moment().isBefore(moment(appointment.startTime).subtract((_a = settings.modificationDeadlineMinutes) !== null && _a !== void 0 ? _a : 0, 'minutes'))) {
            throw new common_1.BadRequestException('Online booking modifications are currently disabled for this clinic.');
        }
        // --- Settings Validation --- End ---
        // --- Rescheduling Validation --- Start ---
        const isRescheduling = updateBookingDto.date ||
            updateBookingDto.startTime ||
            updateBookingDto.endTime ||
            updateBookingDto.doctorId;
        if (isRescheduling) {
            this.logger.log('Performing reschedule validation', {
                appointmentId
            });
            // Use original values as fallback if not provided in DTO
            const newDateStr = updateBookingDto.date ||
                moment(appointment.date).format('DD-MMM-YYYY');
            const newStartTimeStr = updateBookingDto.startTime ||
                moment(appointment.startTime).toISOString();
            const newEndTimeStr = updateBookingDto.endTime ||
                moment(appointment.endTime).toISOString();
            const newDoctorId = updateBookingDto.doctorId ||
                ((_b = appointment.appointmentDoctors[0]) === null || _b === void 0 ? void 0 : _b.doctorId);
            if (!newDoctorId) {
                throw new common_1.BadRequestException('Doctor ID is missing for rescheduling.');
            }
            // Attempt to parse the provided date and times
            const newDateMoment = moment(newDateStr, 'DD-MMM-YYYY');
            const newStartMoment = moment(newStartTimeStr); // Parse ISO string
            const newEndMoment = moment(newEndTimeStr); // Parse ISO string
            // Validate parsed moments
            if (!newDateMoment.isValid() ||
                !newStartMoment.isValid() ||
                !newEndMoment.isValid() ||
                newStartMoment.isSameOrAfter(newEndMoment)) {
                this.logger.warn('Invalid date/time format during reschedule', {
                    date: newDateStr,
                    startTime: newStartTimeStr,
                    endTime: newEndTimeStr
                });
                throw new common_1.BadRequestException('Invalid new date, start time, or end time format/values provided.');
            }
            // --- Run checks using the parsed moment objects (newStartMoment, newEndMoment) ---
            // 2. Re-check Doctor Allowed (if doctor changed)
            if (updateBookingDto.doctorId &&
                settings.allowAllDoctors === false &&
                Array.isArray(settings.allowedDoctorIds) &&
                settings.allowedDoctorIds.length > 0 &&
                !settings.allowedDoctorIds.includes(newDoctorId)) {
                throw new common_1.BadRequestException('Selected doctor is not available for online booking.');
            }
            // 3a. Re-check Lead Time for the NEW time against NOW
            const nowMoment = moment();
            if (settings.minBookingLeadMinutes) {
                const leadTimeThreshold = nowMoment
                    .clone()
                    .add(settings.minBookingLeadMinutes, 'minutes');
                if (newStartMoment.isBefore(leadTimeThreshold)) {
                    // Use parsed moment
                    throw new common_1.BadRequestException(`Rescheduled time must be at least ${settings.minBookingLeadMinutes} minutes in advance.`);
                }
            }
            // 3b. Re-check Max Advance Booking for the NEW time against NOW
            if (settings.maxAdvanceBookingMinutes) {
                const maxAdvanceThreshold = nowMoment
                    .clone()
                    .add(settings.maxAdvanceBookingMinutes, 'minutes');
                if (newStartMoment.isAfter(maxAdvanceThreshold)) {
                    // Use parsed moment
                    throw new common_1.BadRequestException(`Rescheduled time cannot be more than ${settings.maxAdvanceBookingMinutes} minutes in advance.`);
                }
            }
            // 4. Re-check Working Hours for the NEW time
            if (settings.workingHours) {
                const dayOfWeek = newStartMoment
                    .format('dddd')
                    .toLowerCase();
                const daySchedule = (_c = settings.workingHours) === null || _c === void 0 ? void 0 : _c[dayOfWeek];
                const allowedIntervals = Array.isArray(daySchedule)
                    ? daySchedule.filter(i => i.isWorkingDay && i.startTime && i.endTime)
                    : [];
                // Get clinic timezone for proper comparison
                const clinicTimezone = appointment.clinic.timezone || 'Asia/Kolkata'; // Default to IST if no timezone set
                const isWithinAllowedInterval = allowedIntervals.some(interval => {
                    if (!interval.startTime || !interval.endTime)
                        return false;
                    // Convert start/end times to clinic timezone for comparison
                    const startTimeInClinicTz = moment(newStartMoment).tz(clinicTimezone);
                    const endTimeInClinicTz = moment(newEndMoment).tz(clinicTimezone);
                    // Create moments in clinic timezone for working hour intervals
                    const intervalStartMoment = moment.tz(`${newDateMoment.format('YYYY-MM-DD')} ${interval.startTime}`, 'YYYY-MM-DD HH:mm', clinicTimezone);
                    const intervalEndMoment = moment.tz(`${newDateMoment.format('YYYY-MM-DD')} ${interval.endTime}`, 'YYYY-MM-DD HH:mm', clinicTimezone);
                    // Compare the time portions in clinic's timezone
                    const startTimeLocal = startTimeInClinicTz.format('HH:mm');
                    const endTimeLocal = endTimeInClinicTz.format('HH:mm');
                    const intervalStartLocal = intervalStartMoment.format('HH:mm');
                    const intervalEndLocal = intervalEndMoment.format('HH:mm');
                    this.logger.debug('Comparing times in clinic timezone for update', {
                        startTimeLocal,
                        endTimeLocal,
                        intervalStartLocal,
                        intervalEndLocal,
                        clinicTimezone
                    });
                    return (startTimeLocal >= intervalStartLocal &&
                        endTimeLocal <= intervalEndLocal);
                });
                if (!isWithinAllowedInterval) {
                    throw new common_1.BadRequestException('Requested time slot is outside of the allowed booking hours for this day.');
                }
            }
            this.logger.log('Reschedule validation passed', { appointmentId });
        }
        // --- Rescheduling Validation --- End ---
        // --- Update Appointment --- Start ---
        const updatePayload = {
            patientId: appointment.patientId,
            providerIds: []
        };
        const changedFields = {};
        // Use parsed moments for setting Date objects if rescheduling occurred
        if (updateBookingDto.date) {
            const parsedDate = moment(updateBookingDto.date, 'DD-MMM-YYYY');
            if (!parsedDate.isValid())
                throw new common_1.BadRequestException('Invalid date format for update.');
            updatePayload.date = parsedDate.toDate();
            changedFields.date = updateBookingDto.date;
        }
        if (updateBookingDto.startTime) {
            const parsedStartTime = moment(updateBookingDto.startTime); // Parse ISO
            if (!parsedStartTime.isValid())
                throw new common_1.BadRequestException('Invalid startTime format for update.');
            updatePayload.startTime = parsedStartTime.toDate();
            changedFields.startTime = updateBookingDto.startTime;
        }
        if (updateBookingDto.endTime) {
            const parsedEndTime = moment(updateBookingDto.endTime); // Parse ISO
            if (!parsedEndTime.isValid())
                throw new common_1.BadRequestException('Invalid endTime format for update.');
            updatePayload.endTime = parsedEndTime.toDate();
            changedFields.endTime = updateBookingDto.endTime;
        }
        if (updateBookingDto.doctorId) {
            updatePayload.doctorIds = [updateBookingDto.doctorId];
            changedFields.doctorId = updateBookingDto.doctorId;
        }
        else if (isRescheduling) {
            // If rescheduling but doctor didn't change, still need to pass it
            updatePayload.doctorIds = appointment.appointmentDoctors.map(doc => doc.doctorId);
        }
        try {
            // Only call update if there are actual changes
            if (Object.keys(changedFields).length > 0) {
                await this.appointmentsService.updateAppointment(appointmentId, updatePayload);
                // Log audit event only if changes were made
                await this._logAuditEvent(appointmentId, ownerId, appointment_audit_log_entity_1.AppointmentAuditLogAction.UPDATE, changedFields, 'Client booking updated');
            }
            else {
                this.logger.log('No changes detected for appointment update', {
                    appointmentId
                });
            }
            // Fetch the updated appointment for response regardless of whether changes were made
            const updatedAppointment = await this.appointmentsRepository.findOne({
                where: { id: appointmentId },
                relations: [
                    'patient',
                    'clinic',
                    'appointmentDoctors',
                    'appointmentDoctors.clinicUser',
                    'appointmentDoctors.clinicUser.user'
                ]
            });
            if (!updatedAppointment) {
                throw new common_1.InternalServerErrorException('Failed to retrieve appointment details after update.');
            }
            return this.formatAppointmentResponse(updatedAppointment);
        }
        catch (error) {
            this.logger.error('Error updating appointment via client booking', {
                error,
                appointmentId,
                updateBookingDto
            });
            if (error instanceof common_1.BadRequestException ||
                error instanceof common_1.NotFoundException ||
                error instanceof common_1.ForbiddenException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to update the appointment.');
        }
        // --- Update Appointment --- End ---
    }
    /**
     * Format an appointment entity to the client booking response format
     * @param appointment Appointment entity
     * @returns Formatted booking response
     */
    formatAppointmentResponse(appointment) {
        var _a, _b, _c, _d, _e;
        if (!appointment) {
            throw new Error('Cannot format null appointment');
        }
        // Mark deleted appointments as cancelled
        if (appointment.deletedAt) {
            appointment.status = enum_appointment_status_1.EnumAppointmentStatus.Cancelled;
        }
        // Convert dates to strings
        let dateStr = '';
        if (appointment.date) {
            dateStr =
                appointment.date instanceof Date
                    ? appointment.date.toISOString().split('T')[0] // YYYY-MM-DD
                    : String(appointment.date);
        }
        let startTimeStr = '';
        if (appointment.startTime) {
            startTimeStr =
                appointment.startTime instanceof Date
                    ? appointment.startTime.toTimeString().slice(0, 5) // HH:MM
                    : String(appointment.startTime);
        }
        let endTimeStr = '';
        if (appointment.endTime) {
            endTimeStr =
                appointment.endTime instanceof Date
                    ? appointment.endTime.toTimeString().slice(0, 5) // HH:MM
                    : String(appointment.endTime);
        }
        // Get doctor info safely
        let doctorId = '';
        let doctorName = 'Unknown';
        if (((_a = appointment.appointmentDoctors) === null || _a === void 0 ? void 0 : _a.length) > 0) {
            // Get the doctor ID from the appointmentDoctors relation
            doctorId = appointment.appointmentDoctors[0].doctorId || '';
            // Try to get doctor name through the clinicUser relationship
            if ((_c = (_b = appointment.appointmentDoctors[0]) === null || _b === void 0 ? void 0 : _b.clinicUser) === null || _c === void 0 ? void 0 : _c.user) {
                const doctor = appointment.appointmentDoctors[0].clinicUser.user;
                doctorName =
                    `Dr. ${doctor.firstName || ''} ${doctor.lastName || ''}`.trim();
                if (doctorName === 'Dr.') {
                    doctorName = `Dr. ID: ${doctorId.slice(-6)}`;
                }
            }
            else {
                // Fallback to showing just the ID
                doctorName = doctorId
                    ? `Dr. ID: ${doctorId.slice(-6)}`
                    : 'Unknown';
            }
        }
        // Format notes - handle object conversion to avoid "[object Object]"
        let notesStr = '';
        if (appointment.notes) {
            if (typeof appointment.notes === 'object') {
                // If it's an object with a text property (common format)
                if (appointment.notes.text) {
                    notesStr = appointment.notes.text;
                }
                else {
                    // Otherwise try to stringify it
                    try {
                        notesStr = JSON.stringify(appointment.notes);
                    }
                    catch (_f) {
                        notesStr = String(appointment.notes);
                    }
                }
            }
            else {
                notesStr = String(appointment.notes);
            }
        }
        // Get pet name properly
        const petName = ((_d = appointment.patient) === null || _d === void 0 ? void 0 : _d.patientName) || 'Unknown';
        // Get clinic name
        const clinicName = ((_e = appointment.clinic) === null || _e === void 0 ? void 0 : _e.name) || 'Unknown';
        return {
            id: appointment.id,
            petId: appointment.patientId,
            petName: petName,
            doctorId: doctorId,
            doctorName: doctorName,
            clinicId: appointment.clinicId,
            clinicName: clinicName,
            date: dateStr,
            startTime: startTimeStr,
            endTime: endTimeStr,
            status: appointment.status ? String(appointment.status) : '',
            notes: notesStr,
            createdAt: appointment.createdAt || new Date(),
            updatedAt: appointment.updatedAt || new Date()
        };
    }
    /**
     * Delete a client booking (cancel)
     * @param appointmentId Appointment ID
     * @param ownerId Owner ID from auth
     * @returns Deleted booking details
     */
    async deleteClientBooking(appointmentId, ownerId) {
        if (!ownerId) {
            throw new common_1.ForbiddenException('User must be a pet owner to cancel appointments');
        }
        // Get the appointment with relations
        const appointment = await this.appointmentsRepository.findOne({
            where: { id: appointmentId },
            relations: [
                'patient',
                'patient.patientOwners',
                'patient.patientOwners.ownerBrand',
                'clinic',
                'appointmentDoctors',
                'appointmentDoctors.clinicUser',
                'appointmentDoctors.clinicUser.user'
            ]
        });
        if (!appointment) {
            throw new common_1.NotFoundException(`Appointment with ID ${appointmentId} not found`);
        }
        // Verify that the pet belongs to the owner
        const ownerHasPet = appointment.patient.patientOwners.some(patientOwner => patientOwner.ownerId === ownerId);
        if (!ownerHasPet) {
            throw new common_1.ForbiddenException('You can only cancel appointments for your own pets');
        }
        // --- Settings Validation for Cancellation Deadline --- Start ---
        const settings = await this.getEffectiveBookingSettings(appointment.clinicId);
        // Check Modification/Cancellation Deadline (using minutes)
        if (settings.modificationDeadlineMinutes !== null) {
            if (!appointment.startTime) {
                throw new common_1.InternalServerErrorException('Appointment start time is missing, cannot check deadline.');
            }
            const originalAppointmentStartTime = moment(appointment.startTime);
            const deadline = originalAppointmentStartTime
                .clone()
                .subtract(settings.modificationDeadlineMinutes, 'minutes');
            if (moment().isAfter(deadline)) {
                throw new common_1.BadRequestException(`Cancellation is not allowed within ${settings.modificationDeadlineMinutes} minutes of the appointment time.`);
            }
        }
        // --- Settings Validation for Cancellation Deadline --- End ---
        // Verify that the appointment is in a status that allows cancellation
        // Typically, only 'Scheduled' appointments can be cancelled by the client.
        if (appointment.status !== enum_appointment_status_1.EnumAppointmentStatus.Scheduled) {
            throw new common_1.BadRequestException(`Cannot cancel appointment with status ${appointment.status}`);
        }
        // Perform cancellation using the underlying AppointmentsService
        try {
            // Assuming deleteAppointment handles the cancellation logic (e.g., soft delete)
            await this.appointmentsService.deleteAppointment(appointmentId);
            // Log audit event for cancellation
            await this._logAuditEvent(appointmentId, ownerId, appointment_audit_log_entity_1.AppointmentAuditLogAction.CANCEL, // Use CANCEL action
            null, 'Client booking cancelled');
            // Return the (now cancelled/deleted) appointment details
            const cancelledAppointment = {
                ...appointment,
                status: enum_appointment_status_1.EnumAppointmentStatus.Cancelled, // Reflect cancellation
                deletedAt: new Date() // Reflect soft delete if applicable
            };
            return this.formatAppointmentResponse(cancelledAppointment);
        }
        catch (error) {
            this.logger.error('Error cancelling appointment via client booking', {
                error,
                appointmentId
            });
            if (error instanceof common_1.BadRequestException ||
                error instanceof common_1.NotFoundException ||
                error instanceof common_1.ForbiddenException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to cancel the appointment.');
        }
    }
};
exports.ClientBookingService = ClientBookingService;
exports.ClientBookingService = ClientBookingService = ClientBookingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(appointment_entity_1.AppointmentEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(patient_entity_1.Patient)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(3, (0, typeorm_1.InjectRepository)(clinic_entity_1.ClinicEntity)),
    __param(4, (0, typeorm_1.InjectRepository)(clinic_user_entity_1.ClinicUser)),
    __param(9, (0, typeorm_1.InjectRepository)(appointment_audit_log_entity_1.AppointmentAuditLog)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        appointments_service_1.AppointmentsService,
        client_availability_service_1.ClientAvailabilityService,
        patients_service_1.PatientsService,
        clinic_service_1.ClinicService,
        typeorm_2.Repository])
], ClientBookingService);
//# sourceMappingURL=client-booking.service.js.map