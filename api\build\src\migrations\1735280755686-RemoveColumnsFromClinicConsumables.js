"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RemoveColumnsFromClinicConsumables1735280755686 = void 0;
const typeorm_1 = require("typeorm");
class RemoveColumnsFromClinicConsumables1735280755686 {
    async up(queryRunner) {
        await queryRunner.dropColumns('clinic_consumables', [
            'reorder_value',
            'description',
            'purchase_price'
        ]);
    }
    async down(queryRunner) {
        await queryRunner.addColumn('clinic_consumables', new typeorm_1.TableColumn({
            name: 'reorder_value',
            type: 'int',
            isNullable: true
        }));
        await queryRunner.addColumn('clinic_consumables', new typeorm_1.TableColumn({
            name: 'description',
            type: 'varchar',
            isNullable: true
        }));
        await queryRunner.addColumn('clinic_consumables', new typeorm_1.TableColumn({
            name: 'purchase_price',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: true
        }));
    }
}
exports.RemoveColumnsFromClinicConsumables1735280755686 = RemoveColumnsFromClinicConsumables1735280755686;
//# sourceMappingURL=1735280755686-RemoveColumnsFromClinicConsumables.js.map