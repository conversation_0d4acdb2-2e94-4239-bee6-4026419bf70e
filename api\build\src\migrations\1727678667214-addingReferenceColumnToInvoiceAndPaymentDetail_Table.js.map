{"version": 3, "file": "1727678667214-addingReferenceColumnToInvoiceAndPaymentDetail_Table.js", "sourceRoot": "", "sources": ["../../../src/migrations/1727678667214-addingReferenceColumnToInvoiceAndPaymentDetail_Table.ts"], "names": [], "mappings": ";;;AAAA,qCAAuE;AAEvE,MAAa,gEAAgE;IAGrE,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,MAAM,WAAW,CAAC,UAAU,CAAC,UAAU,EAAE;YACxC,IAAI,qBAAW,CAAC;gBACf,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,IAAI;aAChB,CAAC;YACF,IAAI,qBAAW,CAAC;gBACf,IAAI,EAAE,2BAA2B;gBACjC,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,IAAI;aAChB,CAAC;SACF,CAAC,CAAC;QAEH,MAAM,WAAW,CAAC,SAAS,CAC1B,iBAAiB,EACjB,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,cAAc;YACpB,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,MAAM,WAAW,CAAC,UAAU,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;QAEhE,MAAM,WAAW,CAAC,WAAW,CAAC,UAAU,EAAE;YACzC,cAAc;YACd,2BAA2B;SAC3B,CAAC,CAAC;IACJ,CAAC;CACD;AAnCD,4IAmCC"}