import { TemplateType } from '../entities/diagnostic-template.entity';
declare class AssignedDiagnosticDto {
    id: string;
    name: string;
}
declare class TableColumnDto {
    name: string;
    type: 'text' | 'number' | 'range';
    options?: string[];
}
declare class TableStructureDto {
    columns: TableColumnDto[];
}
export declare class CreateDiagnosticTemplateDto {
    templateName: string;
    clinicId: string;
    assignedDiagnostics: AssignedDiagnosticDto[];
    templateType: TemplateType;
    tableStructure?: TableStructureDto;
    notes?: string;
    isActive?: boolean;
}
export {};
