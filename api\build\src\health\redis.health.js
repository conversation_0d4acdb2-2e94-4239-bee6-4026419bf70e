"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RedisHealthIndicator_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisHealthIndicator = void 0;
const common_1 = require("@nestjs/common");
const terminus_1 = require("@nestjs/terminus");
const redis_service_1 = require("../utils/redis/redis.service");
const ioredis_1 = require("ioredis");
/**
 * Enhanced Redis Health Indicator with comprehensive cluster monitoring
 * Provides detailed information about Redis single-node or cluster configurations
 */
let RedisHealthIndicator = RedisHealthIndicator_1 = class RedisHealthIndicator extends terminus_1.HealthIndicator {
    constructor(redisService) {
        super();
        this.redisService = redisService;
        this.logger = new common_1.Logger(RedisHealthIndicator_1.name);
    }
    /**
     * Basic health check - ping test for backward compatibility
     */
    async isHealthy(key) {
        try {
            // Use the Redis client from our centralized service
            const client = this.redisService.getClient();
            // Check if Redis is responding
            const ping = await client.ping();
            const isHealthy = ping === 'PONG';
            return this.getStatus(key, isHealthy);
        }
        catch (err) {
            this.logger.error('Redis health check failed:', err);
            throw new terminus_1.HealthCheckError('Redis health check failed', err);
        }
    }
    /**
     * Comprehensive cluster health check with detailed information
     * Returns cluster mode, node status, performance metrics, and connectivity info
     */
    async getClusterHealth(key) {
        try {
            const client = this.redisService.getClient();
            const isCluster = client instanceof ioredis_1.Cluster;
            // Get basic connectivity first
            const ping = await client.ping();
            const baseHealth = ping === 'PONG';
            if (!baseHealth) {
                return this.getStatus(key, false, {
                    error: 'Redis ping failed',
                    mode: isCluster ? 'cluster' : 'single',
                    connectivity: 'failed'
                });
            }
            if (isCluster) {
                // Cluster-specific health information
                const clusterInfo = await this.getClusterInfo(client);
                const nodeHealth = await this.getClusterNodesHealth(client);
                const clusterStats = await this.getClusterStats(client);
                return this.getStatus(key, baseHealth, {
                    mode: 'cluster',
                    connectivity: 'connected',
                    cluster: {
                        ...clusterInfo,
                        nodes: nodeHealth,
                        statistics: clusterStats
                    }
                });
            }
            else {
                // Single node information
                const nodeInfo = await this.getSingleNodeInfo(client);
                return this.getStatus(key, baseHealth, {
                    mode: 'single',
                    connectivity: 'connected',
                    node: nodeInfo
                });
            }
        }
        catch (err) {
            this.logger.error('Redis cluster health check failed:', err);
            throw new terminus_1.HealthCheckError('Redis cluster health check failed', err);
        }
    }
    /**
     * Get cluster information and status
     */
    async getClusterInfo(cluster) {
        try {
            // Use proper ioredis cluster commands with explicit typing
            const info = (await cluster.cluster('INFO'));
            const nodes = (await cluster.cluster('NODES'));
            // Parse cluster info
            const infoLines = info.split('\r\n');
            const infoObj = {};
            infoLines.forEach((line) => {
                const [key, value] = line.split(':');
                if (key && value) {
                    infoObj[key] = value;
                }
            });
            // Parse nodes info for basic counts
            const nodeLines = nodes
                .split('\n')
                .filter((line) => line.trim());
            const masterNodes = nodeLines.filter((line) => line.includes('master'));
            const slaveNodes = nodeLines.filter((line) => line.includes('slave'));
            return {
                state: infoObj.cluster_state,
                slots_assigned: parseInt(infoObj.cluster_slots_assigned) || 0,
                slots_ok: parseInt(infoObj.cluster_slots_ok) || 0,
                slots_pfail: parseInt(infoObj.cluster_slots_pfail) || 0,
                slots_fail: parseInt(infoObj.cluster_slots_fail) || 0,
                known_nodes: parseInt(infoObj.cluster_known_nodes) || 0,
                size: parseInt(infoObj.cluster_size) || 0,
                current_epoch: parseInt(infoObj.cluster_current_epoch) || 0,
                master_count: masterNodes.length,
                slave_count: slaveNodes.length,
                healthy: infoObj.cluster_state === 'ok'
            };
        }
        catch (error) {
            this.logger.warn('Failed to get cluster info:', error);
            return {
                state: 'unknown',
                healthy: false,
                error: 'Unable to retrieve cluster information'
            };
        }
    }
    /**
     * Get individual cluster node health status
     */
    async getClusterNodesHealth(cluster) {
        try {
            const nodes = (await cluster.cluster('NODES'));
            const nodeLines = nodes
                .split('\n')
                .filter((line) => line.trim());
            return nodeLines
                .map((line) => {
                const parts = line.split(' ');
                if (parts.length >= 8) {
                    const [id, endpoint, flags, // masterId - not used in health check
                    , pingSent, pongReceived, // configEpoch - not used in health check
                    , linkState] = parts;
                    const [host, port] = endpoint.split(':');
                    return {
                        id: id.substring(0, 8), // Shortened for readability
                        host,
                        port: parseInt(port) || 0,
                        role: flags.includes('master') ? 'master' : 'slave',
                        flags: flags.split(','),
                        link_state: linkState,
                        healthy: linkState === 'connected' &&
                            !flags.includes('fail'),
                        ping_sent: parseInt(pingSent) || 0,
                        pong_received: parseInt(pongReceived) || 0
                    };
                }
                return null;
            })
                .filter((node) => node !== null);
        }
        catch (error) {
            this.logger.warn('Failed to get cluster nodes health:', error);
            return [];
        }
    }
    /**
     * Get cluster performance statistics
     */
    async getClusterStats(cluster) {
        try {
            // Get stats from one of the master nodes
            const stats = await cluster.info();
            const lines = stats.split('\r\n');
            const statsObj = {};
            lines.forEach((line) => {
                const [key, value] = line.split(':');
                if (key && value) {
                    statsObj[key] = value;
                }
            });
            return {
                connected_clients: parseInt(statsObj.connected_clients) || 0,
                used_memory: statsObj.used_memory_human || '0B',
                used_memory_peak: statsObj.used_memory_peak_human || '0B',
                keyspace_hits: parseInt(statsObj.keyspace_hits) || 0,
                keyspace_misses: parseInt(statsObj.keyspace_misses) || 0,
                total_commands_processed: parseInt(statsObj.total_commands_processed) || 0,
                instantaneous_ops_per_sec: parseInt(statsObj.instantaneous_ops_per_sec) || 0,
                hit_rate: this.calculateHitRate(parseInt(statsObj.keyspace_hits) || 0, parseInt(statsObj.keyspace_misses) || 0)
            };
        }
        catch (error) {
            this.logger.warn('Failed to get cluster stats:', error);
            return { error: 'Unable to retrieve cluster statistics' };
        }
    }
    /**
     * Get single node information
     */
    async getSingleNodeInfo(redis) {
        try {
            const info = await redis.info();
            const lines = info.split('\r\n');
            const infoObj = {};
            lines.forEach((line) => {
                const [key, value] = line.split(':');
                if (key && value) {
                    infoObj[key] = value;
                }
            });
            return {
                role: infoObj.role || 'master',
                connected_clients: parseInt(infoObj.connected_clients) || 0,
                used_memory: infoObj.used_memory_human || '0B',
                used_memory_peak: infoObj.used_memory_peak_human || '0B',
                keyspace_hits: parseInt(infoObj.keyspace_hits) || 0,
                keyspace_misses: parseInt(infoObj.keyspace_misses) || 0,
                total_commands_processed: parseInt(infoObj.total_commands_processed) || 0,
                instantaneous_ops_per_sec: parseInt(infoObj.instantaneous_ops_per_sec) || 0,
                uptime_in_seconds: parseInt(infoObj.uptime_in_seconds) || 0,
                hit_rate: this.calculateHitRate(parseInt(infoObj.keyspace_hits) || 0, parseInt(infoObj.keyspace_misses) || 0)
            };
        }
        catch (error) {
            this.logger.warn('Failed to get single node info:', error);
            return { error: 'Unable to retrieve node information' };
        }
    }
    /**
     * Calculate cache hit rate percentage
     */
    calculateHitRate(hits, misses) {
        const total = hits + misses;
        if (total === 0)
            return '0.00%';
        const rate = (hits / total) * 100;
        return `${rate.toFixed(2)}%`;
    }
};
exports.RedisHealthIndicator = RedisHealthIndicator;
exports.RedisHealthIndicator = RedisHealthIndicator = RedisHealthIndicator_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [redis_service_1.RedisService])
], RedisHealthIndicator);
//# sourceMappingURL=redis.health.js.map