"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateLabReportUniqueId1727690824407 = void 0;
const typeorm_1 = require("typeorm");
class UpdateLabReportUniqueId1727690824407 {
    async up(queryRunner) {
        await queryRunner.changeColumn('clinic_lab_reports', 'unique_id', new typeorm_1.TableColumn({
            name: 'unique_id',
            type: 'varchar',
            isNullable: true,
            isUnique: false
        }));
    }
    async down(queryRunner) {
        await queryRunner.changeColumn('clinic_lab_reports', 'unique_id', new typeorm_1.TableColumn({
            name: 'unique_id',
            type: 'varchar',
            isNullable: true,
            isUnique: true
        }));
    }
}
exports.UpdateLabReportUniqueId1727690824407 = UpdateLabReportUniqueId1727690824407;
//# sourceMappingURL=1727690824407-update-lab-report-unique-id.js.map