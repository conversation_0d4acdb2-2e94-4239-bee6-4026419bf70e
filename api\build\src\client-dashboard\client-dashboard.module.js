"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientDashboardModule = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const client_dashboard_controller_1 = require("./client-dashboard.controller");
const client_dashboard_service_1 = require("./client-dashboard.service");
const client_availability_controller_1 = require("./client-availability.controller");
const client_availability_service_1 = require("./client-availability.service");
const client_booking_controller_1 = require("./client-booking.controller");
const client_booking_service_1 = require("./client-booking.service");
const owners_module_1 = require("../owners/owners.module");
const appointments_module_1 = require("../appointments/appointments.module");
const role_module_1 = require("../roles/role.module");
const brands_module_1 = require("../brands/brands.module");
const patients_module_1 = require("../patients/patients.module");
const users_module_1 = require("../users/users.module");
const availability_module_1 = require("../availability/availability.module");
const user_entity_1 = require("../users/entities/user.entity");
const clinic_entity_1 = require("../clinics/entities/clinic.entity");
const clinic_user_entity_1 = require("../clinics/entities/clinic-user.entity");
const availability_exception_entity_1 = require("../users/entities/availability-exception.entity");
const appointment_entity_1 = require("../appointments/entities/appointment.entity");
const patient_entity_1 = require("../patients/entities/patient.entity");
const audit_module_1 = require("../audit/audit.module");
const clinic_module_1 = require("../clinics/clinic.module");
let ClientDashboardModule = class ClientDashboardModule {
};
exports.ClientDashboardModule = ClientDashboardModule;
exports.ClientDashboardModule = ClientDashboardModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                user_entity_1.User,
                clinic_entity_1.ClinicEntity,
                clinic_user_entity_1.ClinicUser,
                availability_exception_entity_1.AvailabilityExceptionEntity,
                appointment_entity_1.AppointmentEntity,
                patient_entity_1.Patient
            ]),
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: configService.get('JWT_SECRET'),
                    signOptions: { expiresIn: '7d' } // Longer expiration for pet owners
                }),
                inject: [config_1.ConfigService]
            }),
            owners_module_1.OwnersModule,
            appointments_module_1.AppointmentsModule,
            role_module_1.RoleModule,
            brands_module_1.BrandsModule,
            config_1.ConfigModule,
            patients_module_1.PatientsModule,
            users_module_1.UsersModule,
            availability_module_1.AvailabilityModule,
            audit_module_1.AuditModule,
            clinic_module_1.ClinicModule
        ],
        controllers: [
            client_dashboard_controller_1.ClientDashboardController,
            client_availability_controller_1.ClientAvailabilityController,
            client_booking_controller_1.ClientBookingController
        ],
        providers: [
            client_dashboard_service_1.ClientDashboardService,
            client_availability_service_1.ClientAvailabilityService,
            client_booking_service_1.ClientBookingService
        ],
        exports: [
            client_dashboard_service_1.ClientDashboardService,
            client_availability_service_1.ClientAvailabilityService,
            client_booking_service_1.ClientBookingService
        ]
    })
], ClientDashboardModule);
//# sourceMappingURL=client-dashboard.module.js.map