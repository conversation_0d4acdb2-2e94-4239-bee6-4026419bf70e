"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicIdexxUtilsService = void 0;
const moment = require("moment");
class ClinicIdexxUtilsService {
    getAuthKey(userName, passowrd) {
        const base64Auth = btoa(userName + ':' + passowrd);
        return base64Auth;
    }
    getSourceId() {
        return process.env.IDEXX_SOURCE_ID;
    }
    getAppVersion() {
        return '1.0';
    }
    getIntegrationBaseURL() {
        return process.env.IDEXX_INTEGRATION_URL;
    }
    getPartnerBaseURL() {
        return process.env.IDEXX_PARTNER_URL;
    }
    // Determine if we're using the new or old API format
    isNewApiFormat() {
        const integrationUrl = this.getIntegrationBaseURL() || '';
        return integrationUrl.includes('ordering.vetconnectplus.com');
    }
    // Get the appropriate date format based on API version
    getDateFormat() {
        return this.isNewApiFormat() ? 'DD/MM/YYYY' : 'MM/DD/YYYY';
    }
    // Format a date using the appropriate format for the current API
    formatDateForApi(dateString, inputFormat = 'YYYY-MM-DD') {
        return moment(dateString, inputFormat).isValid()
            ? moment(dateString, inputFormat).format(this.getDateFormat())
            : moment().format(this.getDateFormat());
    }
    // Determine if API calls need token parameter
    needsTokenParameter() {
        return !this.isNewApiFormat();
    }
    // Get URL with token parameter if needed
    getUrlWithToken(baseUrl, token) {
        if (this.needsTokenParameter() && token) {
            return `${baseUrl}?token=${token}`;
        }
        return baseUrl;
    }
    getHeaders(authKey) {
        return {
            Authorization: `Basic ${authKey}`,
            'x-pims-version': this.getAppVersion(),
            'x-pims-id': this.getSourceId()
        };
    }
    getGender(breed) {
        let response = '';
        const BREED_MAPPING = {
            Male: 'MALE_INTACT',
            Female: 'FEMALE_INTACT',
            Unknown: 'UNKNOWN'
        };
        response = BREED_MAPPING[breed];
        return response !== null && response !== void 0 ? response : 'UNKNOWN';
    }
    getBirthDate(date) {
        return moment(date, 'DD MMM YYYY').format('YYYY-MM-DD');
    }
}
exports.ClinicIdexxUtilsService = ClinicIdexxUtilsService;
//# sourceMappingURL=clinic-idexx-utils.service.js.map