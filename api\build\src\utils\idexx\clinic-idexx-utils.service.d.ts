export declare class ClinicIdexxUtilsService {
    getAuthKey(userName: string, passowrd: string): string;
    getSourceId(): string | undefined;
    getAppVersion(): string;
    getIntegrationBaseURL(): string | undefined;
    getPartnerBaseURL(): string | undefined;
    isNewApiFormat(): boolean;
    getDateFormat(): "DD/MM/YYYY" | "MM/DD/YYYY";
    formatDateForApi(dateString: string, inputFormat?: string): string;
    needsTokenParameter(): boolean;
    getUrlWithToken(baseUrl: string, token?: string): string;
    getHeaders(authKey: string): {
        Authorization: string;
        'x-pims-version': string;
        'x-pims-id': string | undefined;
    };
    getGender(breed: 'Male' | 'Female' | 'Unknown'): string;
    getBirthDate(date: string): string;
}
