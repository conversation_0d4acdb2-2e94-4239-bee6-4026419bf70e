"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataTypeChangeInColumnOfInvoiceTable1727156728813 = void 0;
const typeorm_1 = require("typeorm");
class DataTypeChangeInColumnOfInvoiceTable1727156728813 {
    async up(queryRunner) {
        await queryRunner.changeColumn('invoices', 'file_url', new typeorm_1.TableColumn({
            name: 'file_url',
            type: 'json',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.changeColumn('invoices', 'file_url', new typeorm_1.TableColumn({
            name: 'file_url',
            type: 'varchar',
            isNullable: true
        }));
    }
}
exports.DataTypeChangeInColumnOfInvoiceTable1727156728813 = DataTypeChangeInColumnOfInvoiceTable1727156728813;
//# sourceMappingURL=1727156728813-dataTypeChangeInColumnOfInvoiceTable.js.map