"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddPatientIdColumnToLabReportsTable1726124986269 = void 0;
const typeorm_1 = require("typeorm");
class AddPatientIdColumnToLabReportsTable1726124986269 {
    async up(queryRunner) {
        await queryRunner.addColumn('lab_reports', new typeorm_1.TableColumn({
            name: 'patient_id',
            type: 'uuid',
            isNullable: false
        }));
        await queryRunner.createForeignKey('lab_reports', new typeorm_1.TableForeignKey({
            columnNames: ['patient_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'patients',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable('lab_reports');
        if (table) {
            const foreignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('patient_id') !== -1);
            if (foreignKey) {
                await queryRunner.dropForeignKey('lab_reports', foreignKey);
            }
            await queryRunner.dropColumn('lab_reports', 'patient_id');
        }
    }
}
exports.AddPatientIdColumnToLabReportsTable1726124986269 = AddPatientIdColumnToLabReportsTable1726124986269;
//# sourceMappingURL=1726124986269-addPatientIdColumnToLabReportsTable.js.map