import { SOAPResponse } from '../types/soap.types';
import { <PERSON><PERSON>ogger } from '../../logger/winston-logger.service';
export declare class AIService {
    private readonly logger;
    private openai;
    constructor(logger: WinstonLogger);
    generateSOAPNotes(clinicalNotes: string): Promise<SOAPResponse>;
    regenerateSOAPNotes(clinicalNotes: string, previousNotes?: string): Promise<SOAPResponse>;
}
