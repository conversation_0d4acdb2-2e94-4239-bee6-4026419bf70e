"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUsersTable1722941115184 = void 0;
const typeorm_1 = require("typeorm");
class UpdateUsersTable1722941115184 {
    constructor() {
        this.name = 'UpdateUsersTable1722941115184';
    }
    async up(queryRunner) {
        await queryRunner.changeColumn('users', 'pin', new typeorm_1.TableColumn({
            name: 'pin',
            type: 'varchar',
            isNullable: true,
            length: '4'
        }));
        await queryRunner.addColumns('users', [
            new typeorm_1.TableColumn({
                name: 'mobile_number',
                type: 'varchar',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'working_hours_start',
                type: 'varchar',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'working_hours_end',
                type: 'varchar',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'working_days',
                type: 'text',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'digital_signature',
                type: 'text',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'registered',
                type: 'boolean',
                default: false
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumns('users', [
            'mobile_number',
            'working_hours_start',
            'working_hours_end',
            'working_days',
            'digital_signature',
            'registered'
        ]);
        await queryRunner.changeColumn('users', 'pin', new typeorm_1.TableColumn({
            name: 'pin',
            type: 'varchar',
            isNullable: true,
            length: '6'
        }));
    }
}
exports.UpdateUsersTable1722941115184 = UpdateUsersTable1722941115184;
//# sourceMappingURL=1722941115184-UpdateUsersTable.js.map