"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateBrandsTable1725036437254 = void 0;
const typeorm_1 = require("typeorm");
class UpdateBrandsTable1725036437254 {
    async up(queryRunner) {
        const table = await queryRunner.getTable("clinics");
        if (table) {
            const foreignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf("brand_id") !== -1);
            if (!foreignKey) {
                await queryRunner.createForeignKey("clinics", new typeorm_1.TableForeignKey({
                    columnNames: ["brand_id"],
                    referencedColumnNames: ["id"],
                    referencedTableName: "brands",
                    onDelete: "SET NULL"
                }));
            }
        }
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable("clinics");
        if (table) {
            const foreignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf("brand_id") !== -1);
            if (foreignKey) {
                await queryRunner.dropForeignKey("clinics", foreignKey);
            }
        }
    }
}
exports.UpdateBrandsTable1725036437254 = UpdateBrandsTable1725036437254;
//# sourceMappingURL=1725036437254-UpdateBrandsTable.js.map