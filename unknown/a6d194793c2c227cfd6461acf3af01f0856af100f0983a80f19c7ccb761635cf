"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.IsAddedByUserToAppointmentAssessments1724232728648 = void 0;
const typeorm_1 = require("typeorm");
class IsAddedByUserToAppointmentAssessments1724232728648 {
    async up(queryRunner) {
        await queryRunner.addColumns('appointment_assessments', [
            new typeorm_1.TableColumn({
                name: 'is_added_by_user',
                type: 'boolean',
                isNullable: false,
                default: false
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('appointment_assessments', 'is_added_by_user');
    }
}
exports.IsAddedByUserToAppointmentAssessments1724232728648 = IsAddedByUserToAppointmentAssessments1724232728648;
//# sourceMappingURL=1724232728648-IsAddedByUserToAppointmentAssessments.js.map