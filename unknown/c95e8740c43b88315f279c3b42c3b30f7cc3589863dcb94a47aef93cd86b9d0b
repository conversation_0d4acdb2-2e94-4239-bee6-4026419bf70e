"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InventoryMappingService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const inventory_mapping_entity_1 = require("../entities/inventory-mapping.entity");
const clinic_service_entity_1 = require("../../clinic-services/entities/clinic-service.entity");
const clinic_product_entity_1 = require("../../clinic-products/entities/clinic-product.entity");
const clinic_medication_entity_1 = require("../../clinic-medications/entities/clinic-medication.entity");
const clinic_consumable_entity_1 = require("../../clinic-consumables/entities/clinic-consumable.entity");
const clinic_vaccination_entity_1 = require("../../clinic-vaccinations/entities/clinic-vaccination.entity");
const clinic_lab_report_entity_1 = require("../../clinic-lab-report/entities/clinic-lab-report.entity");
const appointment_assessment_entity_1 = require("../../appointment-assessment/entities/appointment-assessment.entity");
let InventoryMappingService = class InventoryMappingService {
    constructor(clinicServiceRepository, clinicProductRepository, clinicMedicationRepository, clinicConsumableRepository, clinicVaccinationRepository, clinicLabReportRepository, appointmentAssessmentRepository) {
        this.clinicServiceRepository = clinicServiceRepository;
        this.clinicProductRepository = clinicProductRepository;
        this.clinicMedicationRepository = clinicMedicationRepository;
        this.clinicConsumableRepository = clinicConsumableRepository;
        this.clinicVaccinationRepository = clinicVaccinationRepository;
        this.clinicLabReportRepository = clinicLabReportRepository;
        this.appointmentAssessmentRepository = appointmentAssessmentRepository;
    }
    async findAndMapInventoryItem(sourceItemId, itemType, destinationClinicId) {
        const sourceItem = await this.findSourceItem(sourceItemId, itemType);
        if (!sourceItem) {
            return null;
        }
        const sourceItemName = this.getItemName(sourceItem, itemType);
        if (!sourceItemName) {
            return null;
        }
        const destinationItem = await this.findDestinationItemByName(sourceItemName, itemType, destinationClinicId);
        if (destinationItem) {
            return destinationItem.id;
        }
        return null;
    }
    async findSourceItem(itemId, itemType) {
        switch (itemType) {
            case inventory_mapping_entity_1.InventoryItemType.SERVICE:
                return this.clinicServiceRepository.findOne({
                    where: { id: itemId }
                });
            case inventory_mapping_entity_1.InventoryItemType.PRODUCT:
                return this.clinicProductRepository.findOne({
                    where: { id: itemId }
                });
            case inventory_mapping_entity_1.InventoryItemType.MEDICATION:
                return this.clinicMedicationRepository.findOne({
                    where: { id: itemId }
                });
            case inventory_mapping_entity_1.InventoryItemType.CONSUMABLE:
                return this.clinicConsumableRepository.findOne({
                    where: { id: itemId }
                });
            case inventory_mapping_entity_1.InventoryItemType.VACCINATION:
                return this.clinicVaccinationRepository.findOne({
                    where: { id: itemId }
                });
            case inventory_mapping_entity_1.InventoryItemType.LAB_REPORT:
                return this.clinicLabReportRepository.findOne({
                    where: { id: itemId }
                });
            case inventory_mapping_entity_1.InventoryItemType.APPOINTMENT_ASSESSMENT:
                return this.appointmentAssessmentRepository.findOne({
                    where: { id: itemId }
                });
            default:
                return null;
        }
    }
    getItemName(item, itemType) {
        switch (itemType) {
            case inventory_mapping_entity_1.InventoryItemType.SERVICE:
                return item.serviceName;
            case inventory_mapping_entity_1.InventoryItemType.PRODUCT:
            case inventory_mapping_entity_1.InventoryItemType.CONSUMABLE:
            case inventory_mapping_entity_1.InventoryItemType.VACCINATION:
                return item.productName;
            case inventory_mapping_entity_1.InventoryItemType.MEDICATION:
            case inventory_mapping_entity_1.InventoryItemType.LAB_REPORT:
            case inventory_mapping_entity_1.InventoryItemType.APPOINTMENT_ASSESSMENT:
                return item.name;
            default:
                return null;
        }
    }
    async findDestinationItemByName(name, itemType, destinationClinicId) {
        switch (itemType) {
            case inventory_mapping_entity_1.InventoryItemType.SERVICE:
                return this.clinicServiceRepository.findOne({
                    where: { serviceName: name, clinicId: destinationClinicId }
                });
            case inventory_mapping_entity_1.InventoryItemType.PRODUCT:
                return this.clinicProductRepository.findOne({
                    where: { productName: name, clinicId: destinationClinicId }
                });
            case inventory_mapping_entity_1.InventoryItemType.MEDICATION:
                return this.clinicMedicationRepository.findOne({
                    where: { name, clinicId: destinationClinicId }
                });
            case inventory_mapping_entity_1.InventoryItemType.CONSUMABLE:
                return this.clinicConsumableRepository.findOne({
                    where: { productName: name, clinicId: destinationClinicId }
                });
            case inventory_mapping_entity_1.InventoryItemType.VACCINATION:
                return this.clinicVaccinationRepository.findOne({
                    where: { productName: name, clinicId: destinationClinicId }
                });
            case inventory_mapping_entity_1.InventoryItemType.LAB_REPORT:
                return this.clinicLabReportRepository.findOne({
                    where: { name, clinicId: destinationClinicId }
                });
            case inventory_mapping_entity_1.InventoryItemType.APPOINTMENT_ASSESSMENT:
                return this.appointmentAssessmentRepository.findOne({
                    where: { name, clinicId: destinationClinicId }
                });
            default:
                return null;
        }
    }
};
exports.InventoryMappingService = InventoryMappingService;
exports.InventoryMappingService = InventoryMappingService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(clinic_service_entity_1.ClinicServiceEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(clinic_product_entity_1.ClinicProductEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(clinic_medication_entity_1.ClinicMedicationEntity)),
    __param(3, (0, typeorm_1.InjectRepository)(clinic_consumable_entity_1.ClinicConsumableEntity)),
    __param(4, (0, typeorm_1.InjectRepository)(clinic_vaccination_entity_1.ClinicVaccinationEntity)),
    __param(5, (0, typeorm_1.InjectRepository)(clinic_lab_report_entity_1.ClinicLabReport)),
    __param(6, (0, typeorm_1.InjectRepository)(appointment_assessment_entity_1.AppointmentAssessmentEntity)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], InventoryMappingService);
//# sourceMappingURL=inventory-mapping.service.js.map