"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateClinicTable1725036104483 = void 0;
const typeorm_1 = require("typeorm");
class UpdateClinicTable1725036104483 {
    async up(queryRunner) {
        // Add new columns
        await queryRunner.addColumns("clinics", [
            new typeorm_1.TableColumn({
                name: "address_line_1",
                type: "varchar",
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: "address_line_2",
                type: "varchar",
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: "city",
                type: "varchar",
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: "pin",
                type: "varchar",
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: "state",
                type: "varchar",
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: "country",
                type: "varchar",
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: "email",
                type: "varchar",
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: "website",
                type: "varchar",
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: "logo_url",
                type: "varchar",
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: "gst_number",
                type: "varchar",
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: "drug_license_number",
                type: "varchar",
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: "phone_numbers",
                type: "text",
                isArray: true,
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: "is_onboarded",
                type: "boolean",
                default: false
            }),
            new typeorm_1.TableColumn({
                name: "brand_id",
                type: "uuid",
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: "created_by",
                type: "uuid",
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: "updated_by",
                type: "uuid",
                isNullable: true
            })
        ]);
        // Add foreign key for brand relationship
        await queryRunner.createForeignKey("clinics", new typeorm_1.TableForeignKey({
            columnNames: ["brand_id"],
            referencedColumnNames: ["id"],
            referencedTableName: "brands",
            onDelete: "SET NULL"
        }));
        // Add foreign keys for created_by and updated_by
        await queryRunner.createForeignKey("clinics", new typeorm_1.TableForeignKey({
            columnNames: ["created_by"],
            referencedColumnNames: ["id"],
            referencedTableName: "users",
            onDelete: "SET NULL"
        }));
        await queryRunner.createForeignKey("clinics", new typeorm_1.TableForeignKey({
            columnNames: ["updated_by"],
            referencedColumnNames: ["id"],
            referencedTableName: "users",
            onDelete: "SET NULL"
        }));
    }
    async down(queryRunner) {
        // Remove foreign keys
        const table = await queryRunner.getTable("clinics");
        if (table) {
            const brandForeignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf("brand_id") !== -1);
            const createdByForeignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf("created_by") !== -1);
            const updatedByForeignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf("updated_by") !== -1);
            if (brandForeignKey) {
                await queryRunner.dropForeignKey("clinics", brandForeignKey);
            }
            if (createdByForeignKey) {
                await queryRunner.dropForeignKey("clinics", createdByForeignKey);
            }
            if (updatedByForeignKey) {
                await queryRunner.dropForeignKey("clinics", updatedByForeignKey);
            }
            // Remove new columns
            await queryRunner.dropColumns("clinics", [
                "address_line_1", "address_line_2", "city", "pin", "state", "country",
                "email", "website", "logo_url", "gst_number", "drug_license_number",
                "is_onboarded", "brand_id", "created_by", "updated_by"
            ]);
        }
    }
}
exports.UpdateClinicTable1725036104483 = UpdateClinicTable1725036104483;
//# sourceMappingURL=1725036104483-UpdateClinicsTable.js.map