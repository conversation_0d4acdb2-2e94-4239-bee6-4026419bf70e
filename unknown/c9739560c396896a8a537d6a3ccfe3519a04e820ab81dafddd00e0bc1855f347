"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PetTransferController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const pet_transfer_service_1 = require("./pet-transfer.service");
const pet_transfer_dto_1 = require("./dto/pet-transfer.dto");
let PetTransferController = class PetTransferController {
    constructor(petTransferService) {
        this.petTransferService = petTransferService;
    }
    async transferPet(petTransferDto) {
        await this.petTransferService.transferPet(petTransferDto);
        return {
            success: true,
            message: 'Pet transfer completed successfully',
            transferType: petTransferDto.destPatientId ? 'merge' : 'standard',
            patientId: petTransferDto.patientId,
            destPatientId: petTransferDto.destPatientId || null,
            ownerMapping: petTransferDto.ownerMapping || null,
            userMapping: petTransferDto.userMapping || null,
            clinicUserMapping: petTransferDto.clinicUserMapping || null
        };
    }
};
exports.PetTransferController = PetTransferController;
__decorate([
    (0, common_1.Post)('/transfer'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [pet_transfer_dto_1.PetTransferDto]),
    __metadata("design:returntype", Promise)
], PetTransferController.prototype, "transferPet", null);
exports.PetTransferController = PetTransferController = __decorate([
    (0, swagger_1.ApiTags)('Pet Transfer'),
    (0, common_1.Controller)('pet-transfer'),
    __metadata("design:paramtypes", [pet_transfer_service_1.PetTransferService])
], PetTransferController);
//# sourceMappingURL=pet-transfer.controller.js.map