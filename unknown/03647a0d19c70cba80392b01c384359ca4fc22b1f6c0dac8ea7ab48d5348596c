{"version": 3, "file": "1725036437254-UpdateBrandsTable.js", "sourceRoot": "", "sources": ["../../../src/migrations/1725036437254-UpdateBrandsTable.ts"], "names": [], "mappings": ";;;AAAA,qCAA2E;AAE3E,MAAa,8BAA8B;IAChC,KAAK,CAAC,EAAE,CAAC,WAAwB;QAEpC,MAAM,KAAK,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACpD,IAAG,KAAK,EAAC,CAAC;YACN,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAE3F,IAAI,CAAC,UAAU,EAAE,CAAC;gBACd,MAAM,WAAW,CAAC,gBAAgB,CAAC,SAAS,EAAE,IAAI,yBAAe,CAAC;oBAC9D,WAAW,EAAE,CAAC,UAAU,CAAC;oBACzB,qBAAqB,EAAE,CAAC,IAAI,CAAC;oBAC7B,mBAAmB,EAAE,QAAQ;oBAC7B,QAAQ,EAAE,UAAU;iBACvB,CAAC,CAAC,CAAC;YACR,CAAC;QACL,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACtC,MAAM,KAAK,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACpD,IAAG,KAAK,EAAC,CAAC;YACN,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3F,IAAI,UAAU,EAAE,CAAC;gBACb,MAAM,WAAW,CAAC,cAAc,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YAC5D,CAAC;QACL,CAAC;IACL,CAAC;CACJ;AA3BD,wEA2BC"}