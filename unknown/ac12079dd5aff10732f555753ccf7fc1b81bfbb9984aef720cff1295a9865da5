"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicServices1725520998555 = void 0;
const typeorm_1 = require("typeorm");
class ClinicServices1725520998555 {
    constructor() {
        this.name = 'ClinicServices1725520998555';
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'clinic_services',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'clinic_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'brand_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'unique_id',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'service_name',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'chargeable_price',
                    type: 'decimal',
                    scale: 2, // it specifies digits after the decimal point
                    precision: 10, //  it specifies the total number of digits that can be stored, both before and after the decimal point
                    isNullable: true,
                },
                {
                    name: 'tax',
                    type: 'decimal',
                    scale: 2, // it specifies digits after the decimal point
                    precision: 10, //  it specifies the total number of digits that can be stored, both before and after the decimal point
                    isNullable: true,
                },
                {
                    name: 'associated_lab',
                    type: 'varchar',
                    isNullable: true
                },
                {
                    name: 'description',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                },
            ]
        }), true);
        await queryRunner.createForeignKey('clinic_services', new typeorm_1.TableForeignKey({
            columnNames: ['brand_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'brands',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
        await queryRunner.createForeignKey('clinic_services', new typeorm_1.TableForeignKey({
            columnNames: ['clinic_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinics',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('clinic_services');
    }
}
exports.ClinicServices1725520998555 = ClinicServices1725520998555;
//# sourceMappingURL=1725520998555-CreateClinicServices.js.map