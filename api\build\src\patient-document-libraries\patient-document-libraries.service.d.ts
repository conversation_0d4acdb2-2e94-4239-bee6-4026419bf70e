import { CreatePatientDocumentLibraryDto } from './dto/create-patient-document-library.dto';
import { UpdateSignedDocumentDto } from './dto/update-patient-document-library.dto';
import { PatientDocumentLibrary } from './entities/patient-document-library.entity';
import { Repository } from 'typeorm';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { User } from '../users/entities/user.entity';
import { SESMailService } from '../utils/aws/ses/send-mail-service';
import { WhatsappService } from '../utils/whatsapp-integration/whatsapp.service';
import { S3Service } from '../utils/aws/s3/s3.service';
export declare class PatientDocumentLibrariesService {
    private readonly logger;
    private readonly patientDocumentLibraryRepository;
    private readonly userRepository;
    private readonly mailService;
    private readonly whatsappService;
    private s3Service;
    constructor(logger: WinstonLogger, patientDocumentLibraryRepository: Repository<PatientDocumentLibrary>, userRepository: Repository<User>, mailService: SESMailService, whatsappService: WhatsappService, s3Service: S3Service);
    sendMail(body: string, buffers: Buffer[], fileName: string[], email: string, subject?: string): Promise<void>;
    downloadPDF(url: string): Promise<Buffer>;
    create(createPatientDocumentLibraryDto: CreatePatientDocumentLibraryDto): Promise<any>;
    findOne(id: string): Promise<PatientDocumentLibrary>;
    findAll(patientId: string, page?: number, limit?: number, search?: string): Promise<{
        data: any;
        total: number;
        page: number;
        pageCount: number;
    }>;
    sendSignedDocument(documentId: string, updateSignedDocumentDto: UpdateSignedDocumentDto): Promise<import("typeorm").UpdateResult | null>;
    sendSignableDocumentUrl(documentId: string, url: string): Promise<void>;
    sendNonSignableDocumentUrl(documentId: string, documentBody: any): Promise<any>;
    sendNoSignatureRequiredAttachments(documentId: string, documentBody: any): Promise<any>;
}
