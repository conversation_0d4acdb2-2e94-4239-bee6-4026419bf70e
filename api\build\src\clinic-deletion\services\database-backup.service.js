"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseBackupService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const winston_logger_service_1 = require("../../utils/logger/winston-logger.service");
const s3_service_1 = require("../../utils/aws/s3/s3.service");
const query_manager_service_1 = require("./query-manager.service");
let DatabaseBackupService = class DatabaseBackupService {
    constructor(dataSource, logger, s3Service, queryManagerService) {
        this.dataSource = dataSource;
        this.logger = logger;
        this.s3Service = s3Service;
        this.queryManagerService = queryManagerService;
    }
    /**
     * Backup all database records for a clinic or brand
     */
    async backupDatabaseRecords(targetType, targetId, backupId, backupBasePath) {
        const startTime = Date.now();
        const backupPaths = []; // Move outside try block for catch block access
        try {
            this.logger.log('Starting database backup', {
                targetType,
                targetId,
                backupId
            });
            // Get table backup queries from centralized QueryManager
            const tableQueries = this.queryManagerService.getDatabaseBackupQueries(targetType, targetId);
            const manifest = {
                backupId,
                createdAt: new Date(),
                tables: [],
                restoreOrder: [],
                totalRecords: 0,
                totalSizeBytes: 0
            };
            let totalRecords = 0;
            let totalSizeBytes = 0;
            // Process each table
            for (const [tableName, query] of Object.entries(tableQueries)) {
                try {
                    const tableResult = await this.backupTable(tableName, query, backupBasePath, backupId);
                    if (tableResult.recordCount > 0) {
                        manifest.tables.push({
                            tableName,
                            recordCount: tableResult.recordCount,
                            fileName: tableResult.fileName,
                            dependencies: query.dependencies || [],
                            sizeBytes: tableResult.sizeBytes
                        });
                        backupPaths.push(tableResult.s3Path);
                        totalRecords += tableResult.recordCount;
                        totalSizeBytes += tableResult.sizeBytes;
                    }
                }
                catch (error) {
                    const errorMessage = error instanceof Error
                        ? error.message
                        : 'Unknown error';
                    this.logger.error(`Error backing up table ${tableName}`, {
                        error: errorMessage,
                        backupId
                    });
                    throw error;
                }
            }
            // Determine restore order based on dependencies
            manifest.restoreOrder = this.calculateRestoreOrder(manifest.tables);
            manifest.totalRecords = totalRecords;
            manifest.totalSizeBytes = totalSizeBytes;
            // Upload manifest to S3
            const manifestPath = `${backupBasePath}/database/manifest.json`;
            await this.s3Service.uploadBuffer(Buffer.from(JSON.stringify(manifest, null, 2)), manifestPath, 'application/json');
            backupPaths.push(manifestPath);
            const duration = Date.now() - startTime;
            this.logger.log('Database backup completed', {
                backupId,
                totalTables: manifest.tables.length,
                totalRecords,
                totalSizeBytes,
                duration
            });
            return {
                manifest,
                backupPaths,
                totalRecords,
                totalSizeBytes,
                duration
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Database backup failed - initiating rollback', {
                error: errorMessage,
                backupId,
                targetType,
                targetId,
                uploadedFiles: backupPaths.length,
                duration: Date.now() - startTime
            });
            // Rollback: Clean up any uploaded files
            await this.rollbackDatabaseBackup(backupPaths, backupId);
            // Re-throw with enhanced error context
            throw new Error(`Database backup failed for ${targetType} ${targetId}: ${errorMessage}`);
        }
    }
    /**
     * Rollback database backup by cleaning up uploaded files
     */
    async rollbackDatabaseBackup(uploadedFiles, backupId) {
        if (uploadedFiles.length === 0) {
            this.logger.log('No files to rollback for database backup', {
                backupId
            });
            return;
        }
        this.logger.warn('Rolling back database backup files', {
            backupId,
            filesToDelete: uploadedFiles.length
        });
        const rollbackErrors = [];
        for (const filePath of uploadedFiles) {
            try {
                await this.s3Service.deleteFile(filePath);
                this.logger.log('Rolled back database backup file', {
                    filePath,
                    backupId
                });
            }
            catch (rollbackError) {
                const rollbackErrorMessage = rollbackError instanceof Error
                    ? rollbackError.message
                    : 'Unknown rollback error';
                rollbackErrors.push(`Failed to delete ${filePath}: ${rollbackErrorMessage}`);
                this.logger.error('Failed to rollback database backup file', {
                    filePath,
                    backupId,
                    error: rollbackErrorMessage
                });
            }
        }
        if (rollbackErrors.length > 0) {
            this.logger.error('Database backup rollback completed with errors', {
                backupId,
                errors: rollbackErrors,
                totalErrors: rollbackErrors.length,
                totalFiles: uploadedFiles.length
            });
        }
        else {
            this.logger.log('Database backup rollback completed successfully', {
                backupId,
                filesDeleted: uploadedFiles.length
            });
        }
    }
    /**
     * Backup a single table
     */
    async backupTable(tableName, query, backupBasePath, backupId) {
        try {
            // Execute query to get records
            const records = await this.dataSource.query(query.sql, query.params);
            if (records.length === 0) {
                return {
                    recordCount: 0,
                    fileName: '',
                    s3Path: '',
                    sizeBytes: 0
                };
            }
            // Prepare backup data
            const backupData = {
                backupId,
                tableName,
                createdAt: new Date().toISOString(),
                recordCount: records.length,
                records
            };
            const jsonData = JSON.stringify(backupData, null, 2);
            const sizeBytes = Buffer.byteLength(jsonData, 'utf8');
            const fileName = `${tableName}.json`;
            const s3Path = `${backupBasePath}/database/${fileName}`;
            // Upload to S3
            await this.s3Service.uploadBuffer(Buffer.from(jsonData), s3Path, 'application/json');
            this.logger.log(`Table ${tableName} backed up`, {
                recordCount: records.length,
                sizeBytes,
                s3Path
            });
            return {
                recordCount: records.length,
                fileName,
                s3Path,
                sizeBytes
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error(`Failed to backup table ${tableName}`, {
                error: errorMessage
            });
            throw error;
        }
    }
    /**
     * Calculate the correct restore order based on foreign key dependencies
     */
    calculateRestoreOrder(tables) {
        const order = [];
        const processed = new Set();
        const processing = new Set();
        const visit = (tableName) => {
            if (processed.has(tableName))
                return;
            if (processing.has(tableName)) {
                // Circular dependency detected - log warning but continue
                this.logger.warn(`Circular dependency detected for table ${tableName}`);
                return;
            }
            processing.add(tableName);
            const table = tables.find(t => t.tableName === tableName);
            if (table) {
                // Process dependencies first
                for (const dep of table.dependencies) {
                    if (tables.some(t => t.tableName === dep)) {
                        visit(dep);
                    }
                }
            }
            processing.delete(tableName);
            processed.add(tableName);
            order.push(tableName);
        };
        // Visit all tables
        for (const table of tables) {
            visit(table.tableName);
        }
        return order;
    }
    /**
     * @deprecated Use QueryManagerService.getDatabaseBackupQueries() instead
     * This method has been moved to centralized QueryManagerService for consistency
     */
    getDatabaseBackupQueries(targetType, targetId) {
        // DEPRECATED: This method is no longer used
        // Use this.queryManagerService.getDatabaseBackupQueries() instead
        throw new Error('Deprecated method - use QueryManagerService.getDatabaseBackupQueries() instead');
    }
};
exports.DatabaseBackupService = DatabaseBackupService;
exports.DatabaseBackupService = DatabaseBackupService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource,
        winston_logger_service_1.WinstonLogger,
        s3_service_1.S3Service,
        query_manager_service_1.QueryManagerService])
], DatabaseBackupService);
//# sourceMappingURL=database-backup.service.js.map