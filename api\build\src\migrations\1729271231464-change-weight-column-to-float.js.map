{"version": 3, "file": "1729271231464-change-weight-column-to-float.js", "sourceRoot": "", "sources": ["../../../src/migrations/1729271231464-change-weight-column-to-float.ts"], "names": [], "mappings": ";;;AAAA,qCAAuE;AAEvE,MAAa,wCAAwC;IAG7C,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,uDAAuD;QACvD,MAAM,WAAW,CAAC,SAAS,CAC1B,cAAc,EACd,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,SAAS;YACf,SAAS,EAAE,CAAC,EAAE,0CAA0C;YACxD,KAAK,EAAE,CAAC,EAAE,sCAAsC;YAChD,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;QAEF,gFAAgF;QAChF,MAAM,WAAW,CAAC,KAAK,CAAC;;;GAGvB,CAAC,CAAC;QAEH,8BAA8B;QAC9B,MAAM,WAAW,CAAC,UAAU,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAEvD,uDAAuD;QACvD,MAAM,WAAW,CAAC,YAAY,CAAC,cAAc,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;IACzE,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,8CAA8C;QAC9C,MAAM,WAAW,CAAC,SAAS,CAC1B,cAAc,EACd,IAAI,qBAAW,CAAC;YACf,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,SAAS;YACf,UAAU,EAAE,IAAI;SAChB,CAAC,CACF,CAAC;QAEF,MAAM,WAAW,CAAC,KAAK,CAAC;;;GAGvB,CAAC,CAAC;QAEH,MAAM,WAAW,CAAC,UAAU,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QACvD,MAAM,WAAW,CAAC,YAAY,CAAC,cAAc,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;IACzE,CAAC;CACD;AAhDD,4FAgDC"}