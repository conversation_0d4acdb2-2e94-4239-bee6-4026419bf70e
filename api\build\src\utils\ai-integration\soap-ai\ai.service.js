"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIService = void 0;
const common_1 = require("@nestjs/common");
const openai_1 = require("openai");
const winston_logger_service_1 = require("../../logger/winston-logger.service");
let AIService = class AIService {
    constructor(logger) {
        this.logger = logger;
        this.openai = new openai_1.default({
            apiKey: process.env.OPENAI_API_KEY
        });
    }
    async generateSOAPNotes(clinicalNotes) {
        var _a, _b;
        try {
            const completion = await this.openai.chat.completions.create({
                model: "gpt-4o",
                messages: [
                    {
                        role: "system",
                        content: `You are a veterinary professional. Analyze the provided clinical notes and generate a comprehensive SOAP note following these guidelines:

            1. Subjective:
               - Pet's presenting complaints as reported by the owner
               - Duration and progression of symptoms
               - Relevant medical history
               - Current medications and diet
               - Changes in behavior, appetite, or routine
               - Owner's observations and concerns
            
            2. Objective:
               - Vital signs (Temperature, Pulse, Respiration)
               - Physical examination findings
               - Weight and body condition score
               - Laboratory results if provided
               - Diagnostic imaging findings if available
               - Measurable clinical findings
            
            3. Assessment:
               - Primary diagnosis or differential diagnoses
               - Clinical reasoning and interpretation
               - Disease progression or status
               - Response to previous treatments if applicable
               - Current health status evaluation
            
            4. Plan:
               - Prescribed medications with dosages
               - Treatment procedures performed or scheduled
               - Dietary recommendations
               - Activity restrictions or recommendations
               - Follow-up schedule
               - Owner education points
               - Preventive care recommendations
            
            Additional notes:
            - Use veterinary medical terminology appropriately
            - Include relevant species-specific considerations
            - Note any breed-specific concerns
            - Highlight any critical monitoring parameters for owners
            - Include emergency care instructions if applicable
            
            Format the response in JSON with these exact keys:
            {
              "subjectiveNotes": "",
              "objectiveNotes": "",
              "assessmentNotes": "",
              "planNotes": "",
              "dischargeSummary": "",
              "temperature": null
            }`
                    },
                    {
                        role: "user",
                        content: `Please create a SOAP note for the following veterinary case:\n\nClinical Notes:\n${clinicalNotes}`
                    }
                ],
                response_format: { type: "json_object" },
                temperature: 0.7,
            });
            if (completion.usage) {
                this.logger.log('OpenAI API usage', {
                    inputTokens: completion.usage.prompt_tokens,
                    outputTokens: completion.usage.completion_tokens,
                    totalTokens: completion.usage.total_tokens
                });
            }
            const content = (_b = (_a = completion.choices[0]) === null || _a === void 0 ? void 0 : _a.message) === null || _b === void 0 ? void 0 : _b.content;
            if (!content) {
                throw new common_1.InternalServerErrorException('No content received from OpenAI');
            }
            try {
                const parsedResponse = JSON.parse(content);
                return parsedResponse;
            }
            catch (parseError) {
                this.logger.error('Failed to parse OpenAI response', { content, error: parseError });
                throw new common_1.InternalServerErrorException('Invalid response format from OpenAI');
            }
        }
        catch (error) {
            this.logger.error('OpenAI API error', error);
            throw error;
        }
    }
    async regenerateSOAPNotes(clinicalNotes, previousNotes) {
        const systemPrompt = previousNotes ?
            `Consider the previous SOAP notes for context: ${previousNotes}` : '';
        return this.generateSOAPNotes(`${systemPrompt}\n\n${clinicalNotes}`);
    }
};
exports.AIService = AIService;
exports.AIService = AIService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger])
], AIService);
//# sourceMappingURL=ai.service.js.map