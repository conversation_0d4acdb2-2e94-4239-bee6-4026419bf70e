{"version": 3, "file": "1745557427917-CreateAppointmentAuditLogTable.js", "sourceRoot": "", "sources": ["../../../src/migrations/1745557427917-CreateAppointmentAuditLogTable.ts"], "names": [], "mappings": ";;;AAAA,qCAAiE;AAEjE,mCAAmC;AACnC,4FAA4F;AAC5F,uFAAuF;AACvF,IAAY,yBAIX;AAJD,WAAY,yBAAyB;IACpC,8CAAiB,CAAA;IACjB,8CAAiB,CAAA;IACjB,8CAAiB,CAAA,CAAC,iEAAiE;AACpF,CAAC,EAJW,yBAAyB,yCAAzB,yBAAyB,QAIpC;AAED,MAAa,2CAA2C;IAGhD,KAAK,CAAC,EAAE,CAAC,WAAwB;QACvC,yCAAyC;QACzC,MAAM,WAAW,CAAC,WAAW,CAC5B,IAAI,eAAK,CAAC;YACT,IAAI,EAAE,uBAAuB;YAC7B,OAAO,EAAE;gBACR;oBACC,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,IAAI;oBACf,OAAO,EAAE,oBAAoB,CAAC,yCAAyC;iBACvE;gBACD;oBACC,IAAI,EAAE,eAAe;oBACrB,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,KAAK;oBACjB,OAAO,EAAE,qCAAqC;iBAC9C;gBACD;oBACC,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,KAAK;oBACjB,OAAO,EAAE,8CAA8C;iBACvD;gBACD;oBACC,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,SAAS,EAAE,oEAAoE;oBACrF,UAAU,EAAE,KAAK;oBACjB,OAAO,EAAE,+CAA+C;iBACxD;gBACD;oBACC,IAAI,EAAE,eAAe;oBACrB,IAAI,EAAE,OAAO,EAAE,sDAAsD;oBACrE,UAAU,EAAE,IAAI,EAAE,kFAAkF;oBACpG,OAAO,EACN,sFAAsF;iBACvF;gBACD;oBACC,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,IAAI;oBAChB,OAAO,EACN,iDAAiD;iBAClD;gBACD;oBACC,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,0BAA0B;oBAChC,OAAO,EAAE,mBAAmB;oBAC5B,UAAU,EAAE,KAAK;oBACjB,OAAO,EAAE,oCAAoC;iBAC7C;aACD;YACD,+CAA+C;YAC/C,MAAM,EAAE;gBACP;oBACC,IAAI,EAAE,kCAAkC;oBACxC,WAAW,EAAE,CAAC,QAAQ,CAAC;oBACvB,UAAU,EAAE,eAAe,yBAAyB,CAAC,MAAM,OAAO,yBAAyB,CAAC,MAAM,OAAO,yBAAyB,CAAC,MAAM,IAAI;iBAC7I;aACD;SACD,CAAC,CACF,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACzC,iBAAiB;QACjB,MAAM,WAAW,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC;IAEtD,CAAC;CACD;AAxED,kGAwEC"}