"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePhoneNumbersAndAddClinicLogo1726907487082 = void 0;
const typeorm_1 = require("typeorm");
class UpdatePhoneNumbersAndAddClinicLogo1726907487082 {
    async up(queryRunner) {
        // Add clinic_logo column
        await queryRunner.addColumn('clinics', new typeorm_1.TableColumn({
            name: 'clinic_logo',
            type: 'varchar',
            length: '255',
            isNullable: true
        }));
        // First, we'll create a temporary column to store the new format for phone numbers
        await queryRunner.addColumn('clinics', new typeorm_1.TableColumn({
            name: 'phone_numbers_jsonb',
            type: 'jsonb',
            isNullable: true
        }));
        // Convert existing data to the new format
        await queryRunner.query(`
            UPDATE clinics
            SET phone_numbers_jsonb = (
                SELECT json_agg(json_build_object('country_code', '', 'number', phone_number))
                FROM unnest(phone_numbers) AS phone_number
            )
        `);
        // Drop the old phone_numbers column
        await queryRunner.dropColumn('clinics', 'phone_numbers');
        // Rename the new column to the original name
        await queryRunner.renameColumn('clinics', 'phone_numbers_jsonb', 'phone_numbers');
    }
    async down(queryRunner) {
        // Remove clinic_logo column
        await queryRunner.dropColumn('clinics', 'clinic_logo');
        // If we need to revert, we'll do the opposite for phone_numbers
        await queryRunner.addColumn('clinics', new typeorm_1.TableColumn({
            name: 'phone_numbers_text',
            type: 'text',
            isArray: true,
            isNullable: true
        }));
        // Convert data back to the old format
        await queryRunner.query(`
            UPDATE clinics
            SET phone_numbers_text = (
                SELECT array_agg(item->>'number')
                FROM jsonb_array_elements(phone_numbers) AS item
            )
        `);
        // Drop the JSONB column
        await queryRunner.dropColumn('clinics', 'phone_numbers');
        // Rename the text array column to the original name
        await queryRunner.renameColumn('clinics', 'phone_numbers_text', 'phone_numbers');
    }
}
exports.UpdatePhoneNumbersAndAddClinicLogo1726907487082 = UpdatePhoneNumbersAndAddClinicLogo1726907487082;
//# sourceMappingURL=1726907487082-UpdateClinicColumnsPhoneNumbers.js.map