import { CreateClientBookingDto, UpdateClientBookingDto, ClientBookingResponseDto } from './dto/client-booking.dto';
import { RequestWithUser } from '../auth/interfaces/request-with-user.interface';
import { ClientBookingService } from './client-booking.service';
export declare class ClientBookingController {
    private readonly clientBookingService;
    private readonly logger;
    constructor(clientBookingService: ClientBookingService);
    createBooking(createBookingDto: CreateClientBookingDto, req: RequestWithUser): Promise<ClientBookingResponseDto>;
    getBooking(appointmentId: string, req: RequestWithUser): Promise<ClientBookingResponseDto>;
    updateBooking(appointmentId: string, updateBookingDto: UpdateClientBookingDto, req: RequestWithUser): Promise<ClientBookingResponseDto>;
    deleteBooking(appointmentId: string, req: RequestWithUser): Promise<ClientBookingResponseDto>;
}
