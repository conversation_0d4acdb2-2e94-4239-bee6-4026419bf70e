"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AvailableTimeSlotsResponseDto = exports.NextAvailableSlotInfoDto = exports.TimeSlotDto = exports.TimeSlotRequestDto = exports.AvailableDatesResponseDto = exports.AvailableDatesRequestDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
/**
 * DTO for requesting available dates for a doctor
 */
class AvailableDatesRequestDto {
}
exports.AvailableDatesRequestDto = AvailableDatesRequestDto;
__decorate([
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsOptional)(),
    (0, swagger_1.ApiProperty)({
        description: 'Start date for date range (default: current date)',
        required: false,
        example: '2023-06-01'
    }),
    __metadata("design:type", String)
], AvailableDatesRequestDto.prototype, "startDate", void 0);
__decorate([
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsOptional)(),
    (0, swagger_1.ApiProperty)({
        description: 'End date for date range (default: current date + 30 days)',
        required: false,
        example: '2023-06-30'
    }),
    __metadata("design:type", String)
], AvailableDatesRequestDto.prototype, "endDate", void 0);
__decorate([
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, swagger_1.ApiProperty)({
        description: 'Optional clinic ID to filter availability by clinic',
        required: false,
        example: '123e4567-e89b-12d3-a456-************'
    }),
    __metadata("design:type", String)
], AvailableDatesRequestDto.prototype, "clinicId", void 0);
/**
 * DTO for response with available dates for a doctor
 */
class AvailableDatesResponseDto {
}
exports.AvailableDatesResponseDto = AvailableDatesResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of dates available for booking',
        example: ['2023-12-25', '2023-12-26']
    }),
    __metadata("design:type", Array)
], AvailableDatesResponseDto.prototype, "availableDates", void 0);
/**
 * DTO for requesting available time slots for a doctor on a specific date
 */
class TimeSlotRequestDto {
}
exports.TimeSlotRequestDto = TimeSlotRequestDto;
__decorate([
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsOptional)(),
    (0, swagger_1.ApiProperty)({
        description: 'Optional clinic ID to filter availability by clinic',
        required: false,
        example: '123e4567-e89b-12d3-a456-************'
    }),
    __metadata("design:type", String)
], TimeSlotRequestDto.prototype, "clinicId", void 0);
/**
 * DTO representing a time slot
 */
class TimeSlotDto {
}
exports.TimeSlotDto = TimeSlotDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Start time of the slot (HH:mm)',
        example: '09:00'
    }),
    __metadata("design:type", String)
], TimeSlotDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'End time of the slot (HH:mm)',
        example: '09:30'
    }),
    __metadata("design:type", String)
], TimeSlotDto.prototype, "endTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Indicates if the slot is currently available for booking',
        example: true
    }),
    __metadata("design:type", Boolean)
], TimeSlotDto.prototype, "isAvailable", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the doctor associated with the slot',
        example: 'uuid-for-doctor-1',
        required: false // Might not be present if requesting for a specific doctor
    }),
    __metadata("design:type", String)
], TimeSlotDto.prototype, "doctorId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the doctor associated with the slot',
        example: 'Dr. Jane Doe',
        required: false // Might not be present if requesting for a specific doctor
    }),
    __metadata("design:type", String)
], TimeSlotDto.prototype, "doctorName", void 0);
/**
 * DTO for response with available time slots for a doctor on a specific date
 */
class NextAvailableSlotInfoDto {
}
exports.NextAvailableSlotInfoDto = NextAvailableSlotInfoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Date of the next available slot (YYYY-MM-DD)',
        example: '2024-01-10'
    }),
    __metadata("design:type", String)
], NextAvailableSlotInfoDto.prototype, "date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Start time of the next available slot (HH:mm)',
        example: '14:00'
    }),
    __metadata("design:type", String)
], NextAvailableSlotInfoDto.prototype, "startTime", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'End time of the next available slot (HH:mm)',
        example: '14:30'
    }),
    __metadata("design:type", String)
], NextAvailableSlotInfoDto.prototype, "endTime", void 0);
class AvailableTimeSlotsResponseDto {
}
exports.AvailableTimeSlotsResponseDto = AvailableTimeSlotsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The date for which time slots are listed (YYYY-MM-DD)',
        example: '2023-12-25'
    }),
    __metadata("design:type", String)
], AvailableTimeSlotsResponseDto.prototype, "date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of time slots for the specified date',
        type: [TimeSlotDto]
    }),
    __metadata("design:type", Array)
], AvailableTimeSlotsResponseDto.prototype, "timeSlots", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Information about the next available slot for doctors who were unavailable on the requested date. Keyed by doctor ID.',
        type: 'object',
        additionalProperties: {
            $ref: '#/components/schemas/NextAvailableSlotInfoDto'
        },
        required: false, // Only present if applicable
        example: {
            'uuid-doctor-unavailable': {
                date: '2024-01-10',
                startTime: '14:00',
                endTime: '14:30'
            }
        }
    }),
    __metadata("design:type", Object)
], AvailableTimeSlotsResponseDto.prototype, "nextAvailableSlots", void 0);
//# sourceMappingURL=client-availability.dto.js.map