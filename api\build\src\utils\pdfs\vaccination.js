"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateVaccinationCertificate = void 0;
const generateVaccinationCertificate = ({ vaccineName, vaccinationDate, petName, ownerName, clinicName, clinicAddress, clinicPhone, clinicEmail, clinicWebsite, vetName, vetLicense, species, breed, color, weight, reproductiveStatus, dob, age, digitalSignature, ownerEmail, ownerPhone, gender, vaccineId }) => {
    return `
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vaccination Certificate</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Qwitcher+Grypen:wght@400;700&display=swap" rel="stylesheet">
   <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            color: #000000;
            background: #F9F7F2;
            font-weight: 300;
            font-size: 12px;
            line-height: 14.52px;
        }
        * {
            box-sizing: border-box;
        }
        p {
            margin: 0;
        }

        .fw-400 {
            font-size: 14px;
            font-weight: 500;
            line-height: 16.94px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
        }

        .fw-500 {
            font-weight: 500;
        }

        .text-grey {
            color: #000000;
        }

        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        .pb-16 {
            padding-bottom: 16px;
        }


        .block {
            display: block;
        }

        .vertical_divider {
            border-left: 0.5px solid #BEBDBD;
            margin: 0 10px;
        }

        .invoice-container {
            max-width: 612px;
            margin: 0 auto;
            padding: 56px 34px;
        }

        .invoice-header h1 {
            font-size: 36px;
            font-weight: 100;
            margin: 0 0 0px;
            line-height: 43.97px;
        }

        .invoice-header p {
            font-size: 18px;
            font-weight: 300;
            line-height: 24px;
            display: flex;
        }
        
         .invoice-header .vaccination-title {
            border-right: 0.3px solid #BEBDBD;
            padding-right: 10px;
            margin-right: 10px;
        }

         .invoice-header .date{
            margin-left: 20px;
            border-left:0.5px solid #D6D6D6;;
            padding-left: 20px;
            flex-shrink: 0;
        }

        .invoice-details {
            border-top: 0.5px solid #D6D6D6;
            padding: 25px 0;
        }

        .invoice-details h5 {
            font-size: 18px;
            font-weight: 400;
            line-height: 24px;
            margin: 0;
            color: #000000;
        }

        .invoice-details h6 {
            font-size: 14px;
            font-weight: 500;
            line-height: 16.94px;
            margin: 0;
            color: #000000;
        }

        .invoice-details p {
            font-size: 10px;
            font-weight: 400;
            line-height: 18px;
            color: #000000;
        }

        .invoice-info {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin-top: 6px;
        }

        .invoice-info div:first-child {
            max-width: 170px;
        }

        .invoice-info div:last-child {
            max-width: 145px;
        }

        .patient-info {
            border-top: 0.5px solid #D6D6D6;
            display: flex;
            padding-top: 40px;
            margin-bottom: 16px;
        }

        .patient-info div p:first-child {
              font-size: 10px;
            font-weight: 300;
            line-height: 10.89px;
            margin-bottom: 2px;
            color: #000000;
        }

        .patient-info div p:last-child {
            color: #59645D;
            font-size: 14px;
            font-weight: 300;
            line-height: 16.94px;
            color: #000000
        }

       .patient-details {
            display: flex;
            justify-content: space-between;
            max-width: 100%;
            margin-bottom: 30px;
        }

        .patient-details p {
            border-right: 0.5px solid #BEBDBD;
            padding: 5px 20px 0;
            color: #000000;
            width: 33.3%;
        }
        .patient-details p:nth-child(3n+1) {
            padding-left:0 ;
        }

        .patient-details p:nth-child(3n) {
            border: none;
        }


        .vaccination-info h6 {
            border-bottom: 0.5px solid #D6D6D6;
            padding-bottom: 6px;
            margin: 0 0 13px;
            font-family: Inter;
            font-size: 14px;
            font-weight: 500;
            line-height: 16.94px;
            text-align: left;
        }
        .vaccination-info-detail {
             display: flex;
            justify-content: flex-start;
            gap: 30px;
            max-width: 100%;
        }
        
        .vaccination-info-detail div h6 {
            border-bottom: 0.5px solid #D6D6D6;
            font-family: Inter;
            font-size: 8px;
            font-weight: 400;
            line-height: 7.26px;
            letter-spacing: 0.05em;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
        }

        .vaccination-info-detail div p {
            font-family: Inter;
            font-size: 11px;
            font-weight: 400;
            line-height: 16px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
        }

        .vaccination-info p {
            font-size: 12px;
            font-weight: 300;
            line-height: 14.52px;
            margin-bottom: 8px;
        }

        .vaccination-info .vertical_divider {
            margin: 0 5px;
        }
        .signature-content {
            display: flex;
            align-items: flex-end;
            gap: 20px;
            margin-top: 90px;
            justify-content: space-between;
        }

        .signature h6 { font-size: 14px; font-weight: 400; line-height: 16.94px; margin: 10px 0 3px;  margin-top: 20px}
        .signature p { font-size: 10px; font-weight: 400; line-height: 12.1px;}
        .font-Ridenation {
            font-size: 55px !important;
            font-family: "Qwitcher Grypen", cursive;
            font-weight: 700;
            font-style: normal;
            color: #504947
        }
        #patient-name {
            font-weight: 500;
        }
        @media print {
            body {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                margin: 0;
            }
        }
    </style>
</head>

<body>

    <div class="invoice-container">
        <div class="invoice-header pb-16">
            <h1>Vaccination Certificate</h1>
            <p><span class='Vaccination-id'>Vaccination ID #${vaccineId}</span><span class="text-grey date">${vaccinationDate}</span></p>
        </div>

        <div class="invoice-details pt-20">
            <h6>${clinicName}</h6>
            <div class="invoice-info">
                <div>
                    <p>${clinicAddress}</p>
                </div>
                <div>
                    <p>${clinicPhone}</p>
                    <p>
                        <span class="block">${clinicEmail}</span>
                        <span class="block">${clinicWebsite}</span>
                    </p>
                </div>
            </div>
        </div>

        <div class="patient-info">
            <div>
                <p id='patient-name'>${petName}</p>
            </div>
            ${breed ? `<div class="vertical_divider"></div>
            <div>
                <p>${breed}</p>
            </div>` : ''}
        </div>
        <div class="patient-details">

            <div>
                <h6>OWNER DETAILS</h6>
                ${ownerName ? `<p>${ownerName}</p>` : ''} 
                ${ownerEmail ? `<p>${ownerEmail}</p>` : ''} 
                ${ownerPhone ? `<p>${ownerPhone}</p>` : ''} 
             </div>
                ${color ? `<div>
                 <h6>COLOUR</h6>
                 <p>${color}</p>
             </div>` : ''}
                ${gender ? `<div>
                 <h6>GENDER</h6>
                 <p>${gender}</p>
             </div>` : ''}
            ${weight ? `<div>
                 <h6>WEIGHT</h6>
                 <p>${weight}</p>
             </div>` : ''}
             ${dob ? `<div>
                 <h6>D.O.B</h6>
                 <p>${dob}</p>
             </div>` : ''}
             ${age ? `<div>
                 <h6>Age</h6>
                 <p>${age}</p>
             </div>` : ''} 
        </div>

        <div class="vaccination-info">
            <h6>Vaccination Information</h6>
            <div class="vaccination-info-detail">   
             <div>
                 <h6>VACCINATION NAME</h6>
                 <p>${vaccineName}</p>
             </div>
             <div>
                 <h6>DATE OF VACCINATION</h6>
                 <p>${vaccinationDate}</p>
             </div>
          </div>
        </div>

        <div class="signature-content">
            <p class="fw-400">Vaccinated at ${clinicName}</p>
            <div class="signature">
                <p class='font-Ridenation' style='margin-bottom: 4px;'>${digitalSignature}</p>
                <h6>${vetName}</h6>
                ${vetLicense ? `<p class='text-grey'>License no. ${vetLicense}</p>` : ''}
            </div>
        </div>
    </div>
</body>

</html>
`;
};
exports.generateVaccinationCertificate = generateVaccinationCertificate;
//# sourceMappingURL=vaccination.js.map