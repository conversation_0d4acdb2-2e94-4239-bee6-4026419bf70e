"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectedUser1725298064276 = void 0;
const typeorm_1 = require("typeorm");
class ConnectedUser1725298064276 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'chat_user_sessions',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'user_id',
                    type: 'uuid'
                },
                {
                    name: 'socket_id',
                    type: 'varchar'
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                }
            ],
            foreignKeys: [
                {
                    columnNames: ['user_id'],
                    referencedTableName: 'clinic_users',
                    referencedColumnNames: ['id']
                }
            ]
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('chat_user_sessions');
    }
}
exports.ConnectedUser1725298064276 = ConnectedUser1725298064276;
//# sourceMappingURL=1725298064276-CreateChatUserSessions.js.map