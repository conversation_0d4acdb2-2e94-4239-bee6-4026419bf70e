"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePatientsTable1721818292453 = void 0;
const typeorm_1 = require("typeorm");
class CreatePatientsTable1721818292453 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'patients',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '100',
                    isNullable: false
                },
                {
                    name: 'species',
                    type: 'varchar',
                    length: '50',
                    isNullable: true
                },
                {
                    name: 'breed',
                    type: 'varchar',
                    length: '100',
                    isNullable: true
                },
                {
                    name: 'age',
                    type: 'varchar',
                    length: '20',
                    isNullable: true
                },
                {
                    name: 'gender',
                    type: 'enum',
                    enum: ['Male', 'Female', 'Unknown'],
                    default: "'Unknown'",
                    isNullable: true
                },
                {
                    name: 'reproductive_status',
                    type: 'enum',
                    enum: ['Neutered', 'Spayed', 'Intact'],
                    isNullable: true
                },
                {
                    name: 'microchip_id',
                    type: 'varchar',
                    length: '50',
                    isNullable: true
                },
                {
                    name: 'identification',
                    type: 'text',
                    isNullable: true
                },
                {
                    name: 'allergies',
                    type: 'text',
                    isNullable: true
                },
                {
                    name: 'owner_ids',
                    type: 'uuid',
                    isArray: true,
                    isNullable: false
                },
                {
                    name: 'primary_owner_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP'
                }
            ],
            foreignKeys: [
                {
                    columnNames: ['primary_owner_id'],
                    referencedTableName: 'owners',
                    referencedColumnNames: ['id'],
                    onDelete: 'CASCADE'
                }
            ]
        }), true);
    }
    async down(queryRunner) {
        await queryRunner.dropTable('patients');
    }
}
exports.CreatePatientsTable1721818292453 = CreatePatientsTable1721818292453;
//# sourceMappingURL=1721818292453-CreatePatientsTable.js.map