"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const global_owner_entity_1 = require("../owners/entities/global-owner.entity");
const owner_brand_entity_1 = require("../owners/entities/owner-brand.entity");
const patient_entity_1 = require("../patients/entities/patient.entity");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const patient_owner_entity_1 = require("../patients/entities/patient-owner.entity");
const appointment_details_entity_1 = require("../appointments/entities/appointment-details.entity");
const appointment_doctor_entity_1 = require("../appointments/entities/appointment-doctor.entity");
const appointment_entity_1 = require("../appointments/entities/appointment.entity");
const cart_entity_1 = require("../carts/entites/cart.entity");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
const enum_invoice_types_1 = require("../invoice/enums/enum-invoice-types");
const payment_details_entity_1 = require("../payment-details/entities/payment-details.entity");
const enum_payment_types_1 = require("../invoice/enums/enum-payment-types");
const enum_credit_types_1 = require("../payment-details/enums/enum-credit-types");
const lab_report_entity_1 = require("../clinic-lab-report/entities/lab-report.entity");
const clinic_lab_report_entity_1 = require("../clinic-lab-report/entities/clinic-lab-report.entity");
let DataService = class DataService {
    constructor(globalOwnerRepository, ownerBrandRepository, patientsRepository, patientOwnersRepository, appointmentRepository, appointmentDetailsRepository, appointmentDoctorsRepository, cartRepository, invoiceRepository, paymentDetailsRepository, labReportRepository, clinicLabReportRepository, dataSource, logger) {
        this.globalOwnerRepository = globalOwnerRepository;
        this.ownerBrandRepository = ownerBrandRepository;
        this.patientsRepository = patientsRepository;
        this.patientOwnersRepository = patientOwnersRepository;
        this.appointmentRepository = appointmentRepository;
        this.appointmentDetailsRepository = appointmentDetailsRepository;
        this.appointmentDoctorsRepository = appointmentDoctorsRepository;
        this.cartRepository = cartRepository;
        this.invoiceRepository = invoiceRepository;
        this.paymentDetailsRepository = paymentDetailsRepository;
        this.labReportRepository = labReportRepository;
        this.clinicLabReportRepository = clinicLabReportRepository;
        this.dataSource = dataSource;
        this.logger = logger;
    }
    getHello() {
        return 'Hello World!';
    }
    async findOwnerByClientId(clientId) {
        return this.ownerBrandRepository
            .createQueryBuilder('ownerBrand')
            .where("ownerBrand.dummy_data->>'clientId' = :clientId", {
            clientId
        })
            .getOne();
    }
    async findExistingPatientOwner(patientId, ownerId) {
        return this.patientOwnersRepository.findOne({
            where: {
                patientId,
                globalOwnerId: ownerId
            }
        });
    }
    async createOwner(createOwnerDto) {
        var _a, _b, _c;
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            this.logger.log('Processing owner creation/update', {
                dto: createOwnerDto
            });
            // Check if owner already exists using clientId from dummyData
            if ((_a = createOwnerDto.dummyData) === null || _a === void 0 ? void 0 : _a.clientId) {
                const existingOwner = await this.findOwnerByClientId(createOwnerDto.dummyData.clientId);
                if (existingOwner) {
                    if ((_b = existingOwner.dummyData) === null || _b === void 0 ? void 0 : _b.isDataImported) {
                        this.logger.log('Owner already exists and data imported, skipping', {
                            clientId: createOwnerDto.dummyData.clientId,
                            ownerId: existingOwner.id
                        });
                        return existingOwner;
                    }
                }
            }
            // Create new global owner
            const globalOwner = this.globalOwnerRepository.create({
                phoneNumber: createOwnerDto.phoneNumber,
                countryCode: createOwnerDto.countryCode
            });
            await queryRunner.manager.save(globalOwner);
            // Create new owner brand
            const ownerBrand = this.ownerBrandRepository.create({
                globalOwnerId: globalOwner.id,
                brandId: createOwnerDto.brandId,
                firstName: createOwnerDto.firstName,
                lastName: createOwnerDto.lastName,
                email: createOwnerDto.email,
                address: createOwnerDto.address,
                ownerBalance: createOwnerDto.ownerBalance || 0,
                openingBalance: createOwnerDto.openingBalance || 0,
                dummyData: {
                    ...createOwnerDto.dummyData,
                    isDataImported: true
                }
            });
            await queryRunner.manager.save(ownerBrand);
            await queryRunner.commitTransaction();
            this.logger.log('Owner created successfully', {
                globalOwnerId: globalOwner.id,
                ownerBrandId: ownerBrand.id,
                clientId: (_c = createOwnerDto.dummyData) === null || _c === void 0 ? void 0 : _c.clientId
            });
            return ownerBrand;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error('Failure in owner operation', error);
            if (error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to process owner operation');
        }
        finally {
            await queryRunner.release();
        }
    }
    async createPatient(createPatientDto) {
        var _a, _b, _c;
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            this.logger.log('Processing patient creation/update', {
                dto: createPatientDto
            });
            // Check for existing patient using old ID
            if ((_a = createPatientDto.dummyData) === null || _a === void 0 ? void 0 : _a.patientId) {
                try {
                    const existingPatient = await this.findByOldId(createPatientDto.dummyData.patientId, createPatientDto.clinicId);
                    // Handle existing patient scenarios
                    if (existingPatient) {
                        // Case 1: Patient exists and balance already updated
                        if ((_b = existingPatient.dummyData) === null || _b === void 0 ? void 0 : _b.isBalanceUpdated) {
                            this.logger.log('Patient exists with updated balance, skipping balance update', {
                                oldPatientId: createPatientDto.dummyData.patientId,
                                patientId: existingPatient.id
                            });
                            return existingPatient;
                        }
                        // Case 2: Patient exists but balance needs updating
                        const updatedPatient = {
                            ...existingPatient,
                            balance: createPatientDto.balance,
                            dummyData: {
                                ...existingPatient.dummyData,
                                isBalanceUpdated: true
                            }
                        };
                        const savedPatient = await queryRunner.manager.save(patient_entity_1.Patient, updatedPatient);
                        await queryRunner.commitTransaction();
                        this.logger.log('Patient balance updated successfully', {
                            patientId: savedPatient.id,
                            oldPatientId: createPatientDto.dummyData.patientId
                        });
                        return savedPatient;
                    }
                }
                catch (error) {
                    if (!(error instanceof common_1.NotFoundException)) {
                        throw error;
                    }
                }
            }
            // Create new patient
            const patient = this.patientsRepository.create({
                ...createPatientDto,
                dummyData: {
                    ...createPatientDto.dummyData,
                    isDataImported: true,
                    isBalanceUpdated: true
                }
            });
            await queryRunner.manager.save(patient);
            await queryRunner.commitTransaction();
            this.logger.log('New patient created successfully', {
                patientId: patient.id,
                oldPatientId: (_c = createPatientDto.dummyData) === null || _c === void 0 ? void 0 : _c.patientId
            });
            return patient;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error('Failure in patient operation', error);
            if (error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Failed to process patient operation');
        }
        finally {
            await queryRunner.release();
        }
    }
    async createPatientOwner(createPatientOwnerDto) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            this.logger.log('Processing patient-owner relationship', {
                patientId: createPatientOwnerDto.patientId,
                ownerId: createPatientOwnerDto.ownerId
            });
            // Check for existing relationship
            const existingRelationship = await this.findExistingPatientOwner(createPatientOwnerDto.patientId, createPatientOwnerDto.ownerId);
            if (existingRelationship) {
                this.logger.log('Patient-owner relationship already exists, returning existing', {
                    patientId: existingRelationship.patientId,
                    ownerId: existingRelationship.globalOwnerId,
                    relationshipId: existingRelationship.id
                });
                await queryRunner.commitTransaction();
                return existingRelationship;
            }
            // Create new relationship if doesn't exist
            const patientOwner = this.patientOwnersRepository.create({
                ...createPatientOwnerDto
            });
            const savedRelationship = await queryRunner.manager.save(patientOwner);
            await queryRunner.commitTransaction();
            this.logger.log('New patient-owner relationship created successfully', {
                patientId: savedRelationship.patientId,
                ownerId: savedRelationship.globalOwnerId,
                relationshipId: savedRelationship.id
            });
            return savedRelationship;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error('Failure in patient-owner relationship operation', {
                error,
                patientId: createPatientOwnerDto.patientId,
                ownerId: createPatientOwnerDto.ownerId
            });
            throw new common_1.InternalServerErrorException('Failed to process patient-owner relationship operation');
        }
        finally {
            await queryRunner.release();
        }
    }
    async findExistingAppointment(patientId, date) {
        return this.appointmentRepository.findOne({
            where: {
                patientId,
                date: date
            },
            relations: ['appointmentDoctors']
        });
    }
    async findExistingAppointmentDetails(appointmentId) {
        return this.appointmentDetailsRepository.findOne({
            where: { appointmentId }
        });
    }
    async createAppointment(createAppointmentDto) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            this.logger.log('Processing appointment creation/update', {
                patientId: createAppointmentDto.patientId,
                date: createAppointmentDto.date
            });
            const appointmentDate = new Date(createAppointmentDto.date);
            // Check for existing appointment
            const existingAppointment = await this.findExistingAppointment(createAppointmentDto.patientId, appointmentDate);
            if (existingAppointment) {
                this.logger.log('Appointment already exists for this date', {
                    appointmentId: existingAppointment.id,
                    patientId: existingAppointment.patientId,
                    date: existingAppointment.date
                });
                await queryRunner.commitTransaction();
                return existingAppointment;
            }
            // Create new appointment if doesn't exist
            const appointment = this.appointmentRepository.create({
                ...createAppointmentDto,
                date: appointmentDate
            });
            const createdAppointment = await queryRunner.manager.save(appointment);
            // Create doctor relationships
            const doctorPromises = createAppointmentDto.doctorIds.map(async (doctorId) => {
                const appointmentDoctor = this.appointmentDoctorsRepository.create({
                    appointmentId: createdAppointment.id,
                    doctorId: doctorId,
                    primary: true
                });
                return queryRunner.manager.save(appointmentDoctor);
            });
            await Promise.all(doctorPromises);
            await queryRunner.commitTransaction();
            this.logger.log('New appointment created successfully', {
                appointmentId: createdAppointment.id,
                patientId: createdAppointment.patientId,
                date: createdAppointment.date
            });
            return createdAppointment;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error('Failure in appointment operation', {
                error,
                patientId: createAppointmentDto.patientId,
                date: createAppointmentDto.date
            });
            throw new common_1.InternalServerErrorException('Failed to process appointment operation');
        }
        finally {
            await queryRunner.release();
        }
    }
    async createAppointmentDetails(createAppointmentDetailsDto) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            this.logger.log('Processing appointment details', {
                appointmentId: createAppointmentDetailsDto.appointmentId
            });
            // Check for existing appointment details
            const existingDetails = await this.findExistingAppointmentDetails(createAppointmentDetailsDto.appointmentId);
            if (existingDetails) {
                // Optionally merge new details with existing ones if needed
                const mergedDetails = {
                    ...existingDetails.details,
                    ...createAppointmentDetailsDto.details
                };
                existingDetails.details = mergedDetails;
                const updatedDetails = await queryRunner.manager.save(existingDetails);
                this.logger.log('Appointment details updated successfully', {
                    appointmentId: updatedDetails.appointmentId
                });
                await queryRunner.commitTransaction();
                return updatedDetails;
            }
            // Create new appointment details if don't exist
            const newAppointmentDetails = this.appointmentDetailsRepository.create({
                appointmentId: createAppointmentDetailsDto.appointmentId,
                details: createAppointmentDetailsDto.details
            });
            const savedDetails = await queryRunner.manager.save(newAppointmentDetails);
            await queryRunner.commitTransaction();
            this.logger.log('New appointment details created successfully', {
                appointmentId: savedDetails.appointmentId
            });
            return savedDetails;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error('Failure in appointment details operation', {
                error,
                appointmentId: createAppointmentDetailsDto.appointmentId
            });
            throw new common_1.InternalServerErrorException('Failed to process appointment details operation');
        }
        finally {
            await queryRunner.release();
        }
    }
    async findByOldId(oldPatientId, clinicId) {
        const patient = await this.patientsRepository
            .createQueryBuilder('patient')
            .where("patient.dummy_data->>'patientId' = :oldPatientId", {
            oldPatientId
        })
            .andWhere('patient.clinicId = :clinicId', { clinicId })
            .getOne();
        if (!patient) {
            throw new common_1.NotFoundException(`Patient with old ID ${oldPatientId} not found`);
        }
        return patient;
    }
    async findByPatientAndDate(patientId, date) {
        const appointment = await this.appointmentRepository
            .createQueryBuilder('appointment')
            .where('appointment.patientId = :patientId', { patientId })
            .andWhere('DATE(appointment.date) = :date', { date })
            .getOne();
        if (!appointment) {
            throw new common_1.NotFoundException(`Appointment for patient ${patientId} on ${date} not found`);
        }
        return appointment;
    }
    async createCart(appointmentId) {
        const cart = this.cartRepository.create({ appointmentId });
        return this.cartRepository.save(cart);
    }
    async createInvoiceAndPaymentDetail(invoiceData) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const appointmentDate = new Date(invoiceData.date);
            if (isNaN(appointmentDate.getTime())) {
                throw new common_1.BadRequestException('Invalid date format');
            }
            // Create invoice
            const invoice = new invoice_entity_1.InvoiceEntity();
            invoice.cartId = invoiceData.cartId;
            invoice.patientId = invoiceData.patientId;
            invoice.ownerId = invoiceData.ownerId;
            invoice.clinicId = invoiceData.clinicId;
            invoice.brandId = invoiceData.brandId;
            invoice.discount = 0;
            invoice.totalPrice = 0;
            invoice.totalDiscount = 0;
            invoice.priceAfterDiscount = 0;
            invoice.totalTax = 0;
            invoice.totalCredit = 0;
            invoice.amountPayable = 0;
            invoice.amountPaid = 0;
            invoice.paymentMode = 'Cash';
            invoice.invoiceType = enum_invoice_types_1.EnumInvoiceType.Invoice;
            invoice.details = [];
            invoice.createdAt = appointmentDate;
            invoice.fileUrl = { invoiceFileKey: invoiceData.s3Key };
            invoice.metadata = invoiceData.metadata || {};
            const savedInvoice = await queryRunner.manager.save(invoice);
            // console.log('savedInvoice', savedInvoice);
            // Create payment detail
            const paymentDetail = new payment_details_entity_1.PaymentDetailsEntity();
            paymentDetail.patientId = invoiceData.patientId;
            paymentDetail.ownerId = invoiceData.ownerId;
            paymentDetail.clinicId = invoiceData.clinicId;
            paymentDetail.brandId = invoiceData.brandId;
            paymentDetail.transactionAmount = 0;
            paymentDetail.previousBalance = 0;
            paymentDetail.invoiceId = savedInvoice.id;
            paymentDetail.type = enum_credit_types_1.EnumAmountType.Invoice;
            paymentDetail.paymentType = enum_payment_types_1.EnumPaymentType.Cash;
            paymentDetail.amountPayable = 0;
            paymentDetail.mainBalance = 0;
            paymentDetail.amount = 0;
            paymentDetail.createdAt = appointmentDate;
            paymentDetail.showInLedger = false;
            paymentDetail.showInInvoice = true;
            const savedPaymentDetail = await queryRunner.manager.save(paymentDetail);
            // console.log('savedPaymentDetail', savedPaymentDetail);
            await queryRunner.commitTransaction();
            return { invoice: savedInvoice, paymentDetail: savedPaymentDetail };
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            console.error('Error creating invoice and payment detail:', error);
            throw new common_1.BadRequestException('Failed to create invoice and payment detail');
        }
        finally {
            await queryRunner.release();
        }
    }
    async processInvoices(invoiceData) {
        const results = [];
        for (const data of invoiceData) {
            try {
                // Find patient
                const patient = await this.findByOldId(data.patientId, data.clinicId);
                if (!patient) {
                    this.logger.error(`Patient not found: ${data.patientId}`);
                    results.push({
                        status: 'error',
                        message: `Patient not found: ${data.patientId}`
                    });
                    continue;
                }
                // Find patient owners
                const patientOwners = await this.patientOwnersRepository.find({
                    where: { patientId: patient.id }
                });
                console.log(patientOwners[0].ownerId);
                if (!(patientOwners === null || patientOwners === void 0 ? void 0 : patientOwners.length)) {
                    this.logger.error(`No owners found for patient: ${patient.id}`);
                    results.push({
                        status: 'error',
                        message: `No owners found for patient ${patient.id}`
                    });
                    continue;
                }
                // Find appointment
                const appointment = await this.findByPatientAndDate(patient.id, '31 Dec 2024');
                if (!appointment) {
                    this.logger.error(`Appointment not found for patient ${patient.id} on ${data.date}`);
                    results.push({
                        status: 'error',
                        message: `Appointment not found for patient ${patient.id} on ${data.date}`
                    });
                    continue;
                }
                // Create cart
                const cart = await this.createCart(appointment.id);
                console.log(cart, appointment);
                // Create invoice
                const invoice = await this.createInvoiceAndPaymentDetail({
                    cartId: cart.id,
                    patientId: patient.id,
                    ownerId: patientOwners[0].ownerId,
                    s3Key: data.s3Key,
                    date: data.date,
                    clinicId: data.clinicId,
                    brandId: data.brandId,
                    metadata: data.metadata
                });
                results.push({
                    status: 'success',
                    patientId: patient.id,
                    appointmentId: appointment.id,
                    ownerId: patientOwners[0].ownerId
                });
            }
            catch (error) {
                console.log(error);
                this.logger.error('Error processing invoice', {
                    error,
                    patientId: data.patientId,
                    date: data.date
                });
                results.push({
                    status: 'error',
                    message: error,
                    data: data
                });
            }
        }
        return results;
    }
    // async processDiagnostics(data: {
    //   patientId: string;
    //   date: string;
    //   labReportTypeId: string;
    //   fileData: any;
    // }): Promise<any> {
    //   const queryRunner = this.dataSource.createQueryRunner();
    //   await queryRunner.connect();
    //   await queryRunner.startTransaction();
    //   try {
    //     // Find patient using old ID
    //     const patient = await this.findByOldId(data.patientId);
    //     if (!patient) {
    //       throw new NotFoundException(`Patient not found: ${data.patientId}`);
    //     }
    //     console.log(patient)
    //     // Find appointment
    //     const appointment = await this.findByPatientAndDate(patient.id, data.date);
    //     if (!appointment) {
    //       throw new NotFoundException(
    //         `Appointment not found for patient ${patient.id} on ${data.date}`
    //       );
    //     }
    //     console.log(appointment.id)
    //     // Create lab report entry
    //     const labReport = await this.createLabReport({
    //       appointmentId: appointment.id,
    //       clinicLabReportId: data.labReportTypeId,
    //       clinicId: appointment.clinicId,
    //       patientId: patient.id,
    //       files: [data.fileData],
    //       status: 'COMPLETED',
    //       date:data.date
    //     });
    //     // Update appointment details
    //     const apdet = await this.updateAppointmentDetailsWithLabReport(
    //       appointment.id,
    //       data.labReportTypeId,
    //       data.fileData
    //     );
    //     console.log(apdet)
    //     await queryRunner.commitTransaction();
    //     return {
    //       status: 'success',
    //       patientId: patient.id,
    //       appointmentId: appointment.id,
    //       labReportId: labReport.id
    //     };
    //   } catch (error) {
    //     await queryRunner.rollbackTransaction();
    //     throw error;
    //   } finally {
    //     await queryRunner.release();
    //   }
    // }
    async createLabReport(data) {
        const appointmentDate = new Date(data.date);
        const labReport = this.labReportRepository.create({
            appointmentId: data.appointmentId,
            clinicLabReportId: data.clinicLabReportId,
            clinicId: data.clinicId,
            patientId: data.patientId,
            files: data.files,
            status: 'COMPLETED',
            createdAt: appointmentDate
        });
        return this.labReportRepository.save(labReport);
    }
    async updateAppointmentDetailsWithLabReport(appointmentId, labReportTypeId, fileData) {
        const appointmentDetails = (await this.appointmentDetailsRepository.findOne({
            where: { appointmentId }
        }));
        if (!appointmentDetails) {
            // Create new appointment details if doesn't exist
            const newDetails = {
                objective: {
                    labReports: [
                        {
                            files: [fileData],
                            label: 'IDEXX',
                            value: labReportTypeId
                        }
                    ]
                }
            };
            await this.appointmentDetailsRepository.save({
                appointmentId,
                details: newDetails
            });
        }
        else {
            const details = appointmentDetails.details;
            const labReports = details.objective.labReports;
            // Find existing report with the same value
            const existingReport = labReports.find(report => report.value === labReportTypeId);
            if (existingReport) {
                // Add new file to existing report
                existingReport.files = existingReport.files || [];
                existingReport.files.push(fileData);
            }
            else {
                // Add new report
                labReports.push({
                    files: [fileData],
                    label: 'IDEXX',
                    value: labReportTypeId
                });
            }
            await this.appointmentDetailsRepository.save(appointmentDetails);
        }
    }
    // async createMigratedAppointment(
    //   appointmentData: CreateAppointmentDto
    // ): Promise<AppointmentEntity> {
    //   const queryRunner = this.dataSource.createQueryRunner();
    //   await queryRunner.connect();
    //   await queryRunner.startTransaction();
    //   try {
    //     this.logger.log('Creating migrated appointment', {
    //       patientId: appointmentData.patientId,
    //       date: appointmentData.date
    //     });
    //     // Check for existing appointment
    //     const existingAppointment = await this.findExistingAppointment(
    //       appointmentData.patientId,
    //       new Date(appointmentData.date)
    //     );
    //     if (existingAppointment) {
    //       this.logger.log('Appointment already exists', {
    //         appointmentId: existingAppointment.id,
    //         patientId: appointmentData.patientId,
    //         date: appointmentData.date
    //       });
    //       return existingAppointment;
    //     }
    //     // Create new appointment
    //     const appointment = this.appointmentRepository.create({
    //       clinicId: appointmentData.clinicId,
    //       patientId: appointmentData.patientId,
    //       date: new Date(appointmentData.date),
    //       startTime: appointmentData.startTime,
    //       endTime: appointmentData.endTime,
    //       reason: appointmentData.reason,
    //       type: appointmentData.type,
    //       status: appointmentData.status,
    //       isBlocked: appointmentData.isBlocked,
    //       weight: appointmentData.weight
    //     });
    //     const savedAppointment = await queryRunner.manager.save(appointment);
    //     // Create doctor relationships
    //     const doctorPromises = appointmentData.doctorIds.map(async (doctorId) => {
    //       const appointmentDoctor = this.appointmentDoctorsRepository.create({
    //         appointmentId: savedAppointment.id,
    //         doctorId,
    //         primary: true
    //       });
    //       return queryRunner.manager.save(appointmentDoctor);
    //     });
    //     await Promise.all(doctorPromises);
    //     await queryRunner.commitTransaction();
    //     this.logger.log('Migrated appointment created successfully', {
    //       appointmentId: savedAppointment.id,
    //       patientId: appointmentData.patientId,
    //       date: savedAppointment.date
    //     });
    //     return savedAppointment;
    //   } catch (error) {
    //     await queryRunner.rollbackTransaction();
    //     this.logger.error('Error creating migrated appointment', {
    //       error: error,
    //       patientId: appointmentData.patientId
    //     });
    //     throw error;
    //   } finally {
    //     await queryRunner.release();
    //   }
    // }
    async bulkUpdateOpeningBalance(updates) {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        const results = [];
        try {
            for (const update of updates) {
                try {
                    // Find owner brand using client ID
                    const ownerBrand = await queryRunner.manager
                        .createQueryBuilder(owner_brand_entity_1.OwnerBrand, 'ownerBrand')
                        .where("dummy_data->>'clientId' = :clientId", {
                        clientId: update.clientId
                    })
                        .getOne();
                    if (!ownerBrand) {
                        results.push({
                            clientId: update.clientId,
                            status: 'error',
                            message: 'Owner not found'
                        });
                        continue;
                    }
                    // Update opening balance only
                    const updateData = {
                        openingBalance: update.openingBalance
                    };
                    // Update owner brand
                    await queryRunner.manager.update(owner_brand_entity_1.OwnerBrand, { id: ownerBrand.id }, updateData);
                    results.push({
                        clientId: update.clientId,
                        status: 'success',
                        ownerId: ownerBrand.id
                    });
                    this.logger.log('Successfully updated owner balance', {
                        clientId: update.clientId,
                        ownerId: ownerBrand.id,
                        openingBalance: update.openingBalance
                    });
                }
                catch (error) {
                    this.logger.error('Error updating individual owner balance', {
                        error,
                        clientId: update.clientId
                    });
                    results.push({
                        clientId: update.clientId,
                        status: 'error',
                        message: error instanceof Error
                            ? error.message
                            : 'Unknown error'
                    });
                }
            }
            await queryRunner.commitTransaction();
            return results;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async findByOldPatientId(oldPatientId, clinicId) {
        try {
            const patient = await this.patientsRepository
                .createQueryBuilder('patient')
                .where("patient.dummy_data->>'patientId' = :oldPatientId", {
                oldPatientId
            })
                .andWhere('patient.clinic_id = :clinicId', { clinicId })
                .getOne();
            if (!patient) {
                this.logger.error('Patient not found with old ID', {
                    oldPatientId,
                    clinicId
                });
                throw new common_1.NotFoundException(`Patient with old ID "${oldPatientId}" not found`);
            }
            this.logger.log('Patient found successfully', {
                oldPatientId,
                newPatientId: patient.id,
                clinicId
            });
            return patient;
        }
        catch (error) {
            this.logger.error('Error finding patient by old ID', {
                error,
                oldPatientId,
                clinicId
            });
            throw error;
        }
    }
    async updateMicrochipId(id, microchipId) {
        try {
            const patient = await this.patientsRepository.findOne({
                where: { id }
            });
            if (!patient) {
                this.logger.error('Patient not found', { patientId: id });
                throw new common_1.NotFoundException(`Patient with ID "${id}" not found`);
            }
            // Update microchip ID
            patient.microchipId = microchipId;
            // Save the updated patient
            const updatedPatient = await this.patientsRepository.save(patient);
            this.logger.log('Microchip ID updated successfully', {
                patientId: id,
                microchipId
            });
            return updatedPatient;
        }
        catch (error) {
            this.logger.error('Error updating microchip ID', {
                error,
                patientId: id
            });
            throw error;
        }
    }
};
exports.DataService = DataService;
exports.DataService = DataService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(global_owner_entity_1.GlobalOwner)),
    __param(1, (0, typeorm_1.InjectRepository)(owner_brand_entity_1.OwnerBrand)),
    __param(2, (0, typeorm_1.InjectRepository)(patient_entity_1.Patient)),
    __param(3, (0, typeorm_1.InjectRepository)(patient_owner_entity_1.PatientOwner)),
    __param(4, (0, typeorm_1.InjectRepository)(appointment_entity_1.AppointmentEntity)),
    __param(5, (0, typeorm_1.InjectRepository)(appointment_details_entity_1.AppointmentDetailsEntity)),
    __param(6, (0, typeorm_1.InjectRepository)(appointment_doctor_entity_1.AppointmentDoctorsEntity)),
    __param(7, (0, typeorm_1.InjectRepository)(cart_entity_1.CartEntity)),
    __param(8, (0, typeorm_1.InjectRepository)(invoice_entity_1.InvoiceEntity)),
    __param(9, (0, typeorm_1.InjectRepository)(payment_details_entity_1.PaymentDetailsEntity)),
    __param(10, (0, typeorm_1.InjectRepository)(lab_report_entity_1.LabReport)),
    __param(11, (0, typeorm_1.InjectRepository)(clinic_lab_report_entity_1.ClinicLabReport)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource,
        winston_logger_service_1.WinstonLogger])
], DataService);
//# sourceMappingURL=data.service.js.map