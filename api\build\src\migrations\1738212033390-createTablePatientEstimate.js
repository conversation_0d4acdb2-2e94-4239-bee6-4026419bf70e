"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTablePatientEstimate1738212033390 = void 0;
const typeorm_1 = require("typeorm");
class CreateTablePatientEstimate1738212033390 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'patient_estimate',
            columns: [
                {
                    name: "id",
                    type: "uuid",
                    isPrimary: true,
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'clinic_id',
                    type: 'uuid'
                },
                {
                    name: 'patient_id',
                    type: 'uuid'
                },
                {
                    name: 'doctor_id',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'estimate_total',
                    type: 'integer'
                },
                {
                    name: 'signature_required',
                    type: 'boolean',
                    default: false
                },
                {
                    name: 'signature_status',
                    type: 'enum',
                    enum: ['pending', 'completed'],
                    default: "'pending'"
                },
                {
                    name: 'treatment_plan',
                    type: 'jsonb',
                    isNullable: true
                },
                {
                    name: 'signatured_by',
                    type: 'varchar',
                    isNullable: true
                },
                {
                    name: 'file_key',
                    type: 'varchar',
                    isNullable: true
                },
                {
                    name: 'document_id',
                    type: 'varchar',
                    isNullable: true
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                }
            ],
            foreignKeys: [
                {
                    columnNames: ['clinic_id'],
                    referencedTableName: 'clinics',
                    referencedColumnNames: ['id'],
                    onDelete: 'CASCADE'
                },
                {
                    columnNames: ['patient_id'],
                    referencedTableName: 'patients',
                    referencedColumnNames: ['id'],
                    onDelete: 'CASCADE'
                },
                {
                    columnNames: ['doctor_id'],
                    referencedTableName: 'users',
                    referencedColumnNames: ['id'],
                    onDelete: 'CASCADE'
                }
            ]
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('patient_estimate');
    }
}
exports.CreateTablePatientEstimate1738212033390 = CreateTablePatientEstimate1738212033390;
//# sourceMappingURL=1738212033390-createTablePatientEstimate.js.map