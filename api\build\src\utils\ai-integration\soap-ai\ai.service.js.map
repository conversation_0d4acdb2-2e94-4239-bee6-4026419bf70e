{"version": 3, "file": "ai.service.js", "sourceRoot": "", "sources": ["../../../../../src/utils/ai-integration/soap-ai/ai.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA0E;AAC1E,mCAA4B;AAE5B,gFAAoE;AAG7D,IAAM,SAAS,GAAf,MAAM,SAAS;IAGpB,YAA6B,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;QAChD,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC;YACvB,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;SACnC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,aAAqB;;QAC3C,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC3D,KAAK,EAAE,QAAQ;gBACf,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cAiDP;qBACH;oBACD;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,oFAAoF,aAAa,EAAE;qBAC7G;iBACF;gBACD,eAAe,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;gBACxC,WAAW,EAAE,GAAG;aACjB,CAAC,CAAC;YAEH,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;gBACrB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE;oBAClC,WAAW,EAAE,UAAU,CAAC,KAAK,CAAC,aAAa;oBAC3C,YAAY,EAAE,UAAU,CAAC,KAAK,CAAC,iBAAiB;oBAChD,WAAW,EAAE,UAAU,CAAC,KAAK,CAAC,YAAY;iBAC3C,CAAC,CAAC;YACL,CAAC;YAED,MAAM,OAAO,GAAG,MAAA,MAAA,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,0CAAE,OAAO,0CAAE,OAAO,CAAC;YACxD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,qCAA4B,CAAC,iCAAiC,CAAC,CAAC;YAC5E,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAiB,CAAC;gBAC3D,OAAO,cAAc,CAAC;YACxB,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;gBACrF,MAAM,IAAI,qCAA4B,CAAC,qCAAqC,CAAC,CAAC;YAChF,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YAC7C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,aAAqB,EAAE,aAAsB;QACrE,MAAM,YAAY,GAAG,aAAa,CAAC,CAAC;YAClC,iDAAiD,aAAa,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAExE,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,YAAY,OAAO,aAAa,EAAE,CAAC,CAAC;IACvE,CAAC;CACF,CAAA;AA7GY,8BAAS;oBAAT,SAAS;IADrB,IAAA,mBAAU,GAAE;qCAI0B,sCAAa;GAHvC,SAAS,CA6GrB"}