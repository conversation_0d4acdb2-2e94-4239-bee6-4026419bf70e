import React, { useMemo } from 'react';
import { PaginationType } from '../molecules/Table';
import BasicInfo from '../molecules/BasicInfo';
import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import { PatientT } from '../types/patient';
import UserProfile from '../molecules/UserProfile';
import moment from 'moment';
import { Table } from '@/app/molecules';
import { useRouter } from 'next/navigation';
import { Text, Tags } from '../atoms';
import { BREEDS } from '../utils/constant';
import IconWarning from '../atoms/customIcons/IconWarning.svg';
import Tooltip from '../molecules/Tooltip';

interface PatientListType {
    className?: string;
    tableData: PatientT[];
    pagination: PaginationType;
    setPagination: React.Dispatch<React.SetStateAction<PaginationType>>;
    totalPages: number;
    listLoadStatus: 'error' | 'success' | 'pending';
    setPatientId: Function;
    setShowDropdown: Function;
    setViewModal: Function;
    setEditModal?: Function;
    onView?: Function;
    onEdit: (data: { row: any }) => void;
    onDelete: (data: { row: any }) => void;
    onEditOwner: (data: { row: any }) => void;
    emptyTableButtonHandle?: () => void;
    onCreditAmount: (data: { row: any }) => void;
    onPaymentHistory: (data: { row: any }) => void;
    onShareDocuments: Function;
    customRule?: { patientLastNameAsOwnerLastName: boolean };
}

const PatientList = ({
    className,
    tableData,
    pagination,
    setPagination,
    totalPages,
    listLoadStatus,
    onEdit,
    onDelete,
    onView,
    emptyTableButtonHandle,
    onCreditAmount,
    onPaymentHistory,
    onShareDocuments,
    onEditOwner,
    setPatientId,
    customRule,
}: PatientListType) => {
    const router = useRouter();
    const handleTableRowClick = (rowData: any) => {
        router.push(`/patients/${rowData.original.id}/details`);
    };

    const columnHelper = createColumnHelper<PatientT>();

    const isValidPhoneNumber = (phoneNumber: string) => {
        if (!phoneNumber) return false;
        const trimmedNumber = phoneNumber.trim();
        return /^\d{10}$/.test(trimmedNumber);
    };

    const columns: ColumnDef<PatientT, any>[] = useMemo(
        () => [
            columnHelper.accessor('patientName', {
                id: 'patientName',
                header: 'Patient Name',
                size: 20,
                meta: {
                    thClassName: 'w-[25%]',
                    tdClassName: 'w-[25%]',
                },
                cell: (info) => {
                    const species = info.row.original?.species?.toLowerCase();
                    const breedValue = info.row.original?.breed;
                    let patientName = info.row.original?.patientName || '';
                    let breedLabel = '';
                    let avatar = info.row.original?.profile;

                    if (species && breedValue && BREEDS[species]) {
                        const breed = BREEDS[species].find(
                            (b) => b.value === breedValue
                        );
                        if (breed) {
                            breedLabel = breed.label;
                            avatar =
                                avatar ||
                                `/images/breeds/${species}/${breed.code}.png`;
                        }
                    }

                    return (
                        <UserProfile
                            avatar={avatar}
                            name={patientName}
                            description={breedLabel || breedValue || ''}
                            descriptionClass="capitalize"
                            labelClass="text-truncation"
                            onProfileClick={() => {}}
                        />
                    );
                },
            }),
            columnHelper.accessor((row) => row.owners[0]?.name, {
                id: 'ownerName',
                header: 'Owner Name',
                size: 20,
                meta: {
                    thClassName: 'w-[15%]',
                    tdClassName: 'w-[15%]',
                },
                cell: (info) => (
                    <div
                        className="flex flex-col gap-1"
                        onClick={(e) => e.stopPropagation()}
                    >
                        {info.row.original.owners.map(
                            (owner: any, index: number) => (
                                <Tags
                                    key={owner.id}
                                    size="small"
                                    shape="rounded"
                                    variant="silver"
                                    label={owner.name || '--'}
                                    className="cursor-pointer hover:bg-neutral-100"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        onEditOwner({
                                            row: {
                                                original: {
                                                    id: info.row.original.id,
                                                },
                                            },
                                        });
                                    }}
                                />
                            )
                        )}
                    </div>
                ),
            }),
            columnHelper.accessor((row) => row.owners[0]?.email, {
                id: 'ownerEmail',
                header: 'Email',
                size: 20,
                meta: {
                    thClassName: 'w-[15%]',
                    tdClassName: 'w-[15%]',
                },
                cell: (info) => (
                    <BasicInfo
                        label={info.getValue() || '--'}
                        value={info.row.original.owners[1]?.email || ''}
                        labelclass="text-truncation"
                    />
                ),
            }),
            columnHelper.accessor((row) => row.owners[0]?.phoneNumber, {
                id: 'ownerPhone',
                header: 'Phone Number',
                meta: {
                    thClassName: 'w-[14%]',
                    tdClassName: 'w-[14%]',
                },
                cell: (info) => {
                    const primaryOwner = info.row.original.owners[0];
                    const secondaryOwner = info.row.original.owners[1];

                    const primaryPhone = primaryOwner
                        ? `+${primaryOwner.countryCode || ''} ${primaryOwner.phoneNumber || ''}`
                        : '--';

                    const secondaryPhone = secondaryOwner
                        ? `+${secondaryOwner.countryCode || ''} ${secondaryOwner.phoneNumber || ''}`
                        : '';

                    const isPrimaryPhoneInvalid =
                        primaryOwner?.phoneNumber &&
                        !isValidPhoneNumber(primaryOwner.phoneNumber);
                    const isSecondaryPhoneInvalid =
                        secondaryOwner?.phoneNumber &&
                        !isValidPhoneNumber(secondaryOwner.phoneNumber);

                    return (
                        <div>
                            <div className="flex items-center gap-2">
                                <Text className="whitespace-nowrap cursor-auto">
                                    {primaryPhone.trim() || '--'}
                                </Text>
                                {isPrimaryPhoneInvalid && (
                                    <Tooltip
                                        content="Please update to a valid phone number"
                                        position="bottom"
                                        tooltipClass="text-warning-100"
                                    >
                                        <IconWarning
                                            size={16}
                                            className="text-warning-100"
                                        />
                                    </Tooltip>
                                )}
                            </div>
                            <div className="flex items-center gap-2">
                                <Text
                                    variant="caption"
                                    textColor="text-primary-700"
                                    className="whitespace-nowrap cursor-auto"
                                >
                                    {secondaryPhone.trim() || ''}
                                </Text>
                                {isSecondaryPhoneInvalid && secondaryPhone && (
                                    <Tooltip
                                        content="Please update to a valid phone number"
                                        position="bottom"
                                        tooltipClass="text-warning-100"
                                    >
                                        <IconWarning
                                            size={16}
                                            className="text-warning-100"
                                        />
                                    </Tooltip>
                                )}
                            </div>
                        </div>
                    );
                },
            }),
            columnHelper.accessor('lastVisit', {
                header: 'Last Visit',
                meta: {
                    thClassName: 'w-[20%]',
                    tdClassName: 'w-[20%]',
                },
                cell: (info) => (
                    <BasicInfo
                        labelclass="text-truncation"
                        label={
                            info.row.original.appointment
                                ? (() => {
                                      const reason =
                                          info.row.original.appointment
                                              .reason || '';
                                      const date = info.row.original.appointment
                                          .date
                                          ? moment(
                                                info.row.original.appointment
                                                    .date
                                            ).format('DD MMM YYYY')
                                          : '';
                                      return reason && date
                                          ? `${reason} | ${date}`
                                          : reason || date || '--';
                                  })()
                                : '--'
                        }
                        value={
                            info.row.original.appointment &&
                            info.row.original.appointment.primaryDoctor &&
                            (info.row.original.dummyData?.isDataImported
                                ? '--'
                                : `Consulted by Dr. ${info.row.original.appointment.primaryDoctor}`) &&
                            info.row.original.appointment.primaryDoctor !==
                                'Saline Patients'
                                ? `Consulted by Dr. ${info.row.original.appointment.primaryDoctor}`
                                : '--'
                        }
                    />
                ),
            }),
            {
                id: 'action',
                header: 'Action',
                meta: {
                    thClassName: 'w-[7%]',
                    tdClassName: 'w-[7%]',
                    thAlign: 'text-center',
                    tdAlign: 'text-center',
                    actionOptions: [
                        { id: 'editPatient', label: 'Edit Patient' },
                        { id: 'editOwner', label: 'Edit Owner' },
                        { id: 'delete', label: 'Delete' },
                        // { id: 'creditAmount', label: 'Credit Amount' },
                        // { id: 'paymentHistory', label: 'Payment History' },
                        // { id: 'shareRecords', label: 'Share Records' },
                    ],
                    onActionClick: ({ row, action }) => {
                        setPatientId(row.original.id);
                        switch (action.id) {
                            case 'editPatient':
                                onEdit({ row });
                                break;
                            case 'editOwner':
                                onEditOwner({ row });
                                break;
                            case 'delete':
                                onDelete({ row });
                                break;
                            case 'creditAmount':
                                onCreditAmount({ row });
                                break;
                            case 'paymentHistory':
                                onPaymentHistory({ row });
                                break;
                            // case 'shareRecords':
                            //     onShareDocuments(row.original.id);
                            //     break;
                            default:
                                break;
                        }
                    },
                },
            },
        ],
        [
            onEdit,
            onDelete,
            onCreditAmount,
            onPaymentHistory,
            onShareDocuments,
            customRule,
            onEditOwner,
            setPatientId,
        ]
    );

    return (
        <div className={`${className}`}>
            <Table<PatientT>
                columns={columns}
                tableData={tableData ?? []}
                pagination={pagination}
                setPagination={setPagination}
                isPaginationOutOfBox={true}
                customTableWrapperClass="h-[calc(100vh_-_260px)] overflow-auto"
                pageCount={totalPages}
                listLoadStatus={listLoadStatus}
                handleTableRowClick={handleTableRowClick}
                emptyTableMessage={'No patients added'}
                emptyTableButtonLabel="Add patient"
                emptyTableButtonHandle={emptyTableButtonHandle}
                headerSticky={true}
                emptyStateHeight=""
            />
        </div>
    );
};

export default PatientList;
