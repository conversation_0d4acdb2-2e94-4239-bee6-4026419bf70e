"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicDeletionAuditTrail = exports.ClinicDeletionAuditTargetType = exports.ClinicDeletionAuditOperation = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../users/entities/user.entity");
var ClinicDeletionAuditOperation;
(function (ClinicDeletionAuditOperation) {
    ClinicDeletionAuditOperation["BACKUP"] = "backup";
    ClinicDeletionAuditOperation["DELETION"] = "deletion";
    ClinicDeletionAuditOperation["RESTORE"] = "restore";
})(ClinicDeletionAuditOperation || (exports.ClinicDeletionAuditOperation = ClinicDeletionAuditOperation = {}));
var ClinicDeletionAuditTargetType;
(function (ClinicDeletionAuditTargetType) {
    ClinicDeletionAuditTargetType["CLINIC"] = "CLINIC";
    ClinicDeletionAuditTargetType["BRAND"] = "BRAND";
})(ClinicDeletionAuditTargetType || (exports.ClinicDeletionAuditTargetType = ClinicDeletionAuditTargetType = {}));
let ClinicDeletionAuditTrail = class ClinicDeletionAuditTrail {
};
exports.ClinicDeletionAuditTrail = ClinicDeletionAuditTrail;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], ClinicDeletionAuditTrail.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ClinicDeletionAuditOperation,
        comment: 'Type of operation performed (backup, deletion, restore)'
    }),
    __metadata("design:type", String)
], ClinicDeletionAuditTrail.prototype, "operation", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ClinicDeletionAuditTargetType,
        name: 'target_type',
        comment: 'Type of target entity (CLINIC or BRAND)'
    }),
    __metadata("design:type", String)
], ClinicDeletionAuditTrail.prototype, "targetType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'uuid',
        name: 'target_id',
        comment: 'ID of the target entity (clinic or brand)'
    }),
    __metadata("design:type", String)
], ClinicDeletionAuditTrail.prototype, "targetId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'varchar',
        length: 255,
        name: 'target_name',
        nullable: true,
        comment: 'Name of the target entity for easier identification'
    }),
    __metadata("design:type", String)
], ClinicDeletionAuditTrail.prototype, "targetName", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'uuid',
        name: 'user_id',
        comment: 'ID of the user who performed the operation'
    }),
    __metadata("design:type", String)
], ClinicDeletionAuditTrail.prototype, "userId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { onDelete: 'SET NULL', nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'user_id' }),
    __metadata("design:type", user_entity_1.User)
], ClinicDeletionAuditTrail.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'boolean',
        default: false,
        comment: 'Whether the operation was successful'
    }),
    __metadata("design:type", Boolean)
], ClinicDeletionAuditTrail.prototype, "success", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'uuid',
        name: 'backup_id',
        nullable: true,
        comment: 'ID of the backup associated with this operation'
    }),
    __metadata("design:type", String)
], ClinicDeletionAuditTrail.prototype, "backupId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'text',
        name: 'backup_location',
        nullable: true,
        comment: 'S3 path where backup is stored'
    }),
    __metadata("design:type", String)
], ClinicDeletionAuditTrail.prototype, "backupLocation", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb',
        name: 'operation_data',
        nullable: true,
        comment: 'Detailed operation data including results, statistics, etc.'
    }),
    __metadata("design:type", Object)
], ClinicDeletionAuditTrail.prototype, "operationData", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'jsonb',
        name: 'error_details',
        nullable: true,
        comment: 'Error details if operation failed'
    }),
    __metadata("design:type", Object)
], ClinicDeletionAuditTrail.prototype, "errorDetails", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'integer',
        name: 'duration_ms',
        nullable: true,
        comment: 'Operation duration in milliseconds'
    }),
    __metadata("design:type", Number)
], ClinicDeletionAuditTrail.prototype, "durationMs", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'inet',
        name: 'ip_address',
        nullable: true,
        comment: 'IP address of the user who performed the operation'
    }),
    __metadata("design:type", String)
], ClinicDeletionAuditTrail.prototype, "ipAddress", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'text',
        name: 'user_agent',
        nullable: true,
        comment: 'User agent of the client that performed the operation'
    }),
    __metadata("design:type", String)
], ClinicDeletionAuditTrail.prototype, "userAgent", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({
        type: 'timestamp with time zone',
        name: 'created_at',
        comment: 'Timestamp when the audit entry was created'
    }),
    __metadata("design:type", Date)
], ClinicDeletionAuditTrail.prototype, "createdAt", void 0);
exports.ClinicDeletionAuditTrail = ClinicDeletionAuditTrail = __decorate([
    (0, typeorm_1.Entity)('clinic_deletion_audit_trail'),
    (0, typeorm_1.Index)(['targetId']),
    (0, typeorm_1.Index)(['userId']),
    (0, typeorm_1.Index)(['operation']),
    (0, typeorm_1.Index)(['targetType']),
    (0, typeorm_1.Index)(['createdAt']),
    (0, typeorm_1.Index)(['backupId']),
    (0, typeorm_1.Index)(['success']),
    (0, typeorm_1.Index)(['targetId', 'operation']),
    (0, typeorm_1.Index)(['userId', 'createdAt'])
], ClinicDeletionAuditTrail);
//# sourceMappingURL=clinic-deletion-audit-trail.entity.js.map