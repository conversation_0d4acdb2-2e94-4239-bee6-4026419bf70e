"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddClinicIdToLabReports1724669680862 = void 0;
const typeorm_1 = require("typeorm");
class AddClinicIdToLabReports1724669680862 {
    async up(queryRunner) {
        await queryRunner.addColumn('lab_reports', new typeorm_1.TableColumn({
            name: 'clinic_id',
            type: 'uuid',
            isNullable: true,
        }));
        await queryRunner.createForeignKey('lab_reports', new typeorm_1.TableForeignKey({
            columnNames: ['clinic_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinics',
            onDelete: 'CASCADE',
        }));
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable('lab_reports');
        if (table) {
            const foreignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('clinic_id') !== -1);
            if (foreignKey) {
                await queryRunner.dropForeignKey('lab_reports', foreignKey);
                await queryRunner.dropColumn('lab_reports', 'clinic_id');
            }
        }
    }
}
exports.AddClinicIdToLabReports1724669680862 = AddClinicIdToLabReports1724669680862;
//# sourceMappingURL=1724669680862-UpdateLabReportsTable.js.map