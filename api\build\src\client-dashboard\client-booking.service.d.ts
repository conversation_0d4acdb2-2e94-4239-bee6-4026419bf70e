import { Repository } from 'typeorm';
import { AppointmentsService } from '../appointments/appointments.service';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { CreateClientBookingDto, UpdateClientBookingDto, ClientBookingResponseDto } from './dto/client-booking.dto';
import { Patient } from '../patients/entities/patient.entity';
import { User } from '../users/entities/user.entity';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { ClinicUser } from '../clinics/entities/clinic-user.entity';
import { ClientAvailabilityService } from './client-availability.service';
import { PatientsService } from '../patients/patients.service';
import { AppointmentAuditLog } from '../audit/entities/appointment-audit-log.entity';
import { ClinicService } from '../clinics/clinic.service';
export declare class ClientBookingService {
    private appointmentsRepository;
    private patientsRepository;
    private usersRepository;
    private clinicsRepository;
    private clinicUsersRepository;
    private appointmentsService;
    private clientAvailabilityService;
    private patientsService;
    private clinicService;
    private auditLogRepository;
    private readonly logger;
    constructor(appointmentsRepository: Repository<AppointmentEntity>, patientsRepository: Repository<Patient>, usersRepository: Repository<User>, clinicsRepository: Repository<ClinicEntity>, clinicUsersRepository: Repository<ClinicUser>, appointmentsService: AppointmentsService, clientAvailabilityService: ClientAvailabilityService, patientsService: PatientsService, clinicService: ClinicService, auditLogRepository: Repository<AppointmentAuditLog>);
    /**
     * Private helper to log audit events
     * @param appointmentId ID of the affected appointment
     * @param userId ID of the user performing the action
     * @param action The action performed
     * @param changedFields Details of the changes (optional)
     * @param context Additional context (optional)
     */
    private _logAuditEvent;
    private getEffectiveBookingSettings;
    /**
     * Create a new client booking
     * @param createBookingDto DTO with booking details
     * @param ownerId Owner ID from auth
     * @returns Booking details
     */
    createClientBooking(createBookingDto: CreateClientBookingDto, ownerId: string): Promise<ClientBookingResponseDto>;
    /**
     * Get details of a client booking
     * @param appointmentId Appointment ID
     * @param ownerId Owner ID from auth
     * @returns Booking details
     */
    getClientBooking(appointmentId: string, ownerId: string): Promise<ClientBookingResponseDto>;
    /**
     * Update a client booking (reschedule or cancel)
     * @param appointmentId Appointment ID
     * @param updateBookingDto DTO with updated booking details
     * @param ownerId Owner ID from auth
     * @returns Updated booking details
     */
    updateClientBooking(appointmentId: string, updateBookingDto: UpdateClientBookingDto, ownerId: string): Promise<ClientBookingResponseDto>;
    /**
     * Format an appointment entity to the client booking response format
     * @param appointment Appointment entity
     * @returns Formatted booking response
     */
    private formatAppointmentResponse;
    /**
     * Delete a client booking (cancel)
     * @param appointmentId Appointment ID
     * @param ownerId Owner ID from auth
     * @returns Deleted booking details
     */
    deleteClientBooking(appointmentId: string, ownerId: string): Promise<ClientBookingResponseDto>;
}
