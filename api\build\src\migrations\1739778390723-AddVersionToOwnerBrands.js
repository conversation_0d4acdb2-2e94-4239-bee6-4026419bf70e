"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddVersionToOwnerBrands1739778390723 = void 0;
class AddVersionToOwnerBrands1739778390723 {
    async up(queryRunner) {
        // Add version column if it doesn't exist
        await queryRunner.query(`
			DO $$ 
			BEGIN 
				IF NOT EXISTS (
					SELECT 1 
					FROM information_schema.columns 
					WHERE table_name = 'owner_brands' 
					AND column_name = 'version'
				) THEN 
					ALTER TABLE owner_brands 
					ADD COLUMN version integer NOT NULL DEFAULT 1;
				END IF;
			END $$;
		`);
    }
    async down(queryRunner) {
        await queryRunner.query(`
			ALTER TABLE owner_brands 
			DROP COLUMN IF EXISTS version;
		`);
    }
}
exports.AddVersionToOwnerBrands1739778390723 = AddVersionToOwnerBrands1739778390723;
//# sourceMappingURL=1739778390723-AddVersionToOwnerBrands.js.map