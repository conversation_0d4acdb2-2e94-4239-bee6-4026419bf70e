import { <PERSON><PERSON><PERSON><PERSON> } from '../utils/logger/winston-logger.service';
import { TasksService } from './tasks.service';
import { ApiDocumentationBase } from '../base/api-documentation-base';
import { CreateTaskDto } from './dto/create-task.dto';
import { Task } from './entities/tasks.entity';
import { UpdateTaskDto } from './dto/update-task.dto';
export declare class TasksController extends ApiDocumentationBase {
    private readonly logger;
    private readonly tasksService;
    constructor(logger: <PERSON>Logger, tasksService: TasksService);
    createTask(createTaskDto: CreateTaskDto): Promise<Task>;
    getTasks(userId: string): Promise<Task[]>;
    deleteTasks(id: string): Promise<{
        status: boolean;
    }>;
    updateTasks(id: string, updateTaskDto: UpdateTaskDto): Promise<Task>;
}
