/**
 * DTO for requesting available dates for a doctor
 */
export declare class AvailableDatesRequestDto {
    startDate?: string;
    endDate?: string;
    clinicId?: string;
}
/**
 * DTO for response with available dates for a doctor
 */
export declare class AvailableDatesResponseDto {
    availableDates: string[];
}
/**
 * DTO for requesting available time slots for a doctor on a specific date
 */
export declare class TimeSlotRequestDto {
    clinicId?: string;
}
/**
 * DTO representing a time slot
 */
export declare class TimeSlotDto {
    startTime: string;
    endTime: string;
    isAvailable: boolean;
    doctorId?: string;
    doctorName?: string;
}
/**
 * DTO for response with available time slots for a doctor on a specific date
 */
export declare class NextAvailableSlotInfoDto {
    date: string;
    startTime: string;
    endTime: string;
}
export declare class AvailableTimeSlotsResponseDto {
    date: string;
    timeSlots: TimeSlotDto[];
    nextAvailableSlots?: Record<string, NextAvailableSlotInfoDto | null>;
}
