"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateUsersTable1722269301001 = void 0;
const typeorm_1 = require("typeorm");
class UpdateUsersTable1722269301001 {
    async up(queryRunner) {
        await queryRunner.addColumns('users', [
            new typeorm_1.TableColumn({
                name: 'first_name',
                type: 'varchar',
                length: '50',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'last_name',
                type: 'varchar',
                length: '50',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'created_by',
                type: 'uuid',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'updated_by',
                type: 'uuid',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'clinic_id',
                type: 'uuid',
                isNullable: true
            })
        ]);
        const table = await queryRunner.getTable('users');
        if (table) {
            const roleColumn = table.findColumnByName('role');
            if (roleColumn) {
                await queryRunner.dropColumn('users', 'role');
            }
        }
        await queryRunner.createForeignKey('users', new typeorm_1.TableForeignKey({
            columnNames: ['created_by'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'SET NULL'
        }));
        await queryRunner.createForeignKey('users', new typeorm_1.TableForeignKey({
            columnNames: ['updated_by'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'SET NULL'
        }));
        await queryRunner.createForeignKey('users', new typeorm_1.TableForeignKey({
            columnNames: ['clinic_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinics',
            onDelete: 'SET NULL'
        }));
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable('users');
        if (table) {
            const foreignKeys = table.foreignKeys.filter(fk => fk.columnNames.indexOf('created_by') !== -1 ||
                fk.columnNames.indexOf('updated_by') !== -1 ||
                fk.columnNames.indexOf('clinic_id') !== -1);
            await queryRunner.dropForeignKeys('users', foreignKeys);
        }
        await queryRunner.dropColumns('users', [
            'first_name',
            'last_name',
            'created_by',
            'updated_by',
            'clinic_id'
        ]);
        await queryRunner.addColumn('users', new typeorm_1.TableColumn({
            name: 'role',
            type: 'varchar',
            length: '50',
            isNullable: true
        }));
    }
}
exports.UpdateUsersTable1722269301001 = UpdateUsersTable1722269301001;
//# sourceMappingURL=1722269301001-UpdateUsersTable.js.map