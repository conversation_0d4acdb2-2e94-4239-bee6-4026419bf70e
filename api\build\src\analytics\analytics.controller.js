"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../roles/roles.decorator");
const role_enum_1 = require("../roles/role.enum");
const analytics_service_1 = require("./analytics.service");
const analytics_dto_1 = require("./dto/analytics.dto");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
let AnalyticsController = class AnalyticsController {
    constructor(analyticsService) {
        this.analyticsService = analyticsService;
    }
    async getRevenueChartData(startDate, endDate, clinicId) {
        const dto = {
            startDate,
            endDate,
            clinicId
        };
        return await this.analyticsService.getRevenueChartData(dto);
    }
    async getCollectedPaymentsChartData(startDate, endDate, clinicId) {
        const dto = {
            startDate,
            endDate,
            clinicId
        };
        return await this.analyticsService.getCollectedPaymentsChartData(dto);
    }
    async getAppointmentsChartData(startDate, endDate, clinicId, type) {
        const dto = {
            startDate,
            endDate,
            clinicId,
            type
        };
        return await this.analyticsService.getAppointmentsChartData(dto);
    }
    async downloadReport(type, startDate, endDate, clinicId, res, reportType) {
        const dto = {
            type,
            startDate,
            endDate,
            clinicId,
            reportType
        };
        const buffer = await this.analyticsService.generateReport(dto);
        res.set({
            'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition': `attachment; filename="${type}-report.xlsx"`,
            'Content-Length': buffer.length,
            'Cache-Control': 'no-cache'
        });
        res.end(buffer);
    }
    async getDoctorSummary(startDate, endDate, clinicId) {
        const dto = {
            startDate,
            endDate,
            clinicId
        };
        return await this.analyticsService.getDoctorSummary(dto);
    }
    async getSummary(startDate, endDate, clinicId) {
        const dto = {
            startDate,
            endDate,
            clinicId
        };
        return await this.analyticsService.getSummary(dto);
    }
};
exports.AnalyticsController = AnalyticsController;
__decorate([
    (0, common_1.Get)('revenue-chart-data'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get revenue chart data' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', type: String }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', type: String }),
    (0, swagger_1.ApiQuery)({ name: 'clinicId', type: String }),
    (0, track_method_decorator_1.TrackMethod)('get-revenue-chart-data'),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __param(2, (0, common_1.Query)('clinicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getRevenueChartData", null);
__decorate([
    (0, common_1.Get)('collected-payments-chart-data'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get collected payments chart data' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', type: String }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', type: String }),
    (0, swagger_1.ApiQuery)({ name: 'clinicId', type: String }),
    (0, track_method_decorator_1.TrackMethod)('get-collected-payments-chart-data'),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __param(2, (0, common_1.Query)('clinicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getCollectedPaymentsChartData", null);
__decorate([
    (0, common_1.Get)('appointments-chart-data'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get appointments chart data' }),
    (0, swagger_1.ApiQuery)({
        name: 'startDate',
        type: String,
        required: true,
        description: 'Start date for the chart data'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'endDate',
        type: String,
        required: true,
        description: 'End date for the chart data'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'clinicId',
        type: String,
        required: true,
        description: 'Clinic ID to filter appointments'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'type',
        enum: analytics_dto_1.AppointmentAnalyticsType,
        required: true,
        description: 'Type of analytics data to retrieve'
    }),
    (0, track_method_decorator_1.TrackMethod)('get-appointments-chart-data'),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __param(2, (0, common_1.Query)('clinicId')),
    __param(3, (0, common_1.Query)('type', new common_1.ParseEnumPipe(analytics_dto_1.AppointmentAnalyticsType))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getAppointmentsChartData", null);
__decorate([
    (0, common_1.Get)('download-report'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Download analytics report' }),
    (0, swagger_1.ApiQuery)({ name: 'type', enum: analytics_dto_1.AnalyticsType }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', type: String }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', type: String }),
    (0, swagger_1.ApiQuery)({
        name: 'reportType',
        enum: analytics_dto_1.AnalyticsReportType,
        required: false
    }),
    (0, track_method_decorator_1.TrackMethod)('download-analytics-report'),
    __param(0, (0, common_1.Query)('type', new common_1.ParseEnumPipe(analytics_dto_1.AnalyticsType))),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __param(3, (0, common_1.Query)('clinicId')),
    __param(4, (0, common_1.Res)()),
    __param(5, (0, common_1.Query)('reportType', new common_1.ParseEnumPipe(analytics_dto_1.AnalyticsReportType, { optional: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, Object, String]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "downloadReport", null);
__decorate([
    (0, common_1.Get)('doctor-summary'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get doctor performance summary' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', type: String }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', type: String }),
    (0, swagger_1.ApiQuery)({ name: 'clinicId', type: String }),
    (0, track_method_decorator_1.TrackMethod)('get-doctor-summary'),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __param(2, (0, common_1.Query)('clinicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getDoctorSummary", null);
__decorate([
    (0, common_1.Get)('summary'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN),
    (0, swagger_1.ApiOperation)({ summary: 'Get clinic performance summary' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', type: String }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', type: String }),
    (0, swagger_1.ApiQuery)({ name: 'clinicId', type: String }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns summary data for clinic'
    }),
    (0, track_method_decorator_1.TrackMethod)('get-clinic-summary'),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __param(2, (0, common_1.Query)('clinicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], AnalyticsController.prototype, "getSummary", null);
exports.AnalyticsController = AnalyticsController = __decorate([
    (0, swagger_1.ApiTags)('Analytics'),
    (0, common_1.Controller)('analytics'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [analytics_service_1.AnalyticsService])
], AnalyticsController);
//# sourceMappingURL=analytics.controller.js.map