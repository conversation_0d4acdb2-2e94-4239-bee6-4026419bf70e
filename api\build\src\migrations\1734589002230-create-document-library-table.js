"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateDocumentLibraryTable1734589002230 = void 0;
const typeorm_1 = require("typeorm");
class CreateDocumentLibraryTable1734589002230 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: "document_library",
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    default: 'uuid_generate_v4()'
                },
                {
                    name: "clinic_id",
                    type: "uuid",
                    isNullable: true,
                },
                {
                    name: "document_name",
                    type: "varchar",
                    isNullable: false,
                },
                {
                    name: "signature_required",
                    type: "boolean",
                    isNullable: false,
                },
                {
                    name: "category",
                    type: "varchar",
                    isNullable: true,
                },
                {
                    name: "document_type",
                    type: "enum",
                    enum: ["upload", "create"],
                    isNullable: false,
                },
                {
                    name: "file_key",
                    type: "varchar",
                    isNullable: true,
                },
                {
                    name: "document_body",
                    type: "json",
                    isNullable: true,
                },
                {
                    name: "created_at",
                    type: "timestamp",
                    default: "CURRENT_TIMESTAMP",
                    isNullable: false,
                },
                {
                    name: "updated_at",
                    type: "timestamp",
                    default: "CURRENT_TIMESTAMP",
                    onUpdate: "CURRENT_TIMESTAMP",
                    isNullable: false,
                },
                {
                    name: "deleted_at",
                    type: "timestamp",
                    isNullable: true,
                },
                {
                    name: "created_by",
                    type: "uuid",
                    isNullable: true,
                },
                {
                    name: "updated_by",
                    type: "uuid",
                    isNullable: true,
                },
            ],
            foreignKeys: [
                {
                    columnNames: ["clinic_id"],
                    referencedColumnNames: ["id"],
                    referencedTableName: "clinics",
                    onDelete: "CASCADE",
                },
            ],
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable("document_library");
    }
}
exports.CreateDocumentLibraryTable1734589002230 = CreateDocumentLibraryTable1734589002230;
//# sourceMappingURL=1734589002230-create-document-library-table.js.map