{"version": 3, "file": "user-migration.service.js", "sourceRoot": "", "sources": ["../../../../src/pet-transfer/services/user-migration.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6CAAmD;AACnD,qCAAqC;AACrC,kEAAwD;AACxD,kFAAuE;AACvE,uFAA4E;AAC5E,iFAAsE;AAG/D,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAChC,YAEkB,cAAgC,EAEhC,oBAA4C,EAE5C,sBAAgD,EAEhD,oBAA4C;QAN5C,mBAAc,GAAd,cAAc,CAAkB;QAEhC,yBAAoB,GAApB,oBAAoB,CAAwB;QAE5C,2BAAsB,GAAtB,sBAAsB,CAA0B;QAEhD,yBAAoB,GAApB,oBAAoB,CAAwB;IAC3D,CAAC;IAEJ,KAAK,CAAC,YAAY,CACjB,SAAiB,EACjB,cAAsB,EACtB,mBAA2B,EAC3B,kBAA0B;QAE1B,MAAM,YAAY,GAAG,IAAI,GAAG,EAAkB,CAAC;QAE/C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;YAC5D,KAAK,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,cAAc,EAAE;SAC9C,CAAC,CAAC;QAEH,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;YACnC,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;gBACpD,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE;aAC5B,CAAC,CAAC;YAEH,IAAI,UAAU,EAAE,CAAC;gBAChB,IAAI,eAAe,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;oBACvD,KAAK,EAAE;wBACN,SAAS,EAAE,UAAU,CAAC,SAAS;wBAC/B,QAAQ,EAAE,UAAU,CAAC,QAAQ;wBAC7B,OAAO,EAAE,kBAAkB;qBAC3B;iBACD,CAAC,CAAC;gBAEH,IAAI,CAAC,eAAe,EAAE,CAAC;oBACtB,yBAAyB;oBACzB,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;wBAC5C,GAAG,UAAU;wBACb,EAAE,EAAE,SAAS,EAAE,qCAAqC;wBACpD,OAAO,EAAE,kBAAkB;wBAC3B,QAAQ,EAAE,KAAK,CAAC,mBAAmB;qBACnC,CAAC,CAAC;oBACH,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;gBACjD,CAAC;gBAED,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,eAAe,CAAC,EAAE,CAAC,CAAC;gBAEpD,oCAAoC;gBACpC,IAAI,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;oBACxD,KAAK,EAAE;wBACN,MAAM,EAAE,eAAe,CAAC,EAAE;wBAC1B,QAAQ,EAAE,mBAAmB;qBAC7B;iBACD,CAAC,CAAC;gBAEH,IAAI,CAAC,UAAU,EAAE,CAAC;oBACjB,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;wBAC7C,MAAM,EAAE,eAAe,CAAC,EAAE;wBAC1B,QAAQ,EAAE,mBAAmB;wBAC7B,OAAO,EAAE,kBAAkB;qBAC3B,CAAC,CAAC;oBACH,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAClD,CAAC;gBAED,qCAAqC;gBACrC,IAAI,UAAU,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC;oBACxD,KAAK,EAAE;wBACN,aAAa,EAAE,eAAe,CAAC,EAAE;wBACjC,OAAO,EAAE,kBAAkB;qBAC3B;iBACD,CAAC,CAAC;gBAEH,IAAI,CAAC,UAAU,EAAE,CAAC;oBACjB,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;wBAC7C,aAAa,EAAE,eAAe,CAAC,EAAE;wBACjC,OAAO,EAAE,kBAAkB;wBAC3B,SAAS,EAAE,eAAe,CAAC,SAAS;wBACpC,QAAQ,EAAE,eAAe,CAAC,QAAQ;qBAClC,CAAC,CAAC;oBACH,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAClD,CAAC;YACF,CAAC;QACF,CAAC;QAED,OAAO,YAAY,CAAC;IACrB,CAAC;CACD,CAAA;AA1FY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;IAGV,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;IAEtB,WAAA,IAAA,0BAAgB,EAAC,+BAAU,CAAC,CAAA;IAE5B,WAAA,IAAA,0BAAgB,EAAC,mCAAY,CAAC,CAAA;IAE9B,WAAA,IAAA,0BAAgB,EAAC,+BAAU,CAAC,CAAA;qCALI,oBAAU;QAEJ,oBAAU;QAER,oBAAU;QAEZ,oBAAU;GATtC,oBAAoB,CA0FhC"}