"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PetTransferHistory1739514962583 = void 0;
const typeorm_1 = require("typeorm");
class PetTransferHistory1739514962583 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'pet_transfer_history',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'patient_id',
                    type: 'uuid'
                },
                {
                    name: 'from_owner_brand_id',
                    type: 'uuid'
                },
                {
                    name: 'to_owner_brand_id',
                    type: 'uuid'
                },
                {
                    name: 'brand_id',
                    type: 'uuid'
                },
                {
                    name: 'clinic_id',
                    type: 'uuid'
                },
                {
                    name: 'transferred_by_user_id',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'now()'
                }
            ]
        }), true);
        // Add foreign key constraints
        await queryRunner.createForeignKey('pet_transfer_history', new typeorm_1.TableForeignKey({
            columnNames: ['patient_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'patients',
            onDelete: 'CASCADE'
        }));
        await queryRunner.createForeignKey('pet_transfer_history', new typeorm_1.TableForeignKey({
            columnNames: ['from_owner_brand_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'owner_brands',
            onDelete: 'SET NULL'
        }));
        await queryRunner.createForeignKey('pet_transfer_history', new typeorm_1.TableForeignKey({
            columnNames: ['to_owner_brand_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'owner_brands',
            onDelete: 'SET NULL'
        }));
        await queryRunner.createForeignKey('pet_transfer_history', new typeorm_1.TableForeignKey({
            columnNames: ['brand_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'brands',
            onDelete: 'CASCADE'
        }));
        await queryRunner.createForeignKey('pet_transfer_history', new typeorm_1.TableForeignKey({
            columnNames: ['clinic_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinics',
            onDelete: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        const table = await queryRunner.getTable('pet_transfer_history');
        if (table) {
            const foreignKeys = table.foreignKeys;
            for (const foreignKey of foreignKeys) {
                await queryRunner.dropForeignKey('pet_transfer_history', foreignKey);
            }
        }
        await queryRunner.dropTable('pet_transfer_history');
    }
}
exports.PetTransferHistory1739514962583 = PetTransferHistory1739514962583;
//# sourceMappingURL=1739514962583-PetTransferHistory.js.map