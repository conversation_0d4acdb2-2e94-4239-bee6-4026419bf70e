"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Task1724407131376 = void 0;
const typeorm_1 = require("typeorm");
class Task1724407131376 {
    constructor() {
        this.name = 'Task1724407131376';
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'tasks',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'user_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'title',
                    type: 'varchar',
                    isNullable: false
                },
                {
                    name: 'is_completed',
                    type: 'boolean',
                    isNullable: true
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'deleted_at',
                    type: 'timestamp',
                    isNullable: true
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                }
            ]
        }));
        await queryRunner.createForeignKey('tasks', new typeorm_1.TableForeignKey({
            columnNames: ['user_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('tasks');
    }
}
exports.Task1724407131376 = Task1724407131376;
//# sourceMappingURL=1724407131376-create-task.js.map