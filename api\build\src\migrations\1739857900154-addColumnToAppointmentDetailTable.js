"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddColumnToAppointmentDetailTable1739857900154 = void 0;
const typeorm_1 = require("typeorm");
class AddColumnToAppointmentDetailTable1739857900154 {
    async up(queryRunner) {
        await queryRunner.addColumn('appointment_details', new typeorm_1.TableColumn({
            name: 'prescription_created_at',
            type: 'timestamp',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('appointment_details', 'prescription_created_at');
    }
}
exports.AddColumnToAppointmentDetailTable1739857900154 = AddColumnToAppointmentDetailTable1739857900154;
//# sourceMappingURL=1739857900154-addColumnToAppointmentDetailTable.js.map