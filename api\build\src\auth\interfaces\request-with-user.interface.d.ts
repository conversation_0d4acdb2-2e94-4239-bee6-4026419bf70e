import { Request } from 'express';
/**
 * Extended Request interface that includes authenticated user data
 */
export interface RequestWithUser extends Request {
    user: {
        id?: string;
        userId?: string;
        email?: string;
        roleId?: string;
        role?: string;
        clinicId?: string;
        brandId?: string;
        owner?: {
            id: string;
            fullName?: string;
        };
    };
}
