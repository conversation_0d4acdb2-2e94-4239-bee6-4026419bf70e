"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlterClinicRoomsDescription1727678848448 = void 0;
const typeorm_1 = require("typeorm");
class AlterClinicRoomsDescription1727678848448 {
    constructor() {
        this.name = 'AlterClinicRoomsDescription1722759737335';
    }
    async up(queryRunner) {
        await queryRunner.changeColumn('clinic_rooms', 'description', new typeorm_1.TableColumn({
            name: 'description',
            type: 'varchar',
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.changeColumn('clinic_rooms', 'description', new typeorm_1.TableColumn({
            name: 'description',
            type: 'varchar',
            isNullable: false
        }));
    }
}
exports.AlterClinicRoomsDescription1727678848448 = AlterClinicRoomsDescription1727678848448;
//# sourceMappingURL=1727678848448-AlterClinicRoomsDescription.js.map