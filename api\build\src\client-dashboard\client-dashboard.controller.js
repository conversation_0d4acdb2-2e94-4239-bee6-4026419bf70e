"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientDashboardController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const client_dashboard_service_1 = require("./client-dashboard.service");
const client_dashboard_response_dto_1 = require("./dto/client-dashboard-response.dto");
const client_appointments_response_dto_1 = require("./dto/client-appointments-response.dto");
const direct_login_dto_1 = require("./dto/direct-login.dto");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const role_enum_1 = require("../roles/role.enum");
const roles_decorator_1 = require("../roles/roles.decorator");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
let ClientDashboardController = class ClientDashboardController {
    constructor(clientDashboardService, logger) {
        this.clientDashboardService = clientDashboardService;
        this.logger = logger;
    }
    async directLogin(directLoginDto) {
        try {
            this.logger.log('Direct login for booking portal', {
                dto: directLoginDto
            });
            const response = await this.clientDashboardService.directLogin(directLoginDto);
            this.logger.log('Direct login successful', {
                phoneNumber: directLoginDto.phoneNumber,
                countryCode: directLoginDto.countryCode || '91',
                brandId: directLoginDto.brandId
            });
            return response;
        }
        catch (error) {
            this.logger.error('Error during direct login for booking portal', {
                error
            });
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Failed to login', common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getClientDashboard(ownerId, req) {
        var _a;
        try {
            const brandId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.brandId;
            if (!brandId) {
                this.logger.error('Brand ID not found in user context', {
                    user: req.user,
                    ownerId
                });
                throw new common_1.HttpException('Brand ID not found in user context', common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log('Getting client dashboard', { ownerId, brandId });
            return await this.clientDashboardService.getClientDashboard(ownerId, brandId);
        }
        catch (error) {
            this.logger.error('Error in getClientDashboard', {
                error,
                ownerId
            });
            throw new common_1.HttpException(error.message || 'Error retrieving client dashboard', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getClientAppointments(ownerId, req, date, status) {
        var _a;
        try {
            const brandId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.brandId;
            if (!brandId) {
                this.logger.error('Brand ID not found in user context', {
                    user: req.user,
                    ownerId
                });
                throw new common_1.HttpException('Brand ID not found in user context', common_1.HttpStatus.BAD_REQUEST);
            }
            // Parse status if provided
            let parsedStatus;
            if (status) {
                try {
                    parsedStatus = JSON.parse(status);
                }
                catch (err) {
                    this.logger.error('Error parsing status', {
                        error: err,
                        status
                    });
                    parsedStatus = undefined;
                }
            }
            this.logger.log('Getting client appointments', {
                ownerId,
                brandId,
                date,
                status: parsedStatus
            });
            return await this.clientDashboardService.getClientAppointments(ownerId, brandId, { date, status: parsedStatus });
        }
        catch (error) {
            this.logger.error('Error in getClientAppointments', {
                error,
                ownerId
            });
            throw new common_1.HttpException(error.message || 'Error retrieving client appointments', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getClientClinics(req) {
        var _a;
        try {
            const brandId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.brandId;
            if (!brandId) {
                this.logger.error('Brand ID not found in user context', {
                    user: req.user
                });
                throw new common_1.HttpException('Brand ID not found in user context', common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log('Getting clinics for client dashboard', {
                brandId
            });
            return await this.clientDashboardService.getClientClinics(brandId);
        }
        catch (error) {
            this.logger.error('Error in getClientClinics', {
                error
            });
            throw new common_1.HttpException(error.message ||
                'Error retrieving clinics for client dashboard', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getClientDoctors(req, clinicId) {
        var _a;
        try {
            const brandId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.brandId;
            if (!brandId) {
                this.logger.error('Brand ID not found in user context', {
                    user: req.user
                });
                throw new common_1.HttpException('Brand ID not found in user context', common_1.HttpStatus.BAD_REQUEST);
            }
            this.logger.log('Getting doctors for client dashboard', {
                brandId,
                clinicId
            });
            return await this.clientDashboardService.getClientDoctors(brandId, clinicId);
        }
        catch (error) {
            this.logger.error('Error in getClientDoctors', {
                error,
                clinicId
            });
            throw new common_1.HttpException(error.message ||
                'Error retrieving doctors for client dashboard', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.ClientDashboardController = ClientDashboardController;
__decorate([
    (0, common_1.Post)('auth/booking/direct-login'),
    (0, swagger_1.ApiOperation)({ summary: 'Direct login for pet owner' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Login successful. Access token provided.'
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Brand not found.' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Failed to login.' }),
    (0, common_1.UsePipes)(common_1.ValidationPipe),
    (0, track_method_decorator_1.TrackMethod)('directLogin-client-dashboard'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [direct_login_dto_1.DirectLoginDto]),
    __metadata("design:returntype", Promise)
], ClientDashboardController.prototype, "directLogin", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns client dashboard information',
        type: client_dashboard_response_dto_1.ClientDashboardResponseDto
    }),
    (0, swagger_1.ApiParam)({ name: 'ownerId', description: 'The ID of the owner' }),
    (0, common_1.Get)('/client-dashboard/:ownerId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.RECEPTIONIST, role_enum_1.Role.OWNER),
    (0, track_method_decorator_1.TrackMethod)('getClientDashboard-client-dashboard'),
    __param(0, (0, common_1.Param)('ownerId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ClientDashboardController.prototype, "getClientDashboard", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns client appointments',
        type: client_appointments_response_dto_1.ClientAppointmentsResponseDto
    }),
    (0, swagger_1.ApiParam)({ name: 'ownerId', description: 'The ID of the owner' }),
    (0, swagger_1.ApiQuery)({
        name: 'date',
        required: false,
        description: 'Filter by date (YYYY-MM-DD)'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'status',
        required: false,
        description: 'Filter by appointment status'
    }),
    (0, common_1.Get)('/client-appointments/:ownerId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.RECEPTIONIST, role_enum_1.Role.OWNER),
    (0, track_method_decorator_1.TrackMethod)('getClientAppointments-client-dashboard'),
    __param(0, (0, common_1.Param)('ownerId')),
    __param(1, (0, common_1.Req)()),
    __param(2, (0, common_1.Query)('date')),
    __param(3, (0, common_1.Query)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String, String]),
    __metadata("design:returntype", Promise)
], ClientDashboardController.prototype, "getClientAppointments", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns list of clinics for client dashboard',
        type: Array
    }),
    (0, common_1.Get)('/client-clinics'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.RECEPTIONIST, role_enum_1.Role.OWNER),
    (0, track_method_decorator_1.TrackMethod)('getClientClinics-client-dashboard'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ClientDashboardController.prototype, "getClientClinics", null);
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns list of doctors for client dashboard',
        type: Array
    }),
    (0, swagger_1.ApiQuery)({
        name: 'clinicId',
        required: false,
        description: 'Filter doctors by clinic ID'
    }),
    (0, common_1.Get)('/client-doctors'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.ADMIN, role_enum_1.Role.DOCTOR, role_enum_1.Role.RECEPTIONIST, role_enum_1.Role.OWNER),
    (0, track_method_decorator_1.TrackMethod)('getClientDoctors-client-dashboard'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('clinicId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], ClientDashboardController.prototype, "getClientDoctors", null);
exports.ClientDashboardController = ClientDashboardController = __decorate([
    (0, swagger_1.ApiTags)('Client Dashboard'),
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [client_dashboard_service_1.ClientDashboardService,
        winston_logger_service_1.WinstonLogger])
], ClientDashboardController);
//# sourceMappingURL=client-dashboard.controller.js.map