import {
	BadRequestException,
	ConflictException,
	Injectable,
	InternalServerErrorException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Brand } from './entities/brand.entity';
import { CreateBrandDto } from './dto/create-brand.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { BrandWithSettingsDto } from './dto/brand-with-settings.dto';

@Injectable()
export class BrandService {
	constructor(
		@InjectRepository(Brand)
		private brandRepository: Repository<Brand>,
		private readonly logger: WinstonLogger
	) {}

	async createBrand(createBrandDto: CreateBrandDto): Promise<Brand> {
		try {
			this.logger.log('Creating Brand', { dto: createBrandDto });
			const existingBrand = await this.brandRepository.findOne({
				where: { name: createBrandDto.name }
			});
			if (existingBrand) {
				this.logger.error('Brand already exists', {
					email: createBrandDto.name
				});
				throw new ConflictException(
					'Brand with this name already exists'
				);
			}
			const brand = this.brandRepository.create(createBrandDto);
			return await this.brandRepository.save(brand);
		} catch (error) {
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while creating the user'
			);
		}
	}

	async getAllBrands(
		page: number = 1,
		limit: number = 10,
		search: string = '',
		orderBy: string = 'DESC'
	): Promise<{ brands: Brand[]; total: number }> {
		try {
			this.logger.log('Fetching all Brands', {
				page,
				limit,
				search,
				orderBy
			});

			const whereCondition = search
				? { name: Like(`%${search}%`) }
				: {};

			const [brands, total] = await this.brandRepository.findAndCount({
				where: whereCondition,
				skip: (page - 1) * limit,
				take: limit,
				order:
					orderBy === 'ASC' ? { createdAt: 'ASC' } : { createdAt: 'DESC' }
			});

			this.logger.log('Fetched all Brands:', {
				brandsCount: brands.length,
				total,
				page,
				limit
			});

			return { brands, total };
		} catch (error) {
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while Fetching the brands'
			);
		}
	}

	// Backward compatibility method for non-paginated calls
	async getAllBrandsSimple(): Promise<Brand[]> {
		try {
			this.logger.log('Fetching all Brands (simple)');
			const brands = await this.brandRepository.find({
				order: { createdAt: 'DESC' }
			});
			this.logger.log('Fetched all Brands (simple):', { brands });
			return brands;
		} catch (error) {
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while Fetching the brands'
			);
		}
	}

	async getBrandById(id: string): Promise<Brand | null> {
		try {
			this.logger.log('Fetching a Brand');
			const brand = await this.brandRepository.findOne({ where: { id } });
			this.logger.log('Fetched a Brand:', { brand });
			return brand;
		} catch (error) {
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while Fetching the brand'
			);
		}
	}

	async getBrandBySlug(slug: string): Promise<BrandWithSettingsDto | null> {
		try {
			this.logger.log('Fetching a Brand');
			const brand = await this.brandRepository.findOne({
				where: { slug },
				relations: ['clinics']
			});

			if (!brand) {
				return null;
			}

			const hasClientBookingEnabled =
				brand.clinics?.some(
					clinic =>
						clinic.customRule?.clientBookingSettings?.isEnabled ===
						true
				) || false;

			const brandDto = new BrandWithSettingsDto();

			// Copy only the basic brand properties (excluding clinics)
			brandDto.id = brand.id;
			brandDto.name = brand.name;
			brandDto.slug = brand.slug;
			brandDto.createdAt = brand.createdAt;
			brandDto.updatedAt = brand.updatedAt;
			brandDto.createdBy = brand.createdBy;
			brandDto.updatedBy = brand.updatedBy;

			// Add the calculated client booking flag
			brandDto.hasClientBookingEnabled = hasClientBookingEnabled;

			// Include clinics with their contact numbers
			if (brand.clinics && brand.clinics.length > 0) {
				// Include basic clinic info including phone numbers
				brandDto.clinics = brand.clinics.map(clinic => ({
					id: clinic.id,
					name: clinic.name,
					phoneNumbers: clinic.phoneNumbers || []
				}));
			}

			this.logger.log('Fetched a Brand with settings:', {
				brandId: brand.id,
				hasClientBookingEnabled,
				clinicsCount: brand.clinics?.length || 0
			});

			return brandDto;
		} catch (error) {
			if (
				error instanceof ConflictException ||
				error instanceof BadRequestException
			) {
				throw error;
			}
			throw new InternalServerErrorException(
				'An unexpected error occurred while Fetching the brand'
			);
		}
	}
}
