"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const winston_logger_service_1 = require("./logger/winston-logger.service");
const newrelic = require("newrelic");
let HttpExceptionFilter = class HttpExceptionFilter {
    constructor(logger) {
        this.logger = logger;
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const status = exception.getStatus();
        let errorResponse = {
            statusCode: status,
            timestamp: new Date().toISOString(),
            path: request.url,
            message: exception.message
        };
        // Expanded condition to handle more types of exceptions
        if (exception instanceof common_1.BadRequestException ||
            exception instanceof common_1.ConflictException ||
            exception instanceof common_1.UnauthorizedException ||
            exception instanceof common_1.ForbiddenException ||
            exception instanceof common_1.NotFoundException ||
            exception instanceof common_1.HttpException) {
            const exceptionResponse = exception.getResponse();
            // If it's a validation error or has a structured error message, use the original error structure
            if (typeof exceptionResponse === 'object' &&
                'message' in exceptionResponse) {
                errorResponse = {
                    ...errorResponse,
                    message: this.getExceptionMessage(exception),
                    errors: exceptionResponse['message']
                };
            }
        }
        const logObject = {
            method: request.method,
            url: request.url,
            headers: request.headers,
            body: request.body,
            params: request.params,
            query: request.query,
            ip: request.ip,
            statusCode: status
        };
        this.logger.error(`HTTP Exception: ${exception.message}`, logObject);
        // Add error to New Relic
        newrelic.noticeError(exception, {
            path: request.url,
            method: request.method,
        });
        response.status(status).json(errorResponse);
    }
    getExceptionMessage(exception) {
        if (exception instanceof common_1.BadRequestException)
            return 'Validation failed';
        if (exception instanceof common_1.ConflictException)
            return 'Conflict occurred';
        if (exception instanceof common_1.UnauthorizedException)
            return 'Unauthorized';
        if (exception instanceof common_1.ForbiddenException)
            return 'Forbidden';
        if (exception instanceof common_1.NotFoundException)
            return 'Not found';
        return 'An error occurred';
    }
};
exports.HttpExceptionFilter = HttpExceptionFilter;
exports.HttpExceptionFilter = HttpExceptionFilter = __decorate([
    (0, common_1.Catch)(common_1.HttpException),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger])
], HttpExceptionFilter);
//# sourceMappingURL=http-exception.filter.js.map