import { Repository } from 'typeorm';
import { DiagnosticTemplate } from './entities/diagnostic-template.entity';
import { CreateDiagnosticTemplateDto } from './dto/create-template.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { CreateDiagnosticNoteDto, UpdateDiagnosticNoteDto } from './dto/diagnostic-note.dto';
import { LabReport } from '../clinic-lab-report/entities/lab-report.entity';
import { ClinicLabReport } from '../clinic-lab-report/entities/clinic-lab-report.entity';
import { DiagnosticNote } from './entities/diagnostic-note.entity';
import { DataSource } from 'typeorm';
import { Patient } from '../patients/entities/patient.entity';
import { PatientsService } from '../patients/patients.service';
import { S3Service } from '../utils/aws/s3/s3.service';
import { AppointmentEntity } from '../appointments/entities/appointment.entity';
import { AppointmentDetailsEntity } from '../appointments/entities/appointment-details.entity';
import { AppointmentGateway } from '../socket/socket.appointment.gateway';
export declare class DiagnosticTemplatesService {
    private templateRepository;
    private diagnosticNoteRepository;
    private labReportRepository;
    private clinicLabReportRepository;
    private appointmentRepository;
    private appointmentDetailsRepository;
    private readonly logger;
    private patientService;
    private s3Service;
    private dataSource;
    private readonly appointmentGateway;
    constructor(templateRepository: Repository<DiagnosticTemplate>, diagnosticNoteRepository: Repository<DiagnosticNote>, labReportRepository: Repository<LabReport>, clinicLabReportRepository: Repository<ClinicLabReport>, appointmentRepository: Repository<AppointmentEntity>, appointmentDetailsRepository: Repository<AppointmentDetailsEntity>, logger: WinstonLogger, patientService: PatientsService, s3Service: S3Service, dataSource: DataSource, appointmentGateway: AppointmentGateway);
    create(createDto: CreateDiagnosticTemplateDto, userId: string): Promise<DiagnosticTemplate>;
    findAll(clinicId: string): Promise<DiagnosticTemplate[]>;
    findOne(id: string, clinicId: string): Promise<DiagnosticTemplate>;
    update(id: string, updateDto: Partial<CreateDiagnosticTemplateDto>, userId: string, clinicId: string): Promise<DiagnosticTemplate>;
    remove(id: string, clinicId: string): Promise<void>;
    createNote(createNoteDto: CreateDiagnosticNoteDto, userId: string): Promise<DiagnosticNote>;
    getTemplatesForLabReport(labReportId: string, clinicId: string): Promise<DiagnosticTemplate[]>;
    updateNote(id: string, updateNoteDto: UpdateDiagnosticNoteDto, userId: string): Promise<DiagnosticNote>;
    findTemplatesByDiagnostic(clinicLabReportId: string, clinicId: string): Promise<{
        status: boolean;
        data: DiagnosticTemplate[];
    }>;
    getNotesByLabReport(labReportId: string, clinicId: string): Promise<DiagnosticNote[]>;
    getPatientNotes(patientId: string): Promise<DiagnosticNote[]>;
    getNote(noteId: string): Promise<DiagnosticNote[]>;
    deleteNote(id: string): Promise<{
        success: boolean;
    }>;
    /**
     * Helper method to update appointment details with diagnostic notes and broadcast changes via socket
     * Uses selective updates to only modify the specific lab report that had diagnostic notes changed.
     */
    private updateAppointmentDetailsAndBroadcastDiagnosticNotes;
    private generateAndUploadPDF;
    getClinicAddress(patientDetail: Patient): string;
    private getCompleteNoteData;
}
