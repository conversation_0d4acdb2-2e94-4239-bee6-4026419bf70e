"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientEstimateController = void 0;
const common_1 = require("@nestjs/common");
const patient_estimate_service_1 = require("./patient-estimate.service");
const create_patient_estimate_dto_1 = require("./dto/create-patient-estimate.dto");
const patient_estimate_entity_1 = require("./entities/patient-estimate.entity");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const api_documentation_base_1 = require("../base/api-documentation-base");
const swagger_1 = require("@nestjs/swagger");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
const update_patient_document_library_dto_1 = require("../patient-document-libraries/dto/update-patient-document-library.dto");
let PatientEstimateController = class PatientEstimateController extends api_documentation_base_1.ApiDocumentationBase {
    constructor(patientEstimateService, logger) {
        super();
        this.patientEstimateService = patientEstimateService;
        this.logger = logger;
    }
    async create(createPatientEstimateDto) {
        return await this.patientEstimateService.create(createPatientEstimateDto);
    }
    async findAll() {
        return await this.patientEstimateService.findAll();
    }
    async findOne(id) {
        return await this.patientEstimateService.findOne(id);
    }
    async findByPatient(patientId, page = 1, limit = 10, search) {
        return await this.patientEstimateService.findByPatient(patientId, +page, +limit, search);
    }
    async update(id, body) {
        return await this.patientEstimateService.sendSignedDocument(id, body);
    }
    async remove(id) {
        await this.patientEstimateService.remove(id);
    }
};
exports.PatientEstimateController = PatientEstimateController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new patient estimate entry' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'The patient estimate entry has been successfully created.',
        type: patient_estimate_entity_1.PatientEstimate
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data.'
    }),
    (0, track_method_decorator_1.TrackMethod)('create-patient-estimate'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_patient_estimate_dto_1.CreatePatientEstimateDto]),
    __metadata("design:returntype", Promise)
], PatientEstimateController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PatientEstimateController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PatientEstimateController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('patient/:patientId'),
    __param(0, (0, common_1.Param)('patientId')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __param(3, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object, String]),
    __metadata("design:returntype", Promise)
], PatientEstimateController.prototype, "findByPatient", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_patient_document_library_dto_1.UpdateSignedDocumentDto]),
    __metadata("design:returntype", Promise)
], PatientEstimateController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PatientEstimateController.prototype, "remove", null);
exports.PatientEstimateController = PatientEstimateController = __decorate([
    (0, swagger_1.ApiTags)('patient-estimates'),
    (0, common_1.Controller)('patient-estimates'),
    __metadata("design:paramtypes", [patient_estimate_service_1.PatientEstimateService,
        winston_logger_service_1.WinstonLogger])
], PatientEstimateController);
//# sourceMappingURL=patient-estimate.controller.js.map