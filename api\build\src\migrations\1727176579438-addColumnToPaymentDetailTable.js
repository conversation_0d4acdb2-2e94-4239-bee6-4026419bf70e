"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddColumnToPaymentDetailTable1727176579438 = void 0;
const typeorm_1 = require("typeorm");
class AddColumnToPaymentDetailTable1727176579438 {
    async up(queryRunner) {
        await queryRunner.addColumns('payment_details', [
            new typeorm_1.TableColumn({
                name: 'receipt_detail',
                type: 'json',
                isNullable: true
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('payment_details', 'receipt_detail');
    }
}
exports.AddColumnToPaymentDetailTable1727176579438 = AddColumnToPaymentDetailTable1727176579438;
//# sourceMappingURL=1727176579438-addColumnToPaymentDetailTable.js.map