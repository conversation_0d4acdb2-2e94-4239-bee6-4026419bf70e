"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateClinicPinColumnName1725262104039 = void 0;
class UpdateClinicPinColumnName1725262104039 {
    async up(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE clinics
            RENAME COLUMN "pin" TO "address_pincode"
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE clinics
            RENAME COLUMN "address_pincode" TO "pin"
        `);
    }
}
exports.UpdateClinicPinColumnName1725262104039 = UpdateClinicPinColumnName1725262104039;
//# sourceMappingURL=1725262104039-UpdateClinicsPinCol.js.map