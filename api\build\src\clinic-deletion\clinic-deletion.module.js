"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicDeletionModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const clinic_deletion_controller_1 = require("./clinic-deletion.controller");
const clinic_deletion_service_1 = require("./clinic-deletion.service");
const database_backup_service_1 = require("./services/database-backup.service");
const file_backup_service_1 = require("./services/file-backup.service");
const database_restore_service_1 = require("./services/database-restore.service");
const file_restore_service_1 = require("./services/file-restore.service");
const query_manager_service_1 = require("./services/query-manager.service");
const startup_validation_service_1 = require("./services/startup-validation.service");
const role_module_1 = require("../roles/role.module");
const s3_module_1 = require("../utils/aws/s3/s3.module");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
// Import all entities that might be affected by deletion
const brand_entity_1 = require("../brands/entities/brand.entity");
const clinic_entity_1 = require("../clinics/entities/clinic.entity");
const patient_entity_1 = require("../patients/entities/patient.entity");
const patient_owner_entity_1 = require("../patients/entities/patient-owner.entity");
const appointment_entity_1 = require("../appointments/entities/appointment.entity");
const appointment_details_entity_1 = require("../appointments/entities/appointment-details.entity");
const appointment_doctor_entity_1 = require("../appointments/entities/appointment-doctor.entity");
const appointment_assessment_entity_1 = require("../appointment-assessment/entities/appointment-assessment.entity");
const user_entity_1 = require("../users/entities/user.entity");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
const payment_details_entity_1 = require("../payment-details/entities/payment-details.entity");
const emr_entity_1 = require("../emr/entities/emr.entity");
const document_library_entity_1 = require("../document-library/entities/document-library.entity");
const patient_document_library_entity_1 = require("../patient-document-libraries/entities/patient-document-library.entity");
const owner_brand_entity_1 = require("../owners/entities/owner-brand.entity");
const pet_transfer_history_entity_1 = require("../owners/entities/pet-transfer-history.entity");
const patient_estimate_entity_1 = require("../patient-estimate/entities/patient-estimate.entity");
const diagnostic_template_entity_1 = require("../diagnostic-notes-templates/entities/diagnostic-template.entity");
const diagnostic_note_entity_1 = require("../diagnostic-notes-templates/entities/diagnostic-note.entity");
const invoice_audit_log_entity_1 = require("../invoice/entities/invoice-audit-log.entity");
const appointment_audit_log_entity_1 = require("../audit/entities/appointment-audit-log.entity");
const credit_transaction_entity_1 = require("../credits/entities/credit-transaction.entity");
const merged_invoice_document_entity_1 = require("../invoice/entities/merged-invoice-document.entity");
const clinic_deletion_audit_trail_entity_1 = require("./entities/clinic-deletion-audit-trail.entity");
let ClinicDeletionModule = class ClinicDeletionModule {
};
exports.ClinicDeletionModule = ClinicDeletionModule;
exports.ClinicDeletionModule = ClinicDeletionModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                // Core entities
                brand_entity_1.Brand,
                clinic_entity_1.ClinicEntity,
                patient_entity_1.Patient,
                patient_owner_entity_1.PatientOwner,
                appointment_entity_1.AppointmentEntity,
                appointment_details_entity_1.AppointmentDetailsEntity,
                appointment_doctor_entity_1.AppointmentDoctorsEntity,
                appointment_assessment_entity_1.AppointmentAssessmentEntity,
                user_entity_1.User,
                // Financial entities
                invoice_entity_1.InvoiceEntity,
                payment_details_entity_1.PaymentDetailsEntity,
                credit_transaction_entity_1.CreditTransactionEntity,
                merged_invoice_document_entity_1.MergedInvoiceDocumentEntity,
                // Medical records
                emr_entity_1.Emr,
                // Document entities
                document_library_entity_1.DocumentLibrary,
                patient_document_library_entity_1.PatientDocumentLibrary,
                // Patient estimate entities
                patient_estimate_entity_1.PatientEstimate,
                // Diagnostic entities
                diagnostic_template_entity_1.DiagnosticTemplate,
                diagnostic_note_entity_1.DiagnosticNote,
                // Audit log entities
                appointment_audit_log_entity_1.AppointmentAuditLog,
                invoice_audit_log_entity_1.InvoiceAuditLogEntity,
                clinic_deletion_audit_trail_entity_1.ClinicDeletionAuditTrail,
                // Owner entities
                owner_brand_entity_1.OwnerBrand,
                pet_transfer_history_entity_1.PetTransferHistory
            ]),
            role_module_1.RoleModule,
            s3_module_1.S3Module
        ],
        controllers: [clinic_deletion_controller_1.ClinicDeletionController],
        providers: [
            clinic_deletion_service_1.ClinicDeletionService,
            query_manager_service_1.QueryManagerService,
            startup_validation_service_1.StartupValidationService,
            database_backup_service_1.DatabaseBackupService,
            file_backup_service_1.FileBackupService,
            database_restore_service_1.DatabaseRestoreService,
            file_restore_service_1.FileRestoreService,
            winston_logger_service_1.WinstonLogger
        ],
        exports: [
            clinic_deletion_service_1.ClinicDeletionService,
            database_backup_service_1.DatabaseBackupService,
            file_backup_service_1.FileBackupService,
            database_restore_service_1.DatabaseRestoreService,
            file_restore_service_1.FileRestoreService
        ]
    })
], ClinicDeletionModule);
//# sourceMappingURL=clinic-deletion.module.js.map