"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const data_controller_1 = require("./data.controller");
const data_service_1 = require("./data.service");
const global_owner_entity_1 = require("../owners/entities/global-owner.entity");
const owner_brand_entity_1 = require("../owners/entities/owner-brand.entity");
const patient_entity_1 = require("../patients/entities/patient.entity");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const patient_owner_entity_1 = require("../patients/entities/patient-owner.entity");
const appointment_entity_1 = require("../appointments/entities/appointment.entity");
const appointment_doctor_entity_1 = require("../appointments/entities/appointment-doctor.entity");
const appointment_details_entity_1 = require("../appointments/entities/appointment-details.entity");
const invoice_entity_1 = require("../invoice/entities/invoice.entity");
const cart_entity_1 = require("../carts/entites/cart.entity");
const s3_service_1 = require("../utils/aws/s3/s3.service");
const payment_details_entity_1 = require("../payment-details/entities/payment-details.entity");
const clinic_lab_report_entity_1 = require("../clinic-lab-report/entities/clinic-lab-report.entity");
const lab_report_entity_1 = require("../clinic-lab-report/entities/lab-report.entity");
let DataModule = class DataModule {
};
exports.DataModule = DataModule;
exports.DataModule = DataModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                global_owner_entity_1.GlobalOwner,
                owner_brand_entity_1.OwnerBrand,
                patient_entity_1.Patient,
                patient_owner_entity_1.PatientOwner,
                appointment_entity_1.AppointmentEntity,
                appointment_doctor_entity_1.AppointmentDoctorsEntity,
                appointment_details_entity_1.AppointmentDetailsEntity,
                invoice_entity_1.InvoiceEntity,
                cart_entity_1.CartEntity,
                payment_details_entity_1.PaymentDetailsEntity,
                clinic_lab_report_entity_1.ClinicLabReport,
                lab_report_entity_1.LabReport
            ])
        ],
        controllers: [data_controller_1.DataController],
        providers: [data_service_1.DataService, winston_logger_service_1.WinstonLogger, s3_service_1.S3Service]
    })
], DataModule);
//# sourceMappingURL=data.module.js.map