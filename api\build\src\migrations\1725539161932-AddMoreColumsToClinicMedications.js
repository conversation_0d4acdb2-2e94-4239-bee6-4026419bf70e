"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddMoreColumsToClinicMedications1725539161932 = void 0;
const typeorm_1 = require("typeorm");
class AddMoreColumsToClinicMedications1725539161932 {
    async up(queryRunner) {
        await queryRunner.addColumns('clinic_medications', [
            new typeorm_1.TableColumn({
                name: 'brand_id',
                type: 'uuid',
                isNullable: false,
                default: `'1a9c7e74-9e8f-4a3e-a045-ea3f141e0fe0'`
            }),
            new typeorm_1.TableColumn({
                name: 'unique_id',
                type: 'varchar',
                isNullable: false,
                default: "''",
            }),
            new typeorm_1.TableColumn({
                name: 'minimum_dosage',
                type: 'integer',
                isNullable: false,
                default: 0
            }),
            new typeorm_1.TableColumn({
                name: 'maximum_dosage',
                type: 'integer',
                isNullable: true,
            }),
            new typeorm_1.TableColumn({
                name: 'purchase_price',
                type: 'decimal',
                scale: 2, // it specifies digits after the decimal point
                precision: 10, //  it specifies the total number of digits that can be stored, both before and after the decimal point
                isNullable: true,
            }),
            new typeorm_1.TableColumn({
                name: 'chargeable_price',
                type: 'decimal',
                scale: 2, // it specifies digits after the decimal point
                precision: 10, //  it specifies the total number of digits that can be stored, both before and after the decimal point
                isNullable: true,
            }),
            new typeorm_1.TableColumn({
                name: 'tax',
                type: 'decimal',
                scale: 2, // it specifies digits after the decimal point
                precision: 10, //  it specifies the total number of digits that can be stored, both before and after the decimal point
                isNullable: true,
            }),
            new typeorm_1.TableColumn({
                name: 'current_stock',
                type: 'integer',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'minimum_quantity',
                type: 'integer',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'reorder_value',
                type: 'integer',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'description',
                type: 'varchar',
                isNullable: false,
                default: "''",
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('clinic_medications', 'brand_id');
        await queryRunner.dropColumn('clinic_medications', 'unique_id');
        await queryRunner.dropColumn('clinic_medications', 'minimum_dosage');
        await queryRunner.dropColumn('clinic_medications', 'maximum_dosage');
        await queryRunner.dropColumn('clinic_medications', 'purchase_price');
        await queryRunner.dropColumn('clinic_medications', 'chargeable_price');
        await queryRunner.dropColumn('clinic_medications', 'tax');
        await queryRunner.dropColumn('clinic_medications', 'current_stock');
        await queryRunner.dropColumn('clinic_medications', 'minimum_quantity');
        await queryRunner.dropColumn('clinic_medications', 'reorder_value');
        await queryRunner.dropColumn('clinic_medications', 'description');
    }
}
exports.AddMoreColumsToClinicMedications1725539161932 = AddMoreColumsToClinicMedications1725539161932;
//# sourceMappingURL=1725539161932-AddMoreColumsToClinicMedications.js.map