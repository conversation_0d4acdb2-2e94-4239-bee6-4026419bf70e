import { Test, TestingModule } from '@nestjs/testing';
import { BrandService } from './brands.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Brand } from './entities/brand.entity';
import { CreateBrandDto } from './dto/create-brand.dto';
import {
	ConflictException,
	InternalServerErrorException
} from '@nestjs/common';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { User } from '../users/entities/user.entity';

describe('BrandService', () => {
	let service: BrandService;
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	let brandRepository: jest.Mocked<Repository<Brand>>;

	const mockBrandRepository = {
		findOne: jest.fn(),
		save: jest.fn(),
		find: jest.fn(),
		findAndCount: jest.fn(),
		create: jest.fn()
	};

	const mockLogger = {
		log: jest.fn(),
		error: jest.fn()
	};

	const mockBrand: Brand = {
		id: '1',
		name: 'Test Brand',
		createdAt: new Date(),
		updatedAt: new Date(),
		createdBy: '',
		createdByUser: new User(),
		updatedBy: '',
		updatedByUser: new User(),
		clinics: [],
		slug: '',
		generateSlug: function (): void {
			throw new Error('Function not implemented.');
		}
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				BrandService,
				{
					provide: getRepositoryToken(Brand),
					useValue: mockBrandRepository
				},
				{
					provide: WinstonLogger,
					useValue: mockLogger
				}
			]
		}).compile();

		service = module.get<BrandService>(BrandService);
		brandRepository = module.get(getRepositoryToken(Brand));
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('createBrand', () => {
		it('should create a new brand successfully', async () => {
			const createBrandDto: CreateBrandDto = { name: 'New Brand' };

			mockBrandRepository.findOne.mockResolvedValue(null); // No brand exists with the same name
			mockBrandRepository.create.mockReturnValue(mockBrand);
			mockBrandRepository.save.mockResolvedValue(mockBrand);

			const result = await service.createBrand(createBrandDto);

			expect(mockBrandRepository.findOne).toHaveBeenCalledWith({
				where: { name: createBrandDto.name }
			});
			expect(mockBrandRepository.create).toHaveBeenCalledWith(
				createBrandDto
			);
			expect(mockBrandRepository.save).toHaveBeenCalledWith(mockBrand);
			expect(result).toEqual(mockBrand);
		});

		it('should throw ConflictException if a brand with the same name already exists', async () => {
			const createBrandDto: CreateBrandDto = { name: 'Existing Brand' };

			mockBrandRepository.findOne.mockResolvedValue(mockBrand); // Brand already exists

			await expect(service.createBrand(createBrandDto)).rejects.toThrow(
				ConflictException
			);
			expect(mockBrandRepository.findOne).toHaveBeenCalledWith({
				where: { name: createBrandDto.name }
			});
			expect(mockBrandRepository.create).not.toHaveBeenCalled();
			expect(mockBrandRepository.save).not.toHaveBeenCalled();
		});

		it('should throw InternalServerErrorException if an unexpected error occurs', async () => {
			const createBrandDto: CreateBrandDto = { name: 'Error Brand' };

			mockBrandRepository.findOne.mockRejectedValue(
				new InternalServerErrorException()
			); // Simulating an unexpected error

			await expect(service.createBrand(createBrandDto)).rejects.toThrow(
				InternalServerErrorException
			);
			expect(mockBrandRepository.findOne).toHaveBeenCalledWith({
				where: { name: createBrandDto.name }
			});
			expect(mockBrandRepository.create).not.toHaveBeenCalled();
			expect(mockBrandRepository.save).not.toHaveBeenCalled();
		});
	});

	describe('getAllBrands', () => {
		it('should return paginated brands with default parameters', async () => {
			const mockResult = [[mockBrand], 1];
			mockBrandRepository.findAndCount.mockResolvedValue(mockResult);

			const result = await service.getAllBrands();

			expect(mockBrandRepository.findAndCount).toHaveBeenCalledWith({
				where: {},
				skip: 0,
				take: 10,
				order: { createdAt: 'DESC' }
			});
			expect(result).toEqual({ brands: [mockBrand], total: 1 });
		});

		it('should return paginated brands with custom parameters', async () => {
			const mockResult = [[mockBrand], 1];
			mockBrandRepository.findAndCount.mockResolvedValue(mockResult);

			const result = await service.getAllBrands(2, 5, 'test', 'ASC');

			expect(mockBrandRepository.findAndCount).toHaveBeenCalledWith({
				where: { name: expect.objectContaining({}) }, // Like operator
				skip: 5,
				take: 5,
				order: { createdAt: 'ASC' }
			});
			expect(result).toEqual({ brands: [mockBrand], total: 1 });
		});

		it('should throw InternalServerErrorException if an error occurs', async () => {
			mockBrandRepository.findAndCount.mockRejectedValue(
				new InternalServerErrorException()
			);

			await expect(service.getAllBrands()).rejects.toThrow(
				InternalServerErrorException
			);
			expect(mockBrandRepository.findAndCount).toHaveBeenCalled();
		});
	});

	describe('getAllBrandsSimple', () => {
		it('should return an array of brands without pagination', async () => {
			mockBrandRepository.find.mockResolvedValue([mockBrand]);

			const result = await service.getAllBrandsSimple();

			expect(mockBrandRepository.find).toHaveBeenCalledWith({
				order: { createdAt: 'DESC' }
			});
			expect(result).toEqual([mockBrand]);
		});

		it('should throw InternalServerErrorException if an error occurs', async () => {
			mockBrandRepository.find.mockRejectedValue(
				new InternalServerErrorException()
			);

			await expect(service.getAllBrandsSimple()).rejects.toThrow(
				InternalServerErrorException
			);
			expect(mockBrandRepository.find).toHaveBeenCalled();
		});
	});

	describe('getBrandById', () => {
		it('should return a brand by id', async () => {
			const id = '1';
			mockBrandRepository.findOne.mockResolvedValue(mockBrand);

			const result = await service.getBrandById(id);

			expect(mockBrandRepository.findOne).toHaveBeenCalledWith({
				where: { id }
			});
			expect(result).toEqual(mockBrand);
		});

		it('should return null if brand is not found', async () => {
			const id = '2';
			mockBrandRepository.findOne.mockResolvedValue(null);

			const result = await service.getBrandById(id);

			expect(mockBrandRepository.findOne).toHaveBeenCalledWith({
				where: { id }
			});
			expect(result).toBeNull();
		});

		it('should throw InternalServerErrorException if an error occurs', async () => {
			const id = '3';
			mockBrandRepository.findOne.mockRejectedValue(
				new InternalServerErrorException()
			);

			await expect(service.getBrandById(id)).rejects.toThrow(
				InternalServerErrorException
			);
			expect(mockBrandRepository.findOne).toHaveBeenCalledWith({
				where: { id }
			});
		});
	});
});
