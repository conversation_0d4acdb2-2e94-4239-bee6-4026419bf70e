"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientDocumentLibrariesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const patient_document_library_entity_1 = require("./entities/patient-document-library.entity");
const typeorm_2 = require("typeorm");
const winston_logger_service_1 = require("../utils/logger/winston-logger.service");
const user_entity_1 = require("../users/entities/user.entity");
const get_login_url_1 = require("../utils/common/get-login-url");
const send_mail_service_1 = require("../utils/aws/ses/send-mail-service");
const constants_1 = require("../utils/constants");
const whatsapp_template_generator_1 = require("../utils/communicatoins/whatsapp-template-generator");
const whatsapp_service_1 = require("../utils/whatsapp-integration/whatsapp.service");
const s3_service_1 = require("../utils/aws/s3/s3.service");
const axios_1 = require("axios");
const documentLibrary_1 = require("../utils/pdfs/documentLibrary");
const moment = require("moment");
const uuidv7_1 = require("uuidv7");
const generatePdf_1 = require("../utils/generatePdf");
const mail_template_generator_1 = require("../utils/mail-generator/mail-template-generator");
const invoice_service_1 = require("../invoice/invoice.service");
const template_helper_util_1 = require("../utils/common/template-helper.util");
let PatientDocumentLibrariesService = class PatientDocumentLibrariesService {
    constructor(logger, patientDocumentLibraryRepository, userRepository, mailService, whatsappService, s3Service) {
        this.logger = logger;
        this.patientDocumentLibraryRepository = patientDocumentLibraryRepository;
        this.userRepository = userRepository;
        this.mailService = mailService;
        this.whatsappService = whatsappService;
        this.s3Service = s3Service;
    }
    async sendMail(body, buffers, fileName, email, subject) {
        try {
            if ((0, get_login_url_1.isProductionOrUat)() && email) {
                this.mailService.sendMail({
                    body: body,
                    subject: subject !== null && subject !== void 0 ? subject : 'signable document attachments',
                    pdfBuffers: buffers,
                    pdfFileNames: fileName,
                    toMailAddress: email
                });
                this.logger.log('Production Mail Log', {
                    body: body,
                    subject: subject !== null && subject !== void 0 ? subject : 'signable document attachments',
                    pdfFileNames: fileName,
                    toMailAddress: constants_1.DEV_SES_EMAIL
                });
            }
            else if (!(0, get_login_url_1.isProduction)()) {
                this.mailService.sendMail({
                    body: body,
                    subject: subject !== null && subject !== void 0 ? subject : 'signable document attachments',
                    pdfBuffers: buffers,
                    pdfFileNames: fileName,
                    toMailAddress: constants_1.DEV_SES_EMAIL
                });
                this.logger.log('UAT Mail Log', {
                    body: body,
                    subject: subject !== null && subject !== void 0 ? subject : 'signable document attachments',
                    pdfFileNames: fileName,
                    toMailAddress: constants_1.DEV_SES_EMAIL
                });
            }
        }
        catch (error) {
            console.log('eroor => ', error);
            this.logger.log('Send Mail Error ', {
                error
            });
        }
    }
    async downloadPDF(url) {
        const response = await axios_1.default.get(url, { responseType: 'arraybuffer' });
        return Buffer.from(response.data);
    }
    async create(createPatientDocumentLibraryDto) {
        this.logger.log('Creating a new PatientDocumentLibrary', {
            action: 'create',
            data: createPatientDocumentLibraryDto
        });
        const userResponse = await this.userRepository.findOne({
            where: { id: createPatientDocumentLibraryDto.doctorId }
        });
        const doctorName = `${(userResponse === null || userResponse === void 0 ? void 0 : userResponse.firstName) || ''} ${(userResponse === null || userResponse === void 0 ? void 0 : userResponse.lastName) || ''}`;
        const patientDocumentLibrary = this.patientDocumentLibraryRepository.create({
            ...createPatientDocumentLibraryDto,
            doctorName
        });
        const response = await this.patientDocumentLibraryRepository.save(patientDocumentLibrary);
        if ((response === null || response === void 0 ? void 0 : response.documentType) === 'notSignable' && (response === null || response === void 0 ? void 0 : response.fileKey)) {
            await this.sendNoSignatureRequiredAttachments(response === null || response === void 0 ? void 0 : response.id, createPatientDocumentLibraryDto === null || createPatientDocumentLibraryDto === void 0 ? void 0 : createPatientDocumentLibraryDto.documentBody);
        }
        else if ((response === null || response === void 0 ? void 0 : response.documentType) === 'notSignable') {
            const fileKey = await this.sendNonSignableDocumentUrl(response === null || response === void 0 ? void 0 : response.id, createPatientDocumentLibraryDto === null || createPatientDocumentLibraryDto === void 0 ? void 0 : createPatientDocumentLibraryDto.documentBody);
            return { ...response, fileKey };
        }
        else if ((response === null || response === void 0 ? void 0 : response.documentType) === 'signable' &&
            (response === null || response === void 0 ? void 0 : response.signedRecieved) === 'pending') {
            await this.sendSignableDocumentUrl(response === null || response === void 0 ? void 0 : response.id, `https://${createPatientDocumentLibraryDto === null || createPatientDocumentLibraryDto === void 0 ? void 0 : createPatientDocumentLibraryDto.urlPath}/signedDoc/${response === null || response === void 0 ? void 0 : response.id}`);
        }
        return response;
    }
    async findOne(id) {
        this.logger.log(`Retrieving document library entry with ID: ${id}`);
        const documentLibrary = await this.patientDocumentLibraryRepository.findOne({
            where: { id },
            relations: [
                'document',
                'patient',
                'patient.clinic',
                'patient.clinic.brand'
            ]
        });
        if (!documentLibrary) {
            this.logger.error(` Patient document library entry with ID: ${id} not found`);
            throw new common_1.NotFoundException(`Patient document library entry with ID: ${id} not found`);
        }
        return documentLibrary;
    }
    async findAll(patientId, page = 1, limit = 10, search) {
        try {
            this.logger.log('Fetching PatientDocumentLibraries', {
                patientId,
                page,
                limit,
                search
            });
            const queryBuilder = this.patientDocumentLibraryRepository
                .createQueryBuilder('patientDocument')
                .leftJoinAndSelect('patientDocument.document', 'documentLibrary')
                .leftJoinAndSelect('patientDocument.doctor', 'user')
                .where('patientDocument.patientId = :patientId', { patientId });
            const updatedSearch = search === null || search === void 0 ? void 0 : search.trim();
            if (updatedSearch) {
                this.logger.log('Adding search filter', { updatedSearch });
                queryBuilder.andWhere(new typeorm_2.Brackets(qb => {
                    qb.where('user.firstName ILIKE :updatedSearch', {
                        updatedSearch: `%${updatedSearch}%`
                    }).orWhere('documentLibrary.documentName ILIKE :updatedSearch', {
                        updatedSearch: `%${updatedSearch}%`
                    });
                }));
            }
            queryBuilder
                .skip((page - 1) * limit)
                // .take(limit)
                .orderBy('patientDocument.createdAt', 'DESC');
            const [data, total] = await queryBuilder.getManyAndCount();
            this.logger.log('PatientDocumentLibraries fetched successfully', {
                count: data.length,
                page,
                limit
            });
            return {
                data,
                total,
                page,
                pageCount: Math.ceil(total / limit)
            };
        }
        catch (error) {
            console.log('🚀 ~ PatientDocumentLibrariesService ~ error:', error);
            this.logger.error('Error fetching PatientDocumentLibraries', {
                error
            });
            throw new common_1.InternalServerErrorException('Failed to fetch PatientDocumentLibraries');
        }
    }
    async sendSignedDocument(documentId, updateSignedDocumentDto) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, _16, _17;
        this.logger.log('start sending document for id', {
            documentId,
            updateSignedDocumentDto
        });
        const documentResponse = await this.patientDocumentLibraryRepository.findOne({
            where: { id: documentId },
            relations: [
                'document',
                'patient',
                'patient.patientOwners',
                'patient.patientOwners.ownerBrand',
                'patient.patientOwners.ownerBrand.globalOwner',
                'patient.clinic',
                'patient.clinic.brand'
            ]
        });
        if (documentResponse) {
            try {
                const fileKey = `patient-document-library/${(0, uuidv7_1.uuidv4)()}`;
                const documentHtml = (0, documentLibrary_1.generateDocumentLibrary)({
                    bodyText: ((_a = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.documentBody) === null || _a === void 0 ? void 0 : _a.bodyText) || '',
                    clinicAddress: `${((_c = (_b = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _b === void 0 ? void 0 : _b.clinic) === null || _c === void 0 ? void 0 : _c.addressLine1) || ''} ${((_e = (_d = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _d === void 0 ? void 0 : _d.clinic) === null || _e === void 0 ? void 0 : _e.addressLine2) || ''}, ${((_g = (_f = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _f === void 0 ? void 0 : _f.clinic) === null || _g === void 0 ? void 0 : _g.city) || ''} ${((_j = (_h = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _h === void 0 ? void 0 : _h.clinic) === null || _j === void 0 ? void 0 : _j.addressPincode) || ''} ${((_l = (_k = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _k === void 0 ? void 0 : _k.clinic) === null || _l === void 0 ? void 0 : _l.state) || ''} ${((_o = (_m = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _m === void 0 ? void 0 : _m.clinic) === null || _o === void 0 ? void 0 : _o.country) || ''}`,
                    clinicEmail: ((_q = (_p = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _p === void 0 ? void 0 : _p.clinic) === null || _q === void 0 ? void 0 : _q.email) || '',
                    clinicName: ((_s = (_r = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _r === void 0 ? void 0 : _r.clinic) === null || _s === void 0 ? void 0 : _s.name) || '',
                    clinicPhone: ((_v = (_u = (_t = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _t === void 0 ? void 0 : _t.clinic) === null || _u === void 0 ? void 0 : _u.phoneNumbers[0]) === null || _v === void 0 ? void 0 : _v.number) || '',
                    clinicWebsite: ((_x = (_w = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _w === void 0 ? void 0 : _w.clinic) === null || _x === void 0 ? void 0 : _x.website) || '',
                    digitalSignature: updateSignedDocumentDto === null || updateSignedDocumentDto === void 0 ? void 0 : updateSignedDocumentDto.signatureSvg,
                    docDate: moment(documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.createdAt).format('DD MM YYYY'),
                    title: ((_y = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.documentBody) === null || _y === void 0 ? void 0 : _y.title) || '',
                    vetName: `${updateSignedDocumentDto === null || updateSignedDocumentDto === void 0 ? void 0 : updateSignedDocumentDto.firstName} ${updateSignedDocumentDto === null || updateSignedDocumentDto === void 0 ? void 0 : updateSignedDocumentDto.lastName}`,
                    species: ((_z = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _z === void 0 ? void 0 : _z.species) || '',
                    breed: ((_1 = (_0 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _0 === void 0 ? void 0 : _0.breed) === null || _1 === void 0 ? void 0 : _1.split('_').join(' ').toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase())) || '',
                    color: (_2 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _2 === void 0 ? void 0 : _2.identification,
                    weight: ((_3 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _3 === void 0 ? void 0 : _3.weight) || '20 kg',
                    reproductiveStatus: ((_4 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _4 === void 0 ? void 0 : _4.reproductiveStatus) || '',
                    dob: ((_5 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _5 === void 0 ? void 0 : _5.age) || '',
                    age: (0, invoice_service_1.calculateAge)((_6 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _6 === void 0 ? void 0 : _6.age),
                    petName: ((_7 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _7 === void 0 ? void 0 : _7.patientName) || '',
                    ownerName: `${((_11 = (_10 = (_9 = (_8 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _8 === void 0 ? void 0 : _8.patientOwners) === null || _9 === void 0 ? void 0 : _9[0]) === null || _10 === void 0 ? void 0 : _10.ownerBrand) === null || _11 === void 0 ? void 0 : _11.firstName) || ''} ${((_15 = (_14 = (_13 = (_12 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _12 === void 0 ? void 0 : _12.patientOwners) === null || _13 === void 0 ? void 0 : _13[0]) === null || _14 === void 0 ? void 0 : _14.ownerBrand) === null || _15 === void 0 ? void 0 : _15.lastName) || ''}`
                });
                const pdfbuffer = await (0, generatePdf_1.generatePDF)(documentHtml);
                await this.s3Service.uploadPdfToS3(pdfbuffer, fileKey);
                const viewSignedUrl = await this.s3Service.getViewPreSignedUrl(fileKey);
                // Mail send for signed document
                // Whatsappp document
                const updateDocumentResponse = await this.patientDocumentLibraryRepository.update({ id: documentId }, {
                    documentSendStatus: 'completed',
                    signedBy: {
                        firstName: updateSignedDocumentDto.firstName,
                        lastName: updateSignedDocumentDto === null || updateSignedDocumentDto === void 0 ? void 0 : updateSignedDocumentDto.lastName
                    },
                    signedRecieved: 'completed',
                    fileKey
                });
                (_17 = (_16 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _16 === void 0 ? void 0 : _16.patientOwners) === null || _17 === void 0 ? void 0 : _17.map(async (patient) => {
                    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t;
                    const clientName = `${(_a = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _a === void 0 ? void 0 : _a.firstName} ${(_b = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _b === void 0 ? void 0 : _b.lastName}`;
                    const brandName = ((_e = (_d = (_c = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _c === void 0 ? void 0 : _c.clinic) === null || _d === void 0 ? void 0 : _d.brand) === null || _e === void 0 ? void 0 : _e.name) || '';
                    const clientEmail = ((_f = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _f === void 0 ? void 0 : _f.email) || '';
                    const clientMobileNumber = ((_h = (_g = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _g === void 0 ? void 0 : _g.globalOwner) === null || _h === void 0 ? void 0 : _h.phoneNumber) || '';
                    const patientName = ((_j = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _j === void 0 ? void 0 : _j.patientName) || '';
                    const clinicContactNo = ((_m = (_l = (_k = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _k === void 0 ? void 0 : _k.clinic) === null || _l === void 0 ? void 0 : _l.phoneNumbers[0]) === null || _m === void 0 ? void 0 : _m.number) || '';
                    if ((clientEmail === null || clientEmail === void 0 ? void 0 : clientEmail.length) && (0, get_login_url_1.isProductionOrUat)()) {
                        const { body, subject, email } = (0, mail_template_generator_1.nonSignableDocumentMailTemplate)({
                            clientName,
                            patientName,
                            contactNo: '',
                            brandName,
                            email: clientEmail,
                            documentName: ((_o = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.document) === null || _o === void 0 ? void 0 : _o.documentName) ||
                                '',
                            documentTitle: ((_p = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.document) === null || _p === void 0 ? void 0 : _p.documentName) ||
                                ''
                        });
                        await this.sendMail(body, [pdfbuffer], [`${(_q = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.document) === null || _q === void 0 ? void 0 : _q.documentName}.pdf`], email, `Your signed copy for ${(_r = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.document) === null || _r === void 0 ? void 0 : _r.documentName} is here`);
                        this.logger.log('email successfully send', documentId);
                    }
                    else {
                        this.logger.log('email is not present for user');
                    }
                    if (clientMobileNumber.length && (0, get_login_url_1.isProductionOrUat)()) {
                        // Generate template data
                        const templateArgs = {
                            clientName,
                            brandName,
                            contactNo: clinicContactNo,
                            mobileNumber: clientMobileNumber,
                            patientName,
                            documentName: ((_s = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.document) === null || _s === void 0 ? void 0 : _s.documentName) || '',
                            documentUrl: viewSignedUrl
                        };
                        const { mobileNumber, templateName, valuesArray } = (0, template_helper_util_1.selectTemplate)((_t = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _t === void 0 ? void 0 : _t.clinic, templateArgs, whatsapp_template_generator_1.sendNonSignableDocument, whatsapp_template_generator_1.sendNonSignableDocumentClinicLink);
                        const response = await this.whatsappService.sendTemplateMessage({
                            templateName,
                            valuesArray,
                            mobileNumber
                        });
                        this.logger.log('message on whatsapp successfully send', documentId);
                    }
                    else {
                        this.logger.log('mobile number is not present for user');
                    }
                });
                this.logger.log('successfully update the data', {
                    id: documentId,
                    documentSendStatus: 'completed',
                    signedBy: {
                        firstName: updateSignedDocumentDto.firstName,
                        lastName: updateSignedDocumentDto === null || updateSignedDocumentDto === void 0 ? void 0 : updateSignedDocumentDto.lastName
                    },
                    signedRecieved: 'completed',
                    fileKey
                });
                return updateDocumentResponse;
            }
            catch (error) {
                this.logger.log('🚀 ~ PatientDocumentLibrariesService ~ error:', error);
            }
        }
        return null;
    }
    async sendSignableDocumentUrl(documentId, url) {
        var _a, _b;
        const documentResponse = await this.patientDocumentLibraryRepository.findOne({
            where: { id: documentId },
            relations: [
                'document',
                'patient',
                'patient.patientOwners',
                'patient.patientOwners.ownerBrand',
                'patient.patientOwners.ownerBrand.globalOwner',
                'patient.clinic',
                'patient.clinic.brand'
            ]
        });
        if (documentResponse) {
            const updateDocumentResponse = await this.patientDocumentLibraryRepository.update({ id: documentId }, { documentSendStatus: 'completed' });
            (_b = (_a = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _a === void 0 ? void 0 : _a.patientOwners) === null || _b === void 0 ? void 0 : _b.map(async (patient) => {
                var _a, _b, _c, _d, _e, _f, _g, _h, _j;
                const clientName = `${(_a = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _a === void 0 ? void 0 : _a.firstName} ${(_b = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _b === void 0 ? void 0 : _b.lastName}`;
                const brandName = ((_e = (_d = (_c = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _c === void 0 ? void 0 : _c.clinic) === null || _d === void 0 ? void 0 : _d.brand) === null || _e === void 0 ? void 0 : _e.name) || '';
                const clientEmail = ((_f = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _f === void 0 ? void 0 : _f.email) || '';
                const clientMobileNumber = ((_h = (_g = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _g === void 0 ? void 0 : _g.globalOwner) === null || _h === void 0 ? void 0 : _h.phoneNumber) || '';
                const patientName = ((_j = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _j === void 0 ? void 0 : _j.patientName) || '';
                if ((clientEmail === null || clientEmail === void 0 ? void 0 : clientEmail.length) && (0, get_login_url_1.isProductionOrUat)()) {
                    const { body, subject, email } = (0, mail_template_generator_1.signableDocumentMailTemplate)({
                        clientName,
                        brandName,
                        url,
                        email: clientEmail,
                        patientName
                    });
                    await this.sendMail(body, [], [], email, subject);
                    this.logger.log('email successfully send', documentId);
                }
                else {
                    this.logger.log('email is not present for user');
                }
                if (clientMobileNumber.length && (0, get_login_url_1.isProductionOrUat)()) {
                    const { templateName, mobileNumber, valuesArray } = (0, whatsapp_template_generator_1.sendSignableDocumentUrl)({
                        clientName,
                        brandName,
                        url,
                        mobileNumber: clientMobileNumber,
                        patientName
                    });
                    const response = await this.whatsappService.sendTemplateMessage({
                        templateName,
                        valuesArray,
                        mobileNumber
                    });
                    this.logger.log('message on whatsapp successfully send', documentId);
                }
                else {
                    this.logger.log('mobile number is not present for user');
                }
            });
        }
        else {
            this.logger.log('document not found');
        }
    }
    async sendNonSignableDocumentUrl(documentId, documentBody) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z, _0, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15;
        const documentResponse = await this.patientDocumentLibraryRepository.findOne({
            where: { id: documentId },
            relations: [
                'document',
                'patient',
                'patient.patientOwners',
                'patient.patientOwners.ownerBrand',
                'patient.patientOwners.ownerBrand.globalOwner',
                'patient.clinic',
                'patient.clinic.brand'
            ]
        });
        if (documentResponse) {
            const fileKey = `patient-document-library/${(0, uuidv7_1.uuidv4)()}`;
            const documentHtml = (0, documentLibrary_1.generateDocumentLibrary)({
                bodyText: (documentBody === null || documentBody === void 0 ? void 0 : documentBody.bodyText) || '',
                clinicAddress: `${((_b = (_a = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _a === void 0 ? void 0 : _a.clinic) === null || _b === void 0 ? void 0 : _b.addressLine1) || ''} ${((_d = (_c = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _c === void 0 ? void 0 : _c.clinic) === null || _d === void 0 ? void 0 : _d.addressLine2) || ''}, ${((_f = (_e = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _e === void 0 ? void 0 : _e.clinic) === null || _f === void 0 ? void 0 : _f.city) || ''} ${((_h = (_g = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _g === void 0 ? void 0 : _g.clinic) === null || _h === void 0 ? void 0 : _h.addressPincode) || ''} ${((_k = (_j = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _j === void 0 ? void 0 : _j.clinic) === null || _k === void 0 ? void 0 : _k.state) || ''} ${((_m = (_l = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _l === void 0 ? void 0 : _l.clinic) === null || _m === void 0 ? void 0 : _m.country) || ''}`,
                clinicEmail: ((_p = (_o = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _o === void 0 ? void 0 : _o.clinic) === null || _p === void 0 ? void 0 : _p.email) || '',
                clinicName: ((_r = (_q = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _q === void 0 ? void 0 : _q.clinic) === null || _r === void 0 ? void 0 : _r.name) || '',
                clinicPhone: ((_u = (_t = (_s = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _s === void 0 ? void 0 : _s.clinic) === null || _t === void 0 ? void 0 : _t.phoneNumbers[0]) === null || _u === void 0 ? void 0 : _u.number) || '',
                clinicWebsite: ((_w = (_v = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _v === void 0 ? void 0 : _v.clinic) === null || _w === void 0 ? void 0 : _w.website) || '',
                digitalSignature: '',
                docDate: '',
                title: (documentBody === null || documentBody === void 0 ? void 0 : documentBody.title) || '',
                vetName: ``,
                species: ((_x = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _x === void 0 ? void 0 : _x.species) || '',
                breed: ((_z = (_y = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _y === void 0 ? void 0 : _y.breed) === null || _z === void 0 ? void 0 : _z.split('_').join(' ').toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase())) || '',
                color: (_0 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _0 === void 0 ? void 0 : _0.identification,
                weight: ((_1 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _1 === void 0 ? void 0 : _1.weight) || '20 kg',
                reproductiveStatus: ((_2 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _2 === void 0 ? void 0 : _2.reproductiveStatus) || '',
                dob: ((_3 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _3 === void 0 ? void 0 : _3.age) || '',
                age: (0, invoice_service_1.calculateAge)((_4 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _4 === void 0 ? void 0 : _4.age),
                petName: ((_5 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _5 === void 0 ? void 0 : _5.patientName) || '',
                ownerName: `${((_9 = (_8 = (_7 = (_6 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _6 === void 0 ? void 0 : _6.patientOwners) === null || _7 === void 0 ? void 0 : _7[0]) === null || _8 === void 0 ? void 0 : _8.ownerBrand) === null || _9 === void 0 ? void 0 : _9.firstName) || ''} ${((_13 = (_12 = (_11 = (_10 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _10 === void 0 ? void 0 : _10.patientOwners) === null || _11 === void 0 ? void 0 : _11[0]) === null || _12 === void 0 ? void 0 : _12.ownerBrand) === null || _13 === void 0 ? void 0 : _13.lastName) || ''}`
            });
            const pdfbuffer = await (0, generatePdf_1.generatePDF)(documentHtml);
            await this.s3Service.uploadPdfToS3(pdfbuffer, fileKey);
            const viewSignedUrl = await this.s3Service.getViewPreSignedUrl(fileKey);
            const updateDocumentResponse = await this.patientDocumentLibraryRepository.update({ id: documentId }, {
                documentSendStatus: 'completed',
                signedRecieved: 'pending',
                fileKey
            });
            (_15 = (_14 = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _14 === void 0 ? void 0 : _14.patientOwners) === null || _15 === void 0 ? void 0 : _15.map(async (patient) => {
                var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
                const clientName = `${(_a = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _a === void 0 ? void 0 : _a.firstName} ${(_b = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _b === void 0 ? void 0 : _b.lastName}`;
                const brandName = ((_e = (_d = (_c = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _c === void 0 ? void 0 : _c.clinic) === null || _d === void 0 ? void 0 : _d.brand) === null || _e === void 0 ? void 0 : _e.name) || '';
                const clientEmail = ((_f = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _f === void 0 ? void 0 : _f.email) || '';
                const clientMobileNumber = ((_h = (_g = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _g === void 0 ? void 0 : _g.globalOwner) === null || _h === void 0 ? void 0 : _h.phoneNumber) || '';
                const patientName = ((_j = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _j === void 0 ? void 0 : _j.patientName) || '';
                const clinicContactNo = ((_m = (_l = (_k = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _k === void 0 ? void 0 : _k.clinic) === null || _l === void 0 ? void 0 : _l.phoneNumbers[0]) === null || _m === void 0 ? void 0 : _m.number) || '';
                if ((clientEmail === null || clientEmail === void 0 ? void 0 : clientEmail.length) && (0, get_login_url_1.isProductionOrUat)()) {
                    const { body, subject, email } = (0, mail_template_generator_1.nonSignableDocumentMailTemplate)({
                        clientName,
                        patientName,
                        contactNo: '',
                        brandName,
                        email: clientEmail,
                        documentName: ((_o = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.document) === null || _o === void 0 ? void 0 : _o.documentName) || '',
                        documentTitle: ((_p = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.document) === null || _p === void 0 ? void 0 : _p.documentName) || ''
                    });
                    await this.sendMail(body, [pdfbuffer], [`${(_q = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.document) === null || _q === void 0 ? void 0 : _q.documentName}.pdf`], email, subject);
                    this.logger.log('email successfully send', documentId);
                }
                else {
                    this.logger.log('email is not present for user');
                }
                if (clientMobileNumber.length && (0, get_login_url_1.isProductionOrUat)()) {
                    const templateArgs = {
                        clientName,
                        brandName,
                        contactNo: clinicContactNo,
                        mobileNumber: clientMobileNumber,
                        patientName,
                        documentName: ((_r = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.document) === null || _r === void 0 ? void 0 : _r.documentName) || '',
                        documentUrl: viewSignedUrl
                    };
                    const { mobileNumber, templateName, valuesArray } = (0, template_helper_util_1.selectTemplate)((_s = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _s === void 0 ? void 0 : _s.clinic, templateArgs, whatsapp_template_generator_1.sendNonSignableDocument, whatsapp_template_generator_1.sendNonSignableDocumentClinicLink);
                    const response = await this.whatsappService.sendTemplateMessage({
                        templateName,
                        valuesArray,
                        mobileNumber
                    });
                    this.logger.log('message on whatsapp successfully send', documentId);
                }
                else {
                    this.logger.log('mobile number is not present for user');
                }
            });
            return fileKey;
        }
        else {
            this.logger.log('document not found');
        }
    }
    async sendNoSignatureRequiredAttachments(documentId, documentBody) {
        var _a, _b;
        const documentResponse = await this.patientDocumentLibraryRepository.findOne({
            where: { id: documentId },
            relations: [
                'document',
                'patient',
                'patient.patientOwners',
                'patient.patientOwners.ownerBrand',
                'patient.patientOwners.ownerBrand.globalOwner',
                'patient.clinic',
                'patient.clinic.brand'
            ]
        });
        if (documentResponse) {
            const viewSignedUrl = await this.s3Service.getViewPreSignedUrl(documentResponse.fileKey);
            const pdfBuffer = await this.downloadPDF(viewSignedUrl);
            (_b = (_a = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _a === void 0 ? void 0 : _a.patientOwners) === null || _b === void 0 ? void 0 : _b.map(async (patient) => {
                var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
                const clientName = `${(_a = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _a === void 0 ? void 0 : _a.firstName} ${(_b = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _b === void 0 ? void 0 : _b.lastName}`;
                const brandName = ((_e = (_d = (_c = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _c === void 0 ? void 0 : _c.clinic) === null || _d === void 0 ? void 0 : _d.brand) === null || _e === void 0 ? void 0 : _e.name) || '';
                const clientEmail = ((_f = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _f === void 0 ? void 0 : _f.email) || '';
                const clientMobileNumber = ((_h = (_g = patient === null || patient === void 0 ? void 0 : patient.ownerBrand) === null || _g === void 0 ? void 0 : _g.globalOwner) === null || _h === void 0 ? void 0 : _h.phoneNumber) || '';
                const patientName = ((_j = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _j === void 0 ? void 0 : _j.patientName) || '';
                const clinicContactNo = ((_m = (_l = (_k = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _k === void 0 ? void 0 : _k.clinic) === null || _l === void 0 ? void 0 : _l.phoneNumbers[0]) === null || _m === void 0 ? void 0 : _m.number) || '';
                if ((clientEmail === null || clientEmail === void 0 ? void 0 : clientEmail.length) && (0, get_login_url_1.isProductionOrUat)()) {
                    const { body, subject, email } = (0, mail_template_generator_1.nonSignableDocumentMailTemplate)({
                        clientName,
                        patientName,
                        contactNo: '',
                        brandName,
                        email: clientEmail,
                        documentName: ((_o = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.document) === null || _o === void 0 ? void 0 : _o.documentName) || '',
                        documentTitle: ((_p = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.document) === null || _p === void 0 ? void 0 : _p.documentName) || ''
                    });
                    await this.sendMail(body, [pdfBuffer], [`${(_q = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.document) === null || _q === void 0 ? void 0 : _q.documentName}.pdf`], email, subject);
                    this.logger.log('email successfully send', documentId);
                }
                else {
                    this.logger.log('email is not present for user');
                }
                if (clientMobileNumber.length && (0, get_login_url_1.isProductionOrUat)()) {
                    const templateArgs = {
                        clientName,
                        brandName,
                        contactNo: clinicContactNo,
                        mobileNumber: clientMobileNumber,
                        patientName,
                        documentName: ((_r = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.document) === null || _r === void 0 ? void 0 : _r.documentName) || '',
                        documentUrl: viewSignedUrl
                    };
                    const { mobileNumber, templateName, valuesArray } = (0, template_helper_util_1.selectTemplate)((_s = documentResponse === null || documentResponse === void 0 ? void 0 : documentResponse.patient) === null || _s === void 0 ? void 0 : _s.clinic, templateArgs, whatsapp_template_generator_1.sendNonSignableDocument, whatsapp_template_generator_1.sendNonSignableDocumentClinicLink);
                    const response = await this.whatsappService.sendTemplateMessage({
                        templateName,
                        valuesArray,
                        mobileNumber
                    });
                    this.logger.log('message on whatsapp successfully send', documentId);
                }
                else {
                    this.logger.log('mobile number is not present for user');
                }
            });
        }
        else {
            this.logger.log('document not found');
        }
    }
};
exports.PatientDocumentLibrariesService = PatientDocumentLibrariesService;
exports.PatientDocumentLibrariesService = PatientDocumentLibrariesService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(patient_document_library_entity_1.PatientDocumentLibrary)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        typeorm_2.Repository,
        typeorm_2.Repository,
        send_mail_service_1.SESMailService,
        whatsapp_service_1.WhatsappService,
        s3_service_1.S3Service])
], PatientDocumentLibrariesService);
//# sourceMappingURL=patient-document-libraries.service.js.map