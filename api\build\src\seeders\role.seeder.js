"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.roleSeeder = void 0;
const role_entity_1 = require("../roles/entities/role.entity");
const data_source_1 = require("../database/data-source");
console.log('Seed running');
const roleSeeder = async () => {
    await data_source_1.dataSource.initialize();
    const roleRepository = data_source_1.dataSource.getRepository(role_entity_1.Role);
    const roles = [
        {
            id: '79078ffb-9cad-4fdd-9c44-6842dd0a7773',
            name: 'super_admin',
            description: 'Super Administrator'
        },
        {
            id: '3623c5c1-7621-473e-a2b8-667df02200e3',
            name: 'admin',
            description: 'Administrator'
        },
        {
            id: 'c7bfe395-83b1-4a4f-9b81-6d8a863ea590',
            name: 'receptionist',
            description: 'Receptionist'
        },
        {
            id: 'ea907601-9a89-4551-94a6-8e58bbeeb0c4',
            name: 'doctor',
            description: 'Doctor'
        },
        {
            id: '1de2dd42-e691-4f46-a3ec-6f60f36c317b',
            name: 'vet_technician',
            description: 'Veterinary Technician'
        },
        {
            id: '2d86603f-ab59-4c71-b0f0-15ecff035c63',
            name: 'lab_technician',
            description: 'Laboratory Technician'
        }
    ];
    await roleRepository.save(roles);
    console.log('Role seeding completed');
};
exports.roleSeeder = roleSeeder;
(0, exports.roleSeeder)().catch(error => {
    console.error('Error executing seed script:', error);
});
//# sourceMappingURL=role.seeder.js.map