import { OwnersService } from '../owners/owners.service';
import { AppointmentsService } from '../appointments/appointments.service';
import { PatientsService } from '../patients/patients.service';
import { ClientDashboardResponseDto } from './dto/client-dashboard-response.dto';
import { ClientAppointmentsResponseDto } from './dto/client-appointments-response.dto';
import { WinstonLogger } from '../utils/logger/winston-logger.service';
import { DataSource } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { BrandService } from '../brands/brands.service';
import { DirectLoginDto } from './dto/direct-login.dto';
import { ClinicEntity } from '../clinics/entities/clinic.entity';
import { Repository } from 'typeorm';
import { ClinicService } from '../clinics/clinic.service';
import { SessionService } from '../session/session.service';
export declare class ClientDashboardService {
    private readonly ownersService;
    private readonly appointmentsService;
    private readonly patientsService;
    private readonly logger;
    private readonly dataSource;
    private readonly jwtService;
    private readonly sessionService;
    private readonly brandService;
    private readonly clinicRepository;
    private readonly clinicService;
    constructor(ownersService: OwnersService, appointmentsService: AppointmentsService, patientsService: PatientsService, logger: WinstonLogger, dataSource: DataSource, jwtService: JwtService, sessionService: SessionService, brandService: BrandService, clinicRepository: Repository<ClinicEntity>, clinicService: ClinicService);
    /**
     * Direct login for pet owners in the booking portal
     * @param directLoginDto The login data (phone number, country code, brand ID)
     * @returns Object with JWT token and owner information
     */
    directLogin(directLoginDto: DirectLoginDto): Promise<{
        token: string;
        owner: any;
    }>;
    /**
     * Get client dashboard information including owner details and pets
     * @param ownerId The owner brand ID
     * @param brandId The brand ID
     * @returns ClientDashboardResponseDto
     */
    getClientDashboard(ownerId: string, brandId: string): Promise<ClientDashboardResponseDto>;
    /**
     * Get all appointments for a client's pets
     * @param ownerId The owner brand ID
     * @param brandId The brand ID
     * @param filters Optional filters for date, status, etc.
     * @returns ClientAppointmentsResponseDto
     */
    getClientAppointments(ownerId: string, brandId: string, filters?: {
        date?: string;
        status?: string[];
    }): Promise<ClientAppointmentsResponseDto>;
    /**
     * Map appointment status from database enum to frontend enum
     */
    private mapAppointmentStatus;
    /**
     * Get the list of clinics for the client dashboard
     * @param brandId Brand ID to filter clinics
     * @returns Array of clinic objects with id and name that have client booking enabled
     */
    getClientClinics(brandId: string): Promise<any[]>;
    /**
     * Get the list of doctors/providers for the client dashboard, potentially filtered by clinic's allowed list.
     * @param brandId Brand ID to filter doctors
     * @param clinicId Optional clinic ID to filter doctors by clinic and its booking settings
     * @returns Array of doctor objects with id and name
     */
    getClientDoctors(brandId: string, clinicId?: string): Promise<any[]>;
}
