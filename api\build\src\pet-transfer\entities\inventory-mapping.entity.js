"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InventoryMapping = exports.InventoryItemType = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const typeorm_1 = require("typeorm");
var InventoryItemType;
(function (InventoryItemType) {
    InventoryItemType["SERVICE"] = "service";
    InventoryItemType["PRODUCT"] = "product";
    InventoryItemType["MEDICATION"] = "medication";
    InventoryItemType["CONSUMABLE"] = "consumable";
    InventoryItemType["VACCINATION"] = "vaccination";
    InventoryItemType["LAB_REPORT"] = "lab_report";
    InventoryItemType["APPOINTMENT_ASSESSMENT"] = "appointment_assessment";
})(InventoryItemType || (exports.InventoryItemType = InventoryItemType = {}));
let InventoryMapping = class InventoryMapping {
};
exports.InventoryMapping = InventoryMapping;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    (0, swagger_1.ApiProperty)({
        description: 'Primary key',
        example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef'
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], InventoryMapping.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'source_item_id' }),
    (0, swagger_1.ApiProperty)({ description: 'ID of the item in the source clinic' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], InventoryMapping.prototype, "sourceItemId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'source_item_type', type: 'enum', enum: InventoryItemType }),
    (0, swagger_1.ApiProperty)({
        description: 'Type of the source item',
        enum: InventoryItemType
    }),
    (0, class_validator_1.IsEnum)(InventoryItemType),
    __metadata("design:type", String)
], InventoryMapping.prototype, "sourceItemType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'destination_item_id' }),
    (0, swagger_1.ApiProperty)({ description: 'ID of the item in the destination clinic' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], InventoryMapping.prototype, "destinationItemId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'destination_item_type',
        type: 'enum',
        enum: InventoryItemType
    }),
    (0, swagger_1.ApiProperty)({
        description: 'Type of the destination item',
        enum: InventoryItemType
    }),
    (0, class_validator_1.IsEnum)(InventoryItemType),
    __metadata("design:type", String)
], InventoryMapping.prototype, "destinationItemType", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'source_clinic_id' }),
    (0, swagger_1.ApiProperty)({ description: 'ID of the source clinic' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], InventoryMapping.prototype, "sourceClinicId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'destination_clinic_id' }),
    (0, swagger_1.ApiProperty)({ description: 'ID of the destination clinic' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], InventoryMapping.prototype, "destinationClinicId", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_at' }),
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], InventoryMapping.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_at' }),
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Date)
], InventoryMapping.prototype, "updatedAt", void 0);
exports.InventoryMapping = InventoryMapping = __decorate([
    (0, typeorm_1.Entity)('inventory_mappings')
], InventoryMapping);
//# sourceMappingURL=inventory-mapping.entity.js.map