"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChangeWeightColumnToDecimal1729271231464 = void 0;
const typeorm_1 = require("typeorm");
class ChangeWeightColumnToDecimal1729271231464 {
    async up(queryRunner) {
        // Step 1: Add a temporary column with the desired type
        await queryRunner.addColumn('appointments', new typeorm_1.TableColumn({
            name: 'weight_temp',
            type: 'decimal',
            precision: 7, // Adjust based on your required precision
            scale: 2, // Adjust based on your required scale
            isNullable: true
        }));
        // Step 2: Copy data from the old column to the new column, converting as needed
        await queryRunner.query(`
			UPDATE appointments 
			SET weight_temp = weight::decimal
		`);
        // Step 3: Drop the old column
        await queryRunner.dropColumn('appointments', 'weight');
        // Step 4: Rename the new column to the old column name
        await queryRunner.renameColumn('appointments', 'weight_temp', 'weight');
    }
    async down(queryRunner) {
        // Reverse the process if you need to rollback
        await queryRunner.addColumn('appointments', new typeorm_1.TableColumn({
            name: 'weight_temp',
            type: 'integer',
            isNullable: true
        }));
        await queryRunner.query(`
			UPDATE appointments 
			SET weight_temp = weight::integer
		`);
        await queryRunner.dropColumn('appointments', 'weight');
        await queryRunner.renameColumn('appointments', 'weight_temp', 'weight');
    }
}
exports.ChangeWeightColumnToDecimal1729271231464 = ChangeWeightColumnToDecimal1729271231464;
//# sourceMappingURL=1729271231464-change-weight-column-to-float.js.map