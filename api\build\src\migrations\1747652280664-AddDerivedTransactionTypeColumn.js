"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddDerivedTransactionTypeColumn1747652280664 = void 0;
const typeorm_1 = require("typeorm");
const enum_derived_credit_transaction_type_1 = require("../credits/enums/enum-derived-credit-transaction-type");
const enum_credit_transaction_type_1 = require("../credits/enums/enum-credit-transaction-type");
const enum_credit_types_1 = require("../payment-details/enums/enum-credit-types");
class AddDerivedTransactionTypeColumn1747652280664 {
    async up(queryRunner) {
        // First, check if the column already exists
        const table = await queryRunner.getTable('credit_transactions');
        const derivedTypeColumn = table === null || table === void 0 ? void 0 : table.findColumnByName('derived_transaction_type');
        if (!derivedTypeColumn) {
            // Add the derived_transaction_type column as an enum
            await queryRunner.addColumn('credit_transactions', new typeorm_1.TableColumn({
                name: 'derived_transaction_type',
                type: 'enum',
                enum: [
                    enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_RETURNED,
                    enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_USED,
                    enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.EXCESS_PAYMENT,
                    enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_ADDED,
                    enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.UNKNOWN
                ],
                isNullable: true,
                default: `'${enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.UNKNOWN}'`
            }));
            // Now populate the column based on the existing data and logic
            // 1. CREDITS_RETURNED: USE transaction with Return payment type
            await queryRunner.query(`
                UPDATE credit_transactions ct
                SET derived_transaction_type = '${enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_RETURNED}'
                FROM payment_details pd
                WHERE ct.payment_detail_id = pd.id
                AND ct.transaction_type = '${enum_credit_transaction_type_1.CreditTransactionType.USE}'
                AND pd.type = '${enum_credit_types_1.EnumAmountType.Return}'
            `);
            // 2. CREDITS_USED: USE transaction with ReconcileInvoice or BulkReconcileInvoice payment type
            await queryRunner.query(`
                UPDATE credit_transactions ct
                SET derived_transaction_type = '${enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_USED}'
                FROM payment_details pd
                WHERE ct.payment_detail_id = pd.id
                AND ct.transaction_type = '${enum_credit_transaction_type_1.CreditTransactionType.USE}'
                AND (pd.type = '${enum_credit_types_1.EnumAmountType.ReconcileInvoice}' OR pd.type = '${enum_credit_types_1.EnumAmountType.BulkReconcileInvoice}')
            `);
            // 3. EXCESS_PAYMENT: ADD transaction with Collect payment type, zero amount payable, and credit added
            await queryRunner.query(`
                UPDATE credit_transactions ct
                SET derived_transaction_type = '${enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.EXCESS_PAYMENT}'
                FROM payment_details pd
                WHERE ct.payment_detail_id = pd.id
                AND ct.transaction_type = '${enum_credit_transaction_type_1.CreditTransactionType.ADD}'
                AND pd.type = '${enum_credit_types_1.EnumAmountType.Collect}'
                AND pd.amount_payable = 0
                AND (pd.credit_amount_added > 0 OR ct.amount > 0)
            `);
            // 4. CREDITS_ADDED - Scenario A (Direct Purchase/Grant): ADD transaction with Collect payment type and amount payable > 0
            await queryRunner.query(`
                UPDATE credit_transactions ct
                SET derived_transaction_type = '${enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_ADDED}'
                FROM payment_details pd
                WHERE ct.payment_detail_id = pd.id
                AND ct.transaction_type = '${enum_credit_transaction_type_1.CreditTransactionType.ADD}'
                AND pd.type = '${enum_credit_types_1.EnumAmountType.Collect}'
                AND pd.amount_payable > 0
            `);
            // 5. CREDITS_ADDED - Scenario B (Refund to Credits): ADD transaction with Return payment type
            await queryRunner.query(`
                UPDATE credit_transactions ct
                SET derived_transaction_type = '${enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_ADDED}'
                FROM payment_details pd
                WHERE ct.payment_detail_id = pd.id
                AND ct.transaction_type = '${enum_credit_transaction_type_1.CreditTransactionType.ADD}'
                AND pd.type = '${enum_credit_types_1.EnumAmountType.Return}'
            `);
            // 6. Special case: "Credit added from previous positive balance"
            await queryRunner.query(`
                UPDATE credit_transactions ct
                SET derived_transaction_type = '${enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_ADDED}'
                WHERE ct.transaction_type = '${enum_credit_transaction_type_1.CreditTransactionType.ADD}'
                AND ct.description = 'Credit added from previous positive balance'
            `);
            // 7. Default case for USE transactions without payment details
            await queryRunner.query(`
                UPDATE credit_transactions ct
                SET derived_transaction_type = '${enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_USED}'
                WHERE ct.transaction_type = '${enum_credit_transaction_type_1.CreditTransactionType.USE}'
                AND ct.payment_detail_id IS NULL
                AND ct.derived_transaction_type IS NULL
            `);
            // 8. Default case for ADD transactions without payment details
            await queryRunner.query(`
                UPDATE credit_transactions ct
                SET derived_transaction_type = '${enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.CREDITS_ADDED}'
                WHERE ct.transaction_type = '${enum_credit_transaction_type_1.CreditTransactionType.ADD}'
                AND ct.payment_detail_id IS NULL
                AND ct.derived_transaction_type IS NULL
            `);
            // 9. Set any remaining NULL values to UNKNOWN
            await queryRunner.query(`
                UPDATE credit_transactions
                SET derived_transaction_type = '${enum_derived_credit_transaction_type_1.DerivedCreditTransactionType.UNKNOWN}'
                WHERE derived_transaction_type IS NULL
            `);
        }
    }
    async down(queryRunner) {
        // Check if the column exists before trying to drop it
        const table = await queryRunner.getTable('credit_transactions');
        const derivedTypeColumn = table === null || table === void 0 ? void 0 : table.findColumnByName('derived_transaction_type');
        if (derivedTypeColumn) {
            await queryRunner.dropColumn('credit_transactions', 'derived_transaction_type');
        }
    }
}
exports.AddDerivedTransactionTypeColumn1747652280664 = AddDerivedTransactionTypeColumn1747652280664;
//# sourceMappingURL=1747652280664-AddDerivedTransactionTypeColumn.js.map