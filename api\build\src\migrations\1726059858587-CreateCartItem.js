"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCartItem1726059858587 = void 0;
const typeorm_1 = require("typeorm");
class CreateCartItem1726059858587 {
    constructor() {
        this.name = 'CreateCartItem1726059858587';
    }
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'cart_items',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'cart_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'appointment_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'product_id',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'service_id',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'vaccination_id',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'prescription_id',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'labreport_id',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'type',
                    type: 'varchar',
                    isNullable: true
                },
                {
                    name: 'is_added_to_cart',
                    type: 'boolean',
                    isNullable: false,
                    default: true
                },
                {
                    name: 'quantity',
                    type: 'integer',
                    isNullable: false,
                    default: 1
                },
                {
                    name: 'comment',
                    type: 'varchar',
                    isNullable: true,
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP',
                    isNullable: false
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                },
            ]
        }), true);
        await queryRunner.createForeignKey('cart_items', new typeorm_1.TableForeignKey({
            columnNames: ['cart_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'carts',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
        await queryRunner.createForeignKey('cart_items', new typeorm_1.TableForeignKey({
            columnNames: ['appointment_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'appointments',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
        await queryRunner.createForeignKey('cart_items', new typeorm_1.TableForeignKey({
            columnNames: ['product_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinic_products',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
        await queryRunner.createForeignKey('cart_items', new typeorm_1.TableForeignKey({
            columnNames: ['service_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinic_services',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
        await queryRunner.createForeignKey('cart_items', new typeorm_1.TableForeignKey({
            columnNames: ['vaccination_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinic_vaccinations',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
        await queryRunner.createForeignKey('cart_items', new typeorm_1.TableForeignKey({
            columnNames: ['prescription_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinic_medications',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
        await queryRunner.createForeignKey('cart_items', new typeorm_1.TableForeignKey({
            columnNames: ['labreport_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinic_lab_reports',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE'
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('cart_items');
    }
}
exports.CreateCartItem1726059858587 = CreateCartItem1726059858587;
//# sourceMappingURL=1726059858587-CreateCartItem.js.map