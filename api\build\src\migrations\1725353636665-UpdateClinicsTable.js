"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateClinicsTable1725353636665 = void 0;
const typeorm_1 = require("typeorm");
class UpdateClinicsTable1725353636665 {
    constructor() {
        this.name = 'UpdateClinicsTable1725353636665';
    }
    async up(queryRunner) {
        await queryRunner.changeColumn('clinics', 'name', new typeorm_1.TableColumn({
            name: 'name',
            type: 'varchar',
            isUnique: true,
            isNullable: true,
            length: '50'
        }));
        await queryRunner.changeColumn('clinics', 'mobile', new typeorm_1.TableColumn({
            name: 'mobile',
            type: 'varchar',
            isUnique: true,
            isNullable: true,
            length: '10'
        }));
        await queryRunner.addColumns('clinics', [
            new typeorm_1.TableColumn({
                name: 'admin_first_name',
                type: 'varchar',
                isNullable: true,
                length: '50'
            }),
            new typeorm_1.TableColumn({
                name: 'admin_last_name',
                type: 'varchar',
                isNullable: true,
                length: '50'
            }),
            new typeorm_1.TableColumn({
                name: 'admin_email',
                type: 'varchar',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'admin_mobile',
                type: 'varchar',
                isNullable: true
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumns('clinics', [
            'admin_first_name',
            'admin_last_name',
            'admin_email',
            'admin_mobile'
        ]);
    }
}
exports.UpdateClinicsTable1725353636665 = UpdateClinicsTable1725353636665;
//# sourceMappingURL=1725353636665-UpdateClinicsTable.js.map