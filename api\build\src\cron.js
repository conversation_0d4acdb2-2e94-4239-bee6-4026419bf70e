"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bootstrap = bootstrap;
const config_1 = require("@nestjs/config");
const core_1 = require("@nestjs/core");
const winston_logger_service_1 = require("./utils/logger/winston-logger.service");
const cron_module_1 = require("./cron.module");
async function bootstrap() {
    var _a;
    try {
        // Set the SERVICE_TYPE environment variable to identify this as a cron service
        process.env.SERVICE_TYPE = 'cron';
        const app = await core_1.NestFactory.create(cron_module_1.CronModule);
        app.useLogger(app.get(winston_logger_service_1.WinstonLogger));
        const configService = app.get(config_1.ConfigService);
        const port = (_a = configService === null || configService === void 0 ? void 0 : configService.get('app.webSocketPort')) !== null && _a !== void 0 ? _a : 4800;
        // console.log('Port is at', port);
        await app
            .listen(port)
            .then(() => console.log('Port is at', port))
            .catch(err => console.log('Error', err));
        return app;
    }
    catch (err) {
        console.log(err);
    }
    return null;
}
bootstrap();
//# sourceMappingURL=cron.js.map