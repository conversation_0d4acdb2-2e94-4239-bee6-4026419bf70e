"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateDiagnosticNotes1738650116873 = void 0;
const typeorm_1 = require("typeorm");
class CreateDiagnosticNotes1738650116873 {
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: "diagnostic_notes",
            columns: [
                {
                    name: "id",
                    type: "uuid",
                    isPrimary: true,
                    generationStrategy: "uuid",
                    default: "uuid_generate_v4()"
                },
                {
                    name: "clinic_id",
                    type: "uuid",
                    isNullable: false
                },
                {
                    name: "patient_id",
                    type: "uuid",
                    isNullable: false
                },
                {
                    name: "appointment_id",
                    type: "uuid",
                    isNullable: false
                },
                {
                    name: "lab_report_id",
                    type: "uuid",
                    isNullable: false
                },
                {
                    name: "template_id",
                    type: "uuid",
                    isNullable: false
                },
                {
                    name: "template_name",
                    type: "varchar",
                    length: "100",
                    isNullable: true
                },
                {
                    name: "note_data",
                    type: "jsonb",
                    isNullable: false
                },
                {
                    name: "version",
                    type: "integer",
                    default: 1
                },
                {
                    name: "created_by",
                    type: "uuid",
                    isNullable: false
                },
                {
                    name: "updated_by",
                    type: "uuid",
                    isNullable: false
                },
                {
                    name: "created_at",
                    type: "timestamp",
                    default: "CURRENT_TIMESTAMP"
                },
                {
                    name: "updated_at",
                    type: "timestamp",
                    default: "CURRENT_TIMESTAMP",
                    onUpdate: "CURRENT_TIMESTAMP"
                },
                {
                    name: 'file_key',
                    type: 'varchar',
                    length: '255',
                    isNullable: true
                },
                {
                    name: 'file_name',
                    type: 'varchar',
                    length: '255',
                    isNullable: true
                },
                {
                    name: 'diagnostic_number',
                    type: 'varchar',
                    isNullable: true
                },
                {
                    name: 'clinic_lab_report_id',
                    type: "uuid",
                    isNullable: false
                }
            ]
        }), true);
        // Add foreign key constraints
        const foreignKeys = [
            {
                columnNames: ["clinic_id"],
                referencedTableName: "clinics",
                referencedColumnNames: ["id"]
            },
            {
                columnNames: ["patient_id"],
                referencedTableName: "patients",
                referencedColumnNames: ["id"]
            },
            {
                columnNames: ["appointment_id"],
                referencedTableName: "appointments",
                referencedColumnNames: ["id"]
            },
            {
                columnNames: ["lab_report_id"],
                referencedTableName: "lab_reports",
                referencedColumnNames: ["id"]
            },
            {
                columnNames: ["template_id"],
                referencedTableName: "diagnostic_templates",
                referencedColumnNames: ["id"]
            }
        ];
        for (const fk of foreignKeys) {
            await queryRunner.createForeignKey("diagnostic_notes", new typeorm_1.TableForeignKey({
                columnNames: fk.columnNames,
                referencedColumnNames: fk.referencedColumnNames,
                referencedTableName: fk.referencedTableName,
                onDelete: "CASCADE"
            }));
        }
        // Add index for better querying
        await queryRunner.createIndex("diagnostic_notes", new typeorm_1.TableIndex({
            name: "IDX_clinic_lab_report",
            columnNames: ["clinic_id", "lab_report_id"]
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable("diagnostic_notes");
    }
}
exports.CreateDiagnosticNotes1738650116873 = CreateDiagnosticNotes1738650116873;
//# sourceMappingURL=1738650116873-CreateDiagnosticNotes.js.map