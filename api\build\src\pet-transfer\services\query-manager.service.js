"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PetTransferQueryManagerService = void 0;
const common_1 = require("@nestjs/common");
let PetTransferQueryManagerService = class PetTransferQueryManagerService {
    constructor() {
        // Tables with both clinic_id and brand_id columns
        this.directMigrationTables = [
            {
                tableName: 'appointments',
                patientIdColumn: 'patient_id',
                clinicIdColumn: 'clinic_id',
                hasUserColumns: true
            },
            {
                tableName: 'emr',
                patientIdColumn: 'patient_id',
                clinicIdColumn: 'clinic_id',
                hasUserColumns: true
            },
            {
                tableName: 'invoices',
                patientIdColumn: 'patient_id',
                clinicIdColumn: 'clinic_id',
                hasUserColumns: true
            },
            {
                tableName: 'lab_reports',
                patientIdColumn: 'patient_id',
                clinicIdColumn: 'clinic_id',
                hasUserColumns: true
            },
            {
                tableName: 'long-term-medications',
                patientIdColumn: 'patient_id',
                clinicIdColumn: 'clinic_id',
                hasUserColumns: true
            },
            {
                tableName: 'patient_owners',
                patientIdColumn: 'patient_id',
                clinicIdColumn: 'clinic_id',
                hasUserColumns: false
            },
            {
                tableName: 'patients',
                patientIdColumn: 'id',
                clinicIdColumn: 'clinic_id',
                hasUserColumns: true
            },
            {
                tableName: 'patient_reminders',
                patientIdColumn: 'patient_id',
                clinicIdColumn: 'clinic_id',
                hasUserColumns: true
            },
            {
                tableName: 'payment_details',
                patientIdColumn: 'patient_id',
                clinicIdColumn: 'clinic_id',
                hasUserColumns: true
            },
            {
                tableName: 'pet_transfer_history',
                patientIdColumn: 'patient_id',
                clinicIdColumn: 'clinic_id',
                hasUserColumns: false
            }
        ];
        // Tables with clinic_id but no brand_id column
        this.clinicOnlyMigrationTables = [
            {
                tableName: 'diagnostic_notes',
                patientIdColumn: 'patient_id',
                clinicIdColumn: 'clinic_id',
                hasUserColumns: true
            },
            {
                tableName: 'patient_estimate',
                patientIdColumn: 'patient_id',
                clinicIdColumn: 'clinic_id',
                hasUserColumns: true
            }
        ];
        // Tables with only patient_id (no clinic_id or brand_id columns)
        this.patientOnlyMigrationTables = [
            {
                tableName: 'patient_alerts',
                patientIdColumn: 'patient_id',
                clinicIdColumn: '', // No clinic_id column
                hasUserColumns: true
            },
            {
                tableName: 'patient_document_libraries',
                patientIdColumn: 'patient_id',
                clinicIdColumn: '', // No clinic_id column
                hasUserColumns: true
            },
            {
                tableName: 'patient_vaccinations',
                patientIdColumn: 'patient_id',
                clinicIdColumn: '', // No clinic_id column
                hasUserColumns: true
            }
        ];
    }
    getPetTransferQueries(patientId, sourceClinicId, destClinicId, destBrandId, userMappings, destPatientId) {
        const queries = [];
        // Add special handling for appointment_doctors table
        queries.push(...this.generateAppointmentDoctorsQueries(patientId, sourceClinicId, destClinicId, userMappings));
        // Handle patient merging if destination patient exists
        if (destPatientId) {
            // For patient merging, we update all related records to point to the destination patient
            // and then delete the source patient
            for (const table of this.directMigrationTables) {
                if (table.tableName === 'patients') {
                    // Skip updating the patients table - we'll delete the source patient later
                    continue;
                }
                if (table.tableName === 'patient_owners') {
                    // Skip patient_owners - this is handled separately in handleOwnerMigration to avoid duplicates
                    continue;
                }
                let query = `UPDATE "${table.tableName}" SET clinic_id = '${destClinicId}', brand_id = '${destBrandId}'`;
                // For patient-related tables, also update the patient_id to merge records
                if (table.patientIdColumn === 'patient_id') {
                    query += `, patient_id = '${destPatientId}'`;
                }
                else if (table.patientIdColumn === 'id' &&
                    table.tableName === 'patients') {
                    // This is the patients table itself - skip it
                    continue;
                }
                // Add user mapping updates if there are user mappings and table has user columns
                if (userMappings.size > 0 && table.hasUserColumns) {
                    let caseStatement = `CASE`;
                    for (const [sourceUserId, destUserId] of userMappings.entries()) {
                        caseStatement += ` WHEN created_by = '${sourceUserId}' THEN '${destUserId}'`;
                    }
                    caseStatement += ` ELSE created_by END`;
                    query += `, created_by = ${caseStatement}`;
                    caseStatement = `CASE`;
                    for (const [sourceUserId, destUserId] of userMappings.entries()) {
                        caseStatement += ` WHEN updated_by = '${sourceUserId}' THEN '${destUserId}'`;
                    }
                    caseStatement += ` ELSE updated_by END`;
                    query += `, updated_by = ${caseStatement}`;
                }
                query += ` WHERE ${table.patientIdColumn} = '${patientId}' AND ${table.clinicIdColumn} = '${sourceClinicId}'`;
                queries.push(query);
            }
            // Process tables that only have clinic_id (no brand_id)
            for (const table of this.clinicOnlyMigrationTables) {
                let query = `UPDATE "${table.tableName}" SET clinic_id = '${destClinicId}'`;
                // For patient-related tables, also update the patient_id to merge records
                if (table.patientIdColumn === 'patient_id') {
                    query += `, patient_id = '${destPatientId}'`;
                }
                // Only add user mapping updates if there are user mappings and table has user columns
                if (userMappings.size > 0 && table.hasUserColumns) {
                    let caseStatement = `CASE`;
                    for (const [sourceUserId, destUserId] of userMappings.entries()) {
                        caseStatement += ` WHEN created_by = '${sourceUserId}' THEN '${destUserId}'`;
                    }
                    caseStatement += ` ELSE created_by END`;
                    query += `, created_by = ${caseStatement}`;
                    caseStatement = `CASE`;
                    for (const [sourceUserId, destUserId] of userMappings.entries()) {
                        caseStatement += ` WHEN updated_by = '${sourceUserId}' THEN '${destUserId}'`;
                    }
                    caseStatement += ` ELSE updated_by END`;
                    query += `, updated_by = ${caseStatement}`;
                }
                query += ` WHERE ${table.patientIdColumn} = '${patientId}' AND ${table.clinicIdColumn} = '${sourceClinicId}'`;
                queries.push(query);
            }
            // Process tables that only have patient_id (no clinic_id or brand_id)
            for (const table of this.patientOnlyMigrationTables) {
                let query = `UPDATE "${table.tableName}" SET patient_id = '${destPatientId}'`;
                // Only add user mapping updates if there are user mappings and table has user columns
                if (userMappings.size > 0 && table.hasUserColumns) {
                    let caseStatement = `CASE`;
                    for (const [sourceUserId, destUserId] of userMappings.entries()) {
                        caseStatement += ` WHEN created_by = '${sourceUserId}' THEN '${destUserId}'`;
                    }
                    caseStatement += ` ELSE created_by END`;
                    query += `, created_by = ${caseStatement}`;
                    caseStatement = `CASE`;
                    for (const [sourceUserId, destUserId] of userMappings.entries()) {
                        caseStatement += ` WHEN updated_by = '${sourceUserId}' THEN '${destUserId}'`;
                    }
                    caseStatement += ` ELSE updated_by END`;
                    query += `, updated_by = ${caseStatement}`;
                }
                query += ` WHERE ${table.patientIdColumn} = '${patientId}'`;
                queries.push(query);
            }
            // Add query to delete the source patient after merging
            queries.push(`DELETE FROM patients WHERE id = '${patientId}' AND clinic_id = '${sourceClinicId}'`);
        }
        else {
            // Normal transfer without merging
            for (const table of this.directMigrationTables) {
                let query = `UPDATE "${table.tableName}" SET clinic_id = '${destClinicId}', brand_id = '${destBrandId}'`;
                // Only add user mapping updates if there are user mappings and table has user columns
                if (userMappings.size > 0 && table.hasUserColumns) {
                    let caseStatement = `CASE`;
                    for (const [sourceUserId, destUserId] of userMappings.entries()) {
                        caseStatement += ` WHEN created_by = '${sourceUserId}' THEN '${destUserId}'`;
                    }
                    caseStatement += ` ELSE created_by END`;
                    query += `, created_by = ${caseStatement}`;
                    caseStatement = `CASE`;
                    for (const [sourceUserId, destUserId] of userMappings.entries()) {
                        caseStatement += ` WHEN updated_by = '${sourceUserId}' THEN '${destUserId}'`;
                    }
                    caseStatement += ` ELSE updated_by END`;
                    query += `, updated_by = ${caseStatement}`;
                }
                query += ` WHERE ${table.patientIdColumn} = '${patientId}' AND ${table.clinicIdColumn} = '${sourceClinicId}'`;
                queries.push(query);
            }
            // Process tables that only have clinic_id (no brand_id)
            for (const table of this.clinicOnlyMigrationTables) {
                let query = `UPDATE "${table.tableName}" SET clinic_id = '${destClinicId}'`;
                // Only add user mapping updates if there are user mappings and table has user columns
                if (userMappings.size > 0 && table.hasUserColumns) {
                    let caseStatement = `CASE`;
                    for (const [sourceUserId, destUserId] of userMappings.entries()) {
                        caseStatement += ` WHEN created_by = '${sourceUserId}' THEN '${destUserId}'`;
                    }
                    caseStatement += ` ELSE created_by END`;
                    query += `, created_by = ${caseStatement}`;
                    caseStatement = `CASE`;
                    for (const [sourceUserId, destUserId] of userMappings.entries()) {
                        caseStatement += ` WHEN updated_by = '${sourceUserId}' THEN '${destUserId}'`;
                    }
                    caseStatement += ` ELSE updated_by END`;
                    query += `, updated_by = ${caseStatement}`;
                }
                query += ` WHERE ${table.patientIdColumn} = '${patientId}' AND ${table.clinicIdColumn} = '${sourceClinicId}'`;
                queries.push(query);
            }
            // Process tables that only have patient_id (no clinic_id or brand_id) - normal transfer
            for (const table of this.patientOnlyMigrationTables) {
                let query = `UPDATE "${table.tableName}" SET`;
                // Only add user mapping updates if there are user mappings and table has user columns
                if (userMappings.size > 0 && table.hasUserColumns) {
                    let caseStatement = `CASE`;
                    for (const [sourceUserId, destUserId] of userMappings.entries()) {
                        caseStatement += ` WHEN created_by = '${sourceUserId}' THEN '${destUserId}'`;
                    }
                    caseStatement += ` ELSE created_by END`;
                    query += ` created_by = ${caseStatement}`;
                    caseStatement = `CASE`;
                    for (const [sourceUserId, destUserId] of userMappings.entries()) {
                        caseStatement += ` WHEN updated_by = '${sourceUserId}' THEN '${destUserId}'`;
                    }
                    caseStatement += ` ELSE updated_by END`;
                    query += `, updated_by = ${caseStatement}`;
                }
                else {
                    // If no user mappings or table doesn't have user columns, skip this table
                    continue; // Skip this table since there's nothing to update for patient-only tables in normal transfer
                }
                query += ` WHERE ${table.patientIdColumn} = '${patientId}'`;
                queries.push(query);
            }
        }
        return queries;
    }
    generateAppointmentDoctorsQueries(patientId, sourceClinicId, destClinicId, userMappings) {
        const queries = [];
        // Only proceed if we have user mappings
        if (userMappings.size === 0) {
            return queries;
        }
        // Update appointment_doctors to use the destination clinic's clinic_user_id
        // This is needed for Doctor Performance analytics to work correctly
        for (const [sourceUserId, destUserId] of userMappings.entries()) {
            const query = `
				UPDATE appointment_doctors
				SET clinic_user_id = (
					SELECT cu.id
					FROM clinic_users cu
					WHERE cu.user_id = '${destUserId}'
					AND cu.clinic_id = '${destClinicId}'
				)
				WHERE appointment_id IN (
					SELECT a.id
					FROM appointments a
					WHERE a.patient_id = '${patientId}'
				)
				AND clinic_user_id = (
					SELECT cu.id
					FROM clinic_users cu
					WHERE cu.user_id = '${sourceUserId}'
					AND cu.clinic_id = '${sourceClinicId}'
				)
			`;
            queries.push(query);
        }
        return queries;
    }
};
exports.PetTransferQueryManagerService = PetTransferQueryManagerService;
exports.PetTransferQueryManagerService = PetTransferQueryManagerService = __decorate([
    (0, common_1.Injectable)()
], PetTransferQueryManagerService);
//# sourceMappingURL=query-manager.service.js.map