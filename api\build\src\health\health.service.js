"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthService = void 0;
const common_1 = require("@nestjs/common");
const terminus_1 = require("@nestjs/terminus");
const redis_health_1 = require("./redis.health");
const data_source_1 = require("../database/data-source");
const redis_service_1 = require("../utils/redis/redis.service");
let HealthService = class HealthService {
    constructor(health, db, redis, redisService) {
        this.health = health;
        this.db = db;
        this.redis = redis;
        this.redisService = redisService;
    }
    checkDatabase() {
        return this.health.check([async () => this.db.pingCheck('database')]);
    }
    async checkMigrations() {
        return this.health.check([
            async () => {
                if (!data_source_1.dataSource.isInitialized) {
                    await data_source_1.dataSource.initialize();
                }
                const hasPendingMigrations = await data_source_1.dataSource.showMigrations();
                const isHealthy = !hasPendingMigrations;
                const pendingMigrations = hasPendingMigrations
                    ? await data_source_1.dataSource.runMigrations({ transaction: 'none' })
                    : [];
                const result = {
                    migration: {
                        status: isHealthy ? 'up' : 'down',
                        pendingMigrations: pendingMigrations.map(migration => migration.name)
                    }
                };
                return result;
            }
        ]);
    }
    checkRedis() {
        return this.health.check([
            async () => {
                try {
                    return await this.redis.isHealthy('redis');
                }
                catch (error) {
                    // Log the error but return a degraded status instead of failing
                    return {
                        redis: {
                            status: 'down',
                            message: 'Redis connection failed but service can continue operating'
                        }
                    };
                }
            }
        ]);
    }
    async checkRedisLocks() {
        try {
            // List of key lock names to check
            const lockKeys = [
                'reminder_cron_lock',
                'upcoming_appointment_reminder_cron_lock'
            ];
            const lockStatus = await this.redisService.getLockStatus(lockKeys);
            // Create a health check result
            const result = {
                redisLocks: {
                    status: 'up',
                    locks: lockStatus
                }
            };
            return {
                status: 'ok',
                info: result
            };
        }
        catch (error) {
            console.error('Redis locks health check failed:', error);
            return {
                status: 'error',
                error: {
                    message: 'Redis locks health check failed',
                    details: error.message || 'Unknown error'
                }
            };
        }
    }
    checkAll() {
        return this.health.check([
            async () => this.db.pingCheck('database'),
            () => this.redis.isHealthy('redis'),
            async () => {
                const isHealthy = true;
                const result = {
                    migration: {
                        status: isHealthy ? 'up' : 'down'
                    }
                };
                return result;
            }
        ]);
    }
    /**
     * Comprehensive health check including Redis cluster details
     * Use this for detailed system monitoring in production
     */
    checkAllWithCluster() {
        return this.health.check([
            async () => this.db.pingCheck('database'),
            () => this.redis.getClusterHealth('redis_cluster'),
            async () => {
                const isHealthy = true;
                const result = {
                    migration: {
                        status: isHealthy ? 'up' : 'down'
                    }
                };
                return result;
            }
        ]);
    }
    async checkGeneralHealth() {
        return {
            isHealthy: true,
            message: 'Successfully checked health api!'
        };
    }
    /**
     * Enhanced Redis health check with cluster information
     * Provides comprehensive Redis cluster monitoring including:
     * - Cluster mode detection (single vs cluster)
     * - Individual node health status
     * - Performance metrics and statistics
     * - Connection and failover information
     */
    async checkRedisCluster() {
        return this.health.check([
            async () => {
                try {
                    return await this.redis.getClusterHealth('redis_cluster');
                }
                catch (error) {
                    // Return detailed error information for cluster issues
                    return {
                        redis_cluster: {
                            status: 'down',
                            mode: 'unknown',
                            connectivity: 'failed',
                            error: error instanceof Error
                                ? error.message
                                : 'Unknown error',
                            message: 'Redis cluster health check failed but service can continue operating'
                        }
                    };
                }
            }
        ]);
    }
    /**
     * Get Redis connection and performance metrics
     * Useful for monitoring Redis performance in production
     */
    async getRedisMetrics() {
        try {
            const client = this.redisService.getClient();
            // Get basic ping time
            const start = Date.now();
            await client.ping();
            const pingTime = Date.now() - start;
            // Get lock status for monitoring
            const lockKeys = [
                'reminder_cron_lock',
                'upcoming_appointment_reminder_cron_lock'
            ];
            const lockStatus = await this.redisService.getLockStatus(lockKeys);
            // Count active locks
            const activeLocks = Object.values(lockStatus).filter((lock) => lock.exists).length;
            return {
                status: 'ok',
                metrics: {
                    ping_time_ms: pingTime,
                    active_locks: activeLocks,
                    total_monitored_locks: lockKeys.length,
                    connection_type: client.constructor.name.toLowerCase(), // 'redis' or 'cluster'
                    timestamp: new Date().toISOString()
                },
                locks: lockStatus
            };
        }
        catch (error) {
            console.error('Redis metrics check failed:', error);
            return {
                status: 'error',
                error: {
                    message: 'Redis metrics check failed',
                    details: error.message || 'Unknown error'
                }
            };
        }
    }
};
exports.HealthService = HealthService;
exports.HealthService = HealthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [terminus_1.HealthCheckService,
        terminus_1.TypeOrmHealthIndicator,
        redis_health_1.RedisHealthIndicator,
        redis_service_1.RedisService])
], HealthService);
//# sourceMappingURL=health.service.js.map