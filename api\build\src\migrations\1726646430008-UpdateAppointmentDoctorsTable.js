"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataMigrationAndAddFKToAppointmentDoctors1726646430008 = void 0;
const typeorm_1 = require("typeorm");
class DataMigrationAndAddFKToAppointmentDoctors1726646430008 {
    async up(queryRunner) {
        // Step 1: Rename the column
        await queryRunner.query(`ALTER TABLE "appointment_doctors" RENAME COLUMN "doctor_id" TO "clinic_user_id"`);
        // Step 2: Create temporary column to store original doctor_id
        await queryRunner.query(`ALTER TABLE "appointment_doctors" ADD COLUMN "original_doctor_id" UUID`);
        await queryRunner.query(`UPDATE "appointment_doctors" SET "original_doctor_id" = "clinic_user_id"`);
        // Step 3: Update clinic_user_id to match entries in clinic_users table
        await queryRunner.query(`
            UPDATE "appointment_doctors" ad
            SET "clinic_user_id" = cu.id
            FROM "clinic_users" cu
            WHERE cu.user_id = ad.original_doctor_id
        `);
        // Step 4: Remove entries that don't have a matching clinic_user
        await queryRunner.query(`
            DELETE FROM "appointment_doctors"
            WHERE "clinic_user_id" NOT IN (SELECT id FROM "clinic_users")
        `);
        // Step 5: Add the new foreign key
        await queryRunner.createForeignKey('appointment_doctors', new typeorm_1.TableForeignKey({
            columnNames: ['clinic_user_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinic_users',
            onDelete: 'CASCADE'
        }));
        // Step 6: Remove the temporary column
        await queryRunner.query(`ALTER TABLE "appointment_doctors" DROP COLUMN "original_doctor_id"`);
    }
    async down(queryRunner) {
        // Remove the foreign key
        const table = await queryRunner.getTable('appointment_doctors');
        if (table) {
            const foreignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('clinic_user_id') !== -1);
            if (foreignKey) {
                await queryRunner.dropForeignKey('appointment_doctors', foreignKey);
            }
        }
        // Rename the column back
        await queryRunner.query(`ALTER TABLE "appointment_doctors" RENAME COLUMN "clinic_user_id" TO "doctor_id"`);
    }
}
exports.DataMigrationAndAddFKToAppointmentDoctors1726646430008 = DataMigrationAndAddFKToAppointmentDoctors1726646430008;
//# sourceMappingURL=1726646430008-UpdateAppointmentDoctorsTable.js.map