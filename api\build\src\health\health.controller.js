"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthController = void 0;
const common_1 = require("@nestjs/common");
const health_service_1 = require("./health.service");
const swagger_1 = require("@nestjs/swagger");
const track_method_decorator_1 = require("../utils/new-relic/decorators/track-method.decorator");
let HealthController = class HealthController {
    constructor(healthService) {
        this.healthService = healthService;
    }
    checkDatabase() {
        return this.healthService.checkDatabase();
    }
    checkMigrations() {
        return this.healthService.checkMigrations();
    }
    checkRedis() {
        return this.healthService.checkRedis();
    }
    checkRedisLocks() {
        return this.healthService.checkRedisLocks();
    }
    checkAll() {
        return this.healthService.checkAll();
    }
    checkAllWithCluster() {
        return this.healthService.checkAllWithCluster();
    }
    checkGeneralHealth() {
        return this.healthService.checkGeneralHealth();
    }
    checkRedisCluster() {
        return this.healthService.checkRedisCluster();
    }
    getRedisMetrics() {
        return this.healthService.getRedisMetrics();
    }
};
exports.HealthController = HealthController;
__decorate([
    (0, common_1.Get)('/db'),
    (0, swagger_1.ApiOperation)({ summary: 'Check database health' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Database is healthy' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Database health check failed' }),
    (0, track_method_decorator_1.TrackMethod)('checkDatabase-health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "checkDatabase", null);
__decorate([
    (0, common_1.Get)('/db/migration'),
    (0, swagger_1.ApiOperation)({ summary: 'Check database migrations' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Migrations are up to date' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Migration check failed' }),
    (0, track_method_decorator_1.TrackMethod)('checkMigrations-health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "checkMigrations", null);
__decorate([
    (0, common_1.Get)('/redis'),
    (0, swagger_1.ApiOperation)({ summary: 'Check Redis connection' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Redis is connected' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Redis connection check failed' }),
    (0, track_method_decorator_1.TrackMethod)('checkRedis-health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "checkRedis", null);
__decorate([
    (0, common_1.Get)('/redis/locks'),
    (0, swagger_1.ApiOperation)({ summary: 'Check Redis lock states' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Redis lock states retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Redis lock check failed' }),
    (0, track_method_decorator_1.TrackMethod)('checkRedisLocks-health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "checkRedisLocks", null);
__decorate([
    (0, common_1.Get)('/all'),
    (0, swagger_1.ApiOperation)({ summary: 'Check all services' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'All services are healthy' }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'One or more service checks failed'
    }),
    (0, track_method_decorator_1.TrackMethod)('checkAll-health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "checkAll", null);
__decorate([
    (0, common_1.Get)('/all/detailed'),
    (0, swagger_1.ApiOperation)({
        summary: 'Check all services with comprehensive Redis cluster details',
        description: 'Includes detailed Redis cluster information along with database and migration status'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'All services are healthy with detailed information'
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'One or more service checks failed'
    }),
    (0, track_method_decorator_1.TrackMethod)('checkAllWithCluster-health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "checkAllWithCluster", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Check general application health' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Application is healthy' }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Application health check failed'
    }),
    (0, track_method_decorator_1.TrackMethod)('checkGeneralHealth-health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "checkGeneralHealth", null);
__decorate([
    (0, common_1.Get)('/redis/cluster'),
    (0, swagger_1.ApiOperation)({
        summary: 'Check Redis cluster health with detailed information',
        description: 'Provides comprehensive Redis cluster monitoring including node status, performance metrics, and cluster topology information'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Redis cluster health information retrieved successfully',
        schema: {
            example: {
                status: 'ok',
                info: {
                    redis_cluster: {
                        status: 'up',
                        mode: 'cluster',
                        connectivity: 'connected',
                        cluster: {
                            state: 'ok',
                            healthy: true,
                            known_nodes: 6,
                            master_count: 3,
                            slave_count: 3,
                            nodes: [
                                {
                                    id: 'abc12345',
                                    host: 'node1.cache.amazonaws.com',
                                    port: 6379,
                                    role: 'master',
                                    healthy: true,
                                    link_state: 'connected'
                                }
                            ],
                            statistics: {
                                connected_clients: 15,
                                used_memory: '45.2M',
                                hit_rate: '95.67%',
                                instantaneous_ops_per_sec: 1250
                            }
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Redis cluster health check failed'
    }),
    (0, track_method_decorator_1.TrackMethod)('checkRedisCluster-health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "checkRedisCluster", null);
__decorate([
    (0, common_1.Get)('/redis/metrics'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get Redis performance metrics and statistics',
        description: 'Returns Redis connection performance, lock status, and real-time metrics for monitoring'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Redis metrics retrieved successfully',
        schema: {
            example: {
                status: 'ok',
                metrics: {
                    ping_time_ms: 2,
                    active_locks: 0,
                    total_monitored_locks: 2,
                    connection_type: 'cluster',
                    timestamp: '2024-01-15T10:30:00.000Z'
                },
                locks: {
                    reminder_cron_lock: {
                        exists: false,
                        ttl: -2,
                        value: null
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Redis metrics check failed' }),
    (0, track_method_decorator_1.TrackMethod)('getRedisMetrics-health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HealthController.prototype, "getRedisMetrics", null);
exports.HealthController = HealthController = __decorate([
    (0, swagger_1.ApiTags)('Health Checks'),
    (0, common_1.Controller)('healtz'),
    __metadata("design:paramtypes", [health_service_1.HealthService])
], HealthController);
//# sourceMappingURL=health.controller.js.map