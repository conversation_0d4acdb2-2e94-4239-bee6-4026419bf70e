"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateWorkingHoursDefaultValue1728282953178 = void 0;
class UpdateWorkingHoursDefaultValue1728282953178 {
    async up(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE clinics
            ALTER COLUMN working_hours DROP DEFAULT;
        `);
        // Then, set the new default value
        await queryRunner.query(`
            ALTER TABLE clinics
            ALTER COLUMN working_hours SET DEFAULT '{
                "workingHours": {
                  "friday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": false
                    }
                  ],
                  "monday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": false
                    }
                  ],
                  "sunday": [
                    {
                      "endTime": null,
                      "startTime": null,
                      "isWorkingDay": false
                    }
                  ],
                  "tuesday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": false
                    }
                  ],
                  "saturday": [
                    {
                      "endTime": null,
                      "startTime": null,
                      "isWorkingDay": false
                    }
                  ],
                  "thursday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": false
                    }
                  ],
                  "wednesday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": false
                    }
                  ]
                }
              }'::jsonb;
        `);
        // Update existing rows with the new structure
        await queryRunner.query(`
            UPDATE clinics
            SET working_hours = '{
                "workingHours": {
                  "friday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": false
                    }
                  ],
                  "monday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": false
                    }
                  ],
                  "sunday": [
                    {
                      "endTime": null,
                      "startTime": null,
                      "isWorkingDay": false
                    }
                  ],
                  "tuesday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": false
                    }
                  ],
                  "saturday": [
                    {
                      "endTime": null,
                      "startTime": null,
                      "isWorkingDay": false
                    }
                  ],
                  "thursday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": false
                    }
                  ],
                  "wednesday": [
                    {
                      "endTime": "17:00",
                      "startTime": "09:00",
                      "isWorkingDay": false
                    }
                  ]
                }
              }'::jsonb
            WHERE working_hours IS NULL OR working_hours->'workingHours' IS NOT NULL;
        `);
    }
    async down(queryRunner) {
        // Revert to the old default value
        await queryRunner.query(`
            ALTER TABLE clinics
            ALTER COLUMN working_hours SET DEFAULT '{
                "workingHours": {
                    "friday": [
                      {
                        "endTime": "17:00",
                        "startTime": "09:00",
                        "isWorkingDay": true
                      }
                    ],
                    "monday": [
                      {
                        "endTime": "17:00",
                        "startTime": "09:00",
                        "isWorkingDay": true
                      }
                    ],
                    "sunday": [
                      {
                        "endTime": "18:00",
                        "startTime": "09:00",
                        "isWorkingDay": false
                      }
                    ],
                    "tuesday": [
                      {
                        "endTime": "17:00",
                        "startTime": "09:00",
                        "isWorkingDay": true
                      }
                    ],
                    "saturday": [
                      {
                        "endTime": "18:00",
                        "startTime": "09:00",
                        "isWorkingDay": false
                      }
                    ],
                    "thursday": [
                      {
                        "endTime": "17:00",
                        "startTime": "09:00",
                        "isWorkingDay": true
                      }
                    ],
                    "wednesday": [
                      {
                        "endTime": "17:00",
                        "startTime": "09:00",
                        "isWorkingDay": true
                      }
                    ]
                  }
            }'::jsonb;
        `);
    }
}
exports.UpdateWorkingHoursDefaultValue1728282953178 = UpdateWorkingHoursDefaultValue1728282953178;
//# sourceMappingURL=1728282953178-UpadeClinicWorkingHoursDefault.js.map