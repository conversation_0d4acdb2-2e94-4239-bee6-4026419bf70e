interface LineItem {
    medication: string;
    comments: string;
}
interface PrescriptionData {
    prescriptionId: string;
    prescriptionDate: string;
    clinicName: string;
    clinicAddress: string;
    clinicPhone: string;
    clinicEmail: string;
    clinicWebsite: string;
    customerName: string;
    petName: string;
    petDetails: string;
    lineItems: LineItem[];
    dischargeInstructions: string;
    vetName: string;
    vetLicenseNo: string;
    digitalSignature: string;
    customerEmail: string;
    customerPhone: string;
}
export declare const generatePrescription: ({ prescriptionId, prescriptionDate, clinicName, clinicAddress, clinicPhone, clinicEmail, clinicWebsite, customerName, petName, petDetails, lineItems, dischargeInstructions, vetName, vetLicenseNo, digitalSignature, customerEmail, customerPhone }: PrescriptionData) => string;
export {};
