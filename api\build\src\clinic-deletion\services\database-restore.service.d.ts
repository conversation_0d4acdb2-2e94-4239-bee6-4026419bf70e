import { DataSource } from 'typeorm';
import { WinstonLogger } from '../../utils/logger/winston-logger.service';
import { S3Service } from '../../utils/aws/s3/s3.service';
import { ConflictResolution } from '../dto/clinic-deletion.dto';
export interface DatabaseRestoreResult {
    tablesProcessed: number;
    recordsRestored: number;
    recordsSkipped: number;
    conflicts: {
        resolved: number;
        skipped: number;
        failed: number;
    };
    duration: number;
}
export interface RestoreConflict {
    tableName: string;
    conflictingRecords: number;
    conflictType: 'primary_key' | 'unique_constraint' | 'foreign_key';
    details: string[];
}
export declare class DatabaseRestoreService {
    private readonly dataSource;
    private readonly logger;
    private readonly s3Service;
    constructor(dataSource: DataSource, logger: WinstonLogger, s3Service: S3Service);
    /**
     * Restore database records from backup
     */
    restoreDatabaseRecords(backupBasePath: string, conflictResolution?: ConflictResolution): Promise<DatabaseRestoreResult>;
    /**
     * Analyze restore conflicts before actual restore
     */
    analyzeRestoreConflicts(backupBasePath: string): Promise<RestoreConflict[]>;
    /**
     * Load database manifest from S3
     */
    private loadDatabaseManifest;
    /**
     * Restore a single table
     */
    private restoreTable;
    /**
     * Analyze conflicts for a single table
     */
    private analyzeTableConflicts;
    /**
     * Check for primary key conflicts
     */
    private checkPrimaryKeyConflict;
    /**
     * Check for unique constraint conflicts
     */
    private checkUniqueConstraintConflicts;
    /**
     * Get table schema information
     */
    private getTableSchema;
    /**
     * Check if record has conflicts (primary key or unique constraints)
     */
    private checkRecordConflicts;
    /**
     * Delete conflicting record
     */
    private deleteConflictingRecord;
    /**
     * Insert record into table
     */
    private insertRecord;
    /**
     * Prepare values for insertion, handling JSON columns properly
     */
    private prepareValuesForInsertion;
    /**
     * Get column information for a table
     */
    private getColumnInfo;
}
