"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ClientBookingController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientBookingController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const client_booking_dto_1 = require("./dto/client-booking.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../roles/roles.decorator");
const client_booking_service_1 = require("./client-booking.service");
const role_enum_1 = require("../roles/role.enum");
let ClientBookingController = ClientBookingController_1 = class ClientBookingController {
    constructor(clientBookingService) {
        this.clientBookingService = clientBookingService;
        this.logger = new common_1.Logger(ClientBookingController_1.name);
    }
    async createBooking(createBookingDto, req) {
        // Extract owner ID from the authenticated user (uses 'sub' from JWT)
        const ownerId = req.user.userId;
        // Ensure ownerId is present
        if (!ownerId) {
            throw new common_1.ForbiddenException('User must be a pet owner to book appointments');
        }
        return this.clientBookingService.createClientBooking(createBookingDto, ownerId);
    }
    async getBooking(appointmentId, req) {
        // Extract owner ID from the authenticated user
        const ownerId = req.user.userId;
        // Ensure ownerId is present
        if (!ownerId) {
            throw new common_1.ForbiddenException('User must be a pet owner to view appointments');
        }
        return this.clientBookingService.getClientBooking(appointmentId, ownerId);
    }
    async updateBooking(appointmentId, updateBookingDto, req) {
        // Extract owner ID from the authenticated user
        const ownerId = req.user.userId;
        // Ensure ownerId is present
        if (!ownerId) {
            throw new common_1.ForbiddenException('User must be a pet owner to update appointments');
        }
        return this.clientBookingService.updateClientBooking(appointmentId, updateBookingDto, ownerId);
    }
    async deleteBooking(appointmentId, req) {
        // Extract owner ID from the authenticated user
        const ownerId = req.user.userId;
        // Ensure ownerId is present
        if (!ownerId) {
            throw new common_1.ForbiddenException('User must be a pet owner to cancel appointments');
        }
        return this.clientBookingService.deleteClientBooking(appointmentId, ownerId);
    }
};
exports.ClientBookingController = ClientBookingController;
__decorate([
    (0, common_1.Post)('client-booking'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new booking' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Booking created successfully',
        type: client_booking_dto_1.ClientBookingResponseDto
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.OWNER, role_enum_1.Role.ADMIN),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [client_booking_dto_1.CreateClientBookingDto, Object]),
    __metadata("design:returntype", Promise)
], ClientBookingController.prototype, "createBooking", null);
__decorate([
    (0, common_1.Get)('client-booking/:appointmentId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get booking details' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Booking details retrieved successfully',
        type: client_booking_dto_1.ClientBookingResponseDto
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.OWNER, role_enum_1.Role.ADMIN),
    __param(0, (0, common_1.Param)('appointmentId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ClientBookingController.prototype, "getBooking", null);
__decorate([
    (0, common_1.Put)('client-booking/:appointmentId'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a booking (reschedule or cancel)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Booking updated successfully',
        type: client_booking_dto_1.ClientBookingResponseDto
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.OWNER, role_enum_1.Role.ADMIN),
    __param(0, (0, common_1.Param)('appointmentId')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, client_booking_dto_1.UpdateClientBookingDto, Object]),
    __metadata("design:returntype", Promise)
], ClientBookingController.prototype, "updateBooking", null);
__decorate([
    (0, common_1.Delete)('client-booking/:appointmentId'),
    (0, swagger_1.ApiOperation)({ summary: 'Cancel/delete a booking' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Booking cancelled successfully',
        type: client_booking_dto_1.ClientBookingResponseDto
    }),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.OWNER, role_enum_1.Role.ADMIN),
    __param(0, (0, common_1.Param)('appointmentId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ClientBookingController.prototype, "deleteBooking", null);
exports.ClientBookingController = ClientBookingController = ClientBookingController_1 = __decorate([
    (0, swagger_1.ApiTags)('Client Booking'),
    (0, common_1.Controller)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [client_booking_service_1.ClientBookingService])
], ClientBookingController);
//# sourceMappingURL=client-booking.controller.js.map