"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dataSource = exports.dataSourceOptions = void 0;
const typeorm_1 = require("typeorm");
const database_config_1 = require("../config/database.config");
const dbConfig = (0, database_config_1.default)();
exports.dataSourceOptions = {
    ...dbConfig,
    ssl: false,
    synchronize: true,
    type: 'postgres'
};
exports.dataSource = new typeorm_1.DataSource(exports.dataSourceOptions);
//# sourceMappingURL=data-source.js.map