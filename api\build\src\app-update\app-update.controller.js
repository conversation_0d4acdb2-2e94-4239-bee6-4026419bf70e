"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppUpdateController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const api_documentation_base_1 = require("../base/api-documentation-base");
const app_update_dto_1 = require("./app-update.dto");
let AppUpdateController = class AppUpdateController extends api_documentation_base_1.ApiDocumentationBase {
    async isAppUpdateAvailable(bodyParams) {
        console.log(bodyParams);
        const updateInfo = {
            forceUpdate: false
        };
        return updateInfo;
    }
};
exports.AppUpdateController = AppUpdateController;
__decorate([
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns if force update is true or false'
    }),
    (0, common_1.Post)(),
    (0, common_1.UsePipes)(common_1.ValidationPipe),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [app_update_dto_1.AppUpdateDto]),
    __metadata("design:returntype", Promise)
], AppUpdateController.prototype, "isAppUpdateAvailable", null);
exports.AppUpdateController = AppUpdateController = __decorate([
    (0, swagger_1.ApiTags)('App Update'),
    (0, common_1.Controller)('app-update')
], AppUpdateController);
//# sourceMappingURL=app-update.controller.js.map