"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailPhonenumberIndexing1733974875993 = void 0;
class EmailPhonenumberIndexing1733974875993 {
    async up(queryRunner) {
        await queryRunner.query(`
            CREATE INDEX idx_owner_email ON public.owners USING btree (email);
            CREATE INDEX idx_owner_phone ON public.owners USING btree (phone_number, country_code);
            CREATE INDEX idx_owner_contact ON public.owners USING btree (email,phone_number,country_code,first_name,last_name);
            CREATE INDEX idx_patient_owner ON public.patient_owners USING btree (patient_id,owner_id);
        `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
            DROP INDEX IF EXISTS idx_owner_email;
            DROP INDEX IF EXISTS idx_owner_phone;
            DROP INDEX IF EXISTS idx_owner_contact;
            DROP INDEX IF EXISTS idx_patient_owner;
        `);
    }
}
exports.EmailPhonenumberIndexing1733974875993 = EmailPhonenumberIndexing1733974875993;
//# sourceMappingURL=1733974875993-EmailPhonenumberIndexing.js.map