"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddIndexesForPatientSearch1733735381279 = void 0;
class AddIndexesForPatientSearch1733735381279 {
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE INDEX idx_appointment_doctors_appointment_id ON public.appointment_doctors USING btree (appointment_id);
      CREATE INDEX idx_appointments_date ON public.appointments USING btree (date);
      CREATE INDEX idx_appointments_patient_id ON public.appointments USING btree (patient_id);
      CREATE INDEX idx_appointments_patient_id_status ON public.appointments USING btree (patient_id, status);
      CREATE INDEX idx_patient_clinic_id ON public.patients USING btree (clinic_id);
      CREATE INDEX idx_patient_owners_patient_id ON public.patient_owners USING btree (patient_id);
    `);
    }
    async down(queryRunner) {
        await queryRunner.query(`
      DROP INDEX IF EXISTS idx_appointment_doctors_appointment_id;
      DROP INDEX IF EXISTS idx_appointments_date;
      DROP INDEX IF EXISTS idx_appointments_patient_id;
      DROP INDEX IF EXISTS idx_appointments_patient_id_status;
      DROP INDEX IF EXISTS idx_patient_clinic_id;
      DROP INDEX IF EXISTS idx_patient_owners_patient_id;
    `);
    }
}
exports.AddIndexesForPatientSearch1733735381279 = AddIndexesForPatientSearch1733735381279;
//# sourceMappingURL=1733735381279-AddIndexesForPatientSearch.js.map