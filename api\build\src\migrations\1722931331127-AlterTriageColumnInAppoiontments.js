"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AlterTriageColumnInAppoiontments1722931331127 = void 0;
class AlterTriageColumnInAppoiontments1722931331127 {
    async up(queryRunner) {
        await queryRunner.query(`
            ALTER TABLE "appointments" ALTER COLUMN "triage" DROP NOT NULL
        `);
        // await queryRunner.changeColumn("appointments", "triage", new TableColumn({
        //     name: 'triage',
        //     type: 'varchar',
        //     isNullable: true
        // }))
        await queryRunner.query(`
            ALTER TABLE "appointments" ALTER COLUMN "room_id" DROP NOT NULL
        `);
    }
    async down(queryRunner) {
        const defaultTriage = 'Low Priority';
        const defaultUUID = '00000000-0000-0000-0000-000000000000'; // May be, i should read from room table and get the default value
        const test = await queryRunner.query(`SELECT * from "appointments" where "triage" IS NULL`);
        console.log(`test=`, test);
        await queryRunner.query(`UPDATE "appointments" SET "triage" = '${defaultTriage}' WHERE "triage" IS NULL`);
        await queryRunner.query(`UPDATE "appointments" SET "room_id" = '${defaultUUID}' WHERE "room_id" IS NULL`);
        const testAfter = await queryRunner.query(`SELECT * from "appointments" where "triage" IS NULL`);
        console.log(`test after=`, testAfter);
        await queryRunner.query(`
            ALTER TABLE "appointments" ALTER COLUMN "triage" SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "appointments" ALTER COLUMN "room_id" SET NOT NULL
        `);
        // await queryRunner.changeColumn("appointments", "triage", new TableColumn({
        //     name: 'triage',
        //     type: 'varchar',
        //     isNullable: false,
        // }))
    }
}
exports.AlterTriageColumnInAppoiontments1722931331127 = AlterTriageColumnInAppoiontments1722931331127;
//# sourceMappingURL=1722931331127-AlterTriageColumnInAppoiontments.js.map