import { TemplateType } from '../entities/diagnostic-template.entity';
export declare class CreateDiagnosticNoteDto {
    labReportId: string;
    clinicId: string;
    diagnosticId?: string;
    templateId?: string;
    templateName: string;
    noteData: {
        values?: Record<string, any>;
        notes?: string;
    };
}
export declare class UpdateDiagnosticNoteDto {
    templateId?: string;
    templateName?: string;
    templateType?: 'notes' | 'table';
    noteData?: {
        notes?: string;
        values?: Record<string, any>;
    };
}
export declare class CreateTemplateDto {
    templateName: string;
    clinicId: string;
    assignedDiagnostics: Array<{
        id: string;
        name: string;
    }>;
    templateType: TemplateType;
    tableStructure?: {
        columns: Array<{
            name: string;
            type: string;
        }>;
    };
    notes?: string;
}
