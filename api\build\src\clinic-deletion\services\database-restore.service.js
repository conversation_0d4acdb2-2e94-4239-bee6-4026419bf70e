"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseRestoreService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const winston_logger_service_1 = require("../../utils/logger/winston-logger.service");
const s3_service_1 = require("../../utils/aws/s3/s3.service");
const clinic_deletion_dto_1 = require("../dto/clinic-deletion.dto");
let DatabaseRestoreService = class DatabaseRestoreService {
    constructor(dataSource, logger, s3Service) {
        this.dataSource = dataSource;
        this.logger = logger;
        this.s3Service = s3Service;
    }
    /**
     * Restore database records from backup
     */
    async restoreDatabaseRecords(backupBasePath, conflictResolution = clinic_deletion_dto_1.ConflictResolution.FAIL) {
        const startTime = Date.now();
        try {
            this.logger.log('Starting database restore', {
                backupBasePath,
                conflictResolution
            });
            // Load manifest
            const manifest = await this.loadDatabaseManifest(backupBasePath);
            const result = {
                tablesProcessed: 0,
                recordsRestored: 0,
                recordsSkipped: 0,
                conflicts: {
                    resolved: 0,
                    skipped: 0,
                    failed: 0
                },
                duration: 0
            };
            // Restore tables in correct order
            await this.dataSource.transaction(async (manager) => {
                for (const tableName of manifest.restoreOrder) {
                    const tableInfo = manifest.tables.find(t => t.tableName === tableName);
                    if (!tableInfo)
                        continue;
                    // Add detailed logging for patient_owners table
                    if (tableName === 'patient_owners') {
                        this.logger.log(`[PATIENT_OWNERS] Starting table restore`, {
                            tableName,
                            fileName: tableInfo.fileName,
                            backupBasePath,
                            conflictResolution
                        });
                    }
                    try {
                        const tableResult = await this.restoreTable(tableName, tableInfo.fileName, backupBasePath, conflictResolution, manager);
                        if (tableName === 'patient_owners') {
                            this.logger.log(`[PATIENT_OWNERS] Table restore completed`, {
                                tableName,
                                tableResult
                            });
                        }
                        result.tablesProcessed++;
                        result.recordsRestored += tableResult.recordsRestored;
                        result.recordsSkipped += tableResult.recordsSkipped;
                        result.conflicts.resolved +=
                            tableResult.conflicts.resolved;
                        result.conflicts.skipped +=
                            tableResult.conflicts.skipped;
                        result.conflicts.failed += tableResult.conflicts.failed;
                    }
                    catch (error) {
                        const errorMessage = error instanceof Error
                            ? error.message
                            : 'Unknown error';
                        if (tableName === 'patient_owners') {
                            this.logger.error(`[PATIENT_OWNERS] Table restore failed`, {
                                tableName,
                                error: errorMessage,
                                stack: error instanceof Error
                                    ? error.stack
                                    : undefined
                            });
                        }
                        this.logger.error(`Failed to restore table ${tableName}`, {
                            error: errorMessage
                        });
                        if (conflictResolution === clinic_deletion_dto_1.ConflictResolution.FAIL) {
                            throw error;
                        }
                        result.conflicts.failed++;
                    }
                }
            });
            result.duration = Date.now() - startTime;
            this.logger.log('Database restore completed', {
                tablesProcessed: result.tablesProcessed,
                recordsRestored: result.recordsRestored,
                recordsSkipped: result.recordsSkipped,
                duration: result.duration
            });
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Database restore failed', {
                error: errorMessage,
                backupBasePath
            });
            throw error;
        }
    }
    /**
     * Analyze restore conflicts before actual restore
     */
    async analyzeRestoreConflicts(backupBasePath) {
        this.logger.log('Starting restore conflict analysis', {
            backupBasePath
        });
        try {
            const manifest = await this.loadDatabaseManifest(backupBasePath);
            const conflicts = [];
            const analysisErrors = [];
            this.logger.log(`Analyzing conflicts for ${manifest.tables.length} tables`);
            for (const tableInfo of manifest.tables) {
                try {
                    this.logger.log(`Analyzing conflicts for table: ${tableInfo.tableName}`);
                    const tableConflicts = await this.analyzeTableConflicts(tableInfo.tableName, tableInfo.fileName, backupBasePath);
                    if (tableConflicts.conflictingRecords > 0) {
                        conflicts.push(tableConflicts);
                        this.logger.log(`Found ${tableConflicts.conflictingRecords} conflicts in table: ${tableInfo.tableName}`);
                    }
                    else {
                        this.logger.log(`No conflicts found in table: ${tableInfo.tableName}`);
                    }
                }
                catch (error) {
                    const errorMessage = error instanceof Error
                        ? error.message
                        : 'Unknown error';
                    this.logger.error(`Failed to analyze conflicts for table ${tableInfo.tableName}`, {
                        error: errorMessage,
                        tableName: tableInfo.tableName,
                        fileName: tableInfo.fileName
                    });
                    // Track analysis errors but don't fail the entire analysis
                    analysisErrors.push(`${tableInfo.tableName}: ${errorMessage}`);
                    // Add an error entry to conflicts to indicate analysis failed
                    conflicts.push({
                        tableName: tableInfo.tableName,
                        conflictingRecords: -1, // Special value to indicate analysis error
                        conflictType: 'primary_key',
                        details: [`Analysis failed: ${errorMessage}`]
                    });
                }
            }
            // Log summary
            const successfulAnalyses = manifest.tables.length - analysisErrors.length;
            const totalConflicts = conflicts.filter(c => c.conflictingRecords > 0).length;
            const analysisFailures = conflicts.filter(c => c.conflictingRecords === -1).length;
            this.logger.log('Restore conflict analysis completed', {
                totalTables: manifest.tables.length,
                successfulAnalyses,
                analysisFailures,
                tablesWithConflicts: totalConflicts,
                analysisErrors: analysisErrors.length > 0 ? analysisErrors : undefined
            });
            return conflicts;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Failed to analyze restore conflicts', {
                error: errorMessage,
                backupBasePath
            });
            throw error;
        }
    }
    /**
     * Load database manifest from S3
     */
    async loadDatabaseManifest(backupBasePath) {
        try {
            const manifestPath = `${backupBasePath}/database/manifest.json`;
            const manifestData = await this.s3Service.getObject(manifestPath);
            if (!manifestData || !manifestData.Body) {
                throw new Error('Manifest file not found or empty');
            }
            const manifestContent = manifestData.Body.toString();
            return JSON.parse(manifestContent);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Failed to load database manifest', {
                error: errorMessage,
                backupBasePath
            });
            throw error;
        }
    }
    /**
     * Restore a single table
     */
    async restoreTable(tableName, fileName, backupBasePath, conflictResolution, manager) {
        try {
            // Load table data from S3
            const tableDataPath = `${backupBasePath}/database/${fileName}`;
            const tableData = await this.s3Service.getObject(tableDataPath);
            if (!tableData || !tableData.Body) {
                throw new Error(`Table data file not found: ${fileName}`);
            }
            const backupData = JSON.parse(tableData.Body.toString());
            const records = backupData.records || [];
            // Add detailed logging for patient_owners table
            if (tableName === 'patient_owners') {
                this.logger.log(`[PATIENT_OWNERS] Loaded backup data`, {
                    tableName,
                    tableDataPath,
                    recordCount: records.length,
                    records: records
                });
            }
            if (records.length === 0) {
                if (tableName === 'patient_owners') {
                    this.logger.log(`[PATIENT_OWNERS] No records to restore`, {
                        tableName,
                        recordCount: 0
                    });
                }
                return {
                    recordsRestored: 0,
                    recordsSkipped: 0,
                    conflicts: { resolved: 0, skipped: 0, failed: 0 }
                };
            }
            let recordsRestored = 0;
            let recordsSkipped = 0;
            const conflicts = { resolved: 0, skipped: 0, failed: 0 };
            // Get table schema info
            const tableSchema = await this.getTableSchema(tableName, manager);
            const primaryKeys = tableSchema.primaryKeys;
            const uniqueConstraints = tableSchema.uniqueConstraints;
            for (const record of records) {
                try {
                    // Check for conflicts
                    const hasConflict = await this.checkRecordConflicts(tableName, record, primaryKeys, uniqueConstraints.flat(), // Flatten unique constraints array
                    manager);
                    if (hasConflict) {
                        if (conflictResolution === clinic_deletion_dto_1.ConflictResolution.SKIP) {
                            recordsSkipped++;
                            conflicts.skipped++;
                            continue;
                        }
                        else if (conflictResolution === clinic_deletion_dto_1.ConflictResolution.OVERWRITE) {
                            // Delete existing record first
                            await this.deleteConflictingRecord(tableName, record, primaryKeys, manager);
                            conflicts.resolved++;
                        }
                        else {
                            // FAIL mode
                            conflicts.failed++;
                            throw new Error(`Conflict detected for record in ${tableName}`);
                        }
                    }
                    // Insert record
                    await this.insertRecord(tableName, record, manager);
                    recordsRestored++;
                }
                catch (error) {
                    if (conflictResolution === clinic_deletion_dto_1.ConflictResolution.FAIL) {
                        throw error;
                    }
                    recordsSkipped++;
                    conflicts.failed++;
                    this.logger.warn(`Skipped record in ${tableName}`, {
                        error: error instanceof Error
                            ? error.message
                            : 'Unknown error'
                    });
                }
            }
            this.logger.log(`Restored table ${tableName}`, {
                recordsRestored,
                recordsSkipped,
                conflicts
            });
            return { recordsRestored, recordsSkipped, conflicts };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error(`Failed to restore table ${tableName}`, {
                error: errorMessage
            });
            throw error;
        }
    }
    /**
     * Analyze conflicts for a single table
     */
    async analyzeTableConflicts(tableName, fileName, backupBasePath) {
        this.logger.log(`Starting conflict analysis for table: ${tableName}`, {
            fileName,
            backupBasePath
        });
        try {
            // Load the table's backup JSON file from S3
            const tableDataPath = `${backupBasePath}/database/${fileName}`;
            this.logger.log(`Loading backup data from: ${tableDataPath}`);
            const tableData = await this.s3Service.getObject(tableDataPath);
            if (!tableData || !tableData.Body) {
                throw new Error(`Table data file not found: ${fileName} at path: ${tableDataPath}`);
            }
            const backupData = JSON.parse(tableData.Body.toString());
            const records = backupData.records || [];
            this.logger.log(`Loaded ${records.length} records from backup for table: ${tableName}`);
            if (records.length === 0) {
                this.logger.log(`No records to analyze for table: ${tableName}`);
                return {
                    tableName,
                    conflictingRecords: 0,
                    conflictType: 'primary_key',
                    details: ['No records in backup to analyze']
                };
            }
            // Get table schema info to know primary keys and unique constraints
            const manager = this.dataSource.createEntityManager();
            this.logger.log(`Getting schema information for table: ${tableName}`);
            const tableSchema = await this.getTableSchema(tableName, manager);
            const primaryKeys = tableSchema.primaryKeys;
            const uniqueConstraints = tableSchema.uniqueConstraints;
            this.logger.log(`Schema analysis for ${tableName}:`, {
                primaryKeys,
                uniqueConstraintsCount: uniqueConstraints.length
            });
            let conflictingRecords = 0;
            let primaryKeyConflicts = 0;
            let uniqueConstraintConflicts = 0;
            const conflictDetails = [];
            // Check each record for conflicts (limit to first 1000 records for performance)
            const recordsToCheck = records.slice(0, 1000);
            this.logger.log(`Checking ${recordsToCheck.length} records for conflicts in table: ${tableName}`);
            for (let i = 0; i < recordsToCheck.length; i++) {
                const record = recordsToCheck[i];
                // Check for primary key conflicts
                const hasPkConflict = await this.checkPrimaryKeyConflict(tableName, record, primaryKeys, manager);
                // Check for unique constraint conflicts
                const hasUniqueConflict = await this.checkUniqueConstraintConflicts(tableName, record, uniqueConstraints, manager);
                if (hasPkConflict || hasUniqueConflict) {
                    conflictingRecords++;
                    if (hasPkConflict) {
                        primaryKeyConflicts++;
                        const pkValues = primaryKeys
                            .map((pk) => `${pk}=${record[pk]}`)
                            .join(', ');
                        conflictDetails.push(`PK conflict: ${tableName} (${pkValues})`);
                    }
                    if (hasUniqueConflict) {
                        uniqueConstraintConflicts++;
                        conflictDetails.push(`Unique constraint conflict in ${tableName} for record ${i + 1}`);
                    }
                    // Limit the number of details to avoid excessive output
                    if (conflictDetails.length >= 50) {
                        conflictDetails.push(`... and ${conflictingRecords - conflictDetails.length + 1} more conflicts`);
                        break;
                    }
                }
                // Log progress for large tables
                if (i > 0 && i % 100 === 0) {
                    this.logger.log(`Analyzed ${i} records for ${tableName}, found ${conflictingRecords} conflicts so far`);
                }
            }
            // If we checked fewer records than total, add a note
            if (records.length > recordsToCheck.length) {
                conflictDetails.push(`Note: Only checked first ${recordsToCheck.length} of ${records.length} total records`);
            }
            // Determine the primary conflict type
            let conflictType = 'primary_key';
            if (uniqueConstraintConflicts > primaryKeyConflicts) {
                conflictType = 'unique_constraint';
            }
            this.logger.log(`Conflict analysis completed for ${tableName}:`, {
                totalRecords: records.length,
                recordsChecked: recordsToCheck.length,
                conflictingRecords,
                primaryKeyConflicts,
                uniqueConstraintConflicts,
                conflictType
            });
            return {
                tableName,
                conflictingRecords,
                conflictType,
                details: conflictDetails
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error(`CRITICAL: Failed to analyze conflicts for table ${tableName}`, {
                error: errorMessage,
                fileName,
                backupBasePath,
                stack: error instanceof Error ? error.stack : undefined
            });
            // Don't mask the error - throw it so the caller knows analysis failed
            // This prevents the misleading "No conflicts detected" message
            throw new Error(`Conflict analysis failed for table ${tableName}: ${errorMessage}`);
        }
    }
    /**
     * Check for primary key conflicts
     */
    async checkPrimaryKeyConflict(tableName, record, primaryKeys, manager) {
        if (primaryKeys.length === 0) {
            return false;
        }
        // Build WHERE clause for all primary key columns
        const conditions = primaryKeys
            .map((pkCol, index) => `"${pkCol}" = $${index + 1}`)
            .join(' AND ');
        const pkValues = primaryKeys.map(pkCol => record[pkCol]);
        // Skip if any primary key value is missing
        if (pkValues.some(val => val === undefined)) {
            return false;
        }
        const query = `SELECT 1 FROM "${tableName}" WHERE ${conditions} LIMIT 1`;
        const existing = await manager.query(query, pkValues);
        return existing.length > 0;
    }
    /**
     * Check for unique constraint conflicts
     */
    async checkUniqueConstraintConflicts(tableName, record, uniqueConstraints, manager) {
        // Check each unique constraint
        for (const constraint of uniqueConstraints) {
            // Build WHERE clause for all columns in this constraint
            const conditions = constraint
                .map((col, index) => `"${col}" = $${index + 1}`)
                .join(' AND ');
            const values = constraint.map(col => record[col]);
            // Skip if any value is missing
            if (values.some(val => val === undefined)) {
                continue;
            }
            const query = `SELECT 1 FROM "${tableName}" WHERE ${conditions} LIMIT 1`;
            const existing = await manager.query(query, values);
            if (existing.length > 0) {
                return true;
            }
        }
        return false;
    }
    /**
     * Get table schema information
     */
    async getTableSchema(tableName, manager) {
        this.logger.log(`Getting schema information for table: ${tableName}`);
        try {
            // Query database schema to get primary keys and unique constraints
            // Use both table_schema and table_name for better PostgreSQL compatibility
            const primaryKeyQuery = `
				SELECT kcu.column_name
				FROM information_schema.table_constraints tc
				JOIN information_schema.key_column_usage kcu
					ON tc.constraint_name = kcu.constraint_name
					AND tc.table_schema = kcu.table_schema
				WHERE LOWER(tc.table_name) = LOWER($1)
					AND tc.constraint_type = 'PRIMARY KEY'
					AND tc.table_schema = COALESCE($2, 'public')
				ORDER BY kcu.ordinal_position;
			`;
            const uniqueConstraintQuery = `
				SELECT tc.constraint_name, kcu.column_name
				FROM information_schema.table_constraints tc
				JOIN information_schema.key_column_usage kcu
					ON tc.constraint_name = kcu.constraint_name
					AND tc.table_schema = kcu.table_schema
				WHERE LOWER(tc.table_name) = LOWER($1)
					AND tc.constraint_type = 'UNIQUE'
					AND tc.table_schema = COALESCE($2, 'public')
				ORDER BY tc.constraint_name, kcu.ordinal_position;
			`;
            // Try to determine the schema - default to 'public' for PostgreSQL
            const schemaName = 'public';
            this.logger.log(`Querying schema for table: ${tableName} in schema: ${schemaName}`);
            const primaryKeyResult = await manager.query(primaryKeyQuery, [
                tableName,
                schemaName
            ]);
            const uniqueConstraintResult = await manager.query(uniqueConstraintQuery, [tableName, schemaName]);
            this.logger.log(`Schema query results for ${tableName}:`, {
                primaryKeyCount: primaryKeyResult.length,
                uniqueConstraintCount: uniqueConstraintResult.length
            });
            const primaryKeys = primaryKeyResult.map((row) => row.column_name);
            // Group unique constraints by constraint name
            const uniqueConstraints = {};
            for (const row of uniqueConstraintResult) {
                if (!uniqueConstraints[row.constraint_name]) {
                    uniqueConstraints[row.constraint_name] = [];
                }
                uniqueConstraints[row.constraint_name].push(row.column_name);
            }
            // Handle tables without primary keys more gracefully
            if (primaryKeys.length === 0) {
                this.logger.warn(`No primary keys found for table ${tableName}`, {
                    tableName,
                    schemaName,
                    primaryKeyResult,
                    message: 'Table may not have primary keys or may not exist'
                });
                // For conflict analysis, we can still proceed with unique constraints
                // but warn that primary key conflicts cannot be detected
                this.logger.warn(`Proceeding with conflict analysis for ${tableName} without primary key detection`);
            }
            const result = {
                primaryKeys,
                uniqueConstraints: Object.values(uniqueConstraints)
            };
            this.logger.log(`Schema analysis completed for ${tableName}:`, result);
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error(`Schema introspection failed for table ${tableName}`, {
                error: errorMessage,
                tableName,
                stack: error instanceof Error ? error.stack : undefined
            });
            // For conflict analysis, we can try to proceed with basic assumptions
            // but log the issue clearly
            this.logger.error(`Using fallback schema assumptions for ${tableName} due to introspection failure`);
            throw new Error(`Schema introspection failed for table ${tableName}: ${errorMessage}`);
        }
    }
    /**
     * Check if record has conflicts (primary key or unique constraints)
     */
    async checkRecordConflicts(tableName, record, primaryKeys, uniqueConstraints, manager) {
        // Check primary key conflicts
        if (primaryKeys.length > 0) {
            // Build WHERE clause for all primary key columns
            const conditions = primaryKeys
                .map((pkCol, index) => `"${pkCol}" = $${index + 1}`)
                .join(' AND ');
            const pkValues = primaryKeys.map(pkCol => record[pkCol]);
            // Skip if any primary key value is missing
            if (pkValues.some(val => val === undefined)) {
                return false;
            }
            const query = `SELECT 1 FROM "${tableName}" WHERE ${conditions} LIMIT 1`;
            const existing = await manager.query(query, pkValues);
            if (existing.length > 0) {
                return true;
            }
        }
        // Check unique constraint conflicts (if provided as flattened array)
        if (uniqueConstraints.length > 0) {
            // This is a simplified check - in practice, unique constraints should be grouped
            // but this method receives a flattened array from the caller
            for (const constraintCol of uniqueConstraints) {
                if (record[constraintCol] !== undefined) {
                    const query = `SELECT 1 FROM "${tableName}" WHERE "${constraintCol}" = $1 LIMIT 1`;
                    const existing = await manager.query(query, [
                        record[constraintCol]
                    ]);
                    if (existing.length > 0) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
    /**
     * Delete conflicting record
     */
    async deleteConflictingRecord(tableName, record, primaryKeys, manager) {
        if (primaryKeys.length > 0) {
            // Build WHERE clause for all primary key columns
            const conditions = primaryKeys
                .map((pkCol, index) => `"${pkCol}" = $${index + 1}`)
                .join(' AND ');
            const pkValues = primaryKeys.map(pkCol => record[pkCol]);
            // Skip if any primary key value is missing
            if (pkValues.some(val => val === undefined)) {
                this.logger.warn(`Cannot delete record from ${tableName} - missing primary key values`, {
                    tableName,
                    primaryKeys,
                    recordKeys: Object.keys(record)
                });
                return;
            }
            const query = `DELETE FROM "${tableName}" WHERE ${conditions}`;
            await manager.query(query, pkValues);
        }
    }
    /**
     * Insert record into table
     */
    async insertRecord(tableName, record, manager) {
        const columns = Object.keys(record);
        const values = await this.prepareValuesForInsertion(tableName, record, manager);
        const placeholders = values
            .map((_, index) => `$${index + 1}`)
            .join(', ');
        const escapedColumns = columns.map(col => `"${col}"`).join(', ');
        const sql = `INSERT INTO "${tableName}" (${escapedColumns}) VALUES (${placeholders})`;
        await manager.query(sql, values);
    }
    /**
     * Prepare values for insertion, handling JSON columns properly
     */
    async prepareValuesForInsertion(tableName, record, manager) {
        const columns = Object.keys(record);
        const values = Object.values(record);
        // Get column information to identify JSON columns
        const columnInfo = await this.getColumnInfo(tableName, manager);
        const jsonColumns = columnInfo
            .filter(col => col.data_type === 'json' || col.data_type === 'jsonb')
            .map(col => col.column_name);
        // Process values, converting objects to JSON strings for JSON columns
        const processedValues = values.map((value, index) => {
            const columnName = columns[index];
            if (jsonColumns.includes(columnName) &&
                value !== null &&
                typeof value === 'object') {
                // Convert JavaScript object to JSON string for JSON columns
                return JSON.stringify(value);
            }
            return value;
        });
        return processedValues;
    }
    /**
     * Get column information for a table
     */
    async getColumnInfo(tableName, manager) {
        const query = `
			SELECT column_name, data_type
			FROM information_schema.columns
			WHERE table_name = $1
			AND table_schema = 'public'
			ORDER BY ordinal_position;
		`;
        return await manager.query(query, [tableName]);
    }
};
exports.DatabaseRestoreService = DatabaseRestoreService;
exports.DatabaseRestoreService = DatabaseRestoreService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource,
        winston_logger_service_1.WinstonLogger,
        s3_service_1.S3Service])
], DatabaseRestoreService);
//# sourceMappingURL=database-restore.service.js.map