"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatientRemindersTable1734435510113 = void 0;
const typeorm_1 = require("typeorm");
class PatientRemindersTable1734435510113 {
    async up(queryRunner) {
        // Create patient_reminders table
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'patient_reminders',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'patient_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'clinic_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'title',
                    type: 'varchar',
                    length: '255',
                    isNullable: false
                },
                {
                    name: 'inventory_item_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'inventory_type',
                    type: 'varchar',
                    length: '50',
                    isNullable: false
                },
                {
                    name: 'due_date',
                    type: 'timestamp',
                    isNullable: false
                },
                {
                    name: 'status',
                    type: 'varchar',
                    length: '20',
                    default: "'PENDING'",
                    isNullable: false
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'completed_at',
                    type: 'timestamp',
                    isNullable: true
                },
                {
                    name: 'is_recurring',
                    type: 'boolean',
                    default: false
                },
                {
                    name: 'recurrence_frequency',
                    type: 'integer',
                    isNullable: true
                },
                {
                    name: 'recurrence_unit',
                    type: 'varchar',
                    length: '20',
                    isNullable: true
                },
                {
                    name: 'recurrence_end_date',
                    type: 'timestamp',
                    isNullable: true
                },
                {
                    name: 'recurrence_end_occurrences',
                    type: 'integer',
                    isNullable: true
                },
                {
                    name: 'parent_reminder_id',
                    type: 'uuid',
                    isNullable: true
                }
            ]
        }), true);
        // Create patient_reminder_history table
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'patient_reminder_history',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'reminder_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'previous_status',
                    type: 'varchar',
                    length: '20',
                    isNullable: true
                },
                {
                    name: 'new_status',
                    type: 'varchar',
                    length: '20',
                    isNullable: true
                },
                {
                    name: 'changed_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP'
                },
                {
                    name: 'changed_by',
                    type: 'uuid',
                    isNullable: true
                }
            ]
        }), true);
        // Add foreign keys for patient_reminders table
        await queryRunner.createForeignKey('patient_reminders', new typeorm_1.TableForeignKey({
            columnNames: ['patient_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'patients',
            onDelete: 'CASCADE'
        }));
        await queryRunner.createForeignKey('patient_reminders', new typeorm_1.TableForeignKey({
            columnNames: ['clinic_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'clinics',
            onDelete: 'CASCADE'
        }));
        await queryRunner.createForeignKey('patient_reminders', new typeorm_1.TableForeignKey({
            columnNames: ['created_by'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users'
        }));
        await queryRunner.createForeignKey('patient_reminders', new typeorm_1.TableForeignKey({
            columnNames: ['updated_by'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users'
        }));
        await queryRunner.createForeignKey('patient_reminders', new typeorm_1.TableForeignKey({
            columnNames: ['parent_reminder_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'patient_reminders'
        }));
        // Add foreign keys for patient_reminder_history table
        await queryRunner.createForeignKey('patient_reminder_history', new typeorm_1.TableForeignKey({
            columnNames: ['reminder_id'],
            referencedColumnNames: ['id'],
            referencedTableName: 'patient_reminders',
            onDelete: 'CASCADE'
        }));
        await queryRunner.createForeignKey('patient_reminder_history', new typeorm_1.TableForeignKey({
            columnNames: ['changed_by'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users'
        }));
    }
    async down(queryRunner) {
        // Drop patient_reminder_history table and its foreign keys
        await queryRunner.dropTable('patient_reminder_history', true, true);
        // Drop patient_reminders table and its foreign keys
        await queryRunner.dropTable('patient_reminders', true, true);
    }
}
exports.PatientRemindersTable1734435510113 = PatientRemindersTable1734435510113;
//# sourceMappingURL=1734435510113-PatientRemindersTable.js.map