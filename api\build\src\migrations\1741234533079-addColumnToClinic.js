"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addColumnToClinic1741234533079 = void 0;
const typeorm_1 = require("typeorm");
class addColumnToClinic1741234533079 {
    async up(queryRunner) {
        const hasColumn = await queryRunner.hasColumn('clinics', 'custom_rule');
        if (hasColumn) {
            await queryRunner.dropColumn('clinics', 'custom_rule');
        }
        await queryRunner.addColumn('clinics', new typeorm_1.TableColumn({
            name: 'custom_rule',
            type: 'jsonb',
            default: `'{"patientLastNameAsOwnerLastName": true}'::jsonb`,
            isNullable: true
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('clinics', 'custom_rule');
    }
}
exports.addColumnToClinic1741234533079 = addColumnToClinic1741234533079;
//# sourceMappingURL=1741234533079-addColumnToClinic.js.map