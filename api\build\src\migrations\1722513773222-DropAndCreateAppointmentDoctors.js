"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DropAndCreateAppointmentDoctors1722497685201 = void 0;
const typeorm_1 = require("typeorm");
class DropAndCreateAppointmentDoctors1722497685201 {
    async up(queryRunner) {
        await queryRunner.dropTable('appointment_doctors', true, true, true);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'appointment_doctors',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()'
                },
                {
                    name: 'appointment_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'doctor_id',
                    type: 'uuid',
                    isNullable: false
                },
                {
                    name: 'created_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'updated_by',
                    type: 'uuid',
                    isNullable: true
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'now()'
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'now()',
                    onUpdate: 'now()'
                }
            ]
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('appointment_doctors', true, true, true);
    }
}
exports.DropAndCreateAppointmentDoctors1722497685201 = DropAndCreateAppointmentDoctors1722497685201;
//# sourceMappingURL=1722513773222-DropAndCreateAppointmentDoctors.js.map