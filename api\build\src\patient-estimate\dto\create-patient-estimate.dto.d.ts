declare class TreatmentPlanItemDto {
    itemId: string;
    itemType: string;
    itemName: string;
    unitPrice: number;
    quantity: number;
    itemTotal: number;
}
export declare class CreatePatientEstimateDto {
    clinicId: string;
    patientId: string;
    doctorId?: string;
    estimateTotal: number;
    signatureRequired: boolean;
    treatmentPlan: TreatmentPlanItemDto[];
    urlPath?: string;
}
export {};
