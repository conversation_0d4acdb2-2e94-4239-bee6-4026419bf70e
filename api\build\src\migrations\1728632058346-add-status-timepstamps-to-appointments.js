"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddStatusTimepstampsToAppointments1728632058346 = void 0;
const typeorm_1 = require("typeorm");
class AddStatusTimepstampsToAppointments1728632058346 {
    async up(queryRunner) {
        await queryRunner.addColumns('appointments', [
            new typeorm_1.TableColumn({
                name: 'checkin_time',
                type: 'timestamp',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'receiving_care_time',
                type: 'timestamp',
                isNullable: true
            }),
            new typeorm_1.TableColumn({
                name: 'checkout_time',
                type: 'timestamp',
                isNullable: true
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('appointments', 'checkin_time');
        await queryRunner.dropColumn('appointments', 'receiving_care_time');
        await queryRunner.dropColumn('appointments', 'checkout_time');
    }
}
exports.AddStatusTimepstampsToAppointments1728632058346 = AddStatusTimepstampsToAppointments1728632058346;
//# sourceMappingURL=1728632058346-add-status-timepstamps-to-appointments.js.map