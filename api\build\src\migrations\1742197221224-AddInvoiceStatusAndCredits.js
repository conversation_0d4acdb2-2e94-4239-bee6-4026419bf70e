"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddInvoiceStatusAndCredits1742197221224 = void 0;
const enum_invoice_types_1 = require("../invoice/enums/enum-invoice-types");
class AddInvoiceStatusAndCredits1742197221224 {
    generateRandomString(length) {
        const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        return Array.from({ length }, () => characters.charAt(Math.floor(Math.random() * characters.length))).join('');
    }
    async up(queryRunner) {
        // Create invoice status enum
        await queryRunner.query(`
            CREATE TYPE "invoice_status_enum" AS ENUM (
                'unknown',
                'pending',
                'partially_paid',
                'fully_paid'
            )
        `);
        // Add new columns to invoices table
        await queryRunner.query(`
            ALTER TABLE "invoices" 
            ADD COLUMN "status" "invoice_status_enum" NOT NULL DEFAULT 'unknown',
            ADD COLUMN "balance_due" decimal(10,2) NOT NULL DEFAULT 0,
            ADD COLUMN "paid_amount" decimal(10,2) NOT NULL DEFAULT 0
        `);
        // Copy existing amount_paid values to the new paid_amount column
        await queryRunner.query(`
            UPDATE "invoices" 
            SET "paid_amount" = LEAST(COALESCE("amount_paid", 0), 99999999.99)
        `);
        // Calculate balance_due for all invoices (invoice_amount - paid_amount)
        await queryRunner.query(`
            UPDATE "invoices" 
            SET "balance_due" = 0
        `);
        // Update invoice status based on payment status
        await queryRunner.query(`
            UPDATE "invoices"
            SET "status" = 'unknown'::invoice_status_enum
        `);
        // Add fields to payment_details table
        await queryRunner.query(`
            ALTER TABLE "payment_details" 
            ADD COLUMN "is_credit_used" boolean NOT NULL DEFAULT false,
            ADD COLUMN "credit_amount_used" decimal(10,2) NOT NULL DEFAULT 0,
            ADD COLUMN "is_credit_added" boolean NOT NULL DEFAULT false,
            ADD COLUMN "credit_amount_added" decimal(10,2) NOT NULL DEFAULT 0
        `);
        // Add credits field to owner_brands table
        await queryRunner.query(`
            ALTER TABLE "owner_brands" 
            ADD COLUMN "owner_credits" decimal(10,2) NOT NULL DEFAULT 0
        `);
        // Create credit transaction type enum
        await queryRunner.query(`
            CREATE TYPE "credit_transaction_type_enum" AS ENUM (
                'ADD',
                'USE'
            )
        `);
        // Create credit_transactions table
        await queryRunner.query(`
            CREATE TABLE "credit_transactions" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "owner_id" uuid NOT NULL,
                "amount" decimal(10,2) NOT NULL,
                "transaction_type" "credit_transaction_type_enum" NOT NULL,
                "description" text NULL,
                "invoice_id" uuid NULL,
                "payment_detail_id" uuid NULL,
                "clinic_id" uuid NOT NULL,
                "brand_id" uuid NOT NULL,
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "created_by" uuid NULL,
                "updated_by" uuid NULL,
                "metadata" jsonb NULL,
                PRIMARY KEY ("id")
            )
        `);
        // Add foreign key constraints
        await queryRunner.query(`
            ALTER TABLE "credit_transactions" 
            ADD CONSTRAINT "FK_credit_transactions_owner_id" 
            FOREIGN KEY ("owner_id") REFERENCES "owner_brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "credit_transactions" 
            ADD CONSTRAINT "FK_credit_transactions_invoice_id" 
            FOREIGN KEY ("invoice_id") REFERENCES "invoices"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        await queryRunner.query(`
            ALTER TABLE "credit_transactions" 
            ADD CONSTRAINT "FK_credit_transactions_payment_detail_id" 
            FOREIGN KEY ("payment_detail_id") REFERENCES "payment_details"("id") ON DELETE NO ACTION ON UPDATE NO ACTION
        `);
        // Create indexes for performance
        await queryRunner.query(`
            CREATE INDEX "IDX_credit_transactions_owner_id" ON "credit_transactions" ("owner_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_credit_transactions_invoice_id" ON "credit_transactions" ("invoice_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_credit_transactions_payment_detail_id" ON "credit_transactions" ("payment_detail_id")
        `);
        // Handle old balances
        // Get all owners with non-zero balance
        const owners = await queryRunner.query(`
            SELECT DISTINCT ON (ob.id)
                ob.id as owner_id,
                ob.owner_balance,
                po.clinic_id,
                po.brand_id,
                po.patient_id
            FROM owner_brands ob
            INNER JOIN patient_owners po ON po.owner_id = ob.id
            WHERE ob.owner_balance != 0
            AND po.is_primary = true
            AND po.clinic_id IS NOT NULL
            AND po.brand_id IS NOT NULL
            AND po.patient_id IS NOT NULL
        `);
        // Process each owner
        for (const owner of owners) {
            if (owner.owner_balance > 0) {
                // Move positive balance to credits
                await queryRunner.query(`
                    UPDATE owner_brands 
                    SET 
                        owner_credits = owner_credits + $1,
                        owner_balance = 0
                    WHERE id = $2
                `, [owner.owner_balance, owner.owner_id]);
                // Create a credit transaction record
                await queryRunner.query(`
                    INSERT INTO credit_transactions (
                        owner_id,
                        amount,
                        transaction_type,
                        description,
                        clinic_id,
                        brand_id,
                        metadata
                    ) VALUES (
                        $1, $2, 'ADD', 'Credit added from previous positive balance',
                        $3, $4, '{"source": "old_balance_migration"}'
                    )
                `, [
                    owner.owner_id,
                    owner.owner_balance,
                    owner.clinic_id,
                    owner.brand_id
                ]);
            }
            else if (owner.owner_balance < 0 && owner.patient_id) {
                // Create a cart for the negative balance
                const cartResult = await queryRunner.query(`
                    INSERT INTO carts (
                        appointment_id,
                        created_at,
                        updated_at
                    ) VALUES (
                        NULL,
                        NOW(),
                        NOW()
                    ) RETURNING id
                `);
                const cartId = cartResult[0].id;
                // Generate unique reference IDs for both invoice and payment
                const invoiceRefString = this.generateRandomString(6);
                const paymentRefString = this.generateRandomString(6);
                const invoiceReferenceAlphaId = `${invoiceRefString}`;
                const paymentReferenceAlphaId = `${paymentRefString}`;
                // Check if reference IDs already exist
                const existingRefs = await queryRunner.query(`
                    SELECT id FROM payment_details WHERE reference_alpha_id IN ($1, $2)
                    UNION
                    SELECT id FROM invoices WHERE reference_alpha_id IN ($1, $2)
                    `, [invoiceReferenceAlphaId, paymentReferenceAlphaId]);
                // If either exists, generate new ones
                if (existingRefs.length > 0) {
                    continue; // Skip this iteration and try again
                }
                // Create an invoice for the negative balance
                const absBalance = Math.abs(owner.owner_balance);
                const invoiceResult = await queryRunner.query(`
                    INSERT INTO invoices (
                        cart_id,
                        patient_id,
                        clinic_id,
                        brand_id,
                        owner_id,
                        discount,
                        total_price,
                        total_discount,
                        price_after_discount,
                        total_tax,
                        total_credit,
                        amount_payable,
                        invoice_amount,
                        amount_paid,
                        payment_mode,
                        invoice_type,
                        status,
                        balance_due,
                        paid_amount,
                        details,
                        metadata,
                        reference_alpha_id,
                        created_at,
                        updated_at
                    ) VALUES (
                        $1, $2, $3, $4, $5,
                        0, $6, 0, $6, 0, 0,
                        $6, $6, 0, 'pending',
                        $7, 'pending', $6, 0,
                        '[]',
                        '{"source": "old_balance_migration", "isOldBalanceInvoice": true}',
                        $8,
                        NOW(),
                        NOW()
                    ) RETURNING id
                `, [
                    cartId,
                    owner.patient_id,
                    owner.clinic_id,
                    owner.brand_id,
                    owner.owner_id,
                    absBalance,
                    enum_invoice_types_1.EnumInvoiceType.Invoice,
                    invoiceReferenceAlphaId
                ]);
                // Create payment details entry with amount_received as 0
                await queryRunner.query(`
                    INSERT INTO payment_details (
                        invoice_id,
                        owner_id,
                        patient_id,
                        clinic_id,
                        brand_id,
                        type,
                        payment_type,
                        amount_payable,
                        main_balance,
                        transaction_amount,
                        previous_balance,
                        amount,
                        is_credit_used,
                        credit_amount_used,
                        is_credit_added,
                        credit_amount_added,
                        showinledger,
                        showininvoice,
                        reference_alpha_id,
                        created_at,
                        updated_at
                    ) VALUES (
                        $1, $2, $3, $4, $5,
                        'Invoice', 'Cash',
                        $6, $6, 0, 0, 0,
                        false, 0, false, 0,
                        true, true,
                        $7,
                        NOW(),
                        NOW()
                    )
                `, [
                    invoiceResult[0].id,
                    owner.owner_id,
                    owner.patient_id,
                    owner.clinic_id,
                    owner.brand_id,
                    absBalance,
                    paymentReferenceAlphaId
                ]);
                // Reset owner balance to 0
                await queryRunner.query(`
                    UPDATE owner_brands 
                    SET owner_balance = 0
                    WHERE id = $1
                `, [owner.owner_id]);
            }
        }
    }
    async down(queryRunner) {
        // First restore the old balances
        // 1. Get all invoices created from old balances
        const oldBalanceInvoices = await queryRunner.query(`
            SELECT 
                i.id,
                i.owner_id,
                i.invoice_amount
            FROM invoices i
            WHERE i.metadata->>'isOldBalanceInvoice' = 'true'
        `);
        // 2. Restore negative balances from these invoices
        for (const invoice of oldBalanceInvoices) {
            await queryRunner.query(`
                UPDATE owner_brands
                SET owner_balance = owner_balance - $1
                WHERE id = $2
            `, [invoice.invoice_amount, invoice.owner_id]);
            // Delete the invoice
            await queryRunner.query(`
                DELETE FROM invoices WHERE id = $1
            `, [invoice.id]);
        }
        // 3. Get all credit transactions from old balances
        const oldBalanceCredits = await queryRunner.query(`
            SELECT 
                ct.owner_id,
                ct.amount
            FROM credit_transactions ct
            WHERE ct.metadata->>'source' = 'old_balance_migration'
        `);
        // 4. Restore positive balances from credit transactions
        for (const credit of oldBalanceCredits) {
            await queryRunner.query(`
                UPDATE owner_brands
                SET 
                    owner_credits = owner_credits - $1,
                    owner_balance = owner_balance + $1
                WHERE id = $2
            `, [credit.amount, credit.owner_id]);
            // Delete the credit transaction
            await queryRunner.query(`
                DELETE FROM credit_transactions 
                WHERE owner_id = $1 
                AND metadata->>'source' = 'old_balance_migration'
            `, [credit.owner_id]);
        }
        // Drop indexes
        await queryRunner.query(`DROP INDEX "IDX_credit_transactions_payment_detail_id"`);
        await queryRunner.query(`DROP INDEX "IDX_credit_transactions_invoice_id"`);
        await queryRunner.query(`DROP INDEX "IDX_credit_transactions_owner_id"`);
        // Drop foreign key constraints
        await queryRunner.query(`ALTER TABLE "credit_transactions" DROP CONSTRAINT "FK_credit_transactions_payment_detail_id"`);
        await queryRunner.query(`ALTER TABLE "credit_transactions" DROP CONSTRAINT "FK_credit_transactions_invoice_id"`);
        await queryRunner.query(`ALTER TABLE "credit_transactions" DROP CONSTRAINT "FK_credit_transactions_owner_id"`);
        // Drop credit_transactions table
        await queryRunner.query(`DROP TABLE "credit_transactions"`);
        // Drop credit transaction type enum
        await queryRunner.query(`DROP TYPE "credit_transaction_type_enum"`);
        // Remove owner_credits column from owner_brands
        await queryRunner.query(`ALTER TABLE "owner_brands" DROP COLUMN "owner_credits"`);
        // Remove columns from payment_details
        await queryRunner.query(`
            ALTER TABLE "payment_details" 
            DROP COLUMN "is_credit_used",
            DROP COLUMN "credit_amount_used",
            DROP COLUMN "is_credit_added",
            DROP COLUMN "credit_amount_added"
        `);
        // Remove columns from invoices
        await queryRunner.query(`
            ALTER TABLE "invoices" 
            DROP COLUMN "status",
            DROP COLUMN "balance_due",
            DROP COLUMN "paid_amount"
        `);
        // Drop invoice status enum
        await queryRunner.query(`DROP TYPE "invoice_status_enum"`);
    }
}
exports.AddInvoiceStatusAndCredits1742197221224 = AddInvoiceStatusAndCredits1742197221224;
//# sourceMappingURL=1742197221224-AddInvoiceStatusAndCredits.js.map