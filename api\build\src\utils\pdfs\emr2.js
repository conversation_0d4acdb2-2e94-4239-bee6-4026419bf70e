"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenerateEMRHtml1 = void 0;
const appointment_detail_object_type_1 = require("../common/appointment-detail-object-type");
const GenerateEMRHtml1 = async ({ visitDate, patientName, clientName, weight, doctorNames, reasonForVisit, subjective, objective, assessmentConditions, assessmentNotes, treatmentPlan, vitalSigns, physicalExam, treatmentNotes, diagnosticsList, ultrasoundExam, clinicName, clinicAddress, clinicPhone, clinicEmail, clinicWebsite, breed, ownerEmail, ownerPhone, color, gender, dob, age }) => {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
    // Fixed type safety for diagnosticsList access
    const diagnosticNotes = (_b = (_a = diagnosticsList === null || diagnosticsList === void 0 ? void 0 : diagnosticsList[1]) === null || _a === void 0 ? void 0 : _a.diagnosticNotes) !== null && _b !== void 0 ? _b : [];
    const firstDiagnosticData = (_f = (_e = (_d = (_c = diagnosticsList === null || diagnosticsList === void 0 ? void 0 : diagnosticsList[0]) === null || _c === void 0 ? void 0 : _c.diagnosticNotes) === null || _d === void 0 ? void 0 : _d[0]) === null || _e === void 0 ? void 0 : _e.data) !== null && _f !== void 0 ? _f : null;
    let bodyMapUrlsAndImages = [];
    if (((_g = objective === null || objective === void 0 ? void 0 : objective.bodyMaps) === null || _g === void 0 ? void 0 : _g.length) > 0) {
        // Added error handling for Promise.all
        try {
            bodyMapUrlsAndImages = await Promise.all(objective.bodyMaps.map(async (bodyMap) => {
                var _a;
                const imageUrl = await (0, appointment_detail_object_type_1.getViewSignedURlForBodyMap)((_a = bodyMap.type) === null || _a === void 0 ? void 0 : _a.label);
                return {
                    url: imageUrl,
                    image: bodyMap.image,
                    notes: bodyMap.notes
                };
            }));
        }
        catch (error) {
            console.error('Error loading body maps:', error);
            bodyMapUrlsAndImages = [];
        }
    }
    const renderDiagnosticNotes = (notes) => {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
        if (!(notes === null || notes === void 0 ? void 0 : notes.templateType))
            return '';
        // Added null checks and default values
        const templateName = (_b = (_a = notes.data) === null || _a === void 0 ? void 0 : _a.templateName) !== null && _b !== void 0 ? _b : '';
        const noteContent = notes.templateType === 'notes'
            ? `
                <div class="diagnostic-note">
                    <div class="template-name">${templateName}</div>
                    <div class="note-content">${(_d = (_c = notes.data) === null || _c === void 0 ? void 0 : _c.notes) !== null && _d !== void 0 ? _d : ''}</div>
                </div>
            `
            : `
                <div class="diagnostic-table-wrapper">
                    <div class="template-name">${templateName}</div>
                    <table class="diagnostic-table">
                        <thead>
                            <tr>
                                ${(_h = (_g = (_f = (_e = notes.data) === null || _e === void 0 ? void 0 : _e.values) === null || _f === void 0 ? void 0 : _f.columns) === null || _g === void 0 ? void 0 : _g.map((col) => { var _a; return `<th>${(_a = col === null || col === void 0 ? void 0 : col.name) !== null && _a !== void 0 ? _a : ''}</th>`; }).join('')) !== null && _h !== void 0 ? _h : ''}
                            </tr>
                        </thead>
                        <tbody>
                            ${(_m = (_l = (_k = (_j = notes.data) === null || _j === void 0 ? void 0 : _j.values) === null || _k === void 0 ? void 0 : _k.data) === null || _l === void 0 ? void 0 : _l.map((row) => {
                var _a, _b, _c, _d;
                return `
                                <tr>
                                    ${(_d = (_c = (_b = (_a = notes.data) === null || _a === void 0 ? void 0 : _a.values) === null || _b === void 0 ? void 0 : _b.columns) === null || _c === void 0 ? void 0 : _c.map((col) => { var _a; return `<td>${(_a = row === null || row === void 0 ? void 0 : row[col === null || col === void 0 ? void 0 : col.name]) !== null && _a !== void 0 ? _a : ''}</td>`; }).join('')) !== null && _d !== void 0 ? _d : ''}
                                </tr>
                            `;
            }).join('')) !== null && _m !== void 0 ? _m : ''}
                        </tbody>
                    </table>
                </div>
            `;
        return noteContent;
    };
    const componentHeights = {
        pageMargin: 47,
        header: 80,
        sectionHeading: 10,
        subHeading: 8,
        bodyMap: 200,
        listItem: 25,
        tableRow: 15,
        ultrasoundTableRow: 25,
        clinicDetail: 80,
        patientDetail: 60,
        lineHeight: 10,
        textBlockPadding: 0,
        diagnosticNote: 30,
        diagnosticTable: (rows) => rows * 20 + 40,
        textBlock: (text) => {
            if (!text)
                return 0;
            const explicitLineBreaks = (text.match(/\n/g) || []).length;
            const avgCharsPerLine = 100;
            const words = text.split(/\s+/);
            let currentLineLength = 0;
            let wrappedLines = 1;
            words.forEach(word => {
                if (currentLineLength + word.length > avgCharsPerLine) {
                    wrappedLines++;
                    currentLineLength = word.length;
                }
                else {
                    currentLineLength += word.length + 1;
                }
            });
            return Math.max(explicitLineBreaks + 1, wrappedLines) * componentHeights.lineHeight + componentHeights.textBlockPadding;
        },
        table: (rows, isUltrasound = false) => rows * (isUltrasound ? componentHeights.ultrasoundTableRow : componentHeights.tableRow) + 15
    };
    const maxPageHeight = 600;
    let currentPageHeight = 0;
    let pageNumber = 1;
    function formatKey(key) {
        return (key || '').replace(/([a-z])([A-Z])/g, '$1 $2').replace(/^./, str => str.toUpperCase());
    }
    function formatLabel(label) {
        var _a, _b, _c;
        return (_c = (_b = (_a = (label || '')) === null || _a === void 0 ? void 0 : _a.replace(/([a-z])([A-Z])/g, '$1 $2')) === null || _b === void 0 ? void 0 : _b.replace(/_/g, ' ')) === null || _c === void 0 ? void 0 : _c.replace(/^./, str => str.toUpperCase());
    }
    const checkPageBreak = (componentHeight) => {
        if (currentPageHeight === 0) {
            currentPageHeight += componentHeights.pageMargin;
        }
        if (currentPageHeight + componentHeight >= maxPageHeight) {
            pageNumber++;
            currentPageHeight = componentHeights.pageMargin + componentHeight;
            return true;
        }
        currentPageHeight += componentHeight;
        return false;
    };
    const wrapGroupIfNeeded = (content, totalHeight) => {
        const needsPageBreak = checkPageBreak(totalHeight);
        return needsPageBreak
            ? `<div class="page-break" data-page="${pageNumber}">${content}</div>`
            : `<div class="content-group" data-page="${pageNumber}">${content}</div>`;
    };
    const renderSubHeadingWithContent = (title, content, contentHeight) => {
        const totalHeight = componentHeights.subHeading + contentHeight;
        const titleClass = (title || '').trim().length > 0 ? 'bottom-divider' : '';
        return wrapGroupIfNeeded(`
            <p class="section-sub-heading ${titleClass}">${title}</p>
            ${content}
            `, totalHeight);
    };
    const renderSectionHeading = (title) => {
        return wrapGroupIfNeeded(`<h3 class="section-heading">${title}</h3>`, componentHeights.sectionHeading);
    };
    const renderBodyMap = (bodyMap, index) => {
        var _a, _b;
        if (!(bodyMap === null || bodyMap === void 0 ? void 0 : bodyMap.url) || !(bodyMap === null || bodyMap === void 0 ? void 0 : bodyMap.image))
            return '';
        const contentHeight = componentHeights.bodyMap +
            (((_a = bodyMap.notes) === null || _a === void 0 ? void 0 : _a.length) > 0 ? componentHeights.textBlock(bodyMap.notes) : 0);
        return renderSubHeadingWithContent('BODY MAP', `
            <div class="image-overlay-container">
                <img 
                    id='myImage${index}'
                    src="${bodyMap.url}"
                    alt='canine-image'
                    class="overlay-image"
                />
                ${bodyMap.image.replace('<svg version="1.1', '<svg class="overlay-svg" version="1.1')}
            </div>
            ${((_b = bodyMap.notes) === null || _b === void 0 ? void 0 : _b.length) > 0 ? `<p class="grey">${bodyMap.notes}</p>` : ''}
            `, contentHeight);
    };
    const renderList = (title, items, formatter = (item) => { var _a; return (_a = item === null || item === void 0 ? void 0 : item.label) !== null && _a !== void 0 ? _a : ''; }) => {
        if (!Array.isArray(items))
            return '';
        const contentHeight = items.reduce((height, item) => {
            let itemHeight = componentHeights.listItem;
            if (Array.isArray(item === null || item === void 0 ? void 0 : item.diagnosticNotes) && item.diagnosticNotes.length > 0) {
                itemHeight += item.diagnosticNotes.reduce((notesHeight, note) => {
                    var _a, _b, _c, _d;
                    return notesHeight + ((note === null || note === void 0 ? void 0 : note.templateType) === 'notes'
                        ? componentHeights.diagnosticNote
                        : componentHeights.diagnosticTable((_d = (_c = (_b = (_a = note === null || note === void 0 ? void 0 : note.data) === null || _a === void 0 ? void 0 : _a.values) === null || _b === void 0 ? void 0 : _b.data) === null || _c === void 0 ? void 0 : _c.length) !== null && _d !== void 0 ? _d : 0));
                }, 0);
            }
            return height + itemHeight;
        }, 0);
        const listContent = `
            <ul class="diagnostic-list">
                ${items
            .filter((item) => item)
            .map((item) => `
                        <li>
                            <div class="diagnostic-header"><b>${formatter(item)}</b></div>
                            ${Array.isArray(item === null || item === void 0 ? void 0 : item.diagnosticNotes) && item.diagnosticNotes.length > 0
            ? `<div class="diagnostic-notes-wrapper">
                                    ${item.diagnosticNotes
                .map((note) => renderDiagnosticNotes(note))
                .join('')}
                                   </div>`
            : ''}
                        </li>
                    `).join('')}
            </ul>
        `;
        return renderSubHeadingWithContent(title, listContent, contentHeight);
    };
    const renderTextBlock = (title, text, className = 'grey') => {
        if (!text)
            return '';
        const contentHeight = componentHeights.textBlock(text);
        return renderSubHeadingWithContent(title, `<p class="${className}" style="white-space: pre-line;">${text}</p>`, contentHeight);
    };
    const renderTable = (title, headers, rows, rowRenderer, isUltrasound = false) => {
        if (!Array.isArray(rows) || rows.length === 0)
            return '';
        const contentHeight = componentHeights.table(rows.length, isUltrasound);
        const tableContent = `
            <table class="three-section-table ${isUltrasound ? 'ultrasound-table' : ''}">
                <thead>
                    <tr>
                        ${headers.map(header => `<th>${header}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
                    ${rows.map(rowRenderer).join('')}
                </tbody>
            </table>
        `;
        return renderSubHeadingWithContent(title, tableContent, contentHeight);
    };
    // Initialize page height
    currentPageHeight = componentHeights.pageMargin +
        componentHeights.header +
        componentHeights.clinicDetail +
        componentHeights.patientDetail;
    pageNumber = 1;
    return `
    <!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
          rel="stylesheet"
        />
        <title>Medical Record</title>
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          body {
            font-family: "Inter", sans-serif;
            color: #000000;
            font-size: 11px;
            background: #f9f7f2;
          }
          .page-break {
            page-break-before: always;
            margin-top: 47px;
          }
          .content-group {
            break-inside: avoid;
            page-break-inside: avoid;
          }
          h1 { font-size: 42px; }
          h2 { font-size: 18px; }
          h3 { font-size: 14px; }
          .sub-heading { font-size: 12px; }
          .body-small { font-size: 9px; }
          .extra-light { 
            font-family: Inter;
            font-size: 36px;
            font-weight: 100;
            line-height: 43.57px;
            text-align: left;
            color: #000000;
        }
          .light { font-weight: 300; color:#000000; size:18px}
          .base { font-weight: 400; }
          .semi-bold { font-weight: 600; }
          .bold { font-weight: 700; }
          .grey { color: #000000; }
          .right-divider {
            padding-right: 11px;
            border-right: 0.5px solid #D6D6D6;
          }
          .bottom-divider {
            border-bottom: 0.5px solid #D6D6D6;
          }
          .font-12 { font-size: 12px }
          .main {
            padding: 47px 34px;
            min-height: 900px;
          }
          .header {
            break-after: avoid;
            page-break-after: avoid;
          }
          .sub-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
          }
          .sub-header_left {
            display: flex;
            align-items: center;
            gap: 11px;
          }
          .sub-header_right {
            display: flex;
            align-items: center;
            gap: 11px;
          }
          .info {
            border-top: 0.5px solid #D6D6D6;
            border-bottom: 0.5px solid #D6D6D6;
            margin-top: 24px;
            padding: 18px 0;
            display: flex;
            align-items: space-between;
            gap: 21px;
          }
          .section-wrapper {
            margin-top: 24px;
          }
          .section-heading {
            margin-bottom: 12px;
            padding-bottom: 6px;
            border-bottom: 0.5px solid #D6D6D6;
            break-after: avoid;
            page-break-after: avoid;
          }

          .section-sub-heading {
            font-family: Inter;
            font-size: 10px;
            font-weight: 400;
            line-height: 9.68px;
            letter-spacing: 0.05em;
            text-align: left;
            padding-bottom: 2px;
            margin-top: 14px;
            break-after: avoid;
            page-break-after: avoid;
            color: #000000;
          }

          .image-overlay-container {
            position: relative;
            width: 400px;
            height: 352px;
            margin: auto;
            border: 1px solid #ccc;
            break-inside: avoid;
            page-break-inside: avoid;
          }
          .overlay-image {
            width: 100%;
            height: 100%;
            object-fit: fill;
            z-index: 1;
          }
          .overlay-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2;
          }
          .unordered-list {
            color: #000000;
            padding-left: 14px;
            break-inside: avoid;
            page-break-inside: avoid;
          }
          table {
            width: 100%;
            border: 1px solid #D6D6D6;
            color: #000000;
            text-align: left;
            border-collapse: collapse;
            break-inside: avoid;
            page-break-inside: avoid;
          }
          table th,
          table td {
            padding: 6px 10px;
            border: 1px solid #D6D6D6;
          }
          .plan-table th:first-child { width: 80%; }
          .plan-table th:last-child { width: 20%; }
          .three-section-table th:last-child { width: 60%; }
          .three-section-table th:first-child, table td:first-child {
              border-left: 0.5px solid #59645D;
          }
          .three-section-table th {
              border-top: 0.5px solid #59645D;
          }
          .three-section-table th, .three-section-table td {
              border-right: 0.5px solid #59645D;
          }
          .three-section-table tr:last-child td {
              border-bottom: 0.5px solid #59645D;
          }
          .dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 3px;
          }
          .invoice-details {
            margin-top: 25px;
            border-top: 0.5px solid #D6D6D6;
            padding: 25px 0;
          }
          .invoice-details h5 {
            font-size: 18px;
            font-weight: 400;
            line-height: 24px;
            margin: 0;
            color: #000000;
          }
          .invoice-details h6 {
            font-size: 14px;
            font-weight: 500;
            line-height: 16.94px;
            margin: 0;
            color: #000000;
          }
          .invoice-details p {
            font-size: 10px;
            font-weight: 400;
            line-height: 16px;
            color: #000000;
          }
          .invoice-info {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin-top: 6px;
          }
          .patient-details {
            display: flex;
            justify-content: space-between;
            max-width: 100%;
            margin-bottom: 30px;
          }
          .patient-details div h6 {
            border-bottom: 0.5px solid #D6D6D6;
            font-family: Inter;
            font-size: 8px;
            font-weight: 400;
            line-height: 7.26px;
            letter-spacing: 0.05em;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
          }
          .patient-details div p {
            font-family: Inter;
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            text-align: left;
            text-underline-position: from-font;
            text-decoration-skip-ink: none;
            padding-top: 5px;
          }
          #patient-name {
            font-weight: 500;
          }
          .patient-info {
            display: flex;
            padding-top: 40px;
            padding-bottom: 20px;
          }
          .patient-info div p:first-child {
            color: #000000;
            font-size: 14px;
            font-weight: 500;
            line-height: 16.94px;
          }
          .patient-info div p:last-child {
            color: #000000;
            font-size: 14px;
            font-weight: 300;
            line-height: 16.94px;
          }
          .vertical_divider {
            border-left: 0.5px solid #BEBDBD;
            margin: 0 10px;
          }
          .diagnostic-list {
            list-style: none;
            padding: 0;
        }
        .diagnostic-list li {
            margin-bottom: 16px;
        }
        .diagnostic-header {
            font-weight: 600;
            margin-top:4px;
            margin-bottom: 8px;
        }
        .diagnostic-notes-wrapper {
            margin-left: 16px;
        }
        .template-name {
            font-weight: 500;
            font-size: 10px;
            margin-top:4px;
            margin-bottom: 4px;
            color: #59645D;
        }
        .diagnostic-note {
            margin-bottom: 12px;
        }
        .note-content {
            white-space: pre-line;
            color: #000000;
        }
        
          .green { background-color: #29823b; }
          .red { background-color: #dc2020; }
          @media print {
            body {
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
              margin: 0;
              background: white;
            }
            .main {
              background: white;
            }
            @page {
              size: A4;
              margin: 0;
            }
          }
        </style>
      </head>
      <body>
        <div class="main">
          <!-- Header Section -->
          <div class="header">
            <h1 class="extra-light">Medical Record</h1>
            <div class="sub-header">
              <div class="sub-header_left">
                <h2 class="light right-divider">General Visit</h2>
                <h2 class="light grey">${visitDate}</h2>
              </div>
            </div>
            <div class="invoice-details pt-20">
            <h6>${clinicName}</h6>
            <div class="invoice-info">
                <div>
                    <p>${clinicAddress}</p>
                </div>
                <div>
                    <p>${clinicPhone}</p>
                    <p>
                        ${clinicEmail}
                        </p>
                    <p>
                        ${clinicWebsite}
                    </p>
                </div>
            </div>
        </div>
            <div class="info">
              <div class="">
                <h3 class="body-small grey light bottom-divide">DOCTOR</h3>
                <p class="sub-heading font-12">${doctorNames}</p>
              </div>
              <div class="">
                <h3 class="body-small grey light bottom-divide">REASON FOR VISIT</h3>
                <p class="sub-heading font-12 ">${reasonForVisit}</p>
              </div>
            </div>
          </div>

        <div class="patient-info">
            <div>
                <p id='patient-name'>${formatKey(patientName)}</p>
            </div>
            ${breed
        ? `<div class="vertical_divider"></div>
            <div>
                <p>${formatLabel(breed)}</p>
            </div>`
        : ''}
        </div>
        <div class="patient-details">

            <div>
                <h6>OWNER DETAILS</h6>
                ${clientName ? `<p>${clientName}</p>` : ''} 
                ${ownerEmail ? `<p>${ownerEmail}</p>` : ''} 
                ${ownerPhone ? `<p>${ownerPhone}</p>` : ''} 
             </div>
                ${color
        ? `<div>
                 <h6>COLOUR</h6>
                 <p>${color}</p>
             </div>`
        : ''}
                ${gender
        ? `<div>
                 <h6>GENDER</h6>
                 <p>${gender}</p>
             </div>`
        : ''}
            ${weight
        ? `<div>
                 <h6>WEIGHT</h6>
                 <p>${weight}</p>
             </div>`
        : ''}
             ${dob
        ? `<div>
                 <h6>D.O.B</h6>
                 <p>${dob}</p>
             </div>`
        : ''}
             ${age
        ? `<div>
                 <h6>Age</h6>
                 <p>${age}</p>
             </div>`
        : ''} 
        </div>

          <!-- Subjective Section -->
           ${(subjective === null || subjective === void 0 ? void 0 : subjective.length) > 0
        ? `<section class="section-wrapper">
          ${renderSectionHeading('Subjective')}
          ${renderTextBlock('', subjective.trim())}
        </section>`
        : ''}

          <!-- Objective Section -->
          ${((_h = objective === null || objective === void 0 ? void 0 : objective.bodyMaps) === null || _h === void 0 ? void 0 : _h.length) > 0 ||
        ((_j = objective === null || objective === void 0 ? void 0 : objective.notes) === null || _j === void 0 ? void 0 : _j.length) > 0 ||
        (diagnosticsList === null || diagnosticsList === void 0 ? void 0 : diagnosticsList.length) > 0 ||
        (ultrasoundExam === null || ultrasoundExam === void 0 ? void 0 : ultrasoundExam.find((exam) => exam.status)) ||
        (vitalSigns === null || vitalSigns === void 0 ? void 0 : vitalSigns.some((entry) => Object.entries(entry).some(([key, value]) => key !== 'time' && value !== ''))) ||
        (physicalExam === null || physicalExam === void 0 ? void 0 : physicalExam.find((exam) => exam.status !== '')) ||
        (ultrasoundExam === null || ultrasoundExam === void 0 ? void 0 : ultrasoundExam.find((exam) => exam.status))
        ? `<section class="section-wrapper">
                ${renderSectionHeading('Objective')}
                ${(vitalSigns === null || vitalSigns === void 0 ? void 0 : vitalSigns.some((entry) => Object.entries(entry).some(([key, value]) => key !== 'time' && value !== '')))
            ? renderTable('VITALS', ['Vital', 'Time', 'Value'], [
                ...vitalSigns.reduce((acc, entry) => {
                    Object.entries(entry).forEach(([key, value]) => {
                        if (key !== 'time' &&
                            value !== '')
                            acc.add(key);
                    });
                    return acc;
                }, new Set())
            ].flatMap(key => {
                const formattedKey = formatKey(key);
                return vitalSigns
                    .filter((vital) => vital[key] !== undefined &&
                    vital[key] !== '')
                    .map((vital) => ({
                    vital: formattedKey,
                    time: vital.time,
                    value: vital[key]
                }));
            }), row => `
                          <tr>
                            <td>${row.vital}</td>
                            <td>${row.time.toUpperCase()}</td>
                            <td>${row.value}</td>
                          </tr>
                        `)
            : ''}
  
                  ${(physicalExam === null || physicalExam === void 0 ? void 0 : physicalExam.find((exam) => exam.status))
            ? renderTable('PHYSICAL EXAM', ['Area', 'Status', 'Comments'], physicalExam.filter((exam) => exam.status === 'normal' ||
                exam.status === 'abnormal'), exam => `
                          <tr>
                            <td>${exam.category}</td>
                            <td>
                              <span class="${exam.status === 'normal' ? 'green' : 'red'} dot"></span>
                              ${exam.status === 'normal' ? 'Normal' : 'Abnormal'}
                            </td>
                            <td>${exam.notes}</td>
                          </tr>
                        `)
            : ''}
                    ${(ultrasoundExam === null || ultrasoundExam === void 0 ? void 0 : ultrasoundExam.find((exam) => exam.status))
            ? renderTable('ULTRASOUND EXAM', ['Area', 'Status', 'Comments'], ultrasoundExam.filter((exam) => exam.status === 'normal' ||
                exam.status === 'abnormal'), exam => `
                        <tr>
                          <td>${exam.category}</td>
                          <td>
                            <span class="${exam.status === 'normal' ? 'green' : 'red'} dot"></span>
                            ${exam.status === 'normal' ? 'Normal' : 'Abnormal'}
                            </td>
                          <td style="white-space: pre-line;">${exam.notes.trim()}</td>
                          </tr>
                        `, true)
            : ''}
                ${bodyMapUrlsAndImages.map((bodyMap, index) => renderBodyMap(bodyMap, index)).join('')}
                
                ${((_k = objective === null || objective === void 0 ? void 0 : objective.notes) === null || _k === void 0 ? void 0 : _k.length) > 0
            ? renderTextBlock('OBJECTIVE NOTES', objective.notes)
            : ''}	

                ${(diagnosticsList === null || diagnosticsList === void 0 ? void 0 : diagnosticsList.length) > 0
            ? renderList('DIAGNOSTICS', diagnosticsList)
            : ''}
                </section>`
        : ''}
  
            <!-- Assessment Section -->
            ${(assessmentNotes === null || assessmentNotes === void 0 ? void 0 : assessmentNotes.length) > 0 ||
        (Array.isArray(assessmentConditions) &&
            (assessmentConditions === null || assessmentConditions === void 0 ? void 0 : assessmentConditions.length) > 0)
        ? `<section class="section-wrapper">
                  ${renderSectionHeading('Assessment')}
                  
                  ${(assessmentConditions === null || assessmentConditions === void 0 ? void 0 : assessmentConditions.length) > 0
            ? renderList('CONDITIONS', assessmentConditions, condition => formatLabel(condition === null || condition === void 0 ? void 0 : condition.label))
            : ''}
  
                  ${(assessmentNotes === null || assessmentNotes === void 0 ? void 0 : assessmentNotes.length) > 0
            ? renderTextBlock('ASSESSMENT NOTES', assessmentNotes)
            : ''}
                </section>`
        : ''}
  
            <!-- Plan Section -->
            ${(treatmentPlan === null || treatmentPlan === void 0 ? void 0 : treatmentPlan.length) > 0 || (treatmentNotes === null || treatmentNotes === void 0 ? void 0 : treatmentNotes.length) > 0
        ? `<section class="section-wrapper">
                  ${renderSectionHeading('Plan')}
  
                  ${(treatmentPlan === null || treatmentPlan === void 0 ? void 0 : treatmentPlan.length) > 0
            ? renderTable('TREATMENT', ['Treatment', 'Quantity'], treatmentPlan, treatment => {
                var _a;
                return `
                          <tr>
                            <td>
                              <p class="bold">${treatment.name}</p>
                              <p>${(_a = treatment === null || treatment === void 0 ? void 0 : treatment.subList) === null || _a === void 0 ? void 0 : _a.join(' ')}</p>
                            </td>
                            <td>${treatment.qty}</td>
                          </tr>
                        `;
            })
            : ''}
  
                 ${(treatmentNotes === null || treatmentNotes === void 0 ? void 0 : treatmentNotes.length) > 0
            ? renderTextBlock('NOTES', treatmentNotes)
            : ''}
  
                </section>`
        : ''}
          </div>
        </body>
      </html>
    `;
};
exports.GenerateEMRHtml1 = GenerateEMRHtml1;
//# sourceMappingURL=emr2.js.map