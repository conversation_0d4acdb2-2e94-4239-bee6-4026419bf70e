"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClinicIdexxService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const create_clinic_idexx_entity_1 = require("./entities/create-clinic-idexx.entity");
const typeorm_2 = require("typeorm");
const axios_1 = require("@nestjs/axios");
const rxjs_1 = require("rxjs");
const clinic_lab_report_entity_1 = require("../../clinic-lab-report/entities/clinic-lab-report.entity");
const patients_service_1 = require("../../patients/patients.service");
const clinic_lab_report_service_1 = require("../../clinic-lab-report/clinic-lab-report.service");
const s3_service_1 = require("../../utils/aws/s3/s3.service");
const uuidv7_1 = require("uuidv7");
const clinic_idexx_utils_service_1 = require("../../utils/idexx/clinic-idexx-utils.service");
const schedule_1 = require("@nestjs/schedule");
const appointments_service_1 = require("../../appointments/appointments.service");
const lab_report_entity_1 = require("../../clinic-lab-report/entities/lab-report.entity");
const appointment_details_entity_1 = require("../../appointments/entities/appointment-details.entity");
const appointment_entity_1 = require("../../appointments/entities/appointment.entity");
const enum_appointment_status_1 = require("../../appointments/enums/enum-appointment-status");
const winston_logger_service_1 = require("../../utils/logger/winston-logger.service");
const moment = require("moment");
// Import tough-cookie and axios-cookiejar-support
const tough_cookie_1 = require("tough-cookie");
const axios_2 = require("axios");
const axios_cookiejar_support_1 = require("axios-cookiejar-support");
let ClinicIdexxService = class ClinicIdexxService {
    constructor(httpService, patientService, s3Service, logger, appointmentsService, clinisIdexxUtilsService, clinicLabReportService, clinicLabReportRepository, clinicIdexxRepository, labReportRepository, appointmentRepository, appointmentDetailsRepository) {
        this.httpService = httpService;
        this.patientService = patientService;
        this.s3Service = s3Service;
        this.logger = logger;
        this.appointmentsService = appointmentsService;
        this.clinisIdexxUtilsService = clinisIdexxUtilsService;
        this.clinicLabReportService = clinicLabReportService;
        this.clinicLabReportRepository = clinicLabReportRepository;
        this.clinicIdexxRepository = clinicIdexxRepository;
        this.labReportRepository = labReportRepository;
        this.appointmentRepository = appointmentRepository;
        this.appointmentDetailsRepository = appointmentDetailsRepository;
        this.deviceUnitsCache = new Map();
        this.DEVICE_CACHE_TTL_MS = 15 * 60 * 1000; // 15 minutes cache time
    }
    async createIdexxEntry(createClinicIdexxDto, brandId) {
        try {
            this.logger.log('info', `Creating IDEXX entry for clinic ${createClinicIdexxDto.clinicId}`);
            const idexxItem = await this.clinicIdexxRepository.findOne({
                where: {
                    clinicId: createClinicIdexxDto.clinicId
                }
            });
            // if (idexxItem) {
            // 	this.logger.log(
            // 		`Conflict: IDEXX entry with clinic ID ${idexxItem.clinicId} already exists`
            // 	);
            // 	throw new ConflictException(
            // 		`The idexx entry with clinic ${idexxItem.clinicId} already exists`
            // 	);
            // }
            const base64Auth = this.clinisIdexxUtilsService.getAuthKey(createClinicIdexxDto.userName, createClinicIdexxDto.password);
            const updatedData = {
                ...createClinicIdexxDto,
                authKey: base64Auth
            };
            // this.logger.log(
            // 	'debug',
            // 	`Saving new IDEXX entry: ${JSON.stringify(updatedData)}`
            // );
            // const savedEntry = this.clinicIdexxRepository.save(updatedData);
            // this.logger.log(
            // 	'info',
            // 	`IDEXX entry created successfully for clinic ${createClinicIdexxDto.clinicId}`
            // );
            // return savedEntry;
            let savedEntry;
            if (idexxItem) {
                this.logger.log('debug', `Updating existing IDEXX entry for clinic ${createClinicIdexxDto.clinicId}`);
                savedEntry = await this.clinicIdexxRepository.save({
                    ...idexxItem,
                    ...updatedData,
                    brandId: brandId
                });
                this.logger.log('info', `IDEXX entry updated successfully for clinic ${createClinicIdexxDto.clinicId}`);
            }
            else {
                this.logger.log('debug', `Saving new IDEXX entry for clinic ${createClinicIdexxDto.clinicId}`);
                savedEntry = await this.clinicIdexxRepository.save({
                    ...updatedData,
                    brandId: brandId
                });
                this.logger.log('info', `IDEXX entry created successfully for clinic ${createClinicIdexxDto.clinicId}`);
            }
            return savedEntry;
        }
        catch (error) {
            this.logger.error('Unhandled error in createIdexxEntry', {
                error,
                clinicId: createClinicIdexxDto.clinicId
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getIdexxEntries(clinicId) {
        try {
            this.logger.log('info', `Fetching IDEXX entries for clinic ${clinicId}`);
            const [items, total] = await this.clinicIdexxRepository.findAndCount({
                where: {
                    clinicId
                }
            });
            this.logger.log('info', `Found ${total} IDEXX entries for clinic ${clinicId}`);
            return { items, total };
        }
        catch (error) {
            this.logger.error('Unhandled error in getIdexxEntries', {
                error,
                clinicId: clinicId
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async deletIdexxEntry(clinicIdexxId) {
        var _a;
        try {
            this.logger.log('info', `Deleting IDEXX entry with ID ${clinicIdexxId}`);
            const item = await this.clinicIdexxRepository.findOne({
                where: { id: clinicIdexxId }
            });
            if (!item) {
                this.logger.log(`NotFound: IDEXX entry with ID ${clinicIdexxId} does not exist`);
                throw new common_1.NotFoundException(`This entry with ${clinicIdexxId} doesn't exist`);
            }
            const result = await this.clinicIdexxRepository.delete({
                id: clinicIdexxId
            });
            const affectedRows = (_a = result.affected) !== null && _a !== void 0 ? _a : 0;
            if (result.affected === 0) {
                this.logger.log(`Delete operation found no records to delete for ID ${clinicIdexxId}`);
                // No rows were deleted
                return {
                    status: false,
                    message: 'No records found to delete.'
                };
            }
            else if (affectedRows > 1) {
                this.logger.error(`Unexpected: Multiple records deleted for ID ${clinicIdexxId}`);
                // More than one row was deleted (unlikely but possible)
                return {
                    status: false,
                    message: 'Multiple records deleted. Check your query.'
                };
            }
            else {
                this.logger.log('info', `IDEXX entry with ID ${clinicIdexxId} deleted successfully`);
                // Exactly one row was deleted
                return { status: true };
            }
        }
        catch (error) {
            this.logger.error('Unhandled error in deletIdexxEntry', {
                error,
                clinicIdexxId: clinicIdexxId
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getAllIDexxTestsList(clinicId) {
        try {
            this.logger.log('info', `Fetching all IDEXX tests list for clinic ID ${clinicId}`);
            const baseIDEXXURL = this.clinisIdexxUtilsService.getIntegrationBaseURL() + 'v1/'; //'https://integration.vetconnectplus.com/api/v1/';
            const apiUrl = baseIDEXXURL + 'ref/tests';
            this.logger.log('debug', `Constructed API URL: ${apiUrl}`);
            // Get autn key based on clinic id
            const clinicIdexxDetails = await this.clinicIdexxRepository.findOne({
                where: {
                    clinicId
                }
            });
            this.logger.log('info', `ClinicIDexx details are ${clinicIdexxDetails}`);
            if (!clinicIdexxDetails) {
                this.logger.log(`NotFound: No IDEXX entry found for clinic ID ${clinicId}`);
                throw new common_1.NotFoundException(`This entry with ${clinicId} doesn't exist`);
            }
            const authKey = clinicIdexxDetails.authKey;
            const headers = this.clinisIdexxUtilsService.getHeaders(authKey);
            const config = {
                headers: headers
            };
            this.logger.log('debug', `Request headers set for API call to IDEXX`);
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(apiUrl, config));
            const testList = response.data.list && response.data.list.length > 0
                ? response.data.list
                : [];
            this.logger.log('info', `Received ${testList.length} IDEXX tests for clinic ID ${clinicId}`);
            return testList;
        }
        catch (error) {
            // Check if the error is an instance of Error
            if (error instanceof Error) {
                this.logger.error(`Failed to fetch IDEXX tests for clinic ID ${clinicId}: ${error.message}`);
            }
            else {
                // Handle unexpected error type
                this.logger.error(`Failed to fetch IDEXX tests for clinic ID ${clinicId}: Unknown error`);
            }
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async createIdexxTestItem(createClinicIdexxTestItemDto, brandId) {
        this.logger.log('info', `Creating a new IDEXX test item`);
        try {
            const savedItem = await this.clinicLabReportRepository.save({
                ...createClinicIdexxTestItemDto,
                brandId: brandId
            });
            this.logger.log('info', `IDEXX test item created with ID ${savedItem.id}`);
            return savedItem;
        }
        catch (error) {
            // Check if the error is an instance of Error
            if (error instanceof Error) {
                this.logger.error(`Failed to create IDEXX test item: ${error.message}`);
            }
            else {
                // Handle unexpected error type
                this.logger.error(`Failed to create IDEXX test item: Unknown error`);
            }
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async deleteIdexxTestItem(clinicLabReportEntryId) {
        var _a;
        try {
            this.logger.log('info', `Deleting IDEXX test item with ID ${clinicLabReportEntryId}`);
            const item = await this.clinicLabReportRepository.findOne({
                where: { id: clinicLabReportEntryId }
            });
            if (!item) {
                this.logger.log(`NotFound: No IDEXX test item found with ID ${clinicLabReportEntryId}`);
                throw new common_1.NotFoundException(`This entry with ${clinicLabReportEntryId} doesn't exist`);
            }
            const result = await this.clinicLabReportRepository.delete({
                id: clinicLabReportEntryId
            });
            const affectedRows = (_a = result.affected) !== null && _a !== void 0 ? _a : 0;
            if (result.affected === 0) {
                this.logger.log(`Delete operation found no records to delete for ID ${clinicLabReportEntryId}`);
                // No rows were deleted
                return {
                    status: false,
                    message: 'No records found to delete.'
                };
            }
            else if (affectedRows > 1) {
                this.logger.error(`Unexpected: Multiple records deleted for ID ${clinicLabReportEntryId}`);
                // More than one row was deleted (unlikely but possible)
                return {
                    status: false,
                    message: 'Multiple records deleted. Check your query.'
                };
            }
            else {
                this.logger.log('info', `IDEXX test item with ID ${clinicLabReportEntryId} deleted successfully`);
                // Exactly one row was deleted
                return { status: true };
            }
        }
        catch (error) {
            this.logger.error('Unhandled error in clinicLabReportEntryId', {
                error,
                clinicLabReportEntryId: clinicLabReportEntryId
            });
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async createIdexxOrder(createIdexxOrderDto, brandId) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y;
        try {
            this.logger.log('info', `[IDEXX-START] Creating new IDEXX order for clinic ID ${createIdexxOrderDto.clinicId}`);
            const startTime = Date.now();
            // Parallelize initial database and setup queries
            const [appointmentDetails, patientDetail, clinicIdexxDetails] = await Promise.all([
                this.appointmentsService.getAppointmentDetails(createIdexxOrderDto.appointmentId),
                this.patientService.getPatientDetails(createIdexxOrderDto.patientId),
                this.clinicIdexxRepository.findOne({
                    where: {
                        clinicId: createIdexxOrderDto.clinicId
                    }
                })
            ]);
            // Check that we have the IDEXX credentials
            if (!clinicIdexxDetails) {
                this.logger.error(`[IDEXX-ERROR] No IDEXX details found for clinic ID ${createIdexxOrderDto.clinicId}`);
                throw new common_1.NotFoundException(`This entry with ${createIdexxOrderDto.clinicId} doesn't exist`);
            }
            // Get device units (needs auth key from clinicIdexxDetails) - WITH CACHING
            const authKey = clinicIdexxDetails.authKey;
            let deviceUnitSelected = '';
            // Check if we have a valid cache entry for device units
            const cacheKey = createIdexxOrderDto.clinicId;
            const cachedDeviceUnits = this.deviceUnitsCache.get(cacheKey);
            let responseDeviceUnits;
            if (cachedDeviceUnits &&
                Date.now() - cachedDeviceUnits.timestamp <
                    this.DEVICE_CACHE_TTL_MS) {
                // Use cached device units
                responseDeviceUnits = cachedDeviceUnits.units;
                this.logger.log('info', `[IDEXX-CACHE] Using cached device units for clinic ${createIdexxOrderDto.clinicId}`);
            }
            else {
                // Fetch device units from IDEXX API
                responseDeviceUnits = await this.getAllIDEXXDeviceUnits(createIdexxOrderDto.clinicId);
                // Cache the device units
                this.deviceUnitsCache.set(cacheKey, {
                    units: responseDeviceUnits,
                    timestamp: Date.now()
                });
                this.logger.log('info', `[IDEXX-CACHE] Cached device units for clinic ${createIdexxOrderDto.clinicId}`);
            }
            if (responseDeviceUnits.length > 0) {
                deviceUnitSelected = responseDeviceUnits[0].deviceSerialNumber;
            }
            const baseIDEXXURL = this.clinisIdexxUtilsService.getIntegrationBaseURL() + 'v1/';
            const apiUrl = baseIDEXXURL + 'order';
            // Get doctor name from appointment or use default "Doctor"
            const doctorName = ((_a = appointmentDetails.appointmentDoctors) === null || _a === void 0 ? void 0 : _a.find(doc => doc.primary))
                ? `${(_b = appointmentDetails.appointmentDoctors.find(doc => doc.primary)) === null || _b === void 0 ? void 0 : _b.doctor.firstName} ${(_c = appointmentDetails.appointmentDoctors.find(doc => doc.primary)) === null || _c === void 0 ? void 0 : _c.doctor.lastName}`
                : 'Doctor';
            this.logger.log('info', `[IDEXX-SETUP] Preparing IDEXX order for patient: ${patientDetail.patientName}, device: ${deviceUnitSelected}, doctor: ${doctorName}`);
            if (deviceUnitSelected.length > 0) {
                // Properly calculate age in years and months from birthdate for later use
                const birthDateString = (patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.age)
                    ? this.clinisIdexxUtilsService.getBirthDate(patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.age)
                    : moment().format('YYYY-MM-DD');
                const birthDateMoment = moment(birthDateString, 'YYYY-MM-DD');
                const nowMoment = moment();
                const ageYearsCalc = Math.max(0, nowMoment.diff(birthDateMoment, 'years'));
                const ageMonthsCalc = Math.max(0, nowMoment.diff(birthDateMoment, 'months') % 12);
                // Format dates using the appropriate format for the current API
                const formattedBirthdateForPut = this.clinisIdexxUtilsService.formatDateForApi(birthDateString);
                const currentDateForPut = this.clinisIdexxUtilsService.formatDateForApi(moment().format('YYYY-MM-DD'));
                // Original payload for initial POST request
                const body = {
                    patients: [
                        {
                            veterinarian: doctorName,
                            name: patientDetail.patientName,
                            patientId: createIdexxOrderDto.patientId,
                            speciesCode: (_d = patientDetail.species) === null || _d === void 0 ? void 0 : _d.toUpperCase(),
                            genderCode: this.clinisIdexxUtilsService.getGender(patientDetail.gender),
                            birthdate: (patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.age)
                                ? this.clinisIdexxUtilsService.getBirthDate(patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.age)
                                : moment().format('YYYY-MM-DD'),
                            microchip: patientDetail.microchipId,
                            client: {
                                id: '',
                                lastName: (_e = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners[0]) === null || _e === void 0 ? void 0 : _e.ownerBrand.lastName,
                                firstName: (_f = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners[0]) === null || _f === void 0 ? void 0 : _f.ownerBrand.firstName
                            }
                        }
                    ],
                    tests: [createIdexxOrderDto.integrationCode],
                    ivls: [
                        {
                            serialNumber: deviceUnitSelected
                        }
                    ]
                };
                const headers = this.clinisIdexxUtilsService.getHeaders(authKey);
                // Step 1: Initial IDEXX Order Creation (POST)
                this.logger.log('info', `[IDEXX-API-CALL] Creating initial IDEXX order via API`);
                const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post(apiUrl, body, { headers }));
                this.logger.log('info', `[IDEXX-RESPONSE] Initial API response status: ${response.status}`);
                if ((_g = response === null || response === void 0 ? void 0 : response.data) === null || _g === void 0 ? void 0 : _g.idexxOrderId) {
                    const idexxOrderId = (_h = response === null || response === void 0 ? void 0 : response.data) === null || _h === void 0 ? void 0 : _h.idexxOrderId;
                    const originalUiURL = (_j = response === null || response === void 0 ? void 0 : response.data) === null || _j === void 0 ? void 0 : _j.uiURL;
                    this.logger.log('info', `[IDEXX-ORDER-CREATED] IDEXX order created with ID ${idexxOrderId}, proceeding to automation`);
                    // Extract the token from the UI URL (needed for subsequent requests)
                    const tokenMatch = originalUiURL.match(/token=([^&]+)/);
                    const token = tokenMatch ? tokenMatch[1] : null;
                    if (!token) {
                        this.logger.error(`[IDEXX-TOKEN-ERROR] Failed to extract token from UI URL: ${originalUiURL}`);
                        // Store the lab report and return the UI URL for manual completion
                        const createLabReportDto = {
                            appointmentId: createIdexxOrderDto.appointmentId,
                            clinicLabReportId: createIdexxOrderDto.clinicLabReportId,
                            clinicId: createIdexxOrderDto.clinicId,
                            files: [],
                            status: 'PENDING',
                            integrationDetails: {
                                orderId: idexxOrderId,
                                uiURL: originalUiURL,
                                automationError: 'Failed to extract token from UI URL',
                                requiresManualCompletion: true
                            },
                            patientId: createIdexxOrderDto.patientId,
                            integrationOrderId: idexxOrderId,
                            lineItemId: createIdexxOrderDto.lineItemId
                        };
                        const labReport = await this.clinicLabReportService.createOrUpdateLabReport(createLabReportDto, brandId);
                        return {
                            orderId: idexxOrderId,
                            uiURL: originalUiURL,
                            labReportId: labReport === null || labReport === void 0 ? void 0 : labReport.id,
                            automationFailed: true,
                            message: 'Unable to automate IDEXX order completion. Please complete manually via the popup.'
                        };
                    }
                    // Create a cookie jar for handling cookies
                    const jar = new tough_cookie_1.CookieJar();
                    // Create a new axios instance with cookie jar support
                    const axiosInstance = (0, axios_cookiejar_support_1.wrapper)(axios_2.default.create({ jar }));
                    const idexxUiBaseUrl = (_k = this.clinisIdexxUtilsService
                        .getIntegrationBaseURL()) === null || _k === void 0 ? void 0 : _k.replace('/api/', '');
                    try {
                        // Step 2: First load the UI URL to establish a session with cookies
                        this.logger.log('info', `[IDEXX-SESSION] Establishing session with IDEXX UI`);
                        // Add headers that a browser would normally send
                        const browserLikeHeaders = {
                            Accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                            'Accept-Language': 'en-US,en;q=0.9',
                            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
                            'Cache-Control': 'no-cache',
                            Pragma: 'no-cache'
                        };
                        // Load the UI URL to establish a session
                        const cookieResponse = await axiosInstance.get(originalUiURL, {
                            headers: browserLikeHeaders
                        });
                        this.logger.log('info', `[IDEXX-SESSION] Session established, status: ${cookieResponse.status}`);
                        // Try to construct our own payload without fetching order details
                        const orderUrl = `${idexxUiBaseUrl}/ui/order/${idexxOrderId}`;
                        // Construct the minimal payload directly
                        const completeOrderPayload = {
                            id: parseInt(idexxOrderId, 10),
                            patients: [
                                {
                                    patientId: createIdexxOrderDto.patientId,
                                    name: patientDetail.patientName,
                                    microchip: patientDetail.microchipId || null,
                                    client: {
                                        id: null,
                                        firstName: ((_l = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners[0]) === null || _l === void 0 ? void 0 : _l.ownerBrand.firstName) || '',
                                        lastName: ((_m = patientDetail === null || patientDetail === void 0 ? void 0 : patientDetail.patientOwners[0]) === null || _m === void 0 ? void 0 : _m.ownerBrand.lastName) || '',
                                        address: null
                                    },
                                    kennelClub: null,
                                    species: patientDetail.species || 'Canine',
                                    speciesCode: (patientDetail.species || 'Canine').toUpperCase(),
                                    breed: patientDetail.breed || null,
                                    breedCode: null,
                                    gender: patientDetail.gender || 'Unknown',
                                    speciesType: (patientDetail.species || 'Canine').toUpperCase(),
                                    ageYears: ageYearsCalc,
                                    ageMonths: ageMonthsCalc,
                                    birthdate: formattedBirthdateForPut
                                }
                            ],
                            practiceOrderId: null,
                            followOn: false,
                            localDate: currentDateForPut,
                            external: false,
                            vet: doctorName,
                            ivls: [
                                {
                                    serialNumber: deviceUnitSelected,
                                    displayName: null
                                }
                            ],
                            notes: {
                                staff: null,
                                collectionDate: currentDateForPut,
                                urgent: null,
                                clinicalDetails: '',
                                fastedSample: null,
                                idexxAdvantage: null,
                                prevRefNum: null,
                                patientTravel: null,
                                zoonoticPathogen: null
                            },
                            tests: [
                                {
                                    code: createIdexxOrderDto.integrationCode,
                                    sd: []
                                }
                            ]
                        };
                        // Step 3: Submit the final order
                        this.logger.log('info', `[IDEXX-COMPLETE] Submitting order completion request`);
                        const completeOrderHeaders = {
                            Accept: 'application/json, text/plain, */*',
                            'Content-Type': 'application/json',
                            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
                            Referer: originalUiURL,
                            'X-Requested-With': 'XMLHttpRequest'
                        };
                        // Add the token as a query parameter if needed based on API version
                        const completeUrl = this.clinisIdexxUtilsService.getUrlWithToken(orderUrl, token);
                        const completeResponse = await axiosInstance.put(completeUrl, completeOrderPayload, {
                            headers: completeOrderHeaders
                        });
                        this.logger.log('info', `[IDEXX-SUCCESS] Successfully completed IDEXX order ${idexxOrderId}, status: ${completeResponse.status}`);
                        // Store this info in the lab report table - the order is now fully completed
                        const createLabReportDto = {
                            appointmentId: createIdexxOrderDto.appointmentId,
                            clinicLabReportId: createIdexxOrderDto.clinicLabReportId,
                            clinicId: createIdexxOrderDto.clinicId,
                            files: [],
                            status: 'PENDING', // This will get updated when results come in
                            integrationDetails: {
                                orderId: idexxOrderId,
                                automated: true,
                                selectedVet: doctorName // Store the appointment doctor name
                            },
                            patientId: createIdexxOrderDto.patientId,
                            integrationOrderId: idexxOrderId,
                            lineItemId: createIdexxOrderDto.lineItemId
                        };
                        const labReport = await this.clinicLabReportService.createOrUpdateLabReport(createLabReportDto, brandId);
                        const totalTime = Date.now() - startTime;
                        this.logger.log('info', `[IDEXX-API-TIMING] Total IDEXX order process took ${totalTime}ms`);
                        // Return success without the uiURL since we've automated the process
                        return {
                            orderId: idexxOrderId,
                            labReportId: labReport === null || labReport === void 0 ? void 0 : labReport.id,
                            message: 'IDEXX order created and automatically completed',
                            selectedVet: doctorName // Return the appointment doctor name
                        };
                    }
                    catch (automationError) {
                        // If automation fails, fall back to manual process by returning the uiURL
                        this.logger.error(`[IDEXX-AUTOMATION-ERROR] Error during automated IDEXX order completion for order ${idexxOrderId}:`, {
                            error: automationError.message,
                            status: (_o = automationError.response) === null || _o === void 0 ? void 0 : _o.status
                        });
                        // Still create the lab report with a special status indicating manual completion needed
                        const createLabReportDto = {
                            appointmentId: createIdexxOrderDto.appointmentId,
                            clinicLabReportId: createIdexxOrderDto.clinicLabReportId,
                            clinicId: createIdexxOrderDto.clinicId,
                            files: [],
                            status: 'PENDING',
                            integrationDetails: {
                                orderId: idexxOrderId,
                                uiURL: originalUiURL,
                                automationError: automationError.message,
                                requiresManualCompletion: true
                            },
                            patientId: createIdexxOrderDto.patientId,
                            integrationOrderId: idexxOrderId,
                            lineItemId: createIdexxOrderDto.lineItemId
                        };
                        const labReport = await this.clinicLabReportService.createOrUpdateLabReport(createLabReportDto, brandId);
                        const totalTime = Date.now() - startTime;
                        this.logger.log('info', `[IDEXX-API-TIMING] Failed IDEXX order process took ${totalTime}ms`);
                        // If we tried direct construction but it failed, try with fetching order details as a fallback
                        if (((_p = automationError.response) === null || _p === void 0 ? void 0 : _p.status) === 400) {
                            this.logger.log('info', `[IDEXX-FALLBACK] Direct payload construction failed, trying with order details fetch`);
                            try {
                                // Fetch order details as a fallback
                                const orderHeaders = {
                                    Accept: 'application/json, text/plain, */*',
                                    'Content-Type': 'application/json',
                                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
                                    Referer: originalUiURL,
                                    'X-Requested-With': 'XMLHttpRequest'
                                };
                                // Add the token to the query string if needed based on API version
                                const orderUrl = `${idexxUiBaseUrl}/ui/order/${idexxOrderId}`;
                                const orderGetUrl = this.clinisIdexxUtilsService.getUrlWithToken(orderUrl, token);
                                const orderResponse = await axiosInstance.get(orderGetUrl, {
                                    headers: orderHeaders
                                });
                                const currentOrderData = orderResponse.data;
                                // Prepare the payload with the fetched order details
                                const fallbackOrderPayload = {
                                    id: parseInt(idexxOrderId, 10), // Ensure ID is in the expected format
                                    patients: currentOrderData.patients.map((p) => {
                                        var _a, _b, _c, _d;
                                        return ({
                                            patientId: p.patientId,
                                            name: p.name,
                                            microchip: p.microchip || null,
                                            client: {
                                                id: ((_a = p.client) === null || _a === void 0 ? void 0 : _a.id) || null,
                                                firstName: (_b = p.client) === null || _b === void 0 ? void 0 : _b.firstName,
                                                lastName: (_c = p.client) === null || _c === void 0 ? void 0 : _c.lastName,
                                                address: ((_d = p.client) === null || _d === void 0 ? void 0 : _d.address) || null
                                            },
                                            kennelClub: p.kennelClub || null,
                                            species: p.species,
                                            speciesCode: p.speciesCode,
                                            breed: p.breed || null,
                                            breedCode: p.breedCode || null,
                                            gender: p.gender,
                                            speciesType: p.speciesType,
                                            ageYears: p.ageYears,
                                            ageMonths: p.ageMonths,
                                            // Use the birthdate directly from IDEXX's response as it's already in the correct format
                                            birthdate: p.birthdate
                                        });
                                    }),
                                    practiceOrderId: currentOrderData.practiceOrderId ||
                                        null,
                                    followOn: currentOrderData.followOn || false,
                                    localDate: currentOrderData.localDate ||
                                        this.clinisIdexxUtilsService.formatDateForApi(moment().format('YYYY-MM-DD')),
                                    external: currentOrderData.external || false,
                                    vet: doctorName, // Use appointment doctor directly
                                    ivls: currentOrderData.ivls,
                                    notes: {
                                        staff: ((_q = currentOrderData.notes) === null || _q === void 0 ? void 0 : _q.staff) ||
                                            null,
                                        // Use collection date directly or format current date
                                        collectionDate: ((_r = currentOrderData.notes) === null || _r === void 0 ? void 0 : _r.collectionDate) ||
                                            this.clinisIdexxUtilsService.formatDateForApi(moment().format('YYYY-MM-DD')),
                                        urgent: ((_s = currentOrderData.notes) === null || _s === void 0 ? void 0 : _s.urgent) ||
                                            null,
                                        // Ensure clinicalDetails is empty string instead of null
                                        clinicalDetails: ((_t = currentOrderData.notes) === null || _t === void 0 ? void 0 : _t.clinicalDetails) || '',
                                        fastedSample: ((_u = currentOrderData.notes) === null || _u === void 0 ? void 0 : _u.fastedSample) || null,
                                        idexxAdvantage: ((_v = currentOrderData.notes) === null || _v === void 0 ? void 0 : _v.idexxAdvantage) || null,
                                        prevRefNum: ((_w = currentOrderData.notes) === null || _w === void 0 ? void 0 : _w.prevRefNum) || null,
                                        patientTravel: ((_x = currentOrderData.notes) === null || _x === void 0 ? void 0 : _x.patientTravel) || null,
                                        zoonoticPathogen: ((_y = currentOrderData.notes) === null || _y === void 0 ? void 0 : _y.zoonoticPathogen) || null
                                    },
                                    tests: currentOrderData.tests.map((test) => ({
                                        code: test.code, // Only include the test code
                                        sd: test.sd || [] // And any sample data (sd)
                                    }))
                                };
                                // Try the fallback completion with token if needed
                                const fallbackPutUrl = this.clinisIdexxUtilsService.getUrlWithToken(orderUrl, token);
                                await axiosInstance.put(fallbackPutUrl, fallbackOrderPayload, {
                                    headers: orderHeaders // Use orderHeaders instead of completeOrderHeaders
                                });
                                this.logger.log('info', `[IDEXX-FALLBACK-SUCCESS] Successfully completed IDEXX order with fallback method`);
                                // Update the lab report to remove the automation error flag
                                if (labReport.integrationOrderId) {
                                    await this.clinicLabReportService.updateItemByIdexxOrderId(labReport.integrationOrderId, {
                                        integrationDetails: {
                                            orderId: idexxOrderId,
                                            automated: true,
                                            selectedVet: doctorName
                                        }
                                    });
                                }
                                return {
                                    orderId: idexxOrderId,
                                    labReportId: labReport === null || labReport === void 0 ? void 0 : labReport.id,
                                    message: 'IDEXX order created and automatically completed (with fallback)',
                                    selectedVet: doctorName
                                };
                            }
                            catch (fallbackError) {
                                this.logger.error(`[IDEXX-FALLBACK-ERROR] Fallback automation also failed: ${fallbackError.message}`);
                            }
                        }
                        // Return the original UI URL so the frontend can still open the popup for manual completion
                        return {
                            orderId: idexxOrderId,
                            uiURL: originalUiURL, // Include the UI URL for fallback manual completion
                            labReportId: labReport === null || labReport === void 0 ? void 0 : labReport.id,
                            message: 'IDEXX order created but automatic completion failed. Please complete manually via the popup.',
                            automationFailed: true
                        };
                    }
                }
                else {
                    this.logger.error(`[IDEXX-ERROR] Failed to create IDEXX order for clinic ID ${createIdexxOrderDto.clinicId}`);
                }
            }
        }
        catch (error) {
            // Check if the error has response data (likely an Axios error from HttpService)
            if (error.response) {
                this.logger.error(`[IDEXX-ERROR] Error creating IDEXX order: Status ${error.response.status}`, {
                    errorData: error.response.data,
                    clinicId: createIdexxOrderDto.clinicId,
                    patientId: createIdexxOrderDto.patientId
                });
                // Re-throw an HttpException with the status and data from the IDEXX API response
                throw new common_1.HttpException({
                    message: error.response.data || 'IDEXX API Error',
                    errorDetails: {
                        status: error.response.status,
                        data: error.response.data,
                        source: 'IDEXX API'
                    }
                }, error.response.status || common_1.HttpStatus.BAD_GATEWAY // Use response status, or a fallback
                );
            }
            else if (error instanceof Error) {
                // Handle other types of errors (e.g., network issues, code errors)
                this.logger.error(`[IDEXX-ERROR] Error creating IDEXX order: ${error.message}`, {
                    error,
                    clinicId: createIdexxOrderDto.clinicId,
                    patientId: createIdexxOrderDto.patientId
                });
                // Throw a generic server error for non-API related issues
                throw new common_1.HttpException({
                    message: error.message,
                    errorDetails: {
                        name: error.name,
                        source: 'Server'
                    }
                }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
            else {
                // Handle unexpected non-Error types
                this.logger.error(`[IDEXX-ERROR] Error creating IDEXX order: Unknown error`, {
                    clinicId: createIdexxOrderDto.clinicId,
                    patientId: createIdexxOrderDto.patientId
                });
                throw new common_1.HttpException({
                    message: 'An unknown error occurred while creating the IDEXX order.',
                    errorDetails: {
                        source: 'Unknown'
                    }
                }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }
        return {};
    }
    async cancelIdexxOrder(clinicId, idexxOrderId) {
        var _a, _b;
        try {
            this.logger.log('info', `Cancelling IDEXX order with ID ${idexxOrderId} for clinic ID ${clinicId}`);
            console.log('info', `Cancelling IDEXX order with ID ${idexxOrderId} for clinic ID ${clinicId}`);
            const baseIDEXXURL = this.clinisIdexxUtilsService.getIntegrationBaseURL() + 'v1/'; //'https://integration.vetconnectplus.com/api/v1/';
            const apiUrl = baseIDEXXURL + 'order' + '/' + idexxOrderId;
            this.logger.log('info', `API url for cancel order ${apiUrl}`);
            // Get autn key based on clinic id
            const clinicIdexxDetails = await this.clinicIdexxRepository.findOne({
                where: {
                    clinicId
                }
            });
            if (!clinicIdexxDetails) {
                this.logger.log(`No IDEXX details found for clinic ID ${clinicId}`);
                throw new common_1.NotFoundException(`This entry with ${clinicId} doesn't exist`);
            }
            const authKey = clinicIdexxDetails.authKey;
            const headers = this.clinisIdexxUtilsService.getHeaders(authKey);
            const config = {
                headers: headers,
                timeout: 10000
            };
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.delete(apiUrl, config));
            // Log only essential information
            this.logger.log('IDEXX order cancellation response', {
                status: response.status,
                statusText: response.statusText,
                orderId: idexxOrderId
            });
            return response.data;
        }
        catch (error) {
            // Log only essential error information
            this.logger.error('Error cancelling IDEXX order', {
                orderId: idexxOrderId,
                errorMessage: error === null || error === void 0 ? void 0 : error.message,
                errorStatus: (_a = error === null || error === void 0 ? void 0 : error.response) === null || _a === void 0 ? void 0 : _a.status,
                errorData: (_b = error === null || error === void 0 ? void 0 : error.response) === null || _b === void 0 ? void 0 : _b.data
            });
            // Check if the error has response data (likely an Axios error from HttpService)
            if (error.response) {
                throw new common_1.HttpException({
                    message: error.response.data || 'IDEXX API Error',
                    errorDetails: {
                        status: error.response.status,
                        data: error.response.data,
                        source: 'IDEXX API',
                        orderId: idexxOrderId
                    }
                }, error.response.status || common_1.HttpStatus.BAD_GATEWAY);
            }
            else if (error instanceof Error) {
                throw new common_1.HttpException({
                    message: error.message,
                    errorDetails: {
                        name: error.name,
                        stack: error.stack,
                        source: 'Server',
                        orderId: idexxOrderId
                    }
                }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
            else {
                throw new common_1.HttpException({
                    message: 'An unknown error occurred while cancelling the IDEXX order.',
                    errorDetails: {
                        error: error,
                        source: 'Unknown',
                        orderId: idexxOrderId
                    }
                }, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }
    }
    async runGetResults() {
        this.logger.log('runGetResults started.');
        console.log('runGetResults started.');
        try {
            const idexxLabReports = await this.clinicLabReportService
                .getIdexxLabReports()
                .catch(error => {
                this.logger.error('Failed to fetch IDEXX lab reports', {
                    error: error.message,
                    stack: error.stack
                });
                throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
            });
            if (!idexxLabReports || idexxLabReports.length === 0) {
                this.logger.log('No pending lab reports found');
                return;
            }
            // console.log('idexx reports', idexxLabReports.length);
            this.logger.log('Pending lab reports length', idexxLabReports.length);
            const reportsByClinic = new Map();
            this.logger.log('reports by clinic length', reportsByClinic.size);
            if (idexxLabReports.length > 0) {
                // Step 1: Group lab reports by clinic
                try {
                    idexxLabReports.forEach(labReport => {
                        var _a;
                        if (!((_a = labReport.clinic) === null || _a === void 0 ? void 0 : _a.id)) {
                            this.logger.log('Invalid clinic data in lab report', { labReportId: labReport.id });
                            return;
                        }
                        this.logger.log('LabReport inisde idexx', labReport);
                        const clinicId = labReport.clinic.id;
                        this.logger.log('ClinicId inisde idexx', clinicId);
                        this.logger.log('Reports by Clinic inside map', reportsByClinic.has(clinicId));
                        if (!reportsByClinic.has(clinicId)) {
                            reportsByClinic.set(clinicId, []);
                        }
                        this.logger.log('reports by clinic after setting', {
                            size: reportsByClinic.size,
                            keys: Array.from(reportsByClinic.keys()),
                            entries: Array.from(reportsByClinic.entries()).map(([key, value]) => ({
                                clinicId: key,
                                reportsCount: value.length
                            }))
                        });
                        reportsByClinic.get(clinicId).push(labReport);
                    });
                    this.logger.log('Final map size', reportsByClinic.size);
                    this.logger.log('Reports segregated per clinic', {
                        size: reportsByClinic.size
                        // clinics: Object.fromEntries(reportsByClinic)
                    });
                }
                catch (error) {
                    this.logger.error('Error grouping reports by clinic', {
                        error
                    });
                    throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
                }
                for (const [clinicId, clinicReports] of reportsByClinic) {
                    const clinic = clinicReports[0].clinic;
                    // Check if clinic has idexx integration
                    if (!clinic.idexx || clinic.idexx.length === 0)
                        continue;
                    const authKey = clinic.idexx[0].authKey;
                    let idexxApiResults;
                    try {
                        idexxApiResults =
                            await this.getLatestIDEXXResults(authKey);
                        console.log('idexxApi', idexxApiResults);
                        this.logger.log('Fetched latest results', {
                            count: idexxApiResults === null || idexxApiResults === void 0 ? void 0 : idexxApiResults.count,
                            batchId: idexxApiResults === null || idexxApiResults === void 0 ? void 0 : idexxApiResults.batchId
                        });
                    }
                    catch (error) {
                        this.logger.error('Error fetching results from Idexx API', { clinicId, error });
                        continue;
                    }
                    const resultData = idexxApiResults === null || idexxApiResults === void 0 ? void 0 : idexxApiResults.results;
                    if (resultData && resultData.length > 0) {
                        this.logger.log('Processing results', {
                            count: resultData.length
                        });
                        for (const labReport of clinicReports) {
                            for (const item of resultData) {
                                const resultId = item.resultId;
                                const idexxOrderId = item.requisitionId;
                                if (labReport.integrationOrderId !==
                                    idexxOrderId)
                                    continue;
                                this.logger.log('Processing result item', {
                                    resultId,
                                    idexxOrderId
                                });
                                try {
                                    // Step 3: Download the PDF and upload to S3
                                    const s3Result = await this.downloadPDFanduploadToS3(resultId, authKey);
                                    this.logger.log('PDF uploaded to S3', {
                                        fileKey: s3Result.fileKey
                                    });
                                    if (s3Result.fileKey.length > 0) {
                                        // Update the lab report in the database
                                        await this.clinicLabReportService.updateItemByIdexxOrderId(idexxOrderId, {
                                            fileKey: s3Result.fileKey,
                                            fileName: s3Result.fileName,
                                            status: (idexxApiResults === null || idexxApiResults === void 0 ? void 0 : idexxApiResults.hasMoreResults)
                                                ? 'PENDING'
                                                : 'COMPLETED'
                                        });
                                        this.logger.log('Updated lab report', {
                                            idexxOrderId
                                        });
                                    }
                                }
                                catch (error) {
                                    if (error instanceof Error) {
                                        this.logger.error('Error processing result item', { resultId, error: error.message });
                                    }
                                    else {
                                        this.logger.error('Error processing result item: Unknown error');
                                    }
                                }
                            }
                        }
                        //Acknoledge items
                        // If there are more results, take the batch id and acknowledge
                        this.logger.log('Acknowledging results', {
                            batchId: idexxApiResults.batchId
                        });
                        this.acknowledgeResult(idexxApiResults.batchId, authKey);
                        if ((idexxApiResults === null || idexxApiResults === void 0 ? void 0 : idexxApiResults.hasMoreResults) === true) {
                            console.log('More results available, recursively calling runGetResults.');
                            this.logger.log('More results available, recursively calling runGetResults.');
                            // this.runGetResults(); // do we need to do this as it might get convered in the next cron cycle ??
                        }
                        else {
                            this.logger.log('No more results to fetch.');
                        }
                    }
                }
            }
        }
        catch (error) {
            console.log('Error in fetching lab reports', error);
            this.logger.error('Error in fetching Idexx reports', error);
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    // Every Hour
    async syncIdexxLabReports() {
        var _a, _b;
        console.log('Cron Running');
        try {
            this.logger.log('Starting IDEXX lab report sync');
            // Calculate timestamp for 1 hour ago
            const oneHourAgo = new Date();
            oneHourAgo.setHours(oneHourAgo.getHours() - 1);
            // Get all completed appointments from last hour
            const completedAppointments = await this.appointmentRepository.find({
                where: {
                    status: enum_appointment_status_1.EnumAppointmentStatus.Completed,
                    updatedAt: (0, typeorm_2.MoreThan)(oneHourAgo)
                }
            });
            this.logger.log(`Found ${completedAppointments.length} completed appointments`);
            for (const appointment of completedAppointments) {
                try {
                    // Get lab reports for this appointment
                    const labReports = await this.labReportRepository.find({
                        where: { appointmentId: appointment.id },
                        relations: ['clinicLabReport']
                    });
                    for (const labReport of labReports) {
                        if (labReport.clinicLabReport.integrationType ===
                            'IDEXX') {
                            const files = labReport.files;
                            if (files && files.length > 0) {
                                let appointmentDetails = await this.appointmentDetailsRepository.findOne({
                                    where: {
                                        appointmentId: appointment.id
                                    }
                                });
                                if (!appointmentDetails) {
                                    appointmentDetails =
                                        this.appointmentDetailsRepository.create({
                                            appointmentId: appointment.id,
                                            details: {}
                                        });
                                }
                                const currentDetails = appointmentDetails.details;
                                // Map files to required format
                                const formattedFiles = files.map(file => ({
                                    id: file.id,
                                    s3Url: file.s3Url,
                                    fileKey: file.fileKey,
                                    fileName: file.fileName,
                                    fileSize: file.fileSize,
                                    uploadDate: new Date().toISOString()
                                }));
                                const updatedDetails = {
                                    ...currentDetails,
                                    objective: {
                                        ...(currentDetails.objective || {}),
                                        labReports: ((_b = (_a = currentDetails.objective) === null || _a === void 0 ? void 0 : _a.labReports) === null || _b === void 0 ? void 0 : _b.map((report) => {
                                            if (report.value ===
                                                labReport
                                                    .clinicLabReport.id) {
                                                return {
                                                    ...report,
                                                    files: formattedFiles,
                                                    labReportId: labReport.id,
                                                    integrationCode: labReport
                                                        .clinicLabReport
                                                        .integrationCode,
                                                    integrationType: labReport
                                                        .clinicLabReport
                                                        .integrationType
                                                };
                                            }
                                            return report;
                                        })) || []
                                    }
                                };
                                appointmentDetails.details = updatedDetails;
                                await this.appointmentDetailsRepository.save(appointmentDetails);
                                this.logger.log(`Updated appointment ${appointment.id} with IDEXX lab report files`);
                            }
                        }
                    }
                }
                catch (error) {
                    this.logger.error(`Error processing appointment ${appointment.id}`, error);
                    continue;
                }
            }
            this.logger.log('Completed IDEXX lab report sync');
        }
        catch (error) {
            this.logger.error('Error in IDEXX lab report sync', error);
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    // async runGetResults() {
    // 	this.logger.log('runGetResults started.');
    // 	console.log('runGetResults started.');
    // 	try {
    // 		const idexxLabReports =
    // 			await this.clinicLabReportService.getIdexxLabReports();
    // 		console.log(idexxLabReports);
    // 		this.logger.log('Pending lab reports', idexxLabReports);
    // 		if (idexxLabReports.length > 0) {
    // 			for (const labReport of idexxLabReports) {
    // 				console.log('Current lab report', labReport);
    // 				if (!labReport.clinic.idexx) return;
    // 				const authKey = labReport?.clinic?.idexx[0].authKey;
    // 				const idexxApiResults =
    // 					await this.getLatestIDEXXResults(authKey);
    // 				console.log('idexxApi', idexxApiResults);
    // 				this.logger.log('Fetched latest results', {
    // 					count: idexxApiResults?.count,
    // 					batchId: idexxApiResults?.batchId
    // 				});
    // 				const resultData = idexxApiResults?.results;
    // 				if (resultData?.length > 0) {
    // 					this.logger.log('Processing results', {
    // 						count: resultData.length
    // 					});
    // 					resultData.map(async (item: any) => {
    // 						const resultId = item.resultId;
    // 						const idexxOrderId = item.requisitionId;
    // 						console.log({ resultId, idexxOrderId });
    // 						this.logger.log('Processing result item', {
    // 							resultId,
    // 							idexxOrderId
    // 						});
    // 						try {
    // 							// Download the PDF and upload to S3
    // 							const s3Result =
    // 								await this.downloadPDFanduploadToS3(
    // 									resultId,
    // 									authKey
    // 								);
    // 							this.logger.log('PDF uploaded to S3', {
    // 								fileKey: s3Result.fileKey
    // 							});
    // 							if (s3Result.fileKey.length > 0) {
    // 								// Get the url and store in our database
    // 								//const tempUpdatedData =
    // 								await this.clinicLabReportService.updateItemByIdexxOrderId(
    // 									idexxOrderId,
    // 									{
    // 										fileKey: s3Result.fileKey,
    // 										fileName: s3Result.fileName
    // 									}
    // 								);
    // 								this.logger.log('Updated lab report', {
    // 									idexxOrderId
    // 								});
    // 								// If we need to handle this, then uncomment it
    // 								// if(tempUpdatedData) {
    // 								// 	const tempUpdatedFiles = await this.appointmentsService.updateFileDetailsForAppointmentId(tempUpdatedData?.appointmentId, tempUpdatedData.files, tempUpdatedData.clinicLabReportId);
    // 								// 	console.log("tempUpdatedFiles = ", tempUpdatedFiles);
    // 								// }
    // 							}
    // 						} catch (error) {
    // 							// Check if the error is an instance of Error
    // 							if (error instanceof Error) {
    // 								this.logger.error(
    // 									'Error processing result item',
    // 									{ resultId, error: error.message }
    // 								);
    // 							} else {
    // 								// Handle unexpected error type
    // 								this.logger.error(
    // 									'Error processing result item : Unknown error'
    // 								);
    // 							}
    // 						}
    // 					});
    // 				}
    // 			}
    // 		}
    // 		// if (idexxDetailsList.total > 0) {
    // 		// 	// We will see to loop later for all clinics
    // 		// 	const itemDetail = idexxDetailsList.items[0];
    // 		// 	this.logger.log('Processing itemDetail', {
    // 		// 		clinicId: itemDetail.clinicId
    // 		// 	});
    // 		// 	const result = await this.getLatestIDEXXResults(
    // 		// 		itemDetail.authKey
    // 		// 	);
    // 		// 	this.logger.log('Fetched latest results', {
    // 		// 		count: result?.count,
    // 		// 		batchId: result?.batchId
    // 		// 	});
    // 		// We take the result, update whatever needed and send a acknowlegemnt to the server
    // 		// const resultData = result?.results;
    // 		// if (resultData?.length > 0) {
    // 		// 	this.logger.log('Processing results', {
    // 		// 		count: resultData.length
    // 		// 	});
    // 		// 	resultData.map(async (item: any) => {
    // 		// 		const resultId = item.resultId;
    // 		// 		const idexxOrderId = item.requisitionId;
    // 		// 		this.logger.log('Processing result item', {
    // 		// 			resultId,
    // 		// 			idexxOrderId
    // 		// 		});
    // 		// 		try {
    // 		// 			// Download the PDF and upload to S3
    // 		// 			const s3Result =
    // 		// 				await this.downloadPDFanduploadToS3(
    // 		// 					resultId,
    // 		// 					itemDetail.authKey
    // 		// 				);
    // 		// 			this.logger.log('PDF uploaded to S3', {
    // 		// 				fileKey: s3Result.fileKey
    // 		// 			});
    // 		// 			if (s3Result.fileKey.length > 0) {
    // 		// 				// Get the url and store in our database
    // 		// 				//const tempUpdatedData =
    // 		// 				await this.clinicLabReportService.updateItemByIdexxOrderId(
    // 		// 					idexxOrderId,
    // 		// 					{
    // 		// 						fileKey: s3Result.fileKey,
    // 		// 						fileName: s3Result.fileName
    // 		// 					}
    // 		// 				);
    // 		// 				this.logger.log('Updated lab report', {
    // 		// 					idexxOrderId
    // 		// 				});
    // 		// 				// If we need to handle this, then uncomment it
    // 		// 				// if(tempUpdatedData) {
    // 		// 				// 	const tempUpdatedFiles = await this.appointmentsService.updateFileDetailsForAppointmentId(tempUpdatedData?.appointmentId, tempUpdatedData.files, tempUpdatedData.clinicLabReportId);
    // 		// 				// 	console.log("tempUpdatedFiles = ", tempUpdatedFiles);
    // 		// 				// }
    // 		// 			}
    // 		// 		} catch (error) {
    // 		// 			// Check if the error is an instance of Error
    // 		// 			if (error instanceof Error) {
    // 		// 				this.logger.error(
    // 		// 					'Error processing result item',
    // 		// 					{ resultId, error: error.message }
    // 		// 				);
    // 		// 			} else {
    // 		// 				// Handle unexpected error type
    // 		// 				this.logger.error(
    // 		// 					'Error processing result item : Unknown error'
    // 		// 				);
    // 		// 			}
    // 		// 		}
    // 		// 	});
    // 		// } else {
    // 		// 	this.logger.log('No results found to process.');
    // 		// }
    // 		/***********************************************************************/
    // 		// If there are more results, take the batch id and acknowledge
    // 		// 	if (result?.count > 0) {
    // 		// 		this.logger.log('Acknowledging results', {
    // 		// 			batchId: result.batchId
    // 		// 		});
    // 		// 		this.acknowledgeResult(result.batchId, itemDetail.authKey);
    // 		// 		if (result?.hasMoreResults == true) {
    // 		// 			this.logger.log(
    // 		// 				'More results available, recursively calling runGetResults.'
    // 		// 			);
    // 		// 			this.runGetResults();
    // 		// 		} else {
    // 		// 			this.logger.log('No more results to fetch.');
    // 		// 		}
    // 		// 	} else {
    // 		// 		this.logger.log('No batch id to acknowledge.');
    // 		// 	}
    // 		// 	/***********************************************************************/
    // 		// }
    // 	} catch (error) {
    // 		// Check if the error is an instance of Error
    // 		if (error instanceof Error) {
    // 			this.logger.error('Error in runGetResults', {
    // 				error: error.message
    // 			});
    // 		} else {
    // 			// Handle unexpected error type
    // 			this.logger.error('Error in runGetResults : Unknown error');
    // 		}
    // 	}
    // }
    async downloadPDFanduploadToS3(resultId, authKey) {
        try {
            this.logger.log('Starting PDF download and upload', { resultId });
            const baseIDEXXURL = this.clinisIdexxUtilsService.getPartnerBaseURL() + 'v3/'; // 'https://partner.vetconnectplus.com//api/v3/';
            const apiUrl = baseIDEXXURL + 'results/' + resultId + '/pdf';
            const headers = this.clinisIdexxUtilsService.getHeaders(authKey);
            // const response = await firstValueFrom(
            // 	this.httpService.get(apiUrl, {
            // 		responseType: 'arraybuffer',
            // 		headers: headers,
            // 		timeout: 10000
            // 	})
            // );
            const response = await this.downloadWithRetry(apiUrl, headers);
            const buffer = Buffer.from(response === null || response === void 0 ? void 0 : response.data);
            if (buffer.length === 0) {
                this.logger.log('Received empty PDF buffer', { resultId });
            }
            // Create a file
            const fileName = (0, uuidv7_1.uuidv4)() + '.pdf';
            const idexxFileKey = `idexx/${fileName}`;
            await this.s3Service.uploadPdfToS3(buffer, idexxFileKey);
            this.logger.log('PDF uploaded to S3', { fileKey: idexxFileKey });
            const tempUploadedData = {
                fileKey: idexxFileKey,
                fileName: fileName
            };
            return tempUploadedData;
        }
        catch (error) {
            // Check if the error is an instance of Error
            if (error instanceof Error) {
                this.logger.error('Error during PDF download/upload', {
                    resultId,
                    error: error.message
                });
            }
            else {
                // Handle unexpected error type
                this.logger.error('Error during PDF download/upload : Unknown error');
            }
        }
        return {
            fileKey: '',
            fileName: ''
        };
    }
    async getLatestIDEXXResults(authKey) {
        var _a;
        try {
            this.logger.log('Fetching latest IDEXX results', { authKey });
            const baseIDEXXURL = this.clinisIdexxUtilsService.getPartnerBaseURL() + 'v3/'; //'https://partner.vetconnectplus.com//api/v3/';
            const apiUrl = baseIDEXXURL + 'results/latest?limit=100';
            // TBD Need to get the auth key from the table
            const headers = this.clinisIdexxUtilsService.getHeaders(authKey);
            const config = {
                headers: headers
            };
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(apiUrl, config));
            this.logger.log('Fetched latest results successfully', {
                resultCount: (_a = response.data) === null || _a === void 0 ? void 0 : _a.count
            });
            return response.data;
        }
        catch (error) {
            // Check if the error is an instance of Error
            if (error instanceof Error) {
                this.logger.error('Error fetching latest IDEXX results', {
                    error: error.message
                });
            }
            else {
                // Handle unexpected error type
                this.logger.error('Error fetching latest IDEXX results : Unknown error');
            }
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST); // re-throw to handle in the caller if needed
        }
    }
    async acknowledgeResult(batchId, authKey) {
        try {
            this.logger.log('Acknowledging results', { batchId });
            const baseIDEXXURL = this.clinisIdexxUtilsService.getPartnerBaseURL() + 'v3/'; //'https://partner.vetconnectplus.com//api/v3/';
            const apiUrl = baseIDEXXURL + 'results/latest/confirm/' + batchId;
            const headers = this.clinisIdexxUtilsService.getHeaders(authKey);
            const config = {
                headers: headers
            };
            await (0, rxjs_1.firstValueFrom)(this.httpService.post(apiUrl, null, config));
            this.logger.log('Acknowledgement successful', { batchId });
            return true;
        }
        catch (error) {
            // Check if the error is an instance of Error
            if (error instanceof Error) {
                this.logger.warn('Warning in acknowledging results', {
                    batchId
                });
            }
            else {
                // Handle unexpected error type
                this.logger.warn('Warning in acknowledging results : unknown');
            }
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getAllIdexxEntries() {
        this.logger.log('Fetching all IDEXX entries from the database.');
        try {
            const [items, total] = await this.clinicIdexxRepository.findAndCount();
            this.logger.log('Fetched IDEXX entries successfully', { total });
            return { items, total };
        }
        catch (error) {
            // Check if the error is an instance of Error
            if (error instanceof Error) {
                this.logger.error('Error fetching IDEXX entries', {
                    error: error.message
                });
            }
            else {
                // Handle unexpected error type
                this.logger.error('Error fetching IDEXX entries : Unknown error');
            }
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    // Right now, this is used only in this class. But have removed private as it needs to be mocked in test clas
    async getAllIDEXXDeviceUnits(clinicId) {
        this.logger.log('Fetching all IDEXX device units', { clinicId });
        try {
            const baseIDEXXURL = this.clinisIdexxUtilsService.getIntegrationBaseURL() + 'v1/';
            const apiUrl = baseIDEXXURL + 'ivls/devices';
            // Get autn key based on clinic id
            const clinicIdexxDetails = await this.clinicIdexxRepository.findOne({
                where: {
                    clinicId
                }
            });
            if (!clinicIdexxDetails) {
                this.logger.log('Clinic IDEXX entry not found', { clinicId });
                throw new common_1.NotFoundException(`This entry with ${clinicId} doesn't exist`);
            }
            const authKey = clinicIdexxDetails.authKey;
            const headers = this.clinisIdexxUtilsService.getHeaders(authKey);
            const config = {
                headers: headers
            };
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(apiUrl, config));
            const deviceList = response.data.ivlsDeviceList &&
                response.data.ivlsDeviceList.length > 0
                ? response.data.ivlsDeviceList
                : [];
            this.logger.log('Fetched IDEXX device units successfully', {
                deviceCount: deviceList.length
            });
            return deviceList;
        }
        catch (error) {
            // Check if the error is an instance of Error
            if (error instanceof Error) {
                this.logger.error('Error fetching IDEXX device units', {
                    clinicId,
                    error: error.message
                });
            }
            else {
                // Handle unexpected error type
                this.logger.error('Error fetching IDEXX device units : Unknown error');
            }
            throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async downloadWithRetry(url, headers, retries = 3, delayMs = 2000) {
        for (let i = 0; i < retries; i++) {
            try {
                return await (0, rxjs_1.firstValueFrom)(this.httpService.get(url, {
                    responseType: 'arraybuffer',
                    headers: headers,
                    timeout: 10000
                }));
            }
            catch (error) {
                if (i === retries - 1)
                    throw new common_1.HttpException(error.message, common_1.HttpStatus.BAD_REQUEST);
                this.logger.warn(`Retrying download (attempt ${i + 1}/${retries}) due to error: ${error.message}`);
                console.log(`Retrying download (attempt ${i + 1}/${retries}) due to error: ${error.message}`);
                await (0, rxjs_1.delay)(delayMs);
            }
        }
        return null;
    }
    /**
     * Check if IDEXX orders can be deleted by verifying their status
     * @param clinicId - The clinic ID
     * @param labReportIds - Array of lab report IDs to check
     * @returns Promise<{ canBeDeleted: boolean, details: Array<{ labReportId: string, idexxOrderId: string, status: string, canDelete: boolean }> }>
     */
    async checkIdexxOrdersCanBeDeleted(clinicId, labReportIds) {
        var _a, _b, _c;
        try {
            this.logger.log('info', `Checking if IDEXX orders can be deleted for lab reports: ${labReportIds.join(', ')} in clinic ID ${clinicId}`);
            const baseIDEXXURL = this.clinisIdexxUtilsService.getIntegrationBaseURL() + 'v1/';
            // Get clinic IDEXX details for authentication
            const clinicIdexxDetails = await this.clinicIdexxRepository.findOne({
                where: {
                    clinicId
                }
            });
            if (!clinicIdexxDetails) {
                this.logger.log(`No IDEXX details found for clinic ID ${clinicId}`);
                throw new common_1.NotFoundException(`This entry with ${clinicId} doesn't exist`);
            }
            const authKey = clinicIdexxDetails.authKey;
            const headers = this.clinisIdexxUtilsService.getHeaders(authKey);
            const config = {
                headers: headers,
                timeout: 10000
            };
            const details = [];
            // Get all lab reports and their IDEXX order IDs
            for (const labReportId of labReportIds) {
                try {
                    // Get the lab report to extract IDEXX order ID
                    const labReport = await this.clinicLabReportService.findLabReportById(labReportId);
                    if (!labReport) {
                        details.push({
                            labReportId,
                            idexxOrderId: null,
                            status: null,
                            canDelete: false,
                            error: 'Lab report not found'
                        });
                        continue;
                    }
                    const idexxOrderId = labReport.integrationOrderId;
                    if (!idexxOrderId) {
                        details.push({
                            labReportId,
                            idexxOrderId: null,
                            status: null,
                            canDelete: false,
                            error: 'No IDEXX order ID found'
                        });
                        continue;
                    }
                    // Check the IDEXX order status using GET API
                    const apiUrl = baseIDEXXURL + 'order' + '/' + idexxOrderId;
                    try {
                        const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(apiUrl, config));
                        // Extract status from response.data.status.data.status
                        const orderStatus = (_a = response.data) === null || _a === void 0 ? void 0 : _a.status;
                        if (!orderStatus) {
                            details.push({
                                labReportId,
                                idexxOrderId,
                                status: null,
                                canDelete: false,
                                error: 'Unable to determine order status'
                            });
                            continue;
                        }
                        // Check if status is "CREATED" - only these can be deleted
                        const canDelete = orderStatus === 'CREATED';
                        details.push({
                            labReportId,
                            idexxOrderId,
                            status: orderStatus,
                            canDelete
                        });
                        this.logger.log('info', `IDEXX order ${idexxOrderId} status: ${orderStatus}, can delete: ${canDelete}`);
                    }
                    catch (orderError) {
                        this.logger.error('Error checking IDEXX order status', {
                            orderId: idexxOrderId,
                            labReportId,
                            errorMessage: orderError === null || orderError === void 0 ? void 0 : orderError.message,
                            errorStatus: (_b = orderError === null || orderError === void 0 ? void 0 : orderError.response) === null || _b === void 0 ? void 0 : _b.status
                        });
                        details.push({
                            labReportId,
                            idexxOrderId,
                            status: null,
                            canDelete: false,
                            error: `API error: ${((_c = orderError === null || orderError === void 0 ? void 0 : orderError.response) === null || _c === void 0 ? void 0 : _c.status) || (orderError === null || orderError === void 0 ? void 0 : orderError.message)}`
                        });
                    }
                }
                catch (labReportError) {
                    this.logger.error('Error processing lab report', {
                        labReportId,
                        errorMessage: labReportError === null || labReportError === void 0 ? void 0 : labReportError.message
                    });
                    details.push({
                        labReportId,
                        idexxOrderId: null,
                        status: null,
                        canDelete: false,
                        error: `Lab report error: ${labReportError === null || labReportError === void 0 ? void 0 : labReportError.message}`
                    });
                }
            }
            // All orders can be deleted only if every single one has status "CREATED"
            const canBeDeleted = details.every(detail => detail.canDelete === true);
            this.logger.log('info', `Overall deletion check result: ${canBeDeleted}, checked ${details.length} lab reports`);
            return {
                canBeDeleted,
                details
            };
        }
        catch (error) {
            this.logger.error('Error in checkIdexxOrdersCanBeDeleted', {
                labReportIds,
                clinicId,
                errorMessage: error === null || error === void 0 ? void 0 : error.message
            });
            throw new common_1.HttpException({
                message: error.message ||
                    'Failed to check IDEXX order deletion status',
                errorDetails: {
                    source: 'checkIdexxOrdersCanBeDeleted',
                    labReportIds,
                    clinicId
                }
            }, error instanceof common_1.HttpException
                ? error.getStatus()
                : common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.ClinicIdexxService = ClinicIdexxService;
__decorate([
    (0, schedule_1.Cron)('0 * * * *') // Run every 1 hour
    ,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ClinicIdexxService.prototype, "runGetResults", null);
__decorate([
    (0, schedule_1.Cron)('0 * * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ClinicIdexxService.prototype, "syncIdexxLabReports", null);
exports.ClinicIdexxService = ClinicIdexxService = __decorate([
    (0, common_1.Injectable)(),
    __param(7, (0, typeorm_1.InjectRepository)(clinic_lab_report_entity_1.ClinicLabReport)),
    __param(8, (0, typeorm_1.InjectRepository)(create_clinic_idexx_entity_1.CreateClinicIdexxEntity)),
    __param(9, (0, typeorm_1.InjectRepository)(lab_report_entity_1.LabReport)),
    __param(10, (0, typeorm_1.InjectRepository)(appointment_entity_1.AppointmentEntity)),
    __param(11, (0, typeorm_1.InjectRepository)(appointment_details_entity_1.AppointmentDetailsEntity)),
    __metadata("design:paramtypes", [axios_1.HttpService,
        patients_service_1.PatientsService,
        s3_service_1.S3Service,
        winston_logger_service_1.WinstonLogger,
        appointments_service_1.AppointmentsService,
        clinic_idexx_utils_service_1.ClinicIdexxUtilsService,
        clinic_lab_report_service_1.ClinicLabReportService,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ClinicIdexxService);
//# sourceMappingURL=clinic-idexx.service.js.map