"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddBalanceToPatients1727067288751 = void 0;
const typeorm_1 = require("typeorm");
class AddBalanceToPatients1727067288751 {
    async up(queryRunner) {
        await queryRunner.addColumns('patients', [
            new typeorm_1.TableColumn({
                name: 'balance',
                type: 'decimal',
                isNullable: true,
                default: 0
            })
        ]);
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('patients', 'balance');
    }
}
exports.AddBalanceToPatients1727067288751 = AddBalanceToPatients1727067288751;
//# sourceMappingURL=1727067288751-AddBalanceToPatients.js.map