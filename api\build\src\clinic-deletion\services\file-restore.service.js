"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileRestoreService = void 0;
const common_1 = require("@nestjs/common");
const winston_logger_service_1 = require("../../utils/logger/winston-logger.service");
const s3_service_1 = require("../../utils/aws/s3/s3.service");
const clinic_deletion_dto_1 = require("../dto/clinic-deletion.dto");
let FileRestoreService = class FileRestoreService {
    constructor(logger, s3Service) {
        this.logger = logger;
        this.s3Service = s3Service;
    }
    /**
     * Restore S3 files from backup
     */
    async restoreS3Files(backupBasePath, conflictResolution = clinic_deletion_dto_1.ConflictResolution.FAIL) {
        const startTime = Date.now();
        try {
            this.logger.log('Starting S3 file restore', {
                backupBasePath,
                conflictResolution
            });
            // Load file manifest
            const manifest = await this.loadFileManifest(backupBasePath);
            const result = {
                filesProcessed: 0,
                filesRestored: 0,
                filesSkipped: 0,
                conflicts: {
                    resolved: 0,
                    skipped: 0,
                    failed: 0
                },
                duration: 0
            };
            // Restore each file
            for (const fileInfo of manifest.files) {
                try {
                    const fileResult = await this.restoreSingleFile(fileInfo, conflictResolution);
                    result.filesProcessed++;
                    if (fileResult.restored) {
                        result.filesRestored++;
                    }
                    else {
                        result.filesSkipped++;
                    }
                    if (fileResult.conflictResolved) {
                        result.conflicts.resolved++;
                    }
                    else if (fileResult.conflictSkipped) {
                        result.conflicts.skipped++;
                    }
                    else if (fileResult.conflictFailed) {
                        result.conflicts.failed++;
                    }
                }
                catch (error) {
                    result.filesProcessed++;
                    result.filesSkipped++;
                    result.conflicts.failed++;
                    const errorMessage = error instanceof Error
                        ? error.message
                        : 'Unknown error';
                    this.logger.error(`Failed to restore file: ${fileInfo.originalPath}`, {
                        error: errorMessage
                    });
                    if (conflictResolution === clinic_deletion_dto_1.ConflictResolution.FAIL) {
                        throw error;
                    }
                }
            }
            result.duration = Date.now() - startTime;
            this.logger.log('S3 file restore completed', {
                filesProcessed: result.filesProcessed,
                filesRestored: result.filesRestored,
                filesSkipped: result.filesSkipped,
                duration: result.duration
            });
            return result;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('S3 file restore failed', {
                error: errorMessage,
                backupBasePath
            });
            throw error;
        }
    }
    /**
     * Analyze file restore conflicts before actual restore
     */
    async analyzeFileRestoreConflicts(backupBasePath) {
        try {
            const manifest = await this.loadFileManifest(backupBasePath);
            const conflicts = [];
            for (const fileInfo of manifest.files) {
                try {
                    const conflict = await this.analyzeFileConflict(fileInfo);
                    if (conflict) {
                        conflicts.push(conflict);
                    }
                }
                catch (error) {
                    const errorMessage = error instanceof Error
                        ? error.message
                        : 'Unknown error';
                    this.logger.warn(`Could not analyze conflict for ${fileInfo.originalPath}`, {
                        error: errorMessage
                    });
                }
            }
            return conflicts;
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error('Failed to analyze file restore conflicts', {
                error: errorMessage
            });
            throw error;
        }
    }
    /**
     * Load file manifest from S3
     */
    async loadFileManifest(backupBasePath) {
        try {
            const manifestPath = `${backupBasePath}/files/file-manifest.json`;
            const manifestData = await this.s3Service.getObject(manifestPath);
            if (!manifestData || !manifestData.Body) {
                throw new Error('File manifest not found or empty');
            }
            const manifestContent = manifestData.Body.toString();
            return JSON.parse(manifestContent);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            // If file manifest doesn't exist (e.g., skipS3Backup was used), return empty manifest
            if (errorMessage.includes('The specified key does not exist') ||
                errorMessage.includes('NoSuchKey') ||
                errorMessage.includes('File manifest not found')) {
                this.logger.log('No file manifest found - assuming no files to restore', {
                    backupBasePath
                });
                return {
                    backupId: '',
                    createdAt: new Date(),
                    files: [],
                    totalFiles: 0,
                    totalSizeBytes: 0
                };
            }
            this.logger.error('Failed to load file manifest', {
                error: errorMessage,
                backupBasePath
            });
            throw error;
        }
    }
    /**
     * Restore a single file
     */
    async restoreSingleFile(fileInfo, conflictResolution) {
        try {
            // Clean the original path (remove leading slash if present)
            const cleanOriginalPath = fileInfo.originalPath.startsWith('/')
                ? fileInfo.originalPath.substring(1)
                : fileInfo.originalPath;
            // Check if file already exists at original location
            const fileExists = await this.s3Service.objectExists(cleanOriginalPath);
            if (fileExists) {
                if (conflictResolution === clinic_deletion_dto_1.ConflictResolution.SKIP) {
                    this.logger.log(`Skipping existing file: ${cleanOriginalPath}`);
                    return {
                        restored: false,
                        conflictResolved: false,
                        conflictSkipped: true,
                        conflictFailed: false
                    };
                }
                else if (conflictResolution === clinic_deletion_dto_1.ConflictResolution.OVERWRITE) {
                    this.logger.log(`Overwriting existing file: ${cleanOriginalPath}`);
                    // Continue with restore (will overwrite)
                }
                else {
                    // FAIL mode
                    throw new Error(`File already exists: ${cleanOriginalPath}`);
                }
            }
            // Check if backup file exists
            const backupExists = await this.s3Service.objectExists(fileInfo.backupPath);
            if (!backupExists) {
                throw new Error(`Backup file not found: ${fileInfo.backupPath}`);
            }
            // Copy file from backup location to original location
            await this.s3Service.copyObject(fileInfo.backupPath, cleanOriginalPath);
            this.logger.log(`Restored file: ${cleanOriginalPath}`, {
                backupPath: fileInfo.backupPath,
                sizeBytes: fileInfo.sizeBytes
            });
            return {
                restored: true,
                conflictResolved: fileExists,
                conflictSkipped: false,
                conflictFailed: false
            };
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error(`Failed to restore file: ${fileInfo.originalPath}`, {
                error: errorMessage
            });
            return {
                restored: false,
                conflictResolved: false,
                conflictSkipped: false,
                conflictFailed: true
            };
        }
    }
    /**
     * Analyze conflict for a single file
     */
    async analyzeFileConflict(fileInfo) {
        try {
            // Clean the original path
            const cleanOriginalPath = fileInfo.originalPath.startsWith('/')
                ? fileInfo.originalPath.substring(1)
                : fileInfo.originalPath;
            // Check if file exists at original location
            const fileExists = await this.s3Service.objectExists(cleanOriginalPath);
            if (fileExists) {
                // Get existing file metadata
                const existingMetadata = await this.s3Service.getObjectMetadata(cleanOriginalPath);
                return {
                    originalPath: fileInfo.originalPath,
                    conflictType: 'file_exists',
                    existingFileSize: existingMetadata.ContentLength || 0,
                    backupFileSize: fileInfo.sizeBytes,
                    details: `File exists at original location with size ${existingMetadata.ContentLength || 0} bytes`
                };
            }
            return null;
        }
        catch (error) {
            // If we can't check, assume no conflict
            return null;
        }
    }
};
exports.FileRestoreService = FileRestoreService;
exports.FileRestoreService = FileRestoreService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        s3_service_1.S3Service])
], FileRestoreService);
//# sourceMappingURL=file-restore.service.js.map