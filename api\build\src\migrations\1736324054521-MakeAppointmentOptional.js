"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MakeAppointmentOptional1736324054521 = void 0;
class MakeAppointmentOptional1736324054521 {
    constructor() {
        this.name = 'MakeAppointmentOptional1736324054521';
    }
    async up(queryRunner) {
        // Make appointment_id nullable in carts table
        await queryRunner.query(`
            ALTER TABLE "carts" 
            ALTER COLUMN "appointment_id" DROP NOT NULL
        `);
        // Make appointment_id nullable in cart_items table
        await queryRunner.query(`
            ALTER TABLE "cart_items"
            ALTER COLUMN "appointment_id" DROP NOT NULL
        `);
    }
    async down(queryRunner) {
        // Revert changes if needed to rollback
        await queryRunner.query(`
            ALTER TABLE "carts"
            ALTER COLUMN "appointment_id" SET NOT NULL
        `);
        await queryRunner.query(`
            ALTER TABLE "cart_items"
            ALTER COLUMN "appointment_id" SET NOT NULL
        `);
    }
}
exports.MakeAppointmentOptional1736324054521 = MakeAppointmentOptional1736324054521;
//# sourceMappingURL=1736324054521-MakeAppointmentOptional.js.map