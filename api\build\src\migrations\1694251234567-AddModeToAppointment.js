"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddModeToAppointment1694251234567 = void 0;
class AddModeToAppointment1694251234567 {
    constructor() {
        this.name = 'AddModeToAppointment1694251234567';
    }
    async up(queryRunner) {
        // Add the mode column with default value
        await queryRunner.query(`ALTER TABLE "appointments" ADD "mode" character varying DEFAULT 'Clinic'`);
        // Update all existing records to have 'Clinic' value
        await queryRunner.query(`UPDATE "appointments" SET "mode" = 'Clinic' WHERE "mode" IS NULL`);
        // Make the column non-nullable after setting values
        await queryRunner.query(`ALTER TABLE "appointments" ALTER COLUMN "mode" SET NOT NULL`);
    }
    async down(queryRunner) {
        // Drop the mode column
        await queryRunner.query(`ALTER TABLE "appointments" DROP COLUMN "mode"`);
    }
}
exports.AddModeToAppointment1694251234567 = AddModeToAppointment1694251234567;
//# sourceMappingURL=1694251234567-AddModeToAppointment.js.map